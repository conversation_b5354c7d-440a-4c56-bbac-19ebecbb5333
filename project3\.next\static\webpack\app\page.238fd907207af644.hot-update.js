"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./lib/api-helper.ts":
/*!***************************!*\
  !*** ./lib/api-helper.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Config: () => (/* binding */ Config),\n/* harmony export */   MakeApiCallAsync: () => (/* binding */ MakeApiCallAsync),\n/* harmony export */   convertUSDToIQD: () => (/* binding */ convertUSDToIQD),\n/* harmony export */   fetchCurrencyRate: () => (/* binding */ fetchCurrencyRate),\n/* harmony export */   formatPrice: () => (/* binding */ formatPrice)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./config */ \"(app-pages-browser)/./lib/config.ts\");\n\n\n// Configure axios defaults for HTTPS connections\naxios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].defaults.timeout = 30000; // 30 seconds timeout\n// Handle self-signed certificates for local development\nif ( true && window.location.protocol === 'https:' && _config__WEBPACK_IMPORTED_MODULE_0__.Config.ADMIN_BASE_URL.includes('localhost')) {\n    axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].defaults.httpsAgent = {\n        rejectUnauthorized: false\n    };\n}\nconst Config = {\n    ADMIN_BASE_URL: _config__WEBPACK_IMPORTED_MODULE_0__.Config.ADMIN_BASE_URL,\n    API_VERSION: 'v1',\n    DYNAMIC_METHOD_SUB_URL: 'api/v1/dynamic/dataoperation/',\n    END_POINT_NAMES: {\n        GET_CATEGORIES_LIST: 'get-categories-list',\n        SIGNUP_USER: 'signup-user',\n        GET_HOME_SCREEN_BANNER: 'get-home-screen-banner',\n        GET_RECENT_PRODUCTS: 'get-recents-products-list',\n        GET_POPULAR_PRODUCTS: 'get-popular-products-list',\n        GET_HOT_DEAL_PRODUCTS: 'get-hot-deal-products',\n        GET_CAMPAIGNS_LIST: 'get-web-campaign-list',\n        GET_PRODUCTS_LIST: 'get-products-list',\n        GET_ALL_PRODUCTS: 'api/v1/products/get-all-products',\n        GET_MANUFACTURERS_LIST: 'get-manufacturers-list',\n        GET_TAGS_LIST: 'get-tags-list',\n        GET_CURRENCY_RATE: 'get-currency-rate',\n        GET_COUPON_CODE_DISCOUNT: 'get-coupon-code-discount-value/calculate-coupon-discount',\n        ..._config__WEBPACK_IMPORTED_MODULE_0__.Config.END_POINT_NAMES\n    },\n    COMMON_CONTROLLER_SUB_URL: 'api/v1/common/'\n};\nconst GetTokenForHeader = async ()=>{\n    // Implement token retrieval logic here\n    // For example, from localStorage or a secure storage\n    return localStorage.getItem('token') || null;\n};\nconst GetUserIdForHeader = async ()=>{\n    // Implement user ID retrieval logic here\n    return localStorage.getItem('userId') || null;\n};\nconst MakeApiCallAsync = async function(endPointName, methodSubURL, param, headers, methodType) {\n    let loading = arguments.length > 5 && arguments[5] !== void 0 ? arguments[5] : true;\n    try {\n        // Create a copy of headers to avoid modifying the original object\n        const updatedHeaders = {\n            ...headers\n        };\n        // Check if Authorization header already exists\n        if (!updatedHeaders.hasOwnProperty('Authorization')) {\n            // If not, try to add it using the token from GetTokenForHeader\n            const token = await GetTokenForHeader();\n            if (token) {\n                updatedHeaders['Authorization'] = 'Bearer ' + token;\n            }\n        }\n        // For backward compatibility, also add Token header if it doesn't exist\n        if (!updatedHeaders.hasOwnProperty('Token')) {\n            const token = await GetTokenForHeader();\n            updatedHeaders['Token'] = token !== null && token !== void 0 ? token : \"\";\n        }\n        // Add user id in header\n        if (!updatedHeaders.hasOwnProperty('UserID')) {\n            const UserID = await GetUserIdForHeader();\n            updatedHeaders['UserID'] = UserID !== null && UserID !== void 0 ? UserID : \"\";\n        }\n        // Always ensure proper content type headers are set\n        if (!updatedHeaders.hasOwnProperty('Accept')) {\n            updatedHeaders['Accept'] = 'application/json';\n        }\n        if (!updatedHeaders.hasOwnProperty('Content-Type')) {\n            updatedHeaders['Content-Type'] = 'application/json';\n        }\n        const URL = Config['ADMIN_BASE_URL'] + (methodSubURL === null || methodSubURL == undefined ? Config['DYNAMIC_METHOD_SUB_URL'] : methodSubURL) + endPointName;\n        methodType = methodType !== null && methodType !== void 0 ? methodType : \"POST\";\n        const axiosConfig = {\n            headers: updatedHeaders,\n            responseType: 'json',\n            timeout: 30000,\n            withCredentials: false // Disable sending cookies with cross-origin requests\n        };\n        if (methodType === 'POST') {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(URL, param, axiosConfig);\n            return response;\n        } else if (methodType == 'GET') {\n            axiosConfig.params = param; // For GET requests, params should be used\n            const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(URL, axiosConfig);\n            return response;\n        } else {\n            // Return a default error response for unsupported method types\n            return {\n                data: {\n                    errorMessage: \"Unsupported method type: \".concat(methodType),\n                    status: 'method_not_supported'\n                }\n            };\n        }\n    } catch (error) {\n        console.error('API call failed:', error);\n        // Return a structured error response instead of throwing\n        // This allows components to handle errors more gracefully\n        // Create a response object with the ApiResponse interface\n        const errorResponse = {\n            data: {\n                errorMessage: 'An unexpected error occurred',\n                status: 'unknown_error'\n            }\n        };\n        // Type guard for axios error with response\n        if (error && typeof error === 'object' && 'response' in error && error.response) {\n            var _axiosError_response, _axiosError_response1;\n            // The request was made and the server responded with a status code\n            // that falls out of the range of 2xx\n            const axiosError = error;\n            const responseData = (_axiosError_response = axiosError.response) === null || _axiosError_response === void 0 ? void 0 : _axiosError_response.data;\n            errorResponse.data = {\n                errorMessage: (responseData === null || responseData === void 0 ? void 0 : responseData.errorMessage) || 'An error occurred while processing your request.',\n                status: (_axiosError_response1 = axiosError.response) === null || _axiosError_response1 === void 0 ? void 0 : _axiosError_response1.status\n            };\n        // Type guard for axios error with request but no response\n        } else if (error && typeof error === 'object' && 'request' in error) {\n            // The request was made but no response was received\n            // This is likely a network error, CORS issue, or server not running\n            const axiosError = error;\n            let networkErrorMessage = 'Network error: No response received from server.';\n            // Check if it's a CORS issue\n            if (axiosError.message && axiosError.message.includes('Network Error')) {\n                networkErrorMessage = 'Network Error: This may be due to CORS policy restrictions, server unavailability, or an invalid SSL certificate. Please check that:';\n                networkErrorMessage += '\\n1. The server is running and accessible';\n                networkErrorMessage += '\\n2. The URL is correct: ' + Config.ADMIN_BASE_URL;\n                networkErrorMessage += '\\n3. CORS is properly configured on the server';\n                networkErrorMessage += '\\n4. If using HTTPS, the SSL certificate is valid';\n            }\n            errorResponse.data = {\n                errorMessage: networkErrorMessage,\n                status: 'network_error'\n            };\n        } else {\n            // Something happened in setting up the request that triggered an Error\n            // Type guard for standard Error object\n            const errorMessage = error && typeof error === 'object' && 'message' in error ? error.message : 'An unexpected error occurred';\n            errorResponse.data = {\n                errorMessage,\n                status: 'request_error'\n            };\n        }\n        return errorResponse;\n    }\n};\n// Currency rate service\nconst fetchCurrencyRate = async ()=>{\n    try {\n        const response = await MakeApiCallAsync('getrate', 'api/v1/common/', {}, {}, 'GET');\n        if (response && response.data && !response.data.errorMessage) {\n            return parseInt(response.data) || 1500; // Default rate if parsing fails\n        }\n        return 1500; // Default fallback rate\n    } catch (error) {\n        console.error('Error fetching currency rate:', error);\n        return 1500; // Default fallback rate\n    }\n};\nconst convertUSDToIQD = (usdPrice, rate)=>{\n    return Math.round(usdPrice * rate);\n};\nconst formatPrice = function(price) {\n    let currency = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'USD';\n    if (currency === 'IQD') {\n        return \"\".concat(price.toLocaleString(), \" IQD\");\n    }\n    return \"$\".concat(price.toFixed(2));\n};\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api-helper.ts\n"));

/***/ })

});