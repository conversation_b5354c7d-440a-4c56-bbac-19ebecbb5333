(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2162],{2336:(e,t,r)=>{"use strict";r.d(t,{p:()=>o});var s=r(5155),a=r(2115),n=r(9602);let o=a.forwardRef((e,t)=>{let{className:r,type:a,...o}=e;return(0,s.jsx)("input",{type:a,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...o})});o.displayName="Input"},2523:(e,t,r)=>{"use strict";r.d(t,{T:()=>s});let s={ADMIN_BASE_URL:"https://admin.codemedicalapps.com/",DYNAMIC_METHOD_SUB_URL:"api/",COMMON_CONTROLLER_SUB_URL:"api/common/",END_POINT_NAMES:{DOWNLOAD_DIGITAL_FILE:"downloadDigitalFile",GET_COUPON_CODE_DISCOUNT:"get-coupon-code-discount-value/calculate-coupon-discount"}}},2650:(e,t,r)=>{Promise.resolve().then(r.bind(r,9745))},2862:(e,t,r)=>{"use strict";r.d(t,{$g:()=>u,MakeApiCallAsync:()=>c,TS:()=>n,XX:()=>d,k6:()=>l});var s=r(2651),a=r(2523);s.A.defaults.timeout=3e4,"https:"===window.location.protocol&&a.T.ADMIN_BASE_URL.includes("localhost")&&(s.A.defaults.httpsAgent={rejectUnauthorized:!1});let n={ADMIN_BASE_URL:a.T.ADMIN_BASE_URL,API_VERSION:"v1",DYNAMIC_METHOD_SUB_URL:"api/v1/dynamic/dataoperation/",END_POINT_NAMES:{...a.T.END_POINT_NAMES,GET_CATEGORIES_LIST:"get-categories-list",SIGNUP_USER:"signup-user",GET_USER_LOGIN:"get-user-login",GET_USER_BY_PHONE:"get-user-by-phone",RESET_PASSWORD_BY_PHONE:"reset-password-by-phone",GET_HOME_SCREEN_BANNER:"get-home-screen-banner",GET_RECENT_PRODUCTS:"get-recents-products-list",GET_POPULAR_PRODUCTS:"get-popular-products-list",GET_HOT_DEAL_PRODUCTS:"get-hot-deal-products",GET_CAMPAIGNS_LIST:"get-web-campaign-list",GET_PRODUCTS_LIST:"get-products-list",GET_ALL_PRODUCTS:"api/v1/products/get-all-products",GET_MANUFACTURERS_LIST:"get-manufacturers-list",GET_TAGS_LIST:"get-tags-list",GET_CURRENCY_RATE:"get-currency-rate",GET_COUPON_CODE_DISCOUNT:"get-coupon-code-discount-value/calculate-coupon-discount"},COMMON_CONTROLLER_SUB_URL:"api/v1/common/"},o=async()=>localStorage.getItem("token")||null,i=async()=>localStorage.getItem("userId")||null,c=async function(e,t,r,a,c){arguments.length>5&&void 0!==arguments[5]&&arguments[5];try{let l={...a};if(!l.hasOwnProperty("Authorization")){let e=await o();e&&(l.Authorization="Bearer "+e)}if(!l.hasOwnProperty("Token")){let e=await o();l.Token=null!=e?e:""}if(!l.hasOwnProperty("UserID")){let e=await i();l.UserID=null!=e?e:""}l.hasOwnProperty("Accept")||(l.Accept="application/json"),l.hasOwnProperty("Content-Type")||(l["Content-Type"]="application/json");let d=n.ADMIN_BASE_URL+(null===t||void 0==t?n.DYNAMIC_METHOD_SUB_URL:t)+e;c=null!=c?c:"POST";let u={headers:l,responseType:"json",timeout:3e4,withCredentials:!1};if("POST"===c)return await s.A.post(d,r,u);if("GET"==c)return u.params=r,await s.A.get(d,u);return{data:{errorMessage:"Unsupported method type: ".concat(c),status:"method_not_supported"}}}catch(t){console.error("API call failed:",t);let e={data:{errorMessage:"An unexpected error occurred",status:"unknown_error"}};if(t&&"object"==typeof t&&"response"in t&&t.response){var l,d;let r=null===(l=t.response)||void 0===l?void 0:l.data;e.data={errorMessage:(null==r?void 0:r.errorMessage)||"An error occurred while processing your request.",status:null===(d=t.response)||void 0===d?void 0:d.status}}else if(t&&"object"==typeof t&&"request"in t){let r="Network error: No response received from server.";t.message&&t.message.includes("Network Error")&&(r="Network Error: This may be due to CORS policy restrictions, server unavailability, or an invalid SSL certificate. Please check that:\n1. The server is running and accessible\n2. The URL is correct: "+n.ADMIN_BASE_URL+"\n3. CORS is properly configured on the server\n4. If using HTTPS, the SSL certificate is valid"),e.data={errorMessage:r,status:"network_error"}}else{let r=t&&"object"==typeof t&&"message"in t?t.message:"An unexpected error occurred";e.data={errorMessage:r,status:"request_error"}}return e}},l=async()=>{try{let e=await c("getrate","api/v1/common/",{},{},"GET");if(e&&e.data&&!e.data.errorMessage)return parseInt(e.data)||1500;return 1500}catch(e){return console.error("Error fetching currency rate:",e),1500}},d=(e,t)=>Math.round(e*t),u=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD";return"IQD"===t?"".concat(e.toLocaleString()," IQD"):"$".concat(e.toFixed(2))}},4085:(e,t,r)=>{"use strict";r.d(t,{$:()=>l,r:()=>c});var s=r(5155),a=r(2115),n=r(2317),o=r(1027),i=r(9602);let c=(0,o.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-red-500 text-destructive-foreground hover:bg-red-600",outline:"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=a.forwardRef((e,t)=>{let{className:r,variant:a,size:o,asChild:l=!1,...d}=e,u=l?n.DX:"button";return(0,s.jsx)(u,{className:(0,i.cn)(c({variant:a,size:o,className:r})),ref:t,...d})});l.displayName="Button"},5007:(e,t,r)=>{"use strict";r.d(t,{Wu:()=>i,Zp:()=>o,wL:()=>c});var s=r(5155),a=r(2115),n=r(9602);let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});o.displayName="Card",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",r),...a})}).displayName="CardHeader",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",r),...a})}).displayName="CardTitle",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",r),...a})}).displayName="CardDescription";let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",r),...a})});i.displayName="CardContent";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",r),...a})});c.displayName="CardFooter"},5785:(e,t,r)=>{"use strict";r.d(t,{J:()=>l});var s=r(5155),a=r(2115),n=r(6195),o=r(1027),i=r(9602);let c=(0,o.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(n.b,{ref:t,className:(0,i.cn)(c(),r),...a})});l.displayName=n.b.displayName},6046:(e,t,r)=>{"use strict";var s=r(6658);r.o(s,"useParams")&&r.d(t,{useParams:function(){return s.useParams}}),r.o(s,"usePathname")&&r.d(t,{usePathname:function(){return s.usePathname}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},7110:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i,t:()=>c});var s=r(5155),a=r(2115);let n={en:{menu:"Menu",newsletter:"Newsletter",enterEmail:"Enter your email",newsletterDisclaimer:"Subscribe to our newsletter to receive updates and exclusive offers",popularCategories:"Popular Categories!",settings:"Settings",language:"Language",theme:"Theme",color:"Color",save:"Save",cancel:"Cancel",close:"Close",phone:"***************",phonenumber:"Phone Number",email:"<EMAIL>",liveChat:"Live Chat",welcome:"Welcome",logout:"Logout",login:"Login",signup:"Sign Up",signUp:"Sign Up",category:"Category",categories:"Categories",filters:"Filters",clearAll:"Clear All",products:"Products",loadingCategories:"Loading categories...",home:"Home",todayDeals:"Today's Deals",followUs:"Follow Us",aboutUs:"About Us",contactUs:"Contact Us",tryAgain:"Try Again",noProductsFound:"No products found",allProducts:"All Products",quickLinks:"Quick Links",about:"About Us",contact:"Contact Us",hotDeals:"Hot Deals",customerArea:"Customer Area",myAccount:"My Account",orders:"Orders",cart:"Cart",wishlist:"Wishlist",paymentMethods:"Payment Methods",location:"Location",callUs:"Call Us",emailUs:"Email Us",subscribe:"Subscribe",name:"Name",subject:"Subject",message:"Message",sendMessage:"Send Message",sending:"Sending...",messageSent:"Message sent successfully!",messageError:"Failed to send message",contactInfo:"Contact Information",address:"Address",findUs:"Find Us"},ar:{menu:"القائمة",popularCategories:"الفئات الشائعة!",settings:"الإعدادات",language:"اللغة",theme:"المظهر",color:"اللون",save:"حفظ",cancel:"إلغاء",close:"إغلاق",phone:"***************",phonenumber:"رقم الهاتف",email:"<EMAIL>",liveChat:"محادثة مباشرة",welcome:"مرحباً",logout:"تسجيل الخروج",login:"تسجيل الدخول",signup:"تسجيل جديد",signUp:"تسجيل جديد",category:"الفئة",categories:"الفئات",products:"المنتجات",loadingCategories:"جاري تحميل الفئات...",filters:"المرشحات",clearAll:"مسح الكل",home:"الرئيسية",todayDeals:"عروض اليوم",followUs:"تابعنا",aboutUs:"من نحن",contactUs:"اتصل بنا",tryAgain:"حاول مرة أخرى",noProductsFound:"لم يتم العثور على منتجات",allProducts:"جميع المنتجات",quickLinks:"روابط سريعة",about:"من نحن",contact:"اتصل بنا",hotDeals:"عروض ساخنة",customerArea:"منطقة العملاء",myAccount:"حسابي",orders:"الطلبات",cart:"السلة",wishlist:"المفضلة",paymentMethods:"طرق الدفع",location:"الموقع",callUs:"اتصل بنا",emailUs:"راسلنا",subscribe:"اشترك",name:"الاسم",subject:"الموضوع",message:"الرسالة",sendMessage:"إرسال الرسالة",sending:"جاري الإرسال...",messageSent:"تم إرسال الرسالة بنجاح!",messageError:"فشل في إرسال الرسالة",contactInfo:"معلومات الاتصال",address:"العنوان",findUs:"موقعنا"}},o=(0,a.createContext)(void 0);function i(e){let{children:t}=e,[r,i]=(0,a.useState)("light"),[c,l]=(0,a.useState)("en"),[d,u]=(0,a.useState)("#0074b2");return(0,a.useEffect)(()=>{document.documentElement.style.setProperty("--primary",d),document.documentElement.style.setProperty("--primary-foreground","#ffffff")},[]),(0,s.jsx)(o.Provider,{value:{theme:r,language:c,primaryColor:d,toggleTheme:()=>{i("light"===r?"dark":"light")},setLanguage:e=>{l(e),document.documentElement.dir="ar"===e?"rtl":"ltr"},setPrimaryColor:e=>{u(e)},t:e=>(function(e,t){let r=n[t];return e in r?r[e]:"en"!==t&&e in n.en?n.en[e]:e})(e,c)},children:t})}function c(){let e=(0,a.useContext)(o);if(void 0===e)throw Error("useSettings must be used within a SettingsProvider");return e}},9134:(e,t,r)=>{"use strict";r.d(t,{dj:()=>m});var s=r(2115);let a=0,n=new Map,o=e=>{if(n.has(e))return;let t=setTimeout(()=>{n.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);n.set(e,t)},i=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?o(r):e.toasts.forEach(e=>{o(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},c=[],l={toasts:[]};function d(e){l=i(l,e),c.forEach(e=>{e(l)})}function u(e){let{...t}=e,r=(a=(a+1)%100).toString(),s=()=>d({type:"DISMISS_TOAST",toastId:r});return d({type:"ADD_TOAST",toast:{...t,id:r,open:!0,onOpenChange:e=>{e||s()}}}),{id:r,dismiss:s,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:r}})}}function m(){let[e,t]=s.useState(l);return s.useEffect(()=>(c.push(t),()=>{let e=c.indexOf(t);e>-1&&c.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>d({type:"DISMISS_TOAST",toastId:e})}}},9602:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var s=r(3463),a=r(9795);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}},9664:(e,t,r)=>{"use strict";r.d(t,{j:()=>o});var s=r(9904),a=r(399);let n=(0,s.Wp)({apiKey:"AIzaSyBlr1YG3CXkyn3yUJ44xvFFCcpfSj0pwFU",authDomain:"codemedical-19ec6.firebaseapp.com",projectId:"codemedical-19ec6",storageBucket:"codemedical-19ec6.firebasestorage.app",messagingSenderId:"494556459416",appId:"1:494556459416:web:180c7e662f6e3ae4e43bed",measurementId:"G-BRC9LRF5DE"}),o=(0,a.xI)(n)},9745:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>T});var s=r(5155),a=r(2115),n=r(9664),o=r(399),i=r(5007),c=r(4085),l=r(2336),d=r(5785),u=r(2610),m=r.n(u);r(133);var p=r(9124),f=r(8173),g=r.n(f),h=r(6046),x=r(6954),y=r(6336);let b=(0,r(7401).A)("KeyRound",[["path",{d:"M2.586 17.414A2 2 0 0 0 2 18.828V21a1 1 0 0 0 1 1h3a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1h1a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1h.172a2 2 0 0 0 1.414-.586l.814-.814a6.5 6.5 0 1 0-4-4z",key:"1s6t7t"}],["circle",{cx:"16.5",cy:"7.5",r:".5",fill:"currentColor",key:"w0ekpg"}]]);var v=r(1773),w=r(9053),N=r(8686),S=r(2598),j=r(738),A=r(7110),E=r(2862),_=r(9134);function T(){let[e,t]=(0,a.useState)("phone"),[r,u]=(0,a.useState)("964"),[f,T]=(0,a.useState)("iq"),[P,C]=(0,a.useState)(""),[O,R]=(0,a.useState)(null),[U,I]=(0,a.useState)(0),[D,M]=(0,a.useState)(!1),[k,L]=(0,a.useState)(""),[F,G]=(0,a.useState)(!1),[B,Y]=(0,a.useState)(!1),[H,q]=(0,a.useState)(""),[V,z]=(0,a.useState)({newPassword:"",confirmPassword:""}),{t:$}=(0,A.t)(),{toast:J}=(0,_.dj)(),W=(0,h.useRouter)();(0,a.useEffect)(()=>{fetch("https://ipapi.co/json/").then(e=>e.json()).then(e=>{e.country_code&&(T(e.country_code.toLowerCase()),u(e.country_calling_code.replace("+","")))}).catch(()=>{T("iq"),u("964")})},[]),(0,a.useEffect)(()=>{if(U>0){let e=setTimeout(()=>I(U-1),1e3);return()=>clearTimeout(e)}},[U]);let Q=()=>{I(60)},X=async e=>{if(e.preventDefault(),M(!0),L(""),!r){L("Phone number is required"),M(!1);return}if(r.length<8){L("Phone number must be at least 8 digits"),M(!1);return}if(!/^\+?[1-9]\d{1,14}$/.test(r)){L("Please enter a valid phone number"),M(!1);return}try{var s;let e=await (0,E.MakeApiCallAsync)(E.TS.END_POINT_NAMES.GET_USER_BY_PHONE,null,{requestParameters:{PhoneNumber:"+".concat(r)}},{Accept:"application/json","Content-Type":"application/json"},"POST",!0);if(null===(s=e.data)||void 0===s?void 0:s.errorMessage){L("No account found with this phone number"),M(!1);return}let a=new o.kT(n.j,"recaptcha-container",{size:"invisible",callback:()=>{}}),i="+".concat(r),c=await (0,o.ik)(n.j,i,a);R(c),t("verification"),Q(),J({title:"Verification Code Sent",description:"We've sent a verification code to ".concat(i)})}catch(e){L(e.message||"Failed to send verification code"),console.error("Error:",e),J({title:"Error",description:e.message||"Failed to send verification code"})}finally{M(!1)}},Z=async()=>{if(!(U>0)){M(!0),L("");try{let e=new o.kT(n.j,"recaptcha-container-resend",{size:"invisible",callback:()=>{}}),t="+".concat(r),s=await (0,o.ik)(n.j,t,e);R(s),Q(),J({title:"Code Resent",description:"A new verification code has been sent to your phone"})}catch(e){L("Failed to resend verification code"),console.error("Error:",e),J({title:"Error",description:"Failed to resend verification code"})}finally{M(!1)}}},K=async e=>{e.preventDefault(),M(!0),L("");try{if((await O.confirm(P)).user){let e=await (0,E.MakeApiCallAsync)(E.TS.END_POINT_NAMES.GET_USER_BY_PHONE,null,{requestParameters:{PhoneNumber:"+".concat(r)}},{Accept:"application/json","Content-Type":"application/json"},"POST",!0);if(e.data&&!e.data.errorMessage){let t;t="string"==typeof e.data.data?JSON.parse(e.data.data):e.data.data,Array.isArray(t)&&t.length>0&&q(t[0].EmailAddress||t[0].Email||"")}t("reset"),J({title:"Phone Verified",description:"Your phone number has been verified. You can now reset your password."})}}catch(e){L(e.message||"Invalid verification code"),console.error("Error:",e),J({title:"Verification Failed",description:e.message||"Invalid verification code"})}finally{M(!1)}},ee=async e=>{if(e.preventDefault(),M(!0),L(""),!V.newPassword||V.newPassword.length<6){L("Password must be at least 6 characters long"),M(!1);return}if(V.newPassword!==V.confirmPassword){L("Passwords do not match"),M(!1);return}try{let e={requestParameters:{PhoneNumber:"+".concat(r),NewPassword:V.newPassword,Email:H}},s=await (0,E.MakeApiCallAsync)(E.TS.END_POINT_NAMES.RESET_PASSWORD_BY_PHONE,null,e,{Accept:"application/json","Content-Type":"application/json"},"POST",!0);if(s.data&&!s.data.errorMessage){let e;e="string"==typeof s.data.data?JSON.parse(s.data.data):s.data.data,Array.isArray(e)&&e.length>0&&"Saved Successfully"===e[0].ResponseMsg?(J({title:"Password Reset Successful",description:"Your password has been reset successfully. You can now log in with your new password."}),setTimeout(()=>{W.push("/login")},2e3)):L("Failed to reset password. Please try again.")}else{var t;L((null===(t=s.data)||void 0===t?void 0:t.errorMessage)||"Failed to reset password. Please try again.")}}catch(e){L(e.message||"Failed to reset password"),console.error("Error:",e),J({title:"Reset Failed",description:e.message||"Failed to reset password"})}finally{M(!1)}};return(0,s.jsx)("div",{className:"min-h-screen bg-background flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("h2",{className:"mt-6 text-3xl font-extrabold",children:["phone"===e&&"Reset Password","verification"===e&&"Verify Your Phone","reset"===e&&"Create New Password"]}),(0,s.jsxs)("p",{className:"mt-2 text-sm text-muted-foreground",children:["phone"===e&&"Enter your phone number to reset your password","verification"===e&&"Enter the code we sent to your phone","reset"===e&&"Enter your new password"]})]}),(0,s.jsxs)(i.Zp,{className:"mt-8 p-8 shadow-xl bg-card/100",children:[(0,s.jsx)("div",{className:"flex justify-center mb-8",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center ".concat("phone"===e?"bg-primary text-primary-foreground":"bg-primary/20 text-primary"),children:(0,s.jsx)(x.A,{className:"w-4 h-4"})}),(0,s.jsx)("div",{className:"w-16 h-1 ".concat("phone"===e?"bg-primary/20":"bg-primary")}),(0,s.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center ".concat("verification"===e?"bg-primary text-primary-foreground":"reset"===e?"bg-primary":"bg-primary/20 text-primary"),children:(0,s.jsx)(y.A,{className:"w-4 h-4"})}),(0,s.jsx)("div",{className:"w-16 h-1 ".concat("reset"===e?"bg-primary":"bg-primary/20")}),(0,s.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center ".concat("reset"===e?"bg-primary text-primary-foreground":"bg-primary/20 text-primary"),children:(0,s.jsx)(b,{className:"w-4 h-4"})})]})}),(0,s.jsxs)(p.P.div,{variants:{hidden:{opacity:0,x:-20},visible:{opacity:1,x:0},exit:{opacity:0,x:20}},initial:"hidden",animate:"visible",exit:"exit",transition:{duration:.3},children:["phone"===e&&(0,s.jsxs)("form",{onSubmit:X,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(d.J,{className:"block text-sm font-medium mb-2 text-center",children:"Phone Number"}),(0,s.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,s.jsx)("div",{className:"w-full max-w-[300px]",children:(0,s.jsx)(m(),{country:f,value:r,onChange:e=>{u(e),L("")},enableSearch:!0,searchPlaceholder:"Search country...",containerClass:"w-full",inputClass:"w-full p-4 border rounded-lg focus:ring-2 focus:ring-primary/50 ".concat(k?"border-destructive":""),buttonClass:"!border-input !bg-background hover:!bg-accent",dropdownClass:"!bg-background !border-input",disabled:D,countryCodeEditable:!1,isValid:(e,t)=>!!(e&&!(e.length<8)&&/^\+?[1-9]\d{1,14}$/.test(e))})}),k&&(0,s.jsx)("p",{className:"text-sm text-destructive",children:k})]})]}),(0,s.jsx)("div",{id:"recaptcha-container"}),(0,s.jsx)(c.$,{type:"submit",className:"w-full h-12 text-lg bg-primary text-primary-foreground hover:bg-primary/90 transition-colors flex items-center justify-center gap-2",disabled:D,children:D?(0,s.jsx)(v.A,{className:"w-4 h-4 animate-spin"}):(0,s.jsxs)(s.Fragment,{children:["Send Code ",(0,s.jsx)(w.A,{className:"w-4 h-4"})]})}),(0,s.jsxs)("div",{className:"text-center text-sm mt-4",children:[(0,s.jsx)("span",{className:"text-muted-foreground",children:"Remember your password? "}),(0,s.jsx)(g(),{href:"/login",className:"text-primary hover:text-primary/80 hover:underline transition-colors",children:"Back to Login"})]})]}),"verification"===e&&(0,s.jsxs)("form",{onSubmit:K,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(d.J,{className:"block text-sm font-medium mb-4 text-center",children:"Verification Code"}),(0,s.jsx)("div",{className:"flex justify-center items-center gap-3",children:[...Array(6)].map((e,t)=>(0,s.jsx)(l.p,{type:"text",maxLength:1,className:"w-12 h-12 text-center text-2xl font-semibold rounded-lg focus:ring-2 focus:ring-primary/50 transition-all",value:P[t]||"",onChange:e=>{let r=P.split("");r[t]=e.target.value,C(r.join("")),e.target.value&&e.target.nextElementSibling&&e.target.nextElementSibling.focus(),L("")},disabled:D},t))}),(0,s.jsx)("div",{className:"mt-4 text-center",children:(0,s.jsx)("button",{type:"button",onClick:Z,className:"text-sm ".concat(U>0?"text-muted-foreground":"text-primary hover:underline"),disabled:U>0||D,children:U>0?"Resend code in ".concat((e=>{let t=Math.floor(e/60);return"".concat(t,":").concat((e%60).toString().padStart(2,"0"))})(U)):"Resend code"})}),k&&(0,s.jsx)("p",{className:"text-sm text-destructive text-center mt-2",children:k})]}),(0,s.jsx)("div",{id:"recaptcha-container-resend"}),(0,s.jsx)(c.$,{type:"submit",className:"w-full h-12 text-lg bg-primary text-primary-foreground hover:bg-primary/90 transition-colors flex items-center justify-center gap-2",disabled:D||6!==P.length,children:D?(0,s.jsx)(v.A,{className:"w-4 h-4 animate-spin"}):(0,s.jsxs)(s.Fragment,{children:["Verify ",(0,s.jsx)(y.A,{className:"w-4 h-4"})]})})]}),"reset"===e&&(0,s.jsxs)("form",{onSubmit:ee,className:"space-y-6",children:[H&&(0,s.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 mb-4",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(y.A,{className:"w-5 h-5 text-green-600 mr-2"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-green-800",children:"Account Found"}),(0,s.jsx)("p",{className:"text-sm text-green-600",children:H})]})]})}),(0,s.jsxs)("div",{children:[(0,s.jsx)(d.J,{className:"block text-sm font-medium mb-2",children:"New Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(l.p,{type:F?"text":"password",value:V.newPassword,onChange:e=>{z({...V,newPassword:e.target.value}),L("")},className:"pl-10 pr-10",placeholder:"Enter new password",required:!0,minLength:6,disabled:D}),(0,s.jsx)(N.A,{className:"w-4 h-4 absolute left-3 top-3 text-muted-foreground"}),(0,s.jsx)(c.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 hover:bg-transparent",onClick:()=>G(!F),disabled:D,children:F?(0,s.jsx)(S.A,{className:"w-4 h-4 text-muted-foreground"}):(0,s.jsx)(j.A,{className:"w-4 h-4 text-muted-foreground"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(d.J,{className:"block text-sm font-medium mb-2",children:"Confirm New Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(l.p,{type:B?"text":"password",value:V.confirmPassword,onChange:e=>{z({...V,confirmPassword:e.target.value}),L("")},className:"pl-10 pr-10",placeholder:"Confirm new password",required:!0,minLength:6,disabled:D}),(0,s.jsx)(N.A,{className:"w-4 h-4 absolute left-3 top-3 text-muted-foreground"}),(0,s.jsx)(c.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 hover:bg-transparent",onClick:()=>Y(!B),disabled:D,children:B?(0,s.jsx)(S.A,{className:"w-4 h-4 text-muted-foreground"}):(0,s.jsx)(j.A,{className:"w-4 h-4 text-muted-foreground"})})]})]}),k&&(0,s.jsx)("p",{className:"text-sm text-destructive text-center",children:k}),(0,s.jsx)(c.$,{type:"submit",className:"w-full h-12 text-lg bg-primary text-primary-foreground hover:bg-primary/90 transition-colors",disabled:D||!V.newPassword||!V.confirmPassword,children:D?(0,s.jsx)(v.A,{className:"w-4 h-4 animate-spin"}):"Reset Password"})]})]},e)]})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[7540,7416,1345,2651,2446,9381,8441,6587,7358],()=>t(2650)),_N_E=e.O()}]);