(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[879],{1466:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(7401).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},2336:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var a=r(5155),s=r(2115),i=r(9602);let n=s.forwardRef((e,t)=>{let{className:r,type:s,...n}=e;return(0,a.jsx)("input",{type:s,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...n})});n.displayName="Input"},4085:(e,t,r)=>{"use strict";r.d(t,{$:()=>c,r:()=>o});var a=r(5155),s=r(2115),i=r(2317),n=r(1027),l=r(9602);let o=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-red-500 text-destructive-foreground hover:bg-red-600",outline:"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=s.forwardRef((e,t)=>{let{className:r,variant:s,size:n,asChild:c=!1,...d}=e,m=c?i.DX:"button";return(0,a.jsx)(m,{className:(0,l.cn)(o({variant:s,size:n,className:r})),ref:t,...d})});c.displayName="Button"},5007:(e,t,r)=>{"use strict";r.d(t,{Wu:()=>l,Zp:()=>n,wL:()=>o});var a=r(5155),s=r(2115),i=r(9602);let n=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...s})});n.displayName="Card",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",r),...s})}).displayName="CardHeader",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("h3",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",r),...s})}).displayName="CardTitle",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("p",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",r),...s})}).displayName="CardDescription";let l=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",r),...s})});l.displayName="CardContent";let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",r),...s})});o.displayName="CardFooter"},5785:(e,t,r)=>{"use strict";r.d(t,{J:()=>c});var a=r(5155),s=r(2115),i=r(6195),n=r(1027),l=r(9602);let o=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(i.b,{ref:t,className:(0,l.cn)(o(),r),...s})});c.displayName=i.b.displayName},6462:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(7401).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},7549:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>C});var a=r(5155),s=r(2115),i=r(9664),n=r(399),l=r(5007),o=r(4085),c=r(2336),d=r(5785),m=r(2610),u=r.n(m);r(133);var f=r(9124),x=r(8173),p=r.n(x),h=r(6954),g=r(6336),b=r(1466),y=r(1773),v=r(9053),N=r(6462),j=r(8686),w=r(2598),k=r(738);function C(){let[e,t]=(0,s.useState)("phone"),[r,m]=(0,s.useState)("964"),[x,C]=(0,s.useState)("iq"),[S,A]=(0,s.useState)(""),[E,I]=(0,s.useState)(null),[P,F]=(0,s.useState)(0),[R,q]=(0,s.useState)(!1),[L,_]=(0,s.useState)(""),[J,$]=(0,s.useState)(!1),[D,M]=(0,s.useState)({firstName:"",lastName:"",email:"",password:"",mobileNo:r,cityId:"-999",stateProvinceId:"-999",countryId:"1"});(0,s.useEffect)(()=>{fetch("https://ipapi.co/json/").then(e=>e.json()).then(e=>{e.country_code&&(C(e.country_code.toLowerCase()),m(e.country_calling_code.replace("+","")))}).catch(()=>{C("iq"),m("964")})},[]),(0,s.useEffect)(()=>{if(P>0){let e=setTimeout(()=>F(P-1),1e3);return()=>clearTimeout(e)}},[P]);let z=()=>{F(60)},V=async e=>{if(e.preventDefault(),q(!0),_(""),!r){_("Phone number is required"),q(!1);return}if(r.length<8){_("Phone number must be at least 8 digits"),q(!1);return}if(!/^\+?[1-9]\d{1,14}$/.test(r)){_("Please enter a valid phone number"),q(!1);return}try{let e=new n.kT(i.j,"recaptcha-container",{size:"invisible",callback:()=>{}}),a="+".concat(r),s=await (0,n.ik)(i.j,a,e);I(s),t("verification"),z()}catch(e){_(e.message||"Failed to send verification code"),console.error("Error:",e)}finally{q(!1)}},B=async()=>{if(!(P>0)){q(!0),_("");try{let e=Math.floor(1e5+9e5*Math.random()).toString();console.log("Mock verification code:",e),I({mockVerificationCode:e}),z()}catch(e){_("Failed to resend verification code"),console.error("Error:",e)}finally{q(!1)}}},T=async e=>{e.preventDefault(),q(!0),_("");try{(await E.confirm(S)).user&&t("details")}catch(e){_(e.message||"Invalid verification code"),console.error("Error:",e)}finally{q(!1)}},G=async e=>{if(e.preventDefault(),q(!0),_(""),!D.firstName.trim()){_("First name is required"),q(!1);return}if(!D.lastName.trim()){_("Last name is required"),q(!1);return}if(!D.email.trim()||!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(D.email)){_("Please enter a valid email address"),q(!1);return}if(!D.password||D.password.length<8){_("Password must be at least 8 characters long"),q(!1);return}try{let e={FirstName:D.firstName,LastName:D.lastName,EmailAddress:D.email,Password:D.password,MobileNo:D.mobileNo,CityId:D.cityId,StateProvinceId:D.stateProvinceId,CountryID:D.countryId};console.log("Request parameters:",e)}catch(e){_(e.message||"Failed to create account"),console.error("Error:",e)}finally{q(!1)}};return(0,a.jsx)("div",{className:"min-h-screen bg-background flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("h2",{className:"mt-6 text-3xl font-extrabold",children:["phone"===e&&"Get Started","verification"===e&&"Verify Your Phone","details"===e&&"Complete Your Profile"]}),(0,a.jsxs)("p",{className:"mt-2 text-sm text-muted-foreground",children:["phone"===e&&"Enter your phone number to create an account","verification"===e&&"Enter the code we sent to your phone","details"===e&&"Just a few more details to complete your account"]})]}),(0,a.jsxs)(l.Zp,{className:"mt-8 p-8 shadow-xl bg-card/100",children:[(0,a.jsx)("div",{className:"flex justify-center mb-8",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center ".concat("phone"===e?"bg-primary text-primary-foreground":"bg-primary/20 text-primary"),children:(0,a.jsx)(h.A,{className:"w-4 h-4"})}),(0,a.jsx)("div",{className:"w-16 h-1 ".concat("phone"===e?"bg-primary/20":"bg-primary")}),(0,a.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center ".concat("verification"===e?"bg-primary text-primary-foreground":"details"===e?"bg-primary":"bg-primary/20 text-primary"),children:(0,a.jsx)(g.A,{className:"w-4 h-4"})}),(0,a.jsx)("div",{className:"w-16 h-1 ".concat("details"===e?"bg-primary":"bg-primary/20")}),(0,a.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center ".concat("details"===e?"bg-primary text-primary-foreground":"bg-primary/20 text-primary"),children:(0,a.jsx)(b.A,{className:"w-4 h-4"})})]})}),(0,a.jsxs)(f.P.div,{variants:{hidden:{opacity:0,x:-20},visible:{opacity:1,x:0},exit:{opacity:0,x:20}},initial:"hidden",animate:"visible",exit:"exit",transition:{duration:.3},children:["phone"===e&&(0,a.jsxs)("form",{onSubmit:V,className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{className:"block text-sm font-medium mb-2 text-center",children:"Phone Number"}),(0,a.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,a.jsx)("div",{className:"w-full max-w-[300px]",children:(0,a.jsx)(u(),{country:x,value:r,onChange:e=>{m(e),_("")},enableSearch:!0,searchPlaceholder:"Search country...",containerClass:"w-full",inputClass:"w-full p-4 border rounded-lg focus:ring-2 focus:ring-primary/50 ".concat(L?"border-destructive":""),buttonClass:"!border-input !bg-background hover:!bg-accent",dropdownClass:"!bg-background !border-input",disabled:R,countryCodeEditable:!1,isValid:(e,t)=>!!(e&&!(e.length<8)&&/^\+?[1-9]\d{1,14}$/.test(e))})}),L&&(0,a.jsx)("p",{className:"text-sm text-destructive",children:L})]})]}),(0,a.jsx)("div",{id:"recaptcha-container"}),(0,a.jsx)(o.$,{type:"submit",className:"w-full h-12 text-lg bg-primary text-primary-foreground hover:bg-primary/90 transition-colors flex items-center justify-center gap-2",disabled:R,children:R?(0,a.jsx)(y.A,{className:"w-4 h-4 animate-spin"}):(0,a.jsxs)(a.Fragment,{children:["Continue ",(0,a.jsx)(v.A,{className:"w-4 h-4"})]})}),(0,a.jsxs)("div",{className:"text-center text-sm mt-4",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Already have an account? "}),(0,a.jsx)(p(),{href:"/login",className:"text-primary hover:text-primary/80 hover:underline transition-colors",children:"Login"})]})]}),"verification"===e&&(0,a.jsxs)("form",{onSubmit:T,className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{className:"block text-sm font-medium mb-4 text-center",children:"Verification Code"}),(0,a.jsx)("div",{className:"flex justify-center items-center gap-3",children:[...Array(6)].map((e,t)=>(0,a.jsx)(c.p,{type:"text",maxLength:1,className:"w-12 h-12 text-center text-2xl font-semibold rounded-lg focus:ring-2 focus:ring-primary/50 transition-all",value:S[t]||"",onChange:e=>{let r=S.split("");r[t]=e.target.value,A(r.join("")),e.target.value&&e.target.nextElementSibling&&e.target.nextElementSibling.focus()},disabled:R},t))}),(0,a.jsx)("div",{className:"mt-4 text-center",children:(0,a.jsx)("button",{type:"button",onClick:B,className:"text-sm ".concat(P>0?"text-muted-foreground":"text-primary hover:underline"),disabled:P>0||R,children:P>0?"Resend code in ".concat((e=>{let t=Math.floor(e/60);return"".concat(t,":").concat((e%60).toString().padStart(2,"0"))})(P)):"Resend code"})})]}),(0,a.jsx)(o.$,{type:"submit",className:"w-full h-12 text-lg bg-primary text-primary-foreground hover:bg-primary/90 transition-colors flex items-center justify-center gap-2",disabled:R||6!==S.length,children:R?(0,a.jsx)(y.A,{className:"w-4 h-4 animate-spin"}):(0,a.jsxs)(a.Fragment,{children:["Verify ",(0,a.jsx)(g.A,{className:"w-4 h-4"})]})})]}),"details"===e&&(0,a.jsxs)("form",{onSubmit:G,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{className:"block text-sm font-medium mb-2",children:"First Name"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(c.p,{type:"text",value:D.firstName,onChange:e=>M({...D,firstName:e.target.value}),className:"pl-10",required:!0,disabled:R}),(0,a.jsx)(b.A,{className:"w-4 h-4 absolute left-3 top-3 text-muted-foreground"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{className:"block text-sm font-medium mb-2",children:"Last Name"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(c.p,{type:"text",value:D.lastName,onChange:e=>M({...D,lastName:e.target.value}),className:"pl-10",required:!0,disabled:R}),(0,a.jsx)(b.A,{className:"w-4 h-4 absolute left-3 top-3 text-muted-foreground"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{className:"block text-sm font-medium mb-2",children:"Email"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(c.p,{type:"email",value:D.email,onChange:e=>M({...D,email:e.target.value}),className:"pl-10",required:!0,disabled:R}),(0,a.jsx)(N.A,{className:"w-4 h-4 absolute left-3 top-3 text-muted-foreground"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{className:"block text-sm font-medium mb-2",children:"Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(c.p,{type:J?"text":"password",value:D.password,onChange:e=>M({...D,password:e.target.value}),className:"pl-10 pr-10",required:!0,minLength:8,disabled:R}),(0,a.jsx)(j.A,{className:"w-4 h-4 absolute left-3 top-3 text-muted-foreground"}),(0,a.jsxs)(o.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 hover:bg-transparent",onClick:()=>$(!J),disabled:R,children:[J?(0,a.jsx)(w.A,{className:"w-4 h-4 text-muted-foreground"}):(0,a.jsx)(k.A,{className:"w-4 h-4 text-muted-foreground"}),(0,a.jsx)("span",{className:"sr-only",children:J?"Hide password":"Show password"})]})]})]}),(0,a.jsx)(o.$,{type:"submit",className:"w-full h-12 text-lg bg-primary text-primary-foreground hover:bg-primary/90 transition-colors",disabled:R,children:R?(0,a.jsx)(y.A,{className:"w-4 h-4 animate-spin"}):"Create Account"})]})]},e)]})]})})}},9602:(e,t,r)=>{"use strict";r.d(t,{cn:()=>i});var a=r(3463),s=r(9795);function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}},9664:(e,t,r)=>{"use strict";r.d(t,{j:()=>n});var a=r(9904),s=r(399);let i=(0,a.Wp)({apiKey:"AIzaSyBlr1YG3CXkyn3yUJ44xvFFCcpfSj0pwFU",authDomain:"codemedical-19ec6.firebaseapp.com",projectId:"codemedical-19ec6",storageBucket:"codemedical-19ec6.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:180c7e662f6e3ae4e43bed",measurementId:"G-BRC9LRF5DE"}),n=(0,s.xI)(i)},9789:(e,t,r)=>{Promise.resolve().then(r.bind(r,7549))}},e=>{var t=t=>e(e.s=t);e.O(0,[7540,7416,1345,2446,9381,8441,6587,7358],()=>t(9789)),_N_E=e.O()}]);