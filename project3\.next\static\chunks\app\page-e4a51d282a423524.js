(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8974],{767:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(7401).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},853:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(7401).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},2523:(e,t,a)=>{"use strict";a.d(t,{T:()=>r});let r={ADMIN_BASE_URL:"https://admin.codemedicalapps.com/",DYNAMIC_METHOD_SUB_URL:"api/",COMMON_CONTROLLER_SUB_URL:"api/common/",END_POINT_NAMES:{DOWNLOAD_DIGITAL_FILE:"downloadDigitalFile",GET_COUPON_CODE_DISCOUNT:"get-coupon-code-discount-value/calculate-coupon-discount"}}},2862:(e,t,a)=>{"use strict";a.d(t,{$g:()=>u,MakeApiCallAsync:()=>o,TS:()=>i,XX:()=>d,k6:()=>c});var r=a(2651),s=a(2523);r.A.defaults.timeout=3e4,"https:"===window.location.protocol&&s.T.ADMIN_BASE_URL.includes("localhost")&&(r.A.defaults.httpsAgent={rejectUnauthorized:!1});let i={ADMIN_BASE_URL:s.T.ADMIN_BASE_URL,API_VERSION:"v1",DYNAMIC_METHOD_SUB_URL:"api/v1/dynamic/dataoperation/",END_POINT_NAMES:{...s.T.END_POINT_NAMES,GET_CATEGORIES_LIST:"get-categories-list",SIGNUP_USER:"signup-user",GET_USER_LOGIN:"get-user-login",GET_USER_BY_PHONE:"get-user-by-phone",RESET_PASSWORD_BY_PHONE:"reset-password-by-phone",GET_HOME_SCREEN_BANNER:"get-home-screen-banner",GET_RECENT_PRODUCTS:"get-recents-products-list",GET_POPULAR_PRODUCTS:"get-popular-products-list",GET_HOT_DEAL_PRODUCTS:"get-hot-deal-products",GET_CAMPAIGNS_LIST:"get-web-campaign-list",GET_PRODUCTS_LIST:"get-products-list",GET_ALL_PRODUCTS:"api/v1/products/get-all-products",GET_MANUFACTURERS_LIST:"get-manufacturers-list",GET_TAGS_LIST:"get-tags-list",GET_CURRENCY_RATE:"get-currency-rate",GET_COUPON_CODE_DISCOUNT:"get-coupon-code-discount-value/calculate-coupon-discount"},COMMON_CONTROLLER_SUB_URL:"api/v1/common/"},l=async()=>localStorage.getItem("token")||null,n=async()=>localStorage.getItem("userId")||null,o=async function(e,t,a,s,o){arguments.length>5&&void 0!==arguments[5]&&arguments[5];try{let c={...s};if(!c.hasOwnProperty("Authorization")){let e=await l();e&&(c.Authorization="Bearer "+e)}if(!c.hasOwnProperty("Token")){let e=await l();c.Token=null!=e?e:""}if(!c.hasOwnProperty("UserID")){let e=await n();c.UserID=null!=e?e:""}c.hasOwnProperty("Accept")||(c.Accept="application/json"),c.hasOwnProperty("Content-Type")||(c["Content-Type"]="application/json");let d=i.ADMIN_BASE_URL+(null===t||void 0==t?i.DYNAMIC_METHOD_SUB_URL:t)+e;o=null!=o?o:"POST";let u={headers:c,responseType:"json",timeout:3e4,withCredentials:!1};if("POST"===o)return await r.A.post(d,a,u);if("GET"==o)return u.params=a,await r.A.get(d,u);return{data:{errorMessage:"Unsupported method type: ".concat(o),status:"method_not_supported"}}}catch(t){console.error("API call failed:",t);let e={data:{errorMessage:"An unexpected error occurred",status:"unknown_error"}};if(t&&"object"==typeof t&&"response"in t&&t.response){var c,d;let a=null===(c=t.response)||void 0===c?void 0:c.data;e.data={errorMessage:(null==a?void 0:a.errorMessage)||"An error occurred while processing your request.",status:null===(d=t.response)||void 0===d?void 0:d.status}}else if(t&&"object"==typeof t&&"request"in t){let a="Network error: No response received from server.";t.message&&t.message.includes("Network Error")&&(a="Network Error: This may be due to CORS policy restrictions, server unavailability, or an invalid SSL certificate. Please check that:\n1. The server is running and accessible\n2. The URL is correct: "+i.ADMIN_BASE_URL+"\n3. CORS is properly configured on the server\n4. If using HTTPS, the SSL certificate is valid"),e.data={errorMessage:a,status:"network_error"}}else{let a=t&&"object"==typeof t&&"message"in t?t.message:"An unexpected error occurred";e.data={errorMessage:a,status:"request_error"}}return e}},c=async()=>{try{let e=await o("getrate","api/v1/common/",{},{},"GET");if(e&&e.data&&!e.data.errorMessage)return parseInt(e.data)||1500;return 1500}catch(e){return console.error("Error fetching currency rate:",e),1500}},d=(e,t)=>Math.round(e*t),u=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD";return"IQD"===t?"".concat(e.toLocaleString()," IQD"):"$".concat(e.toFixed(2))}},4085:(e,t,a)=>{"use strict";a.d(t,{$:()=>c,r:()=>o});var r=a(5155),s=a(2115),i=a(2317),l=a(1027),n=a(9602);let o=(0,l.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-red-500 text-destructive-foreground hover:bg-red-600",outline:"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=s.forwardRef((e,t)=>{let{className:a,variant:s,size:l,asChild:c=!1,...d}=e,u=c?i.DX:"button";return(0,r.jsx)(u,{className:(0,n.cn)(o({variant:s,size:l,className:a})),ref:t,...d})});c.displayName="Button"},4348:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(7401).A)("Flame",[["path",{d:"M8.5 14.5A2.5 2.5 0 0 0 11 12c0-1.38-.5-2-1-3-1.072-2.143-.224-4.054 2-6 .5 2.5 2 4.9 4 6.5 2 1.6 3 3.5 3 5.5a7 7 0 1 1-14 0c0-1.153.433-2.294 1-3a2.5 2.5 0 0 0 2.5 2.5z",key:"96xj49"}]])},5007:(e,t,a)=>{"use strict";a.d(t,{Wu:()=>n,Zp:()=>l,wL:()=>o});var r=a(5155),s=a(2115),i=a(9602);let l=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...s})});l.displayName="Card",s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",a),...s})}).displayName="CardHeader",s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("h3",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",a),...s})}).displayName="CardTitle",s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("p",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",a),...s})}).displayName="CardDescription";let n=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",a),...s})});n.displayName="CardContent";let o=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",a),...s})});o.displayName="CardFooter"},5488:(e,t,a)=>{"use strict";a.d(t,{E:()=>i});var r=a(5155),s=a(9602);function i(e){let{className:t,...a}=e;return(0,r.jsx)("div",{className:(0,s.cn)("animate-pulse rounded-md bg-muted",t),...a})}},6664:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>et});var r=a(5155),s=a(2115),i=a(2862),l=a(3518),n=a(6967),o=a(4085),c=a(9602);function d(e){let{images:t,autoPlayInterval:a=5e3,className:i,onSlideChange:d,initialIndex:u=0}=e,[m,h]=(0,s.useState)(u),[p,g]=(0,s.useState)(!0),[x,f]=(0,s.useState)(!1),v=(0,s.useCallback)(()=>{if(x)return;f(!0);let e=m===t.length-1?0:m+1;h(e),d&&d(e),setTimeout(()=>f(!1),500)},[t.length,m,d,x]);return(0,s.useEffect)(()=>{let e;return p&&!x&&(e=setInterval(v,a)),()=>{e&&clearInterval(e)}},[p,v,a,x]),(0,r.jsxs)("div",{className:(0,c.cn)("relative w-full overflow-hidden",i),onMouseEnter:()=>g(!1),onMouseLeave:()=>g(!0),children:[(0,r.jsx)("div",{className:"flex transition-transform duration-500 ease-out",style:{transform:"translateX(-".concat(100*m,"%)")},children:t.map((e,t)=>(0,r.jsx)("div",{className:"w-full flex-shrink-0 relative",style:{aspectRatio:"16/6"},children:(0,r.jsx)("div",{className:"w-full h-full",children:(0,r.jsx)("img",{src:e.url,alt:e.alt,className:"w-full h-full object-cover",style:{objectFit:"cover",width:"100%",height:"100%"}})})},t))}),(0,r.jsx)(o.$,{variant:"ghost",size:"icon",className:"absolute left-4 top-1/2 -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white rounded-full p-2 z-20",onClick:()=>{if(x)return;f(!0);let e=0===m?t.length-1:m-1;h(e),d&&d(e),setTimeout(()=>f(!1),500)},disabled:x,children:(0,r.jsx)(l.A,{className:"h-6 w-6"})}),(0,r.jsx)(o.$,{variant:"ghost",size:"icon",className:"absolute right-4 top-1/2 -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white rounded-full p-2 z-20",onClick:v,disabled:x,children:(0,r.jsx)(n.A,{className:"h-6 w-6"})}),(0,r.jsx)("div",{className:"absolute bottom-6 left-1/2 -translate-x-1/2 flex gap-3 z-20",children:t.map((e,t)=>(0,r.jsx)("button",{className:(0,c.cn)("w-3 h-3 rounded-full transition-all",m===t?"bg-white scale-125":"bg-white/50 hover:bg-white/75"),onClick:()=>{x||(f(!0),h(t),d&&d(t),setTimeout(()=>f(!1),500))},disabled:x},t))})]})}function u(e){let{className:t}=e,[a,l]=(0,s.useState)([]),[n,c]=(0,s.useState)(!0),[u,m]=(0,s.useState)(null),[h,p]=(0,s.useState)(0),[g,x]=(0,s.useState)({transform:"translateX(0%) translateY(0%)"}),f=e=>{e&&(window.location.href=e)};if((0,s.useEffect)(()=>{(async()=>{try{c(!0),m(null);let e=await (0,i.MakeApiCallAsync)(i.TS.END_POINT_NAMES.GET_HOME_SCREEN_BANNER,null,{PageNumber:1,PageSize:100,SortColumn:"BannerID",SortOrder:"ASC"},{"Content-Type":"application/json",Accept:"application/json"},"POST",!0);if(console.log("API Response:",e),null==e?void 0:e.data)try{let t=e.data;if("string"==typeof t&&(t=JSON.parse(t)),t.data&&"string"==typeof t.data&&(t=JSON.parse(t.data)),console.log("Parsed banner data:",t),Array.isArray(t)){let e=t.map(e=>{var t;return{...e,BannerImgUrl:(null===(t=e.BannerImgUrl)||void 0===t?void 0:t.startsWith("http"))?e.BannerImgUrl:"https://admin.codemedicalapps.com".concat(e.BannerImgUrl||"")}});l(e)}else if(t.data&&Array.isArray(t.data)){let e=t.data.map(e=>{var t;return{...e,BannerImgUrl:(null===(t=e.BannerImgUrl)||void 0===t?void 0:t.startsWith("http"))?e.BannerImgUrl:"https://admin.codemedicalapps.com".concat(e.BannerImgUrl||"")}});l(e)}else console.error("Banner data is not in expected format:",t),l([])}catch(e){console.error("Error processing banner data:",e),m("Failed to process banner data"),l([])}else console.error("Invalid or empty response from API"),m("Failed to load banners"),l([])}catch(e){console.error("Error fetching banners:",e),m("Failed to fetch banners"),l([])}finally{c(!1)}})()},[]),n)return(0,r.jsx)("div",{className:"relative w-full h-[450px] overflow-hidden rounded-xl bg-accent/10 animate-pulse ".concat(t),children:(0,r.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,r.jsx)("div",{className:"w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"})})});if((u||0===a.length)&&(l([{BannerID:1,TopTitle:"TOP TITLE",MainTitle:"main title",BottomTitle:"bottom title",LeftButtonText:"test",RightButtonText:"test right",BannerImgUrl:"https://placehold.co/1200x450/333333/FFFFFF?text=Banner+Image",LeftButtonUrl:"#",ThemeTypeID:1}]),u))return(0,r.jsx)("div",{className:"relative w-full h-[450px] overflow-hidden rounded-xl bg-accent/5 flex items-center justify-center ".concat(t),children:(0,r.jsxs)("div",{className:"text-center p-8",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"Failed to load banners"}),(0,r.jsx)("p",{className:"text-muted-foreground mb-4",children:u}),(0,r.jsx)(o.$,{onClick:()=>window.location.reload(),children:"Retry"})]})});let v=a.map(e=>({url:e.BannerImgUrl,alt:e.MainTitle||"Banner",id:e.BannerID,leftButtonUrl:e.LeftButtonUrl}));return(0,r.jsx)("div",{className:"relative",onMouseMove:e=>{let t=e.clientX-window.innerWidth/1,a=e.clientY-window.innerHeight/1;x({transform:"translateX(".concat(7+t/150,"%) translateY(").concat(1+a/150,"%)")})},children:(0,r.jsx)("div",{className:"relative w-full h-full cursor-pointer",onClick:()=>{var e;return f(null===(e=a[h])||void 0===e?void 0:e.LeftButtonUrl)},children:(0,r.jsx)(d,{images:v,className:t,onSlideChange:e=>{p(e)},initialIndex:h,autoPlayInterval:5e3})})})}var m=a(7401);let h=(0,m.A)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);var p=a(719),g=a(767),x=a(7110),f=a(8173),v=a.n(f),N=a(9124),y=a(4710),j=a(9234),w=a(9656),b=a(7249);class P extends s.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=this.props.sizeRef.current;e.height=t.offsetHeight||0,e.width=t.offsetWidth||0,e.top=t.offsetTop,e.left=t.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function S(e){let{children:t,isPresent:a}=e,i=(0,s.useId)(),l=(0,s.useRef)(null),n=(0,s.useRef)({width:0,height:0,top:0,left:0}),{nonce:o}=(0,s.useContext)(b.Q);return(0,s.useInsertionEffect)(()=>{let{width:e,height:t,top:r,left:s}=n.current;if(a||!l.current||!e||!t)return;l.current.dataset.motionPopId=i;let c=document.createElement("style");return o&&(c.nonce=o),document.head.appendChild(c),c.sheet&&c.sheet.insertRule('\n          [data-motion-pop-id="'.concat(i,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            top: ").concat(r,"px !important;\n            left: ").concat(s,"px !important;\n          }\n        ")),()=>{document.head.removeChild(c)}},[a]),(0,r.jsx)(P,{isPresent:a,childRef:l,sizeRef:n,children:s.cloneElement(t,{ref:l})})}let A=e=>{let{children:t,initial:a,isPresent:i,onExitComplete:l,custom:n,presenceAffectsLayout:o,mode:c}=e,d=(0,j.M)(C),u=(0,s.useId)(),m=(0,s.useCallback)(e=>{for(let t of(d.set(e,!0),d.values()))if(!t)return;l&&l()},[d,l]),h=(0,s.useMemo)(()=>({id:u,initial:a,isPresent:i,custom:n,onExitComplete:m,register:e=>(d.set(e,!1),()=>d.delete(e))}),o?[Math.random(),m]:[i,m]);return(0,s.useMemo)(()=>{d.forEach((e,t)=>d.set(t,!1))},[i]),s.useEffect(()=>{i||d.size||!l||l()},[i]),"popLayout"===c&&(t=(0,r.jsx)(S,{isPresent:i,children:t})),(0,r.jsx)(w.t.Provider,{value:h,children:t})};function C(){return new Map}var I=a(5087);let E=e=>e.key||"";function T(e){let t=[];return s.Children.forEach(e,e=>{(0,s.isValidElement)(e)&&t.push(e)}),t}var U=a(5403);let M=e=>{let{children:t,custom:a,initial:i=!0,onExitComplete:l,presenceAffectsLayout:n=!0,mode:o="sync",propagate:c=!1}=e,[d,u]=(0,I.xQ)(c),m=(0,s.useMemo)(()=>T(t),[t]),h=c&&!d?[]:m.map(E),p=(0,s.useRef)(!0),g=(0,s.useRef)(m),x=(0,j.M)(()=>new Map),[f,v]=(0,s.useState)(m),[N,w]=(0,s.useState)(m);(0,U.E)(()=>{p.current=!1,g.current=m;for(let e=0;e<N.length;e++){let t=E(N[e]);h.includes(t)?x.delete(t):!0!==x.get(t)&&x.set(t,!1)}},[N,h.length,h.join("-")]);let b=[];if(m!==f){let e=[...m];for(let t=0;t<N.length;t++){let a=N[t],r=E(a);h.includes(r)||(e.splice(t,0,a),b.push(a))}"wait"===o&&b.length&&(e=b),w(T(e)),v(m);return}let{forceRender:P}=(0,s.useContext)(y.L);return(0,r.jsx)(r.Fragment,{children:N.map(e=>{let t=E(e),s=(!c||!!d)&&(m===N||h.includes(t));return(0,r.jsx)(A,{isPresent:s,initial:(!p.current||!!i)&&void 0,custom:s?void 0:a,presenceAffectsLayout:n,mode:o,onExitComplete:s?void 0:()=>{if(!x.has(t))return;x.set(t,!0);let e=!0;x.forEach(t=>{t||(e=!1)}),e&&(null==P||P(),w(g.current),c&&(null==u||u()),l&&l())},children:e},t)})})};function D(){let[e,t]=(0,s.useState)([]),[a,l]=(0,s.useState)(!0),[d,u]=(0,s.useState)([]),[m,f]=(0,s.useState)(!0),[y,j]=(0,s.useState)(null),{t:w,primaryColor:b}=(0,x.t)();(0,s.useEffect)(()=>{(async()=>{try{var e,a;let r={"Content-Type":"application/json",Accept:"application/json",Authorization:"Bearer "+localStorage.getItem("token")},s=await (0,i.MakeApiCallAsync)(i.TS.END_POINT_NAMES.GET_CATEGORIES_LIST,null,{PageNumber:1,PageSize:100,SortColumn:"Name",SortOrder:"ASC"},r,"POST",!0);if(null==s?void 0:null===(e=s.data)||void 0===e?void 0:e.data)try{let e=JSON.parse(s.data.data);if(Array.isArray(e)){let a=e.filter(e=>!e.ParentCategoryID),r=e.filter(e=>e.ParentCategoryID),s=a.map(e=>({id:e.CategoryID,name:e.Name,subcategories:r.filter(t=>t.ParentCategoryID===e.CategoryID).map(e=>e.Name)}));t(s)}else console.error("Categories data is not an array:",e),t([])}catch(e){console.error("Error parsing categories data:",e),t([])}else(null==s?void 0:null===(a=s.data)||void 0===a?void 0:a.errorMessage)?console.error("API Error:",s.data.errorMessage):console.error("Invalid or empty response from API"),t([])}catch(e){console.error("Error fetching categories:",e),t([])}finally{l(!1)}})()},[]);let P=e=>{j(e)};return a?(0,r.jsx)("div",{className:"p-6 space-y-4 bg-gradient-to-b from-background to-accent/5 min-h-screen border-r",children:[void 0,void 0,void 0,void 0,void 0].map((e,t)=>(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"h-8 bg-accent/10 animate-pulse rounded-lg w-3/4"}),(0,r.jsx)("div",{className:"h-6 bg-accent/5 animate-pulse rounded-lg w-1/2 ml-4"})]},t))}):(0,r.jsxs)("div",{className:"hidden md:block",children:[(0,r.jsx)(o.$,{variant:"ghost",size:"icon",className:"absolute left-4 top-4 z-50 md:hidden",onClick:()=>f(!m),children:(0,r.jsx)(h,{className:"h-6 w-6"})}),(0,r.jsx)(N.P.div,{className:"bg-background md:relative w-64 shadow-sm",style:{background:"linear-gradient(135deg, ".concat(b,"30, ").concat(b,"20, ").concat(b,"10)")},initial:{x:0},animate:{x:m?0:"-100%"},transition:{duration:.3,ease:"easeInOut"},children:(0,r.jsxs)("div",{className:"p-6 space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(p.A,{className:"h-6 w-6",style:{color:b}}),(0,r.jsx)("h2",{className:"text-xl font-semibold",style:{color:b},children:w("categories")})]}),y&&(0,r.jsx)(o.$,{variant:"ghost",size:"icon",onClick:()=>{j(null)},className:"hover:bg-accent/80",children:(0,r.jsx)(g.A,{className:"h-5 w-5"})})]}),(0,r.jsx)(M,{mode:"wait",children:y?(0,r.jsx)(N.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},transition:{duration:.2},className:"space-y-2",children:y.subcategories.map((e,t)=>(0,r.jsx)(N.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{delay:.05*t},children:(0,r.jsx)(v(),{href:"/products?category=".concat(y.id),className:(0,c.cn)("block px-4 py-3 text-sm transition-all duration-200","bg-background/90 hover:bg-accent/50 hover:shadow-lg hover:scale-[1.02] rounded-lg","relative overflow-hidden group"),children:(0,r.jsxs)("span",{className:"relative z-10 flex items-center gap-2",children:[(0,r.jsx)(n.A,{className:"h-4 w-4 text-muted-foreground"}),e]})})},t))},"subcategory-list"):(0,r.jsx)(N.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},exit:{opacity:0,x:20},transition:{duration:.2},className:"space-y-2",children:[...e].reverse().map(e=>(0,r.jsx)(N.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.2},children:e.subcategories.length>0?(0,r.jsxs)("button",{onClick:()=>P(e),className:(0,c.cn)("w-full flex items-center justify-between p-4 transition-all duration-200 rounded-lg","bg-background/90 hover:bg-accent/50 hover:shadow-lg hover:scale-[1.02] relative overflow-hidden"),children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:e.name}),(0,r.jsx)(n.A,{className:"h-4 w-4"})]}):(0,r.jsx)(v(),{href:"/products?category=".concat(e.id),className:(0,c.cn)("w-full flex items-center justify-between p-4 transition-all duration-200 rounded-lg","bg-background/90 hover:bg-accent/50 hover:shadow-lg hover:scale-[1.02] relative overflow-hidden"),children:(0,r.jsx)("span",{className:"text-sm font-medium",children:e.name})})},e.id))},"parent-list")})]})})]})}function k(){let e=e=>({popularCategories:"Popular Categories"})[e]||e,[t,i]=(0,s.useState)([]),[c,d]=(0,s.useState)(!0),[u,m]=(0,s.useState)(0),h=(0,s.useRef)(null),[p,g]=(0,s.useState)(!0),[x]=(0,s.useState)(5e3),f=(0,s.useRef)(null),v={sm:6,md:6,lg:8,xl:10},N=()=>{{let e=window.innerWidth;return e>=1280?v.xl:e>=1024?v.lg:e>=768?v.md:v.sm}},y=N(),j=Math.ceil(t.length/y),w=()=>{u<j-1?m(u+1):m(0)},b=()=>{u>0?m(u-1):m(j-1)},P=()=>{f.current&&clearInterval(f.current),f.current=setInterval(()=>{w()},x)},S=()=>{f.current&&(clearInterval(f.current),f.current=null)},A=()=>{S(),setTimeout(()=>{p&&P()},1e4)},C=e=>{if(!e)return"".concat("https://admin.codemedicalapps.com/","images/no-image.jpg");if(e.startsWith("http"))return e;let t=e.startsWith("/")?e:"/".concat(e);return"".concat("https://admin.codemedicalapps.com/").concat(t)};return((0,s.useEffect)(()=>{(async()=>{try{var e;d(!0);let t=(await a.e(3205).then(a.bind(a,3205))).default,r=await t({method:"post",url:"".concat("https://admin.codemedicalapps.com/","api/v1/dynamic/dataoperation/get-popular-categories"),headers:{Accept:"application/json","Content-Type":"application/json"},data:{requestParameters:{recordValueJson:"[]"}}});if(null==r?void 0:null===(e=r.data)||void 0===e?void 0:e.data)try{let e=JSON.parse(r.data.data);if(Array.isArray(e)){let t=e.map(e=>({id:e.CategoryID,title:e.Name,image:C(e.AttachmentURL),parentId:void 0}));i(t)}else console.error("Categories data is not an array:",e),i([])}catch(e){console.error("Error parsing data:",e),i([])}else console.error("No data returned from API"),i([])}catch(e){console.error("Error fetching categories:",e),i([])}finally{d(!1)}})()},[]),(0,s.useEffect)(()=>{let e=()=>{let e=N(),a=Math.ceil(t.length/e);u>=a&&m(Math.max(0,a-1))};return window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}},[t.length,u]),(0,s.useEffect)(()=>(p&&t.length>0?P():S(),()=>{S()}),[p,t.length,x]),c)?(0,r.jsx)("section",{className:"py-8",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold mb-6",children:e("popularCategories")}),(0,r.jsx)("div",{className:"w-full flex justify-center items-center py-12",children:(0,r.jsx)("div",{className:"animate-pulse text-lg",children:"Loading popular categories..."})})]})}):t.length?(0,r.jsx)("section",{className:"py-8",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold",children:e("popularCategories")}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)(o.$,{variant:"outline",size:"icon",onClick:()=>{b(),A()},className:"rounded-full","aria-label":"Previous page",children:(0,r.jsx)(l.A,{className:"h-4 w-4"})}),(0,r.jsx)(o.$,{variant:"outline",size:"icon",onClick:()=>{w(),A()},className:"rounded-full","aria-label":"Next page",children:(0,r.jsx)(n.A,{className:"h-4 w-4"})})]})]}),(0,r.jsx)("div",{className:"w-full relative overflow-hidden",onMouseEnter:()=>S(),onMouseLeave:()=>p&&P(),children:(0,r.jsx)("div",{ref:h,className:"w-full transition-transform duration-500 ease-in-out",style:{transform:"translateX(-".concat(100*u,"%)")},children:(0,r.jsx)("div",{className:"flex flex-nowrap",style:{width:"".concat(100*j,"%")},children:Array.from({length:j}).map((e,a)=>(0,r.jsx)("div",{className:"w-full flex-shrink-0 grid grid-cols-3 sm:grid-cols-3 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 sm:gap-6 md:gap-8",style:{width:"".concat(100/j,"%")},children:t.slice(a*y,(a+1)*y).map(e=>(0,r.jsx)("div",{className:"flex flex-col items-center px-2",children:(0,r.jsxs)("a",{href:"/products?category=".concat(e.id),className:"group flex flex-col items-center cursor-pointer transition-all duration-300 hover:scale-105",children:[(0,r.jsx)("div",{className:"w-20 h-20 sm:w-24 sm:h-24 md:w-28 md:h-28 lg:w-32 lg:h-32 xl:w-36 xl:h-36 mb-3 relative",children:(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-50 to-blue-100 rounded-full p-1 group-hover:shadow-lg transition-all duration-300",children:(0,r.jsx)("div",{className:"w-full h-full rounded-full overflow-hidden border-2 border-white bg-white",children:(0,r.jsx)("img",{src:e.image||"/placeholder.svg?height=150&width=150",alt:e.title,width:144,height:144,className:"w-full h-full object-cover",onError:t=>{let a=t.target;if(console.error("Image load error for:",e.image),"https://admin.codemedicalapps.com/images/no-image.jpg"===e.image||e.image.includes("no-image"))a.src="/placeholder.svg?height=150&width=150";else{let e="".concat("https://admin.codemedicalapps.com","/images/no-image.jpg");console.log("Trying fallback URL:",e),a.src=e,a.onerror=()=>{console.error("Fallback URL also failed, using simple placeholder"),a.src="/placeholder.svg?height=150&width=150",a.onerror=null}}}})})})}),(0,r.jsx)("h3",{className:"text-sm sm:text-base font-medium text-gray-800 text-center group-hover:text-blue-600 transition-colors duration-300 line-clamp-2",children:e.title})]})},e.id))},"page-".concat(a)))})})}),(0,r.jsx)("div",{className:"flex justify-center mt-4 space-x-2",children:Array.from({length:j}).map((e,t)=>(0,r.jsx)("button",{className:"h-2 rounded-full transition-all ".concat(u===t?"w-6 bg-primary":"w-2 bg-gray-300"),onClick:()=>{m(t),A()},"aria-label":"Go to page ".concat(t+1)},"indicator-".concat(t)))})]})}):(0,r.jsx)("section",{className:"py-8",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold mb-6",children:e("popularCategories")}),(0,r.jsx)("div",{className:"w-full flex justify-center items-center py-12",children:(0,r.jsx)("div",{className:"text-lg text-gray-500",children:"No categories available at the moment. Please check back later."})})]})})}var _=a(5007),R=a(5488),L=a(5565),O=a(591);let F=(0,m.A)("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]]);var B=a(853),z=a(1773),W=a(2523),G=a(9286),q=a(865);function J(e){let{rating:t,maxRating:a=5,size:s="md",showValue:i=!1,className:l=""}=e,n=Math.max(0,Math.min(t,a)),o=n/a*100,c={sm:"h-3 w-3",md:"h-4 w-4",lg:"h-5 w-5"}[s];return(0,r.jsxs)("div",{className:"flex items-center gap-1 ".concat(l),children:[(0,r.jsxs)("div",{className:"relative inline-flex",children:[(0,r.jsx)("div",{className:"flex",children:Array.from({length:a}).map((e,t)=>(0,r.jsx)(q.A,{className:"".concat(c," text-gray-200")},"bg-".concat(t)))}),(0,r.jsx)("div",{className:"absolute top-0 left-0 overflow-hidden flex",style:{width:"".concat(o,"%")},children:Array.from({length:a}).map((e,t)=>(0,r.jsx)(q.A,{className:"".concat(c," text-yellow-400"),fill:"currentColor"},"fg-".concat(t)))})]}),i&&(0,r.jsx)("span",{className:"text-sm font-medium ml-1",children:n.toFixed(1)})]})}var $=a(8936),H=a(9355),V=a(814);function Q(e){let{product:t,effect:a="",layout:i=""}=e,{t:l,language:n}=(0,x.t)(),c=(0,$._)(),d=(0,H.n)(),[u,m]=(0,s.useState)(t.images[0]),[h,p]=(0,s.useState)(!1),[g,f]=(0,s.useState)(!1),N=()=>{if(t.inStock){p(!0);try{c.addToCart({id:parseInt(t.id),name:t.name,price:t.discountedPrice||t.price,discountPrice:t.discountedPrice,image:u.url,originalPrice:t.price},1,[],void 0),V.oR.success("".concat(t.name," added to cart"))}catch(e){console.error("Error adding to cart:",e),V.oR.error("Failed to add product to cart")}finally{p(!1)}}},y=t.discountedPrice?Math.round((t.price-t.discountedPrice)/t.price*100):0;return"list"===i?(0,r.jsx)(_.Zp,{className:"group relative overflow-hidden transition-all hover:shadow-lg",children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row",children:[(0,r.jsxs)("div",{className:"relative w-full md:w-1/3 aspect-square md:aspect-auto overflow-hidden",children:[(0,r.jsx)(v(),{href:"/product/".concat(t.slug),children:(0,r.jsx)(L.default,{src:u.url.startsWith("http")?u.url:u.url.startsWith("/")?"".concat(W.T.ADMIN_BASE_URL).concat(u.url.substring(1)):"".concat(W.T.ADMIN_BASE_URL).concat(u.url),alt:u.alt,fill:!0,sizes:"(max-width: 640px) 100vw, (max-width: 768px) 50vw, 33vw",className:"object-cover transition-transform group-hover:scale-105",onError:e=>{e.target.src="https://placehold.co/400x400/cccccc/666666?text=Image+Not+Found"}})}),(0,r.jsxs)("div",{className:"absolute left-2 top-2 z-10 space-y-1",children:[t.isNew&&(0,r.jsx)(G.E,{variant:"secondary",className:"bg-blue-600 text-white hover:bg-blue-700 text-xs sm:text-sm font-semibold px-2 py-1 shadow-md",children:"NEW"}),y>0&&(0,r.jsxs)(G.E,{variant:"destructive",className:"text-xs sm:text-sm font-semibold px-2 py-1 shadow-md",children:["-",y,"%"]})]})]}),(0,r.jsxs)("div",{className:"p-4 md:p-6 flex-1 flex flex-col",children:[(0,r.jsxs)("div",{className:"mb-auto",children:[(0,r.jsx)(v(),{href:"/category/".concat(t.categorySlug),className:"text-sm text-muted-foreground hover:text-primary",children:t.categoryName}),(0,r.jsx)(v(),{href:"/product/".concat(t.slug),className:"mt-1 block text-xl font-medium hover:text-primary",children:t.name}),(0,r.jsx)("div",{className:"mt-2",children:(0,r.jsx)(J,{rating:t.rating,size:"sm"})}),(0,r.jsx)("p",{className:"mt-4 text-muted-foreground line-clamp-3",children:"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."})]}),(0,r.jsxs)("div",{className:"mt-4 flex items-center justify-between",children:[(0,r.jsx)("div",{className:"space-x-2",children:t.discountedPrice?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("span",{className:"text-xl font-bold text-primary",children:["$",t.discountedPrice.toFixed(2)]}),(0,r.jsxs)("span",{className:"text-sm text-muted-foreground line-through",children:["$",t.price.toFixed(2)]})]}):(0,r.jsxs)("span",{className:"text-xl font-bold text-primary",children:["$",t.price.toFixed(2)]})}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)(o.$,{variant:"outline",size:"sm",className:"h-9",onClick:()=>{f(!0);try{d.isInWishlist(parseInt(t.id))?(d.removeFromWishlist(parseInt(t.id)),V.oR.success("".concat(t.name," removed from wishlist"))):(d.addToWishlist(parseInt(t.id)),V.oR.success("".concat(t.name," added to wishlist")))}catch(e){console.error("Error updating wishlist:",e),V.oR.error("Failed to update wishlist")}finally{f(!1)}},children:[(0,r.jsx)(O.A,{className:"h-4 w-4 mr-2",fill:d.isInWishlist(parseInt(t.id))?"red":"none",color:d.isInWishlist(parseInt(t.id))?"red":"currentColor"}),g?"Updating...":"Wishlist"]}),(0,r.jsxs)(o.$,{variant:"default",size:"sm",className:"h-9",disabled:!t.inStock||h,onClick:N,children:[(0,r.jsx)(F,{className:"h-4 w-4 mr-2"}),h?"Adding...":"Add to Cart"]})]})]}),!t.inStock&&(0,r.jsx)("p",{className:"mt-2 text-sm text-destructive",children:"Out of Stock"})]})]})}):(0,r.jsxs)(_.Zp,{className:"group relative overflow-hidden transition-all hover:shadow-lg",children:[(0,r.jsxs)("div",{className:"absolute left-2 top-2 z-10 space-y-1",children:[t.isNew&&(0,r.jsx)(G.E,{variant:"secondary",className:"bg-blue-600 text-white hover:bg-blue-700 text-xs sm:text-sm font-semibold px-2 py-1 shadow-md",children:"NEW"}),y>0&&(0,r.jsxs)(G.E,{variant:"destructive",className:"text-xs sm:text-sm font-semibold px-2 py-1 shadow-md",children:["-",y,"%"]})]}),(0,r.jsxs)("div",{className:"relative aspect-square overflow-hidden",children:[(0,r.jsx)(v(),{href:"/product/".concat(t.slug),children:(0,r.jsx)(L.default,{src:u.url.startsWith("http")?u.url:u.url.startsWith("/")?"".concat(W.T.ADMIN_BASE_URL).concat(u.url.substring(1)):"".concat(W.T.ADMIN_BASE_URL).concat(u.url),alt:u.alt,fill:!0,sizes:"(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw",className:"object-cover transition-transform group-hover:scale-105",onError:e=>{e.target.src="https://placehold.co/400x400/cccccc/666666?text=Image+Not+Found"}})}),(0,r.jsxs)("div",{className:"absolute right-2 top-2 z-10 flex flex-col gap-2 transition-all ".concat(a),children:[(0,r.jsx)(o.$,{variant:"secondary",size:"icon",className:"h-8 w-8 bg-white/80 backdrop-blur-sm hover:bg-white",onClick:async()=>{if(t.inStock){f(!0);try{d.isInWishlist(parseInt(t.id))?(d.removeFromWishlist(parseInt(t.id)),V.oR.success("Removed from wishlist")):(d.addToWishlist(parseInt(t.id)),V.oR.success("Added to wishlist"))}catch(e){V.oR.error("Error updating wishlist")}finally{f(!1)}}},disabled:g||!t.inStock,children:(0,r.jsx)(O.A,{className:"h-4 w-4",fill:d.isInWishlist(parseInt(t.id))?"red":"none",color:d.isInWishlist(parseInt(t.id))?"red":"currentColor"})}),(0,r.jsx)(o.$,{variant:"secondary",size:"icon",className:"h-8 w-8 bg-white/80 backdrop-blur-sm hover:bg-white",onClick:()=>{},children:(0,r.jsx)(B.A,{className:"h-4 w-4"})}),(0,r.jsx)(o.$,{variant:"secondary",size:"icon",className:"h-8 w-8 bg-white/80 backdrop-blur-sm hover:bg-white",onClick:()=>{},children:(0,r.jsx)(z.A,{className:"h-4 w-4"})})]})]}),(0,r.jsxs)("div",{className:"p-4",children:[(0,r.jsx)(v(),{href:"/category/".concat(t.categorySlug),className:"text-sm text-muted-foreground hover:text-primary",children:t.categoryName}),(0,r.jsx)(v(),{href:"/product/".concat(t.slug),className:"mt-1 block text-lg font-medium hover:text-primary",children:t.name}),(0,r.jsx)("div",{className:"mt-1",children:(0,r.jsx)(J,{rating:t.rating,size:"sm"})}),(0,r.jsxs)("div",{className:"mt-2 flex items-center justify-between",children:[(0,r.jsx)("div",{className:"space-x-2",children:t.discountedPrice?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("span",{className:"text-lg font-bold text-primary",children:["$",t.discountedPrice.toFixed(2)]}),(0,r.jsxs)("span",{className:"text-sm text-muted-foreground line-through",children:["$",t.price.toFixed(2)]})]}):(0,r.jsxs)("span",{className:"text-lg font-bold text-primary",children:["$",t.price.toFixed(2)]})}),(0,r.jsx)(o.$,{variant:"secondary",size:"icon",className:"h-8 w-8",disabled:!t.inStock||h,onClick:N,children:h?(0,r.jsx)(z.A,{className:"h-4 w-4 animate-spin"}):(0,r.jsx)(F,{className:"h-4 w-4"})})]}),!t.inStock&&(0,r.jsx)("p",{className:"mt-2 text-sm text-destructive",children:"Out of Stock"})]})]})}function Z(e){let{effect:t}=e,{t:i}=(0,x.t)(),[l,n]=(0,s.useState)([]),[o,c]=(0,s.useState)(!0),[d,u]=(0,s.useState)(null),m=async()=>{c(!0);try{var e,t;let{MakeApiCallAsync:r}=await Promise.resolve().then(a.bind(a,2862)),s=await r("get-recents-products-list",null,{requestParameters:{PageNo:1,PageSize:12,recordValueJson:"[]"}},{"Content-Type":"application/json",Accept:"application/json"},"POST",!0);if(null==s?void 0:null===(e=s.data)||void 0===e?void 0:e.data)try{let e=JSON.parse(s.data.data);if(console.log("New products data:",e),Array.isArray(e)){let t=e.map(e=>{var t,a;return{id:(null===(t=e.ProductId)||void 0===t?void 0:t.toString())||"",name:e.ProductName||"",slug:(e.ProductName||"").toLowerCase().replace(/\s+/g,"-"),price:e.Price||0,discountedPrice:e.DiscountedPrice,images:(null===(a=e.ProductImagesJson)||void 0===a?void 0:a.map(t=>{var a;return{id:(null===(a=t.AttachmentID)||void 0===a?void 0:a.toString())||"",url:t.AttachmentURL?"".concat(W.T.ADMIN_BASE_URL).concat(t.AttachmentURL):"/images/placeholder.jpg",alt:e.ProductName||"Product image"}}))||[],rating:e.Rating||0,categoryName:e.CategoryName||"",categorySlug:(e.CategoryName||"").toLowerCase().replace(/\s+/g,"-"),isNew:e.MarkAsNew||!1,inStock:(e.StockQuantity||0)>0}});n(t)}else console.error("Products data is not an array:",e),n([]),u("Invalid data format received from server")}catch(e){console.error("Error parsing products data:",e),n([]),u("Error processing product data")}else(null==s?void 0:null===(t=s.data)||void 0===t?void 0:t.errorMessage)?(console.error("API Error:",s.data.errorMessage),n([]),u(s.data.errorMessage||"An error occurred while fetching products")):(console.error("Invalid or empty response from API"),n([]),u("No data received from server"))}catch(e){console.error("Error fetching products:",e),n([]),e&&"object"==typeof e&&"message"in e?u(e.message):u("An unexpected error occurred while fetching products")}finally{c(!1)}};return((0,s.useEffect)(()=>{m()},[]),d)?(0,r.jsx)("div",{className:"w-full p-4 mb-4 text-center",children:(0,r.jsxs)("div",{className:"p-4 bg-red-50 border border-red-200 rounded-md",children:[(0,r.jsx)("p",{className:"text-red-600",children:d}),(0,r.jsx)("button",{onClick:()=>{u(null),m()},className:"mt-2 px-4 py-2 bg-red-100 hover:bg-red-200 text-red-700 rounded-md text-sm transition-colors",children:i("tryAgain")})]})}):(0,r.jsx)("div",{className:"w-full",children:(0,r.jsx)("div",{className:"grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4",children:o?Array.from({length:10}).map((e,t)=>(0,r.jsxs)(_.Zp,{className:"p-3 sm:p-4",children:[(0,r.jsx)(R.E,{className:"h-32 sm:h-40 w-full mb-3 sm:mb-4"}),(0,r.jsx)(R.E,{className:"h-3 sm:h-4 w-2/3 mb-2"}),(0,r.jsx)(R.E,{className:"h-3 sm:h-4 w-1/2"})]},t)):l.length>0?l.map(e=>(0,r.jsx)(Q,{product:e,effect:t},e.id)):(0,r.jsx)("div",{className:"col-span-full text-center py-8",children:(0,r.jsx)("p",{className:"text-muted-foreground",children:i("noProductsFound")})})})})}let X=()=>{let[e,t]=(0,s.useState)([]),[i,l]=(0,s.useState)("https://admin.codemedicalapps.com/");(0,s.useEffect)(()=>{(async()=>{try{var e;let{MakeApiCallAsync:r}=await Promise.resolve().then(a.bind(a,2862)),s=await r("get-web-campaign-list",null,{requestParameters:{PageNo:1,PageSize:3,recordValueJson:"[]"}},{"Content-Type":"application/json",Accept:"application/json"},"POST",!0);if(null==s?void 0:null===(e=s.data)||void 0===e?void 0:e.data)try{let e=JSON.parse(s.data.data);if(console.log("Campaign data:",e),Array.isArray(e)&&e.length>0){let a=e.map(e=>{var t;return{CampaignId:(null===(t=e.CampaignId)||void 0===t?void 0:t.toString())||"",MainTitle:e.MainTitle||e.Title||"",DiscountTitle:e.DiscountTitle||e.SubTitle||"",CoverPictureUrl:e.CoverPictureUrl||e.ImageUrl||"/images/campaign/placeholder.jpg"}});t(a)}else t([{CampaignId:"1",MainTitle:"Summer Collection",DiscountTitle:"Up to 50% Off",CoverPictureUrl:"/images/campaign/summer.jpg"},{CampaignId:"2",MainTitle:"Winter Essentials",DiscountTitle:"Save 30%",CoverPictureUrl:"/images/campaign/winter.jpg"},{CampaignId:"3",MainTitle:"New Arrivals",DiscountTitle:"Fresh Styles",CoverPictureUrl:"/images/campaign/new.jpg"}])}catch(e){console.error("Error parsing campaign data:",e),t([{CampaignId:"1",MainTitle:"Summer Collection",DiscountTitle:"Up to 50% Off",CoverPictureUrl:"/images/campaign/summer.jpg"},{CampaignId:"2",MainTitle:"Winter Essentials",DiscountTitle:"Save 30%",CoverPictureUrl:"/images/campaign/winter.jpg"},{CampaignId:"3",MainTitle:"New Arrivals",DiscountTitle:"Fresh Styles",CoverPictureUrl:"/images/campaign/new.jpg"}])}else console.error("Invalid or empty response from API"),t([{CampaignId:"1",MainTitle:"Summer Collection",DiscountTitle:"Up to 50% Off",CoverPictureUrl:"/images/campaign/summer.jpg"},{CampaignId:"2",MainTitle:"Winter Essentials",DiscountTitle:"Save 30%",CoverPictureUrl:"/images/campaign/winter.jpg"},{CampaignId:"3",MainTitle:"New Arrivals",DiscountTitle:"Fresh Styles",CoverPictureUrl:"/images/campaign/new.jpg"}])}catch(e){console.error("Error fetching campaign data:",e),t([{CampaignId:"1",MainTitle:"Summer Collection",DiscountTitle:"Up to 50% Off",CoverPictureUrl:"/images/campaign/summer.jpg"},{CampaignId:"2",MainTitle:"Winter Essentials",DiscountTitle:"Save 30%",CoverPictureUrl:"/images/campaign/winter.jpg"},{CampaignId:"3",MainTitle:"New Arrivals",DiscountTitle:"Fresh Styles",CoverPictureUrl:"/images/campaign/new.jpg"}])}})()},[]);let n=(e,t)=>e.length<=t?e:e.substring(0,t)+"...",o=()=>"en";return(0,r.jsx)("section",{className:"py-12",children:(0,r.jsx)("div",{className:"container mx-auto px-4",children:(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:e&&e.slice(0,3).map((e,t)=>(0,r.jsxs)("div",{className:"relative overflow-hidden rounded-lg group",children:[(0,r.jsx)("div",{className:"aspect-[4/3] relative",children:(0,r.jsx)(L.default,{src:i+e.CoverPictureUrl,className:"object-cover transition-transform duration-300 group-hover:scale-105",alt:e.MainTitle,fill:!0,sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 33vw"})}),(0,r.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-black/40 p-6 text-center text-white",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-xl font-bold mb-2",children:n(e.DiscountTitle,25)}),(0,r.jsx)("h4",{className:"text-lg mb-4",children:n(e.MainTitle,35)}),(0,r.jsx)(v(),{href:"/".concat(o(),"/campaign/").concat(e.CampaignId,"/").concat(e.MainTitle),className:"inline-block px-6 py-2 bg-white/20 hover:bg-white/30 rounded-full transition-colors duration-200 backdrop-blur-sm",children:"View Detail"})]})})]},t))})})})};var Y=a(4348);function K(){let{t:e}=(0,x.t)(),t=(0,$._)(),[i,l]=(0,s.useState)(null),[n,c]=(0,s.useState)(!0),[d,u]=(0,s.useState)(!1),[m,h]=(0,s.useState)({days:0,hours:0,minutes:0,seconds:0});if((0,s.useEffect)(()=>{(async()=>{c(!0);try{try{var e,t,r;let{MakeApiCallAsync:s}=await Promise.resolve().then(a.bind(a,2862)),i=await s("api/v1/products/get-all-products",null,{requestParameters:{SearchTerm:"",CategoryID:null,TagID:null,ManufacturerID:null,MinPrice:null,MaxPrice:null,Rating:null,OrderByColumnName:"Price DESC",PageNo:1,PageSize:1,recordValueJson:"[]"}},{"Content-Type":"application/json",Accept:"application/json"},"POST",!0);if(null==i?void 0:null===(e=i.data)||void 0===e?void 0:e.data)try{let e=JSON.parse(i.data.data);if(console.log("Hot deal data:",e),Array.isArray(e)&&e.length>0){let a=e[0],s={id:(null===(t=a.ProductId)||void 0===t?void 0:t.toString())||"1",name:a.ProductName||"Hot Deal Product",slug:(a.ProductName||"hot-deal").toLowerCase().replace(/\s+/g,"-"),price:a.Price||99.99,discountedPrice:a.DiscountedPrice||79.99,images:(null===(r=a.ProductImagesJson)||void 0===r?void 0:r.map(e=>{var t;return{id:(null===(t=e.AttachmentID)||void 0===t?void 0:t.toString())||"1",url:e.AttachmentURL?"".concat(W.T.ADMIN_BASE_URL).concat(e.AttachmentURL):"/images/placeholder.jpg",alt:a.ProductName||"Hot deal product"}}))||[{id:"1",url:"/images/placeholder.jpg",alt:"Hot deal product"}],rating:a.Rating||4.5,description:a.ShortDescription||"Limited time offer on this amazing product!",endDate:a.DiscountEndDate||new Date(Date.now()+2592e5).toISOString(),inStock:(a.StockQuantity||0)>0};l(s);return}}catch(e){console.error("Error parsing hot deal data:",e)}}catch(e){console.error("API call failed:",e)}console.log("No hot deal data available from API"),l(null)}catch(e){console.error("Error in fetchHotDeal:",e),l(null)}finally{c(!1)}})()},[]),(0,s.useEffect)(()=>{if(!i)return;let e=setInterval(()=>{let e=new Date(i.endDate).getTime()-new Date().getTime();e>0&&h({days:Math.floor(e/864e5),hours:Math.floor(e/36e5%24),minutes:Math.floor(e/1e3/60%60),seconds:Math.floor(e/1e3%60)})},1e3);return()=>clearInterval(e)},[i]),n)return(0,r.jsxs)(_.Zp,{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,r.jsx)(R.E,{className:"h-6 w-32"}),(0,r.jsx)(R.E,{className:"h-6 w-6"})]}),(0,r.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,r.jsx)(R.E,{className:"aspect-square"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(R.E,{className:"h-8 w-3/4"}),(0,r.jsx)(R.E,{className:"h-4 w-1/2"}),(0,r.jsx)(R.E,{className:"h-20 w-full"}),(0,r.jsx)("div",{className:"grid grid-cols-4 gap-2",children:Array.from({length:4}).map((e,t)=>(0,r.jsx)(R.E,{className:"h-16 w-full"},t))}),(0,r.jsx)(R.E,{className:"h-10 w-full"})]})]})]});if(!i)return null;let p=Math.round((i.price-i.discountedPrice)/i.price*100);return(0,r.jsxs)(_.Zp,{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold",children:"Today's Hot Deal"}),(0,r.jsx)(Y.A,{className:"h-6 w-6 text-red-500"})]}),(0,r.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"relative aspect-square overflow-hidden rounded-lg",children:[(0,r.jsx)(L.default,{src:i.images[0].url,alt:i.images[0].alt,fill:!0,className:"object-cover"}),(0,r.jsxs)(G.E,{variant:"destructive",className:"absolute left-2 top-2",children:["-",p,"%"]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(v(),{href:"/product/".concat(i.slug),className:"block hover:text-primary",children:(0,r.jsx)("h3",{className:"text-2xl font-bold",children:i.name})}),(0,r.jsx)(J,{rating:i.rating}),(0,r.jsx)("p",{className:"text-muted-foreground",children:i.description}),(0,r.jsx)("div",{className:"grid grid-cols-4 gap-2 text-center",children:Object.entries(m).map(e=>{let[t,a]=e;return(0,r.jsxs)("div",{className:"bg-muted p-2 rounded-lg",children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:a}),(0,r.jsx)("div",{className:"text-xs text-muted-foreground",children:t})]},t)})}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("span",{className:"text-2xl font-bold text-primary",children:["$",i.discountedPrice.toFixed(2)]}),(0,r.jsxs)("span",{className:"text-lg text-muted-foreground line-through",children:["$",i.price.toFixed(2)]})]}),(0,r.jsx)(o.$,{size:"lg",className:"w-full",disabled:!i.inStock||d,onClick:()=>{if(i&&i.inStock){u(!0);try{t.addToCart({id:parseInt(i.id,10),name:i.name,price:i.discountedPrice,discountPrice:i.discountedPrice,image:i.images[0].url,originalPrice:i.price},1,[],void 0),V.oR.success("".concat(i.name," added to cart"))}catch(e){console.error("Error adding to cart:",e),V.oR.error("Failed to add product to cart")}finally{u(!1)}}},children:d?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(z.A,{className:"mr-2 h-5 w-5 animate-spin"}),"Adding..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(F,{className:"mr-2 h-5 w-5"}),i.inStock?"Add to Cart":"Out of Stock"]})})]})]})]})}function ee(e){let{title:t="popularProducts",limit:i=8,hoverEffect:l}=e,{t:n}=(0,x.t)(),[o,c]=(0,s.useState)([]),[d,u]=(0,s.useState)(!0),[m,h]=(0,s.useState)(null);return((0,s.useEffect)(()=>{(async()=>{u(!0);try{let{MakeApiCallAsync:t}=await Promise.resolve().then(a.bind(a,2862));try{var e;let a=await t("api/v1/products/get-all-products",null,{requestParameters:{SearchTerm:"",CategoryID:null,TagID:null,ManufacturerID:null,MinPrice:null,MaxPrice:null,Rating:null,OrderByColumnName:"Price DESC",PageNo:1,PageSize:i,recordValueJson:"[]"}},{"Content-Type":"application/json",Accept:"application/json"},"POST",!0);if(null==a?void 0:null===(e=a.data)||void 0===e?void 0:e.data)try{let e=JSON.parse(a.data.data);if(console.log("Popular products data:",e),Array.isArray(e)){let t=e.map(e=>{var t;let a=[];return e.ProductImagesJson&&Array.isArray(e.ProductImagesJson)?a=e.ProductImagesJson.map(t=>{var a;return{id:(null===(a=t.AttachmentID)||void 0===a?void 0:a.toString())||"",url:t.AttachmentURL?t.AttachmentURL.startsWith("http")?t.AttachmentURL:"".concat(W.T.ADMIN_BASE_URL).concat(t.AttachmentURL.startsWith("/")?t.AttachmentURL:"/"+t.AttachmentURL):"/images/placeholder.jpg",alt:e.ProductName||"Product image"}}):e.ProductImages&&Array.isArray(e.ProductImages)&&(a=e.ProductImages.map(t=>{var a;return{id:(null===(a=t.AttachmentID)||void 0===a?void 0:a.toString())||"",url:t.AttachmentURL?t.AttachmentURL.startsWith("http")?t.AttachmentURL:"".concat(W.T.ADMIN_BASE_URL).concat(t.AttachmentURL.startsWith("/")?t.AttachmentURL:"/"+t.AttachmentURL):"/images/placeholder.jpg",alt:e.ProductName||"Product image"}})),0===a.length&&a.push({id:"0",url:"/images/no-image.jpg",alt:e.ProductName||"Product image",isPrimary:!0}),{id:(null===(t=e.ProductId)||void 0===t?void 0:t.toString())||"",name:e.ProductName||"",slug:(e.ProductName||"").toLowerCase().replace(/\s+/g,"-"),price:e.Price||0,discountedPrice:e.DiscountedPrice,images:a,rating:e.Rating||0,categoryName:e.CategoryName||"",categorySlug:(e.CategoryName||"").toLowerCase().replace(/\s+/g,"-"),isNew:!0===e.MarkAsNew||!0===e.IsNew,inStock:(e.StockQuantity||0)>0}});console.log("Transformed popular products:",t),c(t);return}}catch(e){console.error("Error parsing products data:",e)}}catch(e){console.error("API call failed:",e)}console.log("No popular products data available from API"),c([])}catch(e){console.error("Error in fetchPopularProducts:",e),c([]),e&&"object"==typeof e&&"message"in e?h(e.message):h("An unexpected error occurred while fetching products")}finally{u(!1)}})()},[i]),m)?(0,r.jsx)("div",{className:"w-full p-4 mb-4 text-center",children:(0,r.jsxs)("div",{className:"p-4 bg-red-50 border border-red-200 rounded-md",children:[(0,r.jsx)("p",{className:"text-red-600",children:m}),(0,r.jsx)("button",{onClick:()=>{h(null),u(!0),(async()=>{try{let{MakeApiCallAsync:e}=await Promise.resolve().then(a.bind(a,2862));await e("api/v1/products/get-all-products",null,{requestParameters:{SearchTerm:"",CategoryID:null,TagID:null,ManufacturerID:null,MinPrice:null,MaxPrice:null,Rating:null,OrderByColumnName:"Price DESC",PageNo:1,PageSize:i,recordValueJson:"[]"}},{"Content-Type":"application/json",Accept:"application/json"},"POST",!0),u(!1)}catch(e){console.error("Error retrying fetch:",e),u(!1),h("Failed to fetch products. Please try again later.")}})()},className:"mt-2 px-4 py-2 bg-red-100 hover:bg-red-200 text-red-700 rounded-md text-sm transition-colors",children:"Try Again"})]})}):d?(0,r.jsx)("div",{className:"w-full",children:(0,r.jsx)("div",{className:"grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:Array.from({length:i}).map((e,t)=>(0,r.jsxs)(_.Zp,{className:"p-4",children:[(0,r.jsx)(R.E,{className:"h-48 w-full rounded-md mb-4"}),(0,r.jsx)(R.E,{className:"h-4 w-3/4 mb-2"}),(0,r.jsx)(R.E,{className:"h-4 w-1/2"})]},t))})}):0===o.length?(0,r.jsx)("div",{className:"w-full text-center py-8",children:(0,r.jsx)("p",{className:"text-muted-foreground",children:"No popular products found"})}):(0,r.jsx)("div",{className:"w-full",children:(0,r.jsx)("div",{className:"grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:o.map(e=>(0,r.jsx)(Q,{product:e,effect:l},e.id))})})}function et(){return(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row min-h-screen bg-background",children:[(0,r.jsx)("div",{className:"w-full lg:w-64 lg:flex-shrink-0 px-4 sm:px-6 lg:px-0 py-4 lg:py-0",children:(0,r.jsx)(D,{})}),(0,r.jsxs)("div",{className:"flex-1 p-4 sm:p-6",children:[(0,r.jsx)(u,{}),(0,r.jsx)(k,{}),(0,r.jsxs)("div",{className:"space-y-12 mt-8",children:[(0,r.jsxs)("section",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold mb-6",children:"New Products"}),(0,r.jsx)(Z,{effect:"icon-inline"})]}),(0,r.jsx)("section",{children:(0,r.jsx)(X,{})}),(0,r.jsxs)("section",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold mb-6",children:"Today's Hot Deal"}),(0,r.jsx)(K,{})]}),(0,r.jsx)("section",{children:(0,r.jsx)(ee,{hoverEffect:"icon-inline",title:"Popular Products"})})]})]})]})}},7110:(e,t,a)=>{"use strict";a.d(t,{Z:()=>n,t:()=>o});var r=a(5155),s=a(2115);let i={en:{menu:"Menu",newsletter:"Newsletter",enterEmail:"Enter your email",newsletterDisclaimer:"Subscribe to our newsletter to receive updates and exclusive offers",popularCategories:"Popular Categories!",settings:"Settings",language:"Language",theme:"Theme",color:"Color",save:"Save",cancel:"Cancel",close:"Close",phone:"***************",phonenumber:"Phone Number",email:"<EMAIL>",liveChat:"Live Chat",welcome:"Welcome",logout:"Logout",login:"Login",signup:"Sign Up",signUp:"Sign Up",category:"Category",categories:"Categories",filters:"Filters",clearAll:"Clear All",products:"Products",loadingCategories:"Loading categories...",home:"Home",todayDeals:"Today's Deals",followUs:"Follow Us",aboutUs:"About Us",contactUs:"Contact Us",tryAgain:"Try Again",noProductsFound:"No products found",allProducts:"All Products",quickLinks:"Quick Links",about:"About Us",contact:"Contact Us",hotDeals:"Hot Deals",customerArea:"Customer Area",myAccount:"My Account",orders:"Orders",cart:"Cart",wishlist:"Wishlist",paymentMethods:"Payment Methods",location:"Location",callUs:"Call Us",emailUs:"Email Us",subscribe:"Subscribe",name:"Name",subject:"Subject",message:"Message",sendMessage:"Send Message",sending:"Sending...",messageSent:"Message sent successfully!",messageError:"Failed to send message",contactInfo:"Contact Information",address:"Address",findUs:"Find Us"},ar:{menu:"القائمة",popularCategories:"الفئات الشائعة!",settings:"الإعدادات",language:"اللغة",theme:"المظهر",color:"اللون",save:"حفظ",cancel:"إلغاء",close:"إغلاق",phone:"***************",phonenumber:"رقم الهاتف",email:"<EMAIL>",liveChat:"محادثة مباشرة",welcome:"مرحباً",logout:"تسجيل الخروج",login:"تسجيل الدخول",signup:"تسجيل جديد",signUp:"تسجيل جديد",category:"الفئة",categories:"الفئات",products:"المنتجات",loadingCategories:"جاري تحميل الفئات...",filters:"المرشحات",clearAll:"مسح الكل",home:"الرئيسية",todayDeals:"عروض اليوم",followUs:"تابعنا",aboutUs:"من نحن",contactUs:"اتصل بنا",tryAgain:"حاول مرة أخرى",noProductsFound:"لم يتم العثور على منتجات",allProducts:"جميع المنتجات",quickLinks:"روابط سريعة",about:"من نحن",contact:"اتصل بنا",hotDeals:"عروض ساخنة",customerArea:"منطقة العملاء",myAccount:"حسابي",orders:"الطلبات",cart:"السلة",wishlist:"المفضلة",paymentMethods:"طرق الدفع",location:"الموقع",callUs:"اتصل بنا",emailUs:"راسلنا",subscribe:"اشترك",name:"الاسم",subject:"الموضوع",message:"الرسالة",sendMessage:"إرسال الرسالة",sending:"جاري الإرسال...",messageSent:"تم إرسال الرسالة بنجاح!",messageError:"فشل في إرسال الرسالة",contactInfo:"معلومات الاتصال",address:"العنوان",findUs:"موقعنا"}},l=(0,s.createContext)(void 0);function n(e){let{children:t}=e,[a,n]=(0,s.useState)("light"),[o,c]=(0,s.useState)("en"),[d,u]=(0,s.useState)("#0074b2");return(0,s.useEffect)(()=>{document.documentElement.style.setProperty("--primary",d),document.documentElement.style.setProperty("--primary-foreground","#ffffff")},[]),(0,r.jsx)(l.Provider,{value:{theme:a,language:o,primaryColor:d,toggleTheme:()=>{n("light"===a?"dark":"light")},setLanguage:e=>{c(e),document.documentElement.dir="ar"===e?"rtl":"ltr"},setPrimaryColor:e=>{u(e)},t:e=>(function(e,t){let a=i[t];return e in a?a[e]:"en"!==t&&e in i.en?i.en[e]:e})(e,o)},children:t})}function o(){let e=(0,s.useContext)(l);if(void 0===e)throw Error("useSettings must be used within a SettingsProvider");return e}},8936:(e,t,a)=>{"use strict";a.d(t,{_:()=>n,e:()=>l});var r=a(5155),s=a(2115);let i=(0,s.createContext)(void 0);function l(e){let{children:t}=e,[a,l]=(0,s.useState)([]),[n,o]=(0,s.useState)(!1);(0,s.useEffect)(()=>{let e=localStorage.getItem("cart");if(e)try{l(JSON.parse(e))}catch(e){console.error("Failed to parse cart from localStorage:",e)}o(!0)},[]),(0,s.useEffect)(()=>{localStorage.setItem("cart",JSON.stringify(a))},[a]);let c=e=>{l(t=>t.filter(t=>t.id!==e))},d=a.reduce((e,t)=>e+t.quantity,0);(0,s.useEffect)(()=>{localStorage.setItem("cart",JSON.stringify(a))},[a]);let u=a.reduce((e,t)=>e+(t.discountPrice?Math.min(t.discountPrice,t.adjustedPrice):t.adjustedPrice)*t.quantity,0),m=a.reduce((e,t)=>e+(t.adjustedIqdPrice||t.iqdPrice||0)*t.quantity,0);return(0,r.jsx)(i.Provider,{value:{items:a,addToCart:function(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],r=arguments.length>3?arguments[3]:void 0,s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1500;l(i=>{let l=e.price,n=r||Math.round(e.price*s),o=n;a.forEach(t=>{if(t.PriceAdjustment&&t.PriceAdjustmentType){let a=e.originalPrice||e.price;switch(t.PriceAdjustmentType){case 1:l+=t.PriceAdjustment,o+=Math.round(t.PriceAdjustment*s);break;case 2:let r=a*t.PriceAdjustment/100;l+=r,o+=Math.round(r*s)}}});let c=i.findIndex(t=>{var r;return t.id===e.id&&JSON.stringify(null===(r=t.attributes)||void 0===r?void 0:r.sort((e,t)=>e.ProductAttributeID-t.ProductAttributeID))===JSON.stringify(null==a?void 0:a.sort((e,t)=>e.ProductAttributeID-t.ProductAttributeID))});if(!(c>=0))return[...i,{...e,iqdPrice:n,adjustedIqdPrice:Math.max(0,o),quantity:t,attributes:a,adjustedPrice:Math.max(0,l),originalPrice:e.originalPrice}];{let e=[...i];return e[c].quantity+=t,e}})},removeFromCart:c,updateQuantity:(e,t)=>{if(t<=0){c(e);return}l(a=>a.map(a=>a.id===e?{...a,quantity:t}:a))},clearCart:()=>{l([])},totalItems:d,subtotal:u,subtotalIQD:m,total:u,totalIQD:m,isHydrated:n},children:t})}function n(){let e=(0,s.useContext)(i);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e}},9131:(e,t,a)=>{Promise.resolve().then(a.bind(a,6664))},9286:(e,t,a)=>{"use strict";a.d(t,{E:()=>n});var r=a(5155),s=a(1027),i=a(9602);let l=(0,s.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function n(e){let{className:t,variant:a,...s}=e;return(0,r.jsx)("div",{className:(0,i.cn)(l({variant:a}),t),...s})}},9355:(e,t,a)=>{"use strict";a.d(t,{Z:()=>l,n:()=>n});var r=a(5155),s=a(2115);let i=(0,s.createContext)(void 0);function l(e){let{children:t}=e,[a,l]=(0,s.useState)([]),[n,o]=(0,s.useState)(!1);return(0,s.useEffect)(()=>{let e=localStorage.getItem("wishlist");if(e)try{l(JSON.parse(e))}catch(e){console.error("Failed to parse wishlist from localStorage:",e)}o(!0)},[]),(0,s.useEffect)(()=>{localStorage.setItem("wishlist",JSON.stringify(a))},[a]),(0,r.jsx)(i.Provider,{value:{wishlistItems:a,addToWishlist:e=>{a.includes(e)||l([...a,e])},removeFromWishlist:e=>{l(a.filter(t=>t!==e))},isInWishlist:e=>a.includes(e),totalItems:a.length,isHydrated:n},children:t})}function n(){let e=(0,s.useContext)(i);if(void 0===e)throw Error("useWishlist must be used within a WishlistProvider");return e}},9602:(e,t,a)=>{"use strict";a.d(t,{cn:()=>i});var r=a(3463),s=a(9795);function i(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.QP)((0,r.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[1345,2651,2446,6764,6392,8441,6587,7358],()=>t(9131)),_N_E=e.O()}]);