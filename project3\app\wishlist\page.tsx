'use client';

import { useState, useEffect } from 'react';
import { AxiosError } from 'axios';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, <PERSON>readcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';

// Define the error response type
interface ErrorResponse {
  error: string;
  [key: string]: any;
}

interface AxiosErrorResponse {
  data?: ErrorResponse;
  status?: number;
  statusText?: string;
  headers?: any;
  config?: any;
}

interface CustomAxiosError extends Error {
  isAxiosError: boolean;
  response?: AxiosErrorResponse;
  config?: any;
  code?: string;
  request?: any;
}
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { useSettings } from '@/contexts/settings-context';
import { useCart } from '@/contexts/cart-context';
import { useWishlist } from '@/contexts/wishlist-context';
import { Heart, ShoppingCart, Trash2, Eye, Loader2, ChevronRight } from 'lucide-react';
import axios from 'axios';
import { toast } from 'sonner';

// Define the type for wishlist display items
type WishlistDisplayItem = {
  id: number;
  name: string;
  price: number;
  originalPrice?: number; // Added to support displaying original price
  image: string;
  inStock: boolean;
};

// Product type from API
type Product = {
  ProductId: number;
  ProductName: string;
  ProductPrice: number;
  ProductImagesJson: string;
  ProductQuantity: number;
  ProductDescription?: string;
  CategoryName?: string;
  ManufacturerName?: string;
};

// Helper function to parse product images
const parseProductImages = (productImagesJson: string) => {
  if (!productImagesJson) return [];
  
  if (typeof productImagesJson === 'string') {
    const trimmedPath = productImagesJson.trim();
    if (trimmedPath) {
      return [{
        AttachmentName: trimmedPath.split('/').pop() || 'image',
        AttachmentURL: trimmedPath,
        IsPrimary: true
      }];
    }
  }
  
  return [];
};

// Helper function to construct image URL
const constructImageUrl = (attachmentURL: string): string => {
  if (!attachmentURL) return '/placeholder-image.jpg';
  
  const baseUrl = process.env.NEXT_PUBLIC_ADMIN_BASE_URL || 'http://localhost:5000';
  const normalizedBaseUrl = baseUrl.replace(/\/$/, '');
  const normalizedPath = attachmentURL.startsWith('/') ? attachmentURL : `/${attachmentURL}`;
  
  return `${normalizedBaseUrl}${normalizedPath}`;
};

export default function WishlistPage() {
  const { t } = useSettings();
  const cart = useCart();
  const { wishlistItems, removeFromWishlist } = useWishlist();
  
  // State to hold the display items (products with details)
  const [displayItems, setDisplayItems] = useState<WishlistDisplayItem[]>([]);
  const [loading, setLoading] = useState(false);

  // Define types for API response
  interface ProductResponse {
    id?: number;
    ProductId?: number;
    ProductName?: string;
    Name?: string;
    Price?: number;
    ProductPrice?: number;
    OldPrice?: number;
    OriginalPrice?: number;
    ProductImagesJson?: string;
    StockQuantity?: number;
    Quantity?: number;
    [key: string]: any;
  }

  interface ApiResponse {
    data?: ProductResponse | ProductResponse[] | { data: ProductResponse | ProductResponse[] };
    products?: ProductResponse | ProductResponse[];
    [key: string]: any;
  }

  // Function to fetch product details from API
  const fetchProductDetails = async (productIds: number[]) => {
    if (!productIds || productIds.length === 0) {
      setDisplayItems([]);
      return;
    }

    // Filter out invalid product IDs
    const validProductIds = productIds.filter(id => id && !isNaN(Number(id)));
    
    if (validProductIds.length === 0) {
      setDisplayItems([]);
      return;
    }

    setLoading(true);
    
    try {
      console.log('Fetching products for IDs:', validProductIds);
      
      // Check if we have cached products
      const cachedProducts = localStorage.getItem('cachedProducts');
      if (cachedProducts) {
        try {
          const allProducts: ProductResponse[] = JSON.parse(cachedProducts);
          const wishlistProducts = allProducts.filter(product =>
            validProductIds.includes(product.ProductID || product.ProductId || product.id || 0)
          );
          
          if (wishlistProducts.length > 0) {
            console.log('Using cached products:', wishlistProducts.length);
            
            const itemsToDisplay = wishlistProducts.map(product => {
              let imageUrl = '';

              try {
                // Try to parse ProductImagesJson if it exists and is a string
                if (product.ProductImagesJson && typeof product.ProductImagesJson === 'string') {
                  const images = parseProductImages(product.ProductImagesJson);
                  const primaryImage = images.find((img: any) => img.IsPrimary) || images[0];
                  if (primaryImage) {
                    imageUrl = constructImageUrl(primaryImage.AttachmentURL);
                  }
                }
                // Fallback to ImagePath if available
                if (!imageUrl && product.ImagePath) {
                  imageUrl = constructImageUrl(product.ImagePath);
                }
              } catch (error) {
                console.error('Error processing cached product images:', error);
              }

              return {
                id: product.ProductID || product.ProductId || product.id || 0,
                name: product.ProductName || product.Name || 'Unnamed Product',
                price: product.Price || product.ProductPrice || 0,
                originalPrice: product.OldPrice || product.OriginalPrice || product.Price || product.ProductPrice || 0,
                image: imageUrl || '/placeholder-image.jpg',
                inStock: (product.StockQuantity || product.Quantity || 0) > 0
              };
            });
            
            setDisplayItems(itemsToDisplay);
            return;
          }
        } catch (cacheError) {
          console.error('Error reading from cache:', cacheError);
          // Continue to fetch from API if cache read fails
        }
      }

      // If not in cache, fetch from API using product detail API for each product
      console.log('Fetching products from API...');

      // Fetch each product individually using the product detail API
      const productPromises = validProductIds.map(async (productId) => {
        try {
          const response = await axios.post('/api/product-detail', {
            requestParameters: {
              ProductId: productId,
              recordValueJson: "[]",
            },
          });

          if (response.data && response.data.data) {
            const parsedData = JSON.parse(response.data.data);
            return Array.isArray(parsedData) ? parsedData[0] : parsedData;
          }
          return null;
        } catch (error) {
          console.error(`Error fetching product ${productId}:`, error);
          return null;
        }
      });

      const productResults = await Promise.all(productPromises);
      const products = productResults.filter(product => product !== null);

      console.log('Fetched products:', products.length);
      
      console.log('Total products extracted from response:', products.length);
      
      // If no products found, log the structure and set empty array
      if (products.length === 0) {
        console.warn('No products found in the API response.');
        setDisplayItems([]);
        return;
      }
      
      // Convert to display format
      const itemsToDisplay = products.map((product: any) => {
        // Handle different possible image properties
        let imageUrl = '';
        
        try {
          // Try to parse ProductImagesJson if it exists and is a string
          if (product.ProductImagesJson && typeof product.ProductImagesJson === 'string') {
            const images = parseProductImages(product.ProductImagesJson);
            const primaryImage = images.find((img: any) => img.IsPrimary) || images[0];
            if (primaryImage) {
              imageUrl = constructImageUrl(primaryImage.AttachmentURL);
            }
          }
          // Fallback to ImagePath if available
          if (!imageUrl && product.ImagePath) {
            imageUrl = constructImageUrl(product.ImagePath);
          }
        } catch (error) {
          console.error('Error processing product images:', error);
        }
        
        return {
          id: product.ProductId || product.ProductID || product.id,
          name: product.ProductName || product.Name || 'Unnamed Product',
          price: product.Price || product.ProductPrice || 0,
          originalPrice: product.OldPrice || product.OriginalPrice || product.Price || product.ProductPrice || 0,
          image: imageUrl || '/placeholder-image.jpg',
          inStock: (product.StockQuantity || product.Quantity || 0) > 0
        };
      });
      
      console.log('Display items prepared:', itemsToDisplay.length);
      setDisplayItems(itemsToDisplay);
      
      // Cache the products for future use
      try {
        localStorage.setItem('cachedProducts', JSON.stringify(products));
      } catch (error) {
        console.error('Error caching products:', error);
      }
      
    } catch (error) {
      console.error('Error in fetchProductDetails:', error);
      
      let errorMessage = 'An unknown error occurred';
      
      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (error && typeof error === 'object' && 'message' in error) {
        errorMessage = String(error.message);
      }
      
      // Log detailed error information
      if (error && typeof error === 'object') {
        const errorObj = error as Record<string, unknown>;
        const axiosError = error as any;
        
        console.error('Error details:', {
          message: errorMessage,
          response: (axiosError as any)?.response?.data || 'No response data',
          status: (axiosError as any)?.response?.status,
          statusText: (axiosError as any)?.response?.statusText,
          config: {
            url: axiosError?.config?.url,
            method: axiosError?.config?.method,
            params: axiosError?.config?.params
          }
        });
      }
      
      // Extract error message from Axios response if available
      const axiosError = error && 
                        typeof error === 'object' && 
                        'isAxiosError' in error && 
                        (error as CustomAxiosError).response?.data?.error 
                          ? (error as CustomAxiosError).response?.data?.error 
                          : errorMessage;
      
      toast.error('Failed to load wishlist: ' + (axiosError || 'Unknown error'));
      setDisplayItems([]);
    } finally {
      setLoading(false);
    }
  };

  // Fetch product details when wishlist items change
  useEffect(() => {
    fetchProductDetails(wishlistItems);
  }, [wishlistItems]);

  const handleRemoveFromWishlist = (id: number) => {
    removeFromWishlist(id);
    toast.success('Product removed from wishlist');
  };

  if (loading) {
    return (
      <div className="container mx-auto py-12 flex flex-col items-center justify-center min-h-[50vh]">
        <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
        <p className="text-muted-foreground">Loading your wishlist...</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 px-4">
      <Breadcrumb className="mb-6">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/">Home</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbPage>Wishlist</BreadcrumbPage>
        </BreadcrumbList>
      </Breadcrumb>

      <h1 className="text-3xl font-bold mb-8">Your Wishlist</h1>
      
      {displayItems.length > 0 ? (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          {displayItems.map((item) => (
            <Card key={item.id} className="overflow-hidden">
              <div className="relative aspect-square">
                <img
                  src={item.image}
                  alt={item.name}
                  className="w-full h-full object-cover"
                />
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute top-2 right-2 rounded-full bg-background/80 backdrop-blur-sm hover:bg-background/60"
                  onClick={() => handleRemoveFromWishlist(item.id)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
              <div className="p-4">
                <h3 className="font-medium text-lg mb-2 line-clamp-2">{item.name}</h3>
                <div className="flex items-center gap-2 mb-4">
                  <span className="text-lg font-bold">${item.price.toFixed(2)}</span>
                  {item.originalPrice && item.originalPrice > item.price && (
                    <span className="text-sm text-muted-foreground line-through">
                      ${item.originalPrice.toFixed(2)}
                    </span>
                  )}
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1"
                    asChild
                  >
                    <Link href={`/product/${item.id}`}>
                      <Eye className="h-4 w-4 mr-2" />
                      View
                    </Link>
                  </Button>
                  <Button
                    size="sm"
                    className="flex-1"
                    disabled={!item.inStock}
                    onClick={() => {
                      cart.addToCart(
                        {
                          id: item.id,
                          name: item.name,
                          price: item.price,
                          discountPrice: item.originalPrice && item.originalPrice > item.price ? item.price : undefined,
                          originalPrice: item.originalPrice || item.price,
                          image: item.image
                        },
                        1,
                        [], // No attributes by default
                        undefined // No IQD price
                      );
                      toast.success(`Added ${item.name} to cart`);
                    }}
                  >
                    <ShoppingCart className="h-4 w-4 mr-2" />
                    {item.inStock ? 'Add to Cart' : 'Out of Stock'}
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>
      ) : (
        <Card className="p-8 text-center">
          <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
            <Heart className="h-8 w-8 text-muted-foreground" />
          </div>
          <h3 className="text-lg font-medium mb-2">Your wishlist is empty</h3>
          <p className="text-muted-foreground mb-6">
            You haven&apos;t added any products to your wishlist yet.
          </p>
          <Button asChild>
            <Link href="/products">
              Continue Shopping
              <ChevronRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </Card>
      )}
    </div>
  );
}