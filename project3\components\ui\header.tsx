'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { 
  NavigationMenu, 
  NavigationMenuContent, 
  NavigationMenuItem, 
  NavigationMenuLink, 
  NavigationMenuList, 
  NavigationMenuTrigger 
} from '@/components/ui/navigation-menu';
import { toast } from 'sonner';
import { 
  Menu, 
  ShoppingCart, 
  Heart, 
  User, 
  Search as SearchIcon, 
  X, 
  Moon, 
  Sun, 
  ChevronDown, 
  Globe as GlobeIcon,
  Phone,
  Mail
} from 'lucide-react';

// Types
type Language = {
  code: string;
  name: string;
  flag: string;
  rtl?: boolean;
};

type Category = {
  id: string;
  name: string;
  slug: string;
  subcategories?: Array<{
    id: string;
    name: string;
    slug: string;
  }>;
};

type NavItem = {
  name: string;
  href: string;
  icon?: string;
};

// Mock context hooks - Replace with actual context hooks from your app
const useSettings = () => ({
  theme: 'light',
  language: 'en',
  setLanguage: () => {},
  t: (key: string) => key,
});

const useCart = () => ({
  items: [],
});

const useWishlist = () => ({
  items: [],
});

const useUser = () => ({
  user: null,
  isLoggedIn: false,
  logout: async () => {},
});

// Constants
const LANGUAGES: Language[] = [
  { code: 'en', name: 'English', flag: '🇬🇧' },
  { code: 'ar', name: 'العربية', flag: '🇸🇦', rtl: true },
];

const NAV_ITEMS: NavItem[] = [
  { name: 'Home', href: '/', icon: 'home' },
  { name: 'Shop', href: '/products', icon: 'shopping-bag' },
  { name: 'Categories', href: '/categories', icon: 'grid' },
  { name: 'About', href: '/about', icon: 'info' },
  { name: 'Contact', href: '/contact', icon: 'mail' },
];

const MOBILE_NAV_ITEMS: NavItem[] = [
  { name: 'Home', href: '/', icon: 'home' },
  { name: 'Categories', href: '/categories', icon: 'grid' },
  { name: 'Cart', href: '/cart', icon: 'shopping-cart' },
  { name: 'Account', href: '/account', icon: 'user' },
];

export default function Header() {
  const router = useRouter();
  const pathname = usePathname();
  const { theme, language, setLanguage, t } = useSettings();
  const { items: cartItems = [] } = useCart();
  const { items: wishlistItems = [] } = useWishlist();
  const { user, isLoggedIn, logout } = useUser();

  // State
  const [showMobileNav, setShowMobileNav] = useState(false);
  const [showLanguageMenu, setShowLanguageMenu] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [isScrolled, setIsScrolled] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [selectedSubcategory, setSelectedSubcategory] = useState<string>('');
  const [primaryColor, setPrimaryColor] = useState('#1B3764');
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Refs
  const languageMenuRef = useRef<HTMLDivElement>(null);
  const userMenuRef = useRef<HTMLDivElement>(null);
  const mobileMenuRef = useRef<HTMLDivElement>(null);

  // Calculate cart and wishlist counts
  const cartCount = cartItems?.length || 0;
  const wishlistCount = wishlistItems?.length || 0;
  const currentLanguage = LANGUAGES.find(lang => lang.code === language) || LANGUAGES[0];

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchTerm.trim()) {
      router.push(`/search?q=${encodeURIComponent(searchTerm)}`);
    }
  };

  // Handle logout
  const handleLogout = async () => {
    try {
      await logout();
      toast.success('Logged out successfully');
      router.push('/login');
    } catch (error) {
      toast.error('Failed to log out');
      console.error('Logout error:', error);
    }
  };

  // Toggle mobile menu
  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  // Handle language change
  const handleLanguageChange = (lang: Language) => {
    setLanguage(lang.code);
    setShowLanguageMenu(false);
  };

  // Toggle theme
  const toggleTheme = () => {
    document.documentElement.classList.toggle('dark');
  };

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (languageMenuRef.current && !languageMenuRef.current.contains(event.target as Node)) {
        setShowLanguageMenu(false);
      }
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setShowUserMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Fetch categories on component mount
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setIsLoading(true);
        // Mock data - replace with actual API call
        setCategories([
          {
            id: '1',
            name: 'Category 1',
            slug: 'category-1',
            subcategories: [
              { id: '1-1', name: 'Subcategory 1', slug: 'subcategory-1' },
              { id: '1-2', name: 'Subcategory 2', slug: 'subcategory-2' },
            ],
          },
        ]);
      } catch (error) {
        console.error('Failed to fetch categories:', error);
        toast.error('Failed to load categories');
      } finally {
        setIsLoading(false);
      }
    };

    fetchCategories();
  }, []);

  // Handle scroll effect for header
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Main render
  return (
    <header className="sticky top-0 z-50 w-full bg-white shadow-sm dark:bg-gray-900">
      {/* Top Bar */}
      <div className="bg-primary text-white text-sm">
        <div className="container mx-auto px-4 py-2 flex justify-between items-center">
          {/* Contact Info */}
          <div className="flex items-center space-x-6">
            <a href="tel:+1234567890" className="flex items-center hover:text-gray-200">
              <Phone className="h-4 w-4 mr-2" />
              ****** 567 890
            </a>
            <a href="mailto:<EMAIL>" className="flex items-center hover:text-gray-200">
              <Mail className="h-4 w-4 mr-2" />
              <EMAIL>
            </a>
          </div>
          
          {/* Language and User Menu */}
          <div className="flex items-center space-x-4">
            {/* Language Selector */}
            <div className="relative" ref={languageMenuRef}>
              <button 
                onClick={() => setShowLanguageMenu(!showLanguageMenu)}
                className="flex items-center space-x-1 hover:bg-primary-dark px-2 py-1 rounded"
              >
                <GlobeIcon className="h-4 w-4" />
                <span>{currentLanguage.name}</span>
                <ChevronDown className={`h-4 w-4 transition-transform ${showLanguageMenu ? 'transform rotate-180' : ''}`} />
              </button>
              
              {showLanguageMenu && (
                <div className="absolute right-0 mt-2 w-40 bg-white rounded-md shadow-lg py-1 z-50">
                  {LANGUAGES.map((lang) => (
                    <button
                      key={lang.code}
                      onClick={() => handleLanguageChange(lang)}
                      className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-100 flex items-center space-x-2 ${
                        language === lang.code ? 'bg-gray-100 font-medium' : ''
                      }`}
                    >
                      <span>{lang.flag}</span>
                      <span>{lang.name}</span>
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Theme Toggle */}
            <button
              onClick={toggleTheme}
              className="p-1 rounded-full hover:bg-primary-dark"
              aria-label="Toggle theme"
            >
              {theme === 'dark' ? (
                <Sun className="h-4 w-4" />
              ) : (
                <Moon className="h-4 w-4" />
              )}
            </button>

            {/* User Menu */}
            <div className="relative" ref={userMenuRef}>
              <button
                onClick={() => setShowUserMenu(!showUserMenu)}
                className="flex items-center space-x-1 hover:bg-primary-dark px-2 py-1 rounded"
              >
                <User className="h-4 w-4" />
                <span>{isLoggedIn ? user?.name || 'Account' : 'Sign In'}</span>
                <ChevronDown className={`h-4 w-4 transition-transform ${showUserMenu ? 'transform rotate-180' : ''}`} />
              </button>
              
              {showUserMenu && (
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                  {isLoggedIn ? (
                    <>
                      <Link href="/account" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        My Account
                      </Link>
                      <Link href="/orders" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        My Orders
                      </Link>
                      <button
                        onClick={handleLogout}
                        className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Logout
                      </button>
                    </>
                  ) : (
                    <>
                      <Link href="/login" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        Login
                      </Link>
                      <Link href="/register" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        Register
                      </Link>
                    </>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Main Navigation */}
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className="text-xl font-bold">
            Your Logo
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {NAV_ITEMS.map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className={cn(
                  'text-sm font-medium transition-colors hover:text-primary',
                  pathname === item.href ? 'text-primary' : 'text-foreground/60'
                )}
              >
                {item.name}
              </Link>
            ))}
          </nav>

          {/* Search, Cart, and Mobile Menu Button */}
          <div className="flex items-center space-x-4">
            {/* Desktop Search */}
            <div className="hidden md:block">
              <form onSubmit={handleSearch} className="relative">
                <Input
                  type="text"
                  placeholder="Search..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-64"
                />
                <button
                  type="submit"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2"
                >
                  <SearchIcon className="h-4 w-4 text-muted-foreground" />
                </button>
              </form>
            </div>

            {/* Wishlist */}
            <Link href="/wishlist" className="p-2 text-gray-700 hover:text-primary relative">
              <Heart className="h-6 w-6" />
              {wishlistCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-primary text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  {wishlistCount}
                </span>
              )}
            </Link>

            {/* Cart */}
            <Link href="/cart" className="p-2 text-gray-700 hover:text-primary relative">
              <ShoppingCart className="h-6 w-6" />
              {cartCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-primary text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  {cartCount}
                </span>
              )}
            </Link>

            {/* Mobile Menu Button */}
            <button
              className="md:hidden p-2 text-gray-700 hover:text-primary"
              onClick={toggleMobileMenu}
              aria-label="Toggle menu"
            >
              {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Search */}
        <div className="md:hidden mt-4 mb-2">
          <form onSubmit={handleSearch} className="relative">
            <Input
              type="text"
              placeholder="Search..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full"
            />
            <button
              type="submit"
              className="absolute right-2 top-1/2 transform -translate-y-1/2"
            >
              <SearchIcon className="h-4 w-4 text-muted-foreground" />
            </button>
          </form>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isMobileMenuOpen && (
        <div className="md:hidden bg-white border-t">
          <nav className="px-2 pt-2 pb-4 space-y-1">
            {MOBILE_NAV_ITEMS.map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className={cn(
                  'block px-3 py-2 rounded-md text-base font-medium',
                  pathname === item.href
                    ? 'bg-gray-100 text-primary'
                    : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                )}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                {item.name}
              </Link>
            ))}
          </nav>
        </div>
      )}
    </header>
  );
}
      isScrolled ? 'py-0' : 'py-2',
      'dark:bg-gray-900'
    )}>
      {/* Top Bar */}
      <div className="bg-primary text-white text-sm">
        <div className="container mx-auto px-4 py-2 flex justify-between items-center">
          <div className="flex items-center space-x-6">
            <a href="tel:009647836071686" className="flex items-center hover:text-gray-200">
              <Phone className="h-4 w-4 mr-2" />
              +964 ************
            </a>
            <a href="mailto:<EMAIL>" className="flex items-center hover:text-gray-200">
              <Mail className="h-4 w-4 mr-2" />
              <EMAIL>
            </a>
          </div>
          
          <div className="flex items-center space-x-4">
            {/* Language Selector */}
            <div className="relative">
              <button 
                onClick={() => setShowLanguageMenu(!showLanguageMenu)}
                className="flex items-center hover:text-gray-200"
                aria-label="Select language"
              >
                <Globe className="h-4 w-4 mr-1" />
                <span>{currentLanguage.name}</span>
                <ChevronDown className="h-4 w-4 ml-1" />
              </button>
              
              {showLanguageMenu && (
                <div className="absolute right-0 mt-2 w-40 bg-white dark:bg-gray-800 rounded-md shadow-lg py-1 z-50 border border-gray-200 dark:border-gray-700">
                  {LANGUAGES.map((lang) => (
                    <button
                      key={lang.code}
                      onClick={() => {
                        setLanguage(lang.code);
                        setShowLanguageMenu(false);
                      }}
                      className={`w-full text-left px-4 py-2 text-sm flex items-center ${
                        language === lang.code 
                          ? 'bg-gray-100 dark:bg-gray-700 text-primary' 
                          : 'text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700'
                      }`}
                    >
                      <span className="mr-2">{lang.flag}</span>
                      {lang.name}
                    </button>
                  ))}
                </div>
              )}
            </div>
            
            {/* Theme Toggle */}
            <button 
              onClick={toggleTheme}
              className="p-1 rounded-full hover:bg-white/10 transition-colors"
              aria-label={theme === 'dark' ? 'Switch to light mode' : 'Switch to dark mode'}
            >
              {theme === 'dark' ? (
                <Sun className="h-4 w-4" />
              ) : (
                <Moon className="h-4 w-4" />
              )}
            </button>
          </div>
        </div>
      </div>
      
      {/* Main Header */}
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Mobile Menu Button */}
          <div className="flex items-center md:hidden">
            <button
              onClick={() => setShowMobileNav(!showMobileNav)}
              className="p-2 rounded-md text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800"
              aria-label="Toggle menu"
            >
              {showMobileNav ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </button>
          </div>

          {/* Logo */}
          <div className="flex-shrink-0">
            <Link href="/" className="flex items-center">
              <img 
                src={`${process.env.NEXT_PUBLIC_ADMIN_BASE_URL}content/commonImages/otherImages/18b_logo2x.png`} 
                alt="Logo" 
                className={cn(
                  'h-12 w-auto transition-all duration-300',
                  isScrolled ? 'h-10' : 'h-12'
                )} 
              />
            </Link>
          </div>
          </div>
          
          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {NAV_ITEMS.map((item) => (
              <Link 
                key={item.href} 
                href={item.href}
                className="text-gray-700 hover:text-primary font-medium"
              >
                <div className="flex items-center">
                  {item.icon}
                  <span>{item.label}</span>
                </div>
              </Link>
            ))}
          </nav>
          
          {/* Search Bar - Desktop */}
          <div className="hidden md:flex flex-1 max-w-md mx-8">
            <form onSubmit={handleSearch} className="w-full">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search products..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-4 pr-10 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
                <button
                  type="submit"
                  className="absolute right-2 top-1/2 -translate-y-1/2 p-1 text-gray-500 hover:text-primary"
                >
                  <Search className="h-5 w-5" />
                </button>
              </div>
            </form>
          </div>
          
          {/* User Actions */}
          <div className="flex items-center space-x-4">
            <Link href="/wishlist" className="p-2 text-gray-700 hover:text-primary relative">
              <Heart className="h-6 w-6" />
              {wishlistCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-primary text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  {wishlistCount}
                </span>
              )}
            </Link>
            
            <Link href="/cart" className="p-2 text-gray-700 hover:text-primary relative">
              <ShoppingCart className="h-6 w-6" />
              {cartCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-primary text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  {cartCount}
                </span>
              )}
            </Link>
            
            <div className="relative">
              <button 
                className="p-2 text-gray-700 hover:text-primary"
                onClick={() => setShowMobileNav(!showMobileNav)}
              >
                {isLoggedIn ? (
                  <User className="h-6 w-6" />
                ) : (
                  <UserPlus className="h-6 w-6" />
                )}
              </button>
              
              {/* User Dropdown */}
              {showMobileNav && (
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                  {isLoggedIn ? (
                    <>
                      <div className="px-4 py-2 text-sm text-gray-700 border-b">
                        {user?.name || 'User'}
                      </div>
                      <Link 
                        href="/profile" 
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Profile
                      </Link>
                      <button
                        onClick={handleLogout}
                        className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Logout
                      </button>
                    </>
                  ) : (
                    <>
                      <Link 
                        href="/login" 
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Login
                      </Link>
                      <Link 
                        href="/register" 
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Register
                      </Link>
                    </>
                  )}
                </div>
              )}
            </div>
            
            {/* Mobile Menu Button */}
            <button 
              className="md:hidden p-2 text-gray-700 hover:text-primary"
              onClick={() => setShowMobileNav(!showMobileNav)}
            >
              {showMobileNav ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </button>
          </div>
        </div>
        
        {/* Mobile Search */}
        <div className="md:hidden mt-4">
          <form onSubmit={handleSearch}>
            <div className="relative">
              <input
                type="text"
                placeholder="Search products..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-4 pr-10 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              />
              <button
                type="submit"
                className="absolute right-2 top-1/2 -translate-y-1/2 p-1 text-gray-500 hover:text-primary"
              >
                <Search className="h-5 w-5" />
              </button>
            </div>
          </form>
        </div>
      </div>
      
      {/* Mobile Navigation */}
      {showMobileNav && (
        <div className="md:hidden bg-white border-t">
          <nav className="px-4 py-2">
            {MOBILE_NAV_ITEMS.map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className="block px-4 py-3 text-gray-700 hover:bg-gray-50 rounded-md"
                onClick={() => setShowMobileNav(false)}
              >
                <div className="flex items-center">
                  {item.icon}
                  <span>{item.label}</span>
                </div>
              </Link>
            ))}
          </nav>
        </div>
      )}
    </header>
  );
}

  // State
  const [showMobileNav, setShowMobileNav] = useState(false);
  const [showLanguageMenu, setShowLanguageMenu] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [selectedSubcategory, setSelectedSubcategory] = useState<string>('');
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [primaryColor, setPrimaryColor] = useState('#1B3764');
  
  // Refs
  const languageMenuRef = useRef<HTMLDivElement>(null);
  const userMenuRef = useRef<HTMLDivElement>(null);

  // Close menus when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (languageMenuRef.current && !languageMenuRef.current.contains(event.target as Node)) {
        setShowLanguageMenu(false);
      }
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setShowUserMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        // This would be replaced with your actual API call
        // const response = await fetch('/api/categories');
        // const data = await response.json();
        // setCategories(data);
        setIsLoading(false);
      } catch (error) {
        console.error('Failed to fetch categories:', error);
        setIsLoading(false);
      }
    };

    fetchCategories();
  }, []);

  // Handle search
  const handleSearch = () => {
    if (searchTerm.trim()) {
      router.push(`/search?q=${encodeURIComponent(searchTerm)}`);
    }
  };

  // Toggle mobile menu
  const toggleMobileMenu = () => {
    setShowMobileNav(!showMobileNav);
  };

  // Handle language change
  const handleLanguageChange = (lang: Language) => {
    setLanguage(lang.code);
    setShowLanguageMenu(false);
  };

  // Toggle theme
  const toggleTheme = () => {
    // This would be implemented in your theme context
    // setTheme(theme === 'light' ? 'dark' : 'light');
  };

  // Handle logout
  const handleLogout = async () => {
    try {
      await logout();
      toast.success('Logged out successfully');
      router.push('/login');
    } catch (error) {
      toast.error('Failed to log out');
    }
  };

  // Handle search key press
  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  // Get current language
  const currentLanguage = LANGUAGES.find(lang => lang.code === language) || LANGUAGES[0];

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      {/* Top Bar */}
      <div className="border-b">
        <div className="container flex h-14 items-center justify-between px-4">
          <div className="flex items-center space-x-4">
            {/* Language Selector */}
            <div className="relative">
              <select
                value={language}
                onChange={(e) => handleLanguageChange(e.target.value as Language)}
                className="bg-transparent text-sm focus:outline-none"
              >
                {languages.map((lang) => (
                  <option key={lang.code} value={lang.code}>
                    {lang.flag} {lang.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Theme Toggle */}
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleTheme}
              className="h-8 w-8"
              aria-label="Toggle theme"
            >
              {theme === 'dark' ? (
                <Sun className="h-4 w-4" />
              ) : (
                <Moon className="h-4 w-4" />
              )}
            </Button>
          </div>

          <div className="flex items-center space-x-4">
            {userContext?.isLoggedIn ? (
              <div className="flex items-center space-x-2">
                <span className="text-sm">Welcome, {userContext.user?.name || 'User'}</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleLogout}
                  className="h-8 text-xs"
                >
                  Logout
                </Button>
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <Link href="/login">
                  <Button variant="ghost" size="sm" className="h-8 text-xs">
                    Login
                  </Button>
                </Link>
                <span className="text-muted-foreground">|</span>
                <Link href="/register">
                  <Button variant="outline" size="sm" className="h-8 text-xs">
                    Register
                  </Button>
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Main Header */}
      <div className="container flex h-16 items-center justify-between px-4">
        {/* Mobile Menu Button */}
        <Button
          variant="ghost"
          size="icon"
          className="md:hidden"
          onClick={toggleMobileMenu}
          aria-label="Toggle menu"
        >
          {isMobileMenuOpen ? (
            <X className="h-6 w-6" />
          ) : (
            <Menu className="h-6 w-6" />
          )}
        </Button>

        {/* Logo */}
        <Link href="/" className="flex items-center space-x-2">
          <span className="text-xl font-bold">
            {language === 'ar' ? 'المتجر الطبي' : 'Medical Store'}
          </span>
        </Link>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center space-x-6">
          {navItems.slice(0, 5).map((item) => (
            <Link
              key={item.path}
              href={item.path}
              className="text-sm font-medium transition-colors hover:text-primary"
            >
              {item.name}
            </Link>
          ))}
        </nav>

        <div className="flex items-center space-x-4">
          {/* Search Bar */}
          <div className="relative hidden md:block">
            <input
              type="text"
              placeholder="Search products..."
              className="h-9 w-64 rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyPress={handleKeyPress}
            />
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-0 top-0 h-9 w-9"
              onClick={handleSearch}
              aria-label="Search"
            >
              <Search className="h-4 w-4" />
            </Button>
          </div>

          {/* Wishlist */}
          <Button variant="ghost" size="icon" asChild>
            <Link href="/wishlist" className="relative">
              <Heart className="h-5 w-5" />
              {wishlistCount > 0 && (
                <span className="absolute -right-1 -top-1 flex h-4 w-4 items-center justify-center rounded-full bg-primary text-[10px] text-primary-foreground">
                  {wishlistCount > 9 ? '9+' : wishlistCount}
                </span>
              )}
            </Link>
          </Button>

          {/* Cart */}
          <Button variant="ghost" size="icon" asChild>
            <Link href="/cart" className="relative">
              <ShoppingCart className="h-5 w-5" />
              {cartCount > 0 && (
                <span className="absolute -right-1 -top-1 flex h-4 w-4 items-center justify-center rounded-full bg-primary text-[10px] text-primary-foreground">
                  {cartCount > 9 ? '9+' : cartCount}
                </span>
              )}
            </Link>
          </Button>

          {/* Language Selector (Mobile) */}
          <div className="md:hidden">
            <select
              value={language}
              onChange={(e) => handleLanguageChange(e.target.value as Language)}
              className="bg-transparent text-sm focus:outline-none"
            >
              {languages.map((lang) => (
                <option key={lang.code} value={lang.code}>
                  {lang.flag}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <div className="fixed inset-0 z-50 bg-background/95 backdrop-blur-sm md:hidden">
          <div className="container flex h-full flex-col overflow-y-auto">
            <div className="flex h-16 items-center justify-between">
              <Link href="/" className="text-xl font-bold">
                {language === 'ar' ? 'المتجر الطبي' : 'Medical Store'}
              </Link>
              <Button
                variant="ghost"
                size="icon"
                onClick={toggleMobileMenu}
                aria-label="Close menu"
              >
                <X className="h-6 w-6" />
              </Button>
            </div>

            <div className="mt-4 space-y-2">
              {navItems.map((item) => (
                <Link
                  key={item.path}
                  href={item.path}
                  className="flex items-center rounded-md px-4 py-3 text-sm font-medium hover:bg-accent"
                  onClick={toggleMobileMenu}
                >
                  {item.icon}
                  <span>{item.name}</span>
                </Link>
              ))}
            </div>

            <div className="mt-auto space-y-4 border-t p-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Language</span>
                <select
                  value={language}
                  onChange={(e) => handleLanguageChange(e.target.value as Language)}
                  className="rounded-md border p-2 text-sm"
                >
                  {languages.map((lang) => (
                    <option key={lang.code} value={lang.code}>
                      {lang.flag} {lang.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Theme</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={toggleTheme}
                  className="h-8"
                >
                  {theme === 'dark' ? 'Light Mode' : 'Dark Mode'}
                </Button>
              </div>

              {userContext?.isLoggedIn ? (
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={handleLogout}
                >
                  Logout
                </Button>
              ) : (
                <div className="grid grid-cols-2 gap-2">
                  <Button variant="outline" asChild>
                    <Link href="/login">Login</Link>
                  </Button>
                  <Button asChild>
                    <Link href="/register">Register</Link>
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </header>
  );
}
      {/* Desktop Navigation Bar */}
      <div className="hidden md:block text-white py-2.5" style={{ backgroundColor: primaryColor }}>
        <div className="container mx-auto flex flex-col md:flex-row justify-between items-center text-sm px-4">
          <div className="flex md:flex-row items-start justify-start gap-4 md:gap-8">
            <Link href="tel:009647836071686" className="flex items-center gap-2 hover:text-white/80">
              <Phone className="h-4 w-4" />
              <span className="text-xs md:text-sm">{t('phone')}</span>
            </Link>
            <Link href="mailto:<EMAIL>" className="flex items-center gap-2 hover:text-white/80">
              <Mail className="h-4 w-4" />
              <span className="text-xs md:text-sm">{t('email')}</span>
            </Link>
          </div>
          <div className="flex items-center gap-2 md:gap-4">
            <div className="relative group">
              <Button variant="ghost" size="sm" className="text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2">
                <Globe className="h-4 w-4" />
                <span className="text-sm">{languages.find(l => l.code === language)?.name || 'Language'}</span>
                <ChevronDown className="h-3 w-3 opacity-70" />
              </Button>
              <div className="absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden group-hover:block">
                {languages.map((lang) => (
                  <button
                    key={lang.code}
                    onClick={() => setLanguage(lang.code)}
                    className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                  >
                    <span className="text-lg mr-2">{lang.flag}</span>
                    <span>{lang.name}</span>
                  </button>
                ))}
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="text-white hover:text-white/80 flex items-center gap-2"
              onClick={() => window.open(`https://wa.me/9647836071686?text=${encodeURIComponent('Hello! I would like to chat with you regarding your services.')}`, '_blank')}
            >
              <MessageCircle className="h-4 w-4" />
              <span className="text-sm">{t('liveChat')}</span>
            </Button>
            {isLoggedIn ? (
              <div className="flex items-center gap-2">
                <span className="text-white text-sm">
                  {t('welcome')} {user?.FirstName || user?.UserName || user?.Email}
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2"
                  onClick={logout}
                >
                  <User className="h-4 w-4" />
                  <span className="text-sm">{t('logout') || 'Logout'}</span>
                </Button>
              </div>
            ) : (
              <>
                <Link href="/login">
                  <Button variant="ghost" size="sm" className="text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2">
                    <User className="h-4 w-4" />
                    <span className="text-sm">{t('login')}</span>
                  </Button>
                </Link>
                <Link href="/signup">
                  <Button variant="ghost" size="sm" className="text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2">
                    <UserPlus className="h-4 w-4" />
                    <span className="text-sm">{t('signUp')}</span>
                  </Button>
                </Link>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Main Header */}
      <div className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-4">
          {/* Mobile Layout */}
          <div className="md:hidden">
            <div className="flex items-center justify-between mb-4">
              {/* Mobile Menu Button */}
              <Button 
                variant="ghost" 
                size="icon" 
                className="md:hidden mr-2"
                onClick={toggleMobileNav}
              >
                <Menu className="h-6 w-6" />
              </Button>
              
              {/* Logo */}
              <Link href="/" className="flex-1 flex justify-center">
                <div className="text-[#1B3764] flex items-center justify-center">
                  <img 
                    src={`${process.env.NEXT_PUBLIC_ADMIN_BASE_URL}content/commonImages/otherImages/18b_logo2x.png`} 
                    alt="Logo" 
                    className="h-12 w-auto" 
                  />
                </div>
              </Link>
              
              {/* Language Selector - Mobile */}
              <Button
                variant="ghost"
                size="sm"
                className="flex items-center gap-2 px-3 py-2"
                onClick={() => setLanguage(language === 'en' ? 'ar' : 'en')}
              >
                <span className="text-lg">
                  {language === 'en' ? '🇺🇸' : '🇮🇶'}
                </span>
                <span className="text-sm font-medium">
                  {language === 'en' ? 'EN' : 'AR'}
                </span>
              </Button>
            </div>
            
            {/* Search - Mobile */}
            <div className="w-full mb-4">
              <div className="flex items-center gap-2 border rounded-full px-4 py-2 bg-background shadow-sm">
                <input
                  type="text"
                  placeholder={t('products') || 'Search products...'}
                  className="bg-transparent border-none focus:outline-none text-sm flex-1 placeholder:text-gray-400"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                />
                <Button
                  variant="ghost"
                  className="h-8 w-8 p-0 hover:bg-accent/80 transition-colors"
                  style={{ color: primaryColor }}
                  onClick={handleSearch}
                >
                  <Search className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
          
          {/* Desktop Layout */}
          <div className="hidden md:flex items-center justify-between">
            <div className="flex items-center">
              {/* Logo */}
              <Link href="/" className="flex items-center gap-2">
                <div className="text-[#1B3764] flex items-center gap-2">
                  <img 
                    src={`${process.env.NEXT_PUBLIC_ADMIN_BASE_URL}content/commonImages/otherImages/18b_logo2x.png`} 
                    alt="Logo" 
                    className="h-16 w-auto" 
                  />
                </div>
              </Link>
              
              {/* Desktop Navigation */}
              <div className="hidden md:flex items-center space-x-1 ml-8">
                {navItems.slice(0, 5).map((item) => (
                  <Link
                    key={item.path}
                    href={item.path}
                    className="px-3 py-2 text-sm font-medium text-gray-700 hover:text-primary transition-colors rounded-md"
                  >
                    {item.name}
                  </Link>
                ))}
              </div>
            </div>
            
            {/* Search - Desktop */}
            <div className="flex-1 max-w-2xl mx-8">
              <div className="flex items-center gap-2 border rounded-full px-3 py-1.5 bg-background shadow-sm">
                <input
                  type="text"
                  placeholder={t('products') || 'Search products...'}
                  className="bg-transparent border-none focus:outline-none text-sm flex-1 pl-2"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                />
                <Button
                  variant="ghost"
                  className="h-8 w-8 p-0 hover:bg-accent/80 transition-colors"
                  style={{ color: primaryColor }}
                  onClick={handleSearch}
                >
                  <Search className="h-4 w-4" />
                </Button>
              </div>
            </div>
            
            {/* Icons - Desktop */}
            <div className="flex items-center space-x-4">
              <Link href="/wishlist">
                <Button variant="ghost" size="icon" className="relative">
                  <Heart className="h-5 w-5" style={{ color: primaryColor }} />
                  {wishlistCount > 0 && (
                    <span className="absolute -top-1 -right-1 bg-primary text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                      {wishlistCount}
                    </span>
                  )}
                </Button>
              </Link>
              <Link href="/cart">
                <Button variant="ghost" size="icon" className="relative">
                  <ShoppingCart className="h-5 w-5" style={{ color: primaryColor }} />
                  {cartCount > 0 && (
            </Link>
          ))}
        </div>
      </div>
      
      {/* Search - Desktop */}
      <div className="flex-1 max-w-2xl mx-8">
        <div className="flex items-center gap-2 border rounded-full px-3 py-1.5 bg-background shadow-sm">
          <input
            type="text"
            placeholder={t('products') || 'Search products...'}
            className="bg-transparent border-none focus:outline-none text-sm flex-1 pl-2"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
          />
          <Button
            variant="ghost"
            className="h-8 w-8 p-0 hover:bg-accent/80 transition-colors"
            style={{ color: primaryColor }}
            onClick={handleSearch}
          >
            <Search className="h-4 w-4" />
          </Button>
        </div>
      </div>
      
      {/* Icons - Desktop */}
      <div className="flex items-center space-x-4">
        <Link href="/wishlist">
          <Button variant="ghost" size="icon" className="relative">
            <Heart className="h-5 w-5" style={{ color: primaryColor }} />
            {wishlistCount > 0 && (
              <span className="absolute -top-1 -right-1 bg-primary text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                {wishlistCount}
              </span>
            )}
          </Button>
        </Link>
        <Link href="/cart">
          <Button variant="ghost" size="icon" className="relative">
            <ShoppingCart className="h-5 w-5" style={{ color: primaryColor }} />
            {cartCount > 0 && (
              <span className="absolute -top-1 -right-1 bg-primary text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                {cartCount}
              </span>
            )}
          </Button>
        </Link>
      </div>
    </div>
    <div className="hidden md:flex items-center justify-between">
      {/* Logo and Search */}
      <div className="flex items-center gap-4 flex-1">
        {/* Logo */}
        <Link href="/" className="flex items-center gap-2">
          <div className="text-[#1B3764] flex items-center gap-2">
            <img src={`${process.env.NEXT_PUBLIC_ADMIN_BASE_URL}content/commonImages/otherImages/18b_logo2x.png`} alt="Logo" className="h-16 w-auto" />
          </div>
        </Link>

        {/* Search and Category */}
        <div className="flex items-center gap-2 border rounded-full px-3 py-1.5 flex-1 max-w-md ml-4">
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="ghost" className="h-8 flex items-center gap-1 px-2">
                <span className="text-muted-foreground text-sm">{selectedCategory || selectedSubcategory || t('category')}</span>
                <ChevronDown className="h-4 w-4 text-muted-foreground" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-64 p-0" align="start">
              <div className="max-h-[300px] overflow-auto">
                {isLoading ? (
                  <div className="p-4 text-center text-muted-foreground">{t('loadingCategories')}</div>
                ) : (
                  <div className="grid">
                    {categories.map((category) => (
                      <div key={category.id} className="group">
                        <button className="w-full px-4 py-2 text-left hover:bg-accent" onClick={() => {
                          setSelectedCategory(category.name);
                          setSelectedCategoryId(category.id);
                          setSelectedSubcategory(null);
                        }}>
                          {category.name}
                        </button>
                        <div className="hidden group-hover:block absolute left-full top-0 w-48 bg-white shadow-lg rounded-md border">
                          {category.subcategories.map((sub, index) => (
                            <button
                              key={index}
                              className="w-full px-4 py-2 text-left hover:bg-accent"
                              onClick={() => {
                                setSelectedSubcategory(sub);
                                setSelectedCategory(null);
                                // Keep the parent category ID for search purposes
                                setSelectedCategoryId(category.id);
                              }}
                            >
                              {sub}
                        {categories.map((category) => (
                          <div key={category.id} className="group">
                            <button className="w-full px-4 py-2 text-left hover:bg-accent" onClick={() => {
                              setSelectedCategory(category.name);
                              setSelectedCategoryId(category.id);
                              setSelectedSubcategory(null);
                            }}>
                              {category.name}
                            </button>
                            <div className="hidden group-hover:block absolute left-full top-0 w-48 bg-white shadow-lg rounded-md border">
                              {category.subcategories.map((sub, index) => (
                                <button
                                  key={index}
                                  className="w-full px-4 py-2 text-left hover:bg-accent"
                                  onClick={() => {
                                    setSelectedSubcategory(sub);
                                    setSelectedCategory(null);
                                    // Keep the parent category ID for search purposes
                                    setSelectedCategoryId(category.id);
                                  }}
                                >
                                  {sub}
                                </button>
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </PopoverContent>
              </Popover>
              <div className="h-5 w-px bg-border mx-2" />
              <input
                type="text"
                placeholder={t('products')}
                className="bg-transparent border-none focus:outline-none text-sm flex-1"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
              />
              <Button variant="ghost" className="h-8 w-8 p-0" onClick={handleSearch}>
                <Search className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Navigation - Desktop */}
          <div className="hidden md:block">
            <NavigationMenu>
              <NavigationMenuList>
                <NavigationMenuItem>
                  <Link href="/" legacyBehavior passHref>
                    <NavigationMenuLink className={cn(
                      "group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"
                    )}>
                      {t('home')}
                    </NavigationMenuLink>
                  </Link>
                </NavigationMenuItem>
                <NavigationMenuItem>
                  <Link href="/hot-deals" legacyBehavior passHref>
                    <NavigationMenuLink className={cn(
                      "group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"
                    )}>
                      {t('hotDeals')}
                    </NavigationMenuLink>
                  </Link>
                </NavigationMenuItem>
                <NavigationMenuItem>
                  <Link href="/products" legacyBehavior passHref>
                    <NavigationMenuLink className={cn(
                      "group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"
                    )}>
                      {t('products') || 'Products'}
                    </NavigationMenuLink>
                  </Link>
                </NavigationMenuItem>
                <NavigationMenuItem>
                  <Link href="/payment-methods" legacyBehavior passHref>
                    <NavigationMenuLink className={cn(
                      "group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"
                    )}>
                      {t('paymentMethods')}
                    </NavigationMenuLink>
                  </Link>
                </NavigationMenuItem>
                <NavigationMenuItem>
                  <Link href="/follow-us" legacyBehavior passHref>
                    <NavigationMenuLink className={cn(
                      "group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"
                    )}>
                      {t('followUs')}
                    </NavigationMenuLink>
                  </Link>
                </NavigationMenuItem>
                <NavigationMenuItem>
                  <Link href="/about" legacyBehavior passHref>
                    <NavigationMenuLink className={cn(
                      "group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"
                    )}>
                      {t('aboutUs')}
                    </NavigationMenuLink>
                  </Link>
                </NavigationMenuItem>
                <NavigationMenuItem>
                  <Link href="/contact" legacyBehavior passHref>
                    <NavigationMenuLink className={cn(
                      "group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"
                    )}>
                      {t('contactUs')}
                    </NavigationMenuLink>
                  </Link>
                </NavigationMenuItem>
              </NavigationMenuList>
            </NavigationMenu>
          </div>

          {/* Cart and Wishlist Icons - Desktop Only */}
          <div className="hidden md:flex items-center gap-4">
            <Link href="/wishlist">
              <Button variant="ghost" size="icon" className="relative">
                <Heart className="h-5 w-5" style={{ color: primaryColor }} />
                <span className="absolute -top-1 -right-1 bg-primary text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                  {wishlistCount}
                </span>
              </Button>
            </Link>
            <Link href="/cart">
              <Button variant="ghost" size="icon" className="relative">
                <ShoppingCart className="h-5 w-5" style={{ color: primaryColor }} />
                <span className="absolute -top-1 -right-1 bg-primary text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                  {cartCount}
                </span>
              </Button>
            </Link>
          </div>
        </div>
      </div>
      {showColorPicker && (
        <ColorPicker
          onColorSelect={(color) => {
            setPrimaryColor(color);
            setShowColorPicker(false);
          }}
          onClose={() => setShowColorPicker(false)}
        />
      )}
    </header>
  );
}