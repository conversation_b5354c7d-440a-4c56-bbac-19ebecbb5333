'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { 
  NavigationMenu, 
  NavigationMenuContent, 
  NavigationMenuItem, 
  NavigationMenuLink, 
  NavigationMenuList, 
  NavigationMenuTrigger 
} from '@/components/ui/navigation-menu';
import { toast } from 'sonner';
import { 
  Menu, 
  ShoppingCart, 
  Heart, 
  User, 
  Search as SearchIcon, 
  X, 
  Moon, 
  Sun, 
  ChevronDown, 
  ChevronUp, 
  ChevronRight, 
  ChevronLeft, 
  LogOut, 
  MessageCircle, 
  Globe as GlobeIcon,
  LogIn,
  UserPlus,
  Home as HomeIcon,
  BookOpen,
  Phone,
  Mail,
  Grid
} from 'lucide-react';

// Types
type Language = {
  code: string;
  name: string;
  flag: string;
  rtl?: boolean;
};

type Category = {
  id: string;
  name: string;
  slug: string;
  subcategories?: Array<{
    id: string;
    name: string;
    slug: string;
  }>;
};

type NavItem = {
  name: string;
  href: string;
  icon?: string;
};

type SettingsContextType = {
  theme: string;
  language: string;
  setLanguage: (lang: string) => void;
  t: (key: string) => string;
};

type CartContextType = {
  items: any[];
};

type WishlistContextType = {
  items: Array<{ id: string | number }>;
};

type UserContextType = {
  user: { name?: string; email?: string } | null;
  isLoggedIn: boolean;
  logout: () => Promise<void>;
};

// Context hooks - These should be imported from their respective context files
const useSettings = (): SettingsContextType => ({
  theme: 'light',
  language: 'en',
  setLanguage: () => {},
  t: (key: string) => key,
});

const useCart = (): CartContextType => ({
  items: [],
});

const useWishlist = (): WishlistContextType => ({
  items: [],
});

const useUser = (): UserContextType => ({
  user: null,
  isLoggedIn: false,
  logout: async () => {},
});

// Constants
const LANGUAGES: Language[] = [
  { code: 'en', name: 'English', flag: '🇬🇧' },
  { code: 'ar', name: 'العربية', flag: '🇸🇦', rtl: true },
];

const NAV_ITEMS: NavItem[] = [
  { name: 'Home', href: '/', icon: 'home' },
  { name: 'Shop', href: '/products', icon: 'shopping-bag' },
  { name: 'Categories', href: '/categories', icon: 'grid' },
  { name: 'About', href: '/about', icon: 'info' },
  { name: 'Contact', href: '/contact', icon: 'mail' },
];

const MOBILE_NAV_ITEMS: NavItem[] = [
  { name: 'Home', href: '/', icon: 'home' },
  { name: 'Categories', href: '/categories', icon: 'grid' },
  { name: 'Cart', href: '/cart', icon: 'shopping-cart' },
  { name: 'Account', href: '/account', icon: 'user' },
];

export default function Header() {
  const router = useRouter();
  const pathname = usePathname();
  const { theme, language, setLanguage, t } = useSettings();
  const { items: cartItems = [] } = useCart();
  const { items: wishlistItems = [] } = useWishlist();
  const { user, isLoggedIn, logout } = useUser();

  // State
  const [showMobileNav, setShowMobileNav] = useState(false);
  const [showLanguageMenu, setShowLanguageMenu] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [isScrolled, setIsScrolled] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [selectedSubcategory, setSelectedSubcategory] = useState<string>('');
  const [primaryColor, setPrimaryColor] = useState('#1B3764');
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [selectedCategoryId, setSelectedCategoryId] = useState<string | null>(null);
  const [selectedSubcategoryId, setSelectedSubcategoryId] = useState<string | null>(null);

  // Refs
  const languageMenuRef = useRef<HTMLDivElement>(null);
  const userMenuRef = useRef<HTMLDivElement>(null);
  const mobileMenuRef = useRef<HTMLDivElement>(null);

  // Calculate cart and wishlist counts
  const cartCount = cartItems?.length || 0;
  const wishlistCount = wishlistItems?.length || 0;
  const currentLanguage = LANGUAGES.find(lang => lang.code === language) || LANGUAGES[0];

  // Handle search form submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchTerm.trim()) {
      router.push(`/search?q=${encodeURIComponent(searchTerm)}`);
    }
  };

  // Handle user logout
  const handleLogout = async () => {
    try {
      await logout();
      toast.success('Logged out successfully');
      setShowUserMenu(false);
      router.push('/login');
    } catch (error) {
      toast.error('Failed to log out');
      console.error('Logout error:', error);
    }
  };

  // Toggle mobile navigation
  const toggleMobileNav = () => {
    setShowMobileNav(!showMobileNav);
  };

  // Handle language change
  const handleLanguageChange = (lang: Language) => {
    setLanguage(lang.code);
    setShowLanguageMenu(false);
  };

  // Toggle theme
  const toggleTheme = () => {
    // Implement theme toggling logic here
    document.documentElement.classList.toggle('dark');
  };

  // Close all dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (languageMenuRef.current && !languageMenuRef.current.contains(event.target as Node)) {
        setShowLanguageMenu(false);
      }
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setShowUserMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setIsLoading(true);
        // Replace with actual API call to fetch categories
        // const response = await fetch('/api/categories');
        // const data = await response.json();
        // setCategories(data);
        
        // Mock data for now
        setCategories([
          {
            id: '1',
            name: 'Category 1',
            slug: 'category-1',
            subcategories: [
              { id: '1-1', name: 'Subcategory 1', slug: 'subcategory-1' },
              { id: '1-2', name: 'Subcategory 2', slug: 'subcategory-2' },
            ],
          },
        ]);
      } catch (error) {
        console.error('Error fetching categories:', error);
        toast.error('Failed to load categories');
      } finally {
        setIsLoading(false);
      }
    };

    fetchCategories();
  }, []);

  return (
    <header className={cn(
      'sticky top-0 z-50 w-full bg-white shadow-sm transition-all duration-300',
      isScrolled ? 'py-0' : 'py-2',
      'dark:bg-gray-900'
    )}>
      {/* Top Bar */}
      <div className="bg-primary text-white text-sm">
        <div className="container mx-auto px-4 py-2 flex justify-between items-center">
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-2">
              <Phone className="h-4 w-4" />
              <span>****** 567 890</span>
            </div>
            <div className="flex items-center space-x-2">
              <Mail className="h-4 w-4" />
              <span><EMAIL></span>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            {/* Language Selector */}
            <div className="relative" ref={languageMenuRef}>
              <button 
                onClick={() => setShowLanguageMenu(!showLanguageMenu)}
                className="flex items-center space-x-1 hover:bg-primary-dark px-2 py-1 rounded"
              >
                <GlobeIcon className="h-4 w-4" />
                <span>{currentLanguage.name}</span>
                <ChevronDown className={`h-4 w-4 transition-transform ${showLanguageMenu ? 'transform rotate-180' : ''}`} />
              </button>
              
              {showLanguageMenu && (
                <div className="absolute right-0 mt-2 w-40 bg-white rounded-md shadow-lg py-1 z-50">
                  {LANGUAGES.map((lang) => (
                    <button
                      key={lang.code}
                      onClick={() => handleLanguageChange(lang)}
                      className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-100 flex items-center space-x-2 ${
                        language === lang.code ? 'bg-gray-100 font-medium' : ''
                      }`}
                    >
                      <span>{lang.flag}</span>
                      <span>{lang.name}</span>
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Theme Toggle */}
            <button
              onClick={toggleTheme}
              className="p-1 rounded-full hover:bg-primary-dark"
              aria-label="Toggle theme"
            >
              {theme === 'dark' ? (
                <Sun className="h-4 w-4" />
              ) : (
                <Moon className="h-4 w-4" />
              )}
            </button>

            {/* User Menu */}
            <div className="relative" ref={userMenuRef}>
              <button
                onClick={() => setShowUserMenu(!showUserMenu)}
                className="flex items-center space-x-1 hover:bg-primary-dark px-2 py-1 rounded"
              >
                <User className="h-4 w-4" />
                <span>{isLoggedIn ? user?.name || 'Account' : 'Sign In'}</span>
                <ChevronDown className={`h-4 w-4 transition-transform ${showUserMenu ? 'transform rotate-180' : ''}`} />
              </button>
              
              {showUserMenu && (
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                  {isLoggedIn ? (
                    <>
                      <Link href="/account" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        My Account
                      </Link>
                      <Link href="/orders" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        My Orders
                      </Link>
                      <button
                        onClick={handleLogout}
                        className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Logout
                      </button>
                    </>
                  ) : (
                    <>
                      <Link href="/login" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        Login
                      </Link>
                      <Link href="/register" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        Register
                      </Link>
                    </>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Main Navigation */}
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className="text-xl font-bold">
            Your Logo
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {NAV_ITEMS.map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className={cn(
                  'text-sm font-medium transition-colors hover:text-primary',
                  pathname === item.href ? 'text-primary' : 'text-foreground/60'
                )}
              >
                {item.name}
              </Link>
            ))}
          </nav>

          {/* Search, Cart, and Mobile Menu Button */}
          <div className="flex items-center space-x-4">
            <div className="hidden md:block">
              <form onSubmit={handleSearch} className="relative">
                <Input
                  type="text"
                  placeholder="Search..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-64"
                />
                <button
                  type="submit"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2"
                >
                  <SearchIcon className="h-4 w-4 text-muted-foreground" />
                </button>
              </form>
            </div>

            <Link href="/wishlist" className="p-2 text-gray-700 hover:text-primary relative">
              <Heart className="h-6 w-6" />
              {wishlistCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-primary text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  {wishlistCount}
                </span>
              )}
            </Link>

            <Link href="/cart" className="p-2 text-gray-700 hover:text-primary relative">
              <ShoppingCart className="h-6 w-6" />
              {cartCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-primary text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  {cartCount}
                </span>
              )}
            </Link>

            <button
              className="md:hidden p-2 text-gray-700 hover:text-primary"
              onClick={toggleMobileNav}
              aria-label="Toggle menu"
            >
              {showMobileNav ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Search */}
        <div className="md:hidden mt-4">
          <form onSubmit={handleSearch} className="relative">
            <Input
              type="text"
              placeholder="Search..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full"
            />
            <button
              type="submit"
              className="absolute right-2 top-1/2 transform -translate-y-1/2"
            >
              <SearchIcon className="h-4 w-4 text-muted-foreground" />
            </button>
          </form>
        </div>
      </div>

      {/* Mobile Navigation */}
      {showMobileNav && (
        <div className="md:hidden bg-white border-t">
          <nav className="px-2 pt-2 pb-4 space-y-1">
            {MOBILE_NAV_ITEMS.map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className={cn(
                  'block px-3 py-2 rounded-md text-base font-medium',
                  pathname === item.href
                    ? 'bg-gray-100 text-primary'
                    : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                )}
                onClick={() => setShowMobileNav(false)}
              >
                {item.name}
              </Link>
            ))}
          </nav>
        </div>
      )}
    </header>
  );
}
