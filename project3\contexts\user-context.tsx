'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { MakeApiCallAsync, Config } from '@/lib/api-helper';

interface User {
  UserId: number;
  UserName: string;
  Email: string;
  FirstName: string;
  LastName: string;
  PhoneNumber: string;
  ResponseMsg: string;
  [key: string]: any;
}

interface UserContextType {
  user: User | null;
  isLoggedIn: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<{ success: boolean; message: string }>;
  logout: () => void;
  token: string | null;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

interface UserProviderProps {
  children: ReactNode;
}

export function UserProvider({ children }: UserProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize user from localStorage on mount
  useEffect(() => {
    const initializeUser = () => {
      try {
        const storedUser = localStorage.getItem('user');
        const storedToken = localStorage.getItem('token');
        
        if (storedUser && storedUser !== '{}') {
          const parsedUser = JSON.parse(storedUser);
          setUser(parsedUser);
        }
        
        if (storedToken) {
          setToken(storedToken);
        }
      } catch (error) {
        console.error('Error initializing user from localStorage:', error);
        // Clear invalid data
        localStorage.removeItem('user');
        localStorage.removeItem('token');
      } finally {
        setIsLoading(false);
      }
    };

    initializeUser();
  }, []);

  const login = async (email: string, password: string): Promise<{ success: boolean; message: string }> => {
    try {
      setIsLoading(true);

      const headers = {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      };

      const param = {
        requestParameters: {
          Email: email,
          Password: password
        }
      };

      const response = await MakeApiCallAsync(Config.END_POINT_NAMES.GET_USER_LOGIN, null, param, headers, "POST", true);
      
      if (response && response.data && !response.data.errorMessage) {
        let userData;
        
        // Handle different response formats
        if (typeof response.data.data === 'string') {
          userData = JSON.parse(response.data.data);
        } else {
          userData = response.data.data;
        }

        // Check if userData is an array and has valid login response
        if (Array.isArray(userData) && userData.length > 0 && userData[0].ResponseMsg === "Login Successfully") {
          const userInfo = userData[0];
          
          // Save user data
          setUser(userInfo);
          localStorage.setItem('user', JSON.stringify(userInfo));
          
          // Save token if provided
          const authToken = response.data.token || '';
          setToken(authToken);
          localStorage.setItem('token', authToken);
          
          return { success: true, message: 'Login successful!' };
        } else {
          return { success: false, message: 'Incorrect email or password!' };
        }
      } else {
        return { 
          success: false, 
          message: response.data?.errorMessage || 'Login failed. Please try again.' 
        };
      }
    } catch (error) {
      console.error('Login error:', error);
      return { 
        success: false, 
        message: 'An error occurred. Please try again!' 
      };
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    setUser(null);
    setToken(null);
    localStorage.removeItem('user');
    localStorage.removeItem('token');
    localStorage.removeItem('userId');
  };

  const isLoggedIn = user !== null && user.UserId > 0;

  return (
    <UserContext.Provider
      value={{
        user,
        isLoggedIn,
        isLoading,
        login,
        logout,
        token
      }}
    >
      {children}
    </UserContext.Provider>
  );
}

export function useUser() {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
}
