'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

type WishlistItem = {
  id: string | number;
  name: string;
  price: number;
  image?: string;
  // Add other product properties as needed
};

type WishlistContextType = {
  items: WishlistItem[];
  addItem: (item: WishlistItem) => void;
  removeItem: (id: string | number) => void;
  isInWishlist: (id: string | number) => boolean;
  clearWishlist: () => void;
  itemCount: number;
};

const defaultWishlist: WishlistContextType = {
  items: [],
  addItem: () => {},
  removeItem: () => {},
  isInWishlist: () => false,
  clearWishlist: () => {},
  itemCount: 0,
};

const WishlistContext = createContext<WishlistContextType>(defaultWishlist);

export const useWishlist = () => useContext(WishlistContext);

type WishlistProviderProps = {
  children: ReactNode;
};

export function WishlistProvider({ children }: WishlistProviderProps) {
  const [items, setItems] = useState<WishlistItem[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);

  // Load wishlist from localStorage on mount
  useEffect(() => {
    const savedWishlist = localStorage.getItem('wishlist');
    if (savedWishlist) {
      try {
        const parsedWishlist = JSON.parse(savedWishlist);
        setItems(Array.isArray(parsedWishlist) ? parsedWishlist : []);
      } catch (error) {
        console.error('Failed to parse wishlist from localStorage', error);
        setItems([]);
      }
    }
    setIsInitialized(true);
  }, []);

  // Save wishlist to localStorage when it changes
  useEffect(() => {
    if (isInitialized) {
      localStorage.setItem('wishlist', JSON.stringify(items));
    }
  }, [items, isInitialized]);

  const itemCount = items.length;

  const addItem = (item: WishlistItem) => {
    setItems(prevItems => {
      if (prevItems.some(i => i.id === item.id)) {
        return prevItems; // Item already in wishlist
      }
      return [...prevItems, item];
    });
  };

  const removeItem = (id: string | number) => {
    setItems(prevItems => prevItems.filter(item => item.id !== id));
  };

  const isInWishlist = (id: string | number) => {
    return items.some(item => item.id === id);
  };

  const clearWishlist = () => {
    setItems([]);
  };

  return (
    <WishlistContext.Provider
      value={{
        items,
        addItem,
        removeItem,
        isInWishlist,
        clearWishlist,
        itemCount,
      }}
    >
      {children}
    </WishlistContext.Provider>
  );
}

export default WishlistContext;
