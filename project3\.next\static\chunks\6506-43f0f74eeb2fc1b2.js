"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6506],{591:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(7401).A)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},1014:(e,t,r)=>{r.d(t,{UC:()=>eD,YJ:()=>e_,In:()=>eP,q7:()=>eB,VF:()=>eV,p4:()=>eO,JU:()=>eH,ZL:()=>eI,bL:()=>eE,wn:()=>eG,PP:()=>eF,wv:()=>eK,l9:()=>eT,WT:()=>eM,LM:()=>eL});var n=r(2115),l=r(7650);function o(e,[t,r]){return Math.min(r,Math.max(t,e))}var a=r(3610),i=r(9741),s=r(8068),u=r(8166),c=r(4256),d=r(9674),p=r(2292),f=r(196),v=r(7668),m=r(905),h=r(7323),y=r(3360),w=r(2317),g=r(1524),x=r(1488),b=r(6611),S=r(858),C=r(3543),k=r(5587),N=r(2431),j=r(5155),R=[" ","Enter","ArrowUp","ArrowDown"],A=[" ","Enter"],E="Select",[T,M,P]=(0,i.N)(E),[I,D]=(0,u.A)(E,[P,m.Bk]),L=(0,m.Bk)(),[_,H]=I(E),[B,O]=I(E),V=e=>{let{__scopeSelect:t,children:r,open:l,defaultOpen:o,onOpenChange:a,value:i,defaultValue:s,onValueChange:u,dir:d,name:p,autoComplete:f,disabled:h,required:y,form:w}=e,g=L(t),[b,S]=n.useState(null),[C,k]=n.useState(null),[N,R]=n.useState(!1),A=(0,c.jH)(d),[E=!1,M]=(0,x.i)({prop:l,defaultProp:o,onChange:a}),[P,I]=(0,x.i)({prop:i,defaultProp:s,onChange:u}),D=n.useRef(null),H=!b||w||!!b.closest("form"),[O,V]=n.useState(new Set),F=Array.from(O).map(e=>e.props.value).join(";");return(0,j.jsx)(m.bL,{...g,children:(0,j.jsxs)(_,{required:y,scope:t,trigger:b,onTriggerChange:S,valueNode:C,onValueNodeChange:k,valueNodeHasChildren:N,onValueNodeHasChildrenChange:R,contentId:(0,v.B)(),value:P,onValueChange:I,open:E,onOpenChange:M,dir:A,triggerPointerDownPosRef:D,disabled:h,children:[(0,j.jsx)(T.Provider,{scope:t,children:(0,j.jsx)(B,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{V(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{V(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),H?(0,j.jsxs)(ej,{"aria-hidden":!0,required:y,tabIndex:-1,name:p,autoComplete:f,value:P,onChange:e=>I(e.target.value),disabled:h,form:w,children:[void 0===P?(0,j.jsx)("option",{value:""}):null,Array.from(O)]},F):null]})})};V.displayName=E;var F="SelectTrigger",G=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:l=!1,...o}=e,i=L(r),u=H(F,r),c=u.disabled||l,d=(0,s.s)(t,u.onTriggerChange),p=M(r),f=n.useRef("touch"),[v,h,w]=eR(e=>{let t=p().filter(e=>!e.disabled),r=t.find(e=>e.value===u.value),n=eA(t,e,r);void 0!==n&&u.onValueChange(n.value)}),g=e=>{c||(u.onOpenChange(!0),w()),e&&(u.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,j.jsx)(m.Mz,{asChild:!0,...i,children:(0,j.jsx)(y.sG.button,{type:"button",role:"combobox","aria-controls":u.contentId,"aria-expanded":u.open,"aria-required":u.required,"aria-autocomplete":"none",dir:u.dir,"data-state":u.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":eN(u.value)?"":void 0,...o,ref:d,onClick:(0,a.m)(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==f.current&&g(e)}),onPointerDown:(0,a.m)(o.onPointerDown,e=>{f.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(g(e),e.preventDefault())}),onKeyDown:(0,a.m)(o.onKeyDown,e=>{let t=""!==v.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||h(e.key),(!t||" "!==e.key)&&R.includes(e.key)&&(g(),e.preventDefault())})})})});G.displayName=F;var K="SelectValue",W=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:l,children:o,placeholder:a="",...i}=e,u=H(K,r),{onValueNodeHasChildrenChange:c}=u,d=void 0!==o,p=(0,s.s)(t,u.onValueNodeChange);return(0,b.N)(()=>{c(d)},[c,d]),(0,j.jsx)(y.sG.span,{...i,ref:p,style:{pointerEvents:"none"},children:eN(u.value)?(0,j.jsx)(j.Fragment,{children:a}):o})});W.displayName=K;var U=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...l}=e;return(0,j.jsx)(y.sG.span,{"aria-hidden":!0,...l,ref:t,children:n||"▼"})});U.displayName="SelectIcon";var z=e=>(0,j.jsx)(h.Z,{asChild:!0,...e});z.displayName="SelectPortal";var $="SelectContent",q=n.forwardRef((e,t)=>{let r=H($,e.__scopeSelect),[o,a]=n.useState();return((0,b.N)(()=>{a(new DocumentFragment)},[]),r.open)?(0,j.jsx)(Y,{...e,ref:t}):o?l.createPortal((0,j.jsx)(X,{scope:e.__scopeSelect,children:(0,j.jsx)(T.Slot,{scope:e.__scopeSelect,children:(0,j.jsx)("div",{children:e.children})})}),o):null});q.displayName=$;var[X,Z]=I($),Y=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:l="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:u,side:c,sideOffset:v,align:m,alignOffset:h,arrowPadding:y,collisionBoundary:g,collisionPadding:x,sticky:b,hideWhenDetached:S,avoidCollisions:C,...R}=e,A=H($,r),[E,T]=n.useState(null),[P,I]=n.useState(null),D=(0,s.s)(t,e=>T(e)),[L,_]=n.useState(null),[B,O]=n.useState(null),V=M(r),[F,G]=n.useState(!1),K=n.useRef(!1);n.useEffect(()=>{if(E)return(0,k.Eq)(E)},[E]),(0,p.Oh)();let W=n.useCallback(e=>{let[t,...r]=V().map(e=>e.ref.current),[n]=r.slice(-1),l=document.activeElement;for(let r of e)if(r===l||(null==r||r.scrollIntoView({block:"nearest"}),r===t&&P&&(P.scrollTop=0),r===n&&P&&(P.scrollTop=P.scrollHeight),null==r||r.focus(),document.activeElement!==l))return},[V,P]),U=n.useCallback(()=>W([L,E]),[W,L,E]);n.useEffect(()=>{F&&U()},[F,U]);let{onOpenChange:z,triggerPointerDownPosRef:q}=A;n.useEffect(()=>{if(E){let e={x:0,y:0},t=t=>{var r,n,l,o;e={x:Math.abs(Math.round(t.pageX)-(null!==(l=null===(r=q.current)||void 0===r?void 0:r.x)&&void 0!==l?l:0)),y:Math.abs(Math.round(t.pageY)-(null!==(o=null===(n=q.current)||void 0===n?void 0:n.y)&&void 0!==o?o:0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():E.contains(r.target)||z(!1),document.removeEventListener("pointermove",t),q.current=null};return null!==q.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[E,z,q]),n.useEffect(()=>{let e=()=>z(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[z]);let[Z,Y]=eR(e=>{let t=V().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=eA(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),ee=n.useCallback((e,t,r)=>{let n=!K.current&&!r;(void 0!==A.value&&A.value===t||n)&&(_(e),n&&(K.current=!0))},[A.value]),et=n.useCallback(()=>null==E?void 0:E.focus(),[E]),er=n.useCallback((e,t,r)=>{let n=!K.current&&!r;(void 0!==A.value&&A.value===t||n)&&O(e)},[A.value]),en="popper"===l?Q:J,el=en===Q?{side:c,sideOffset:v,align:m,alignOffset:h,arrowPadding:y,collisionBoundary:g,collisionPadding:x,sticky:b,hideWhenDetached:S,avoidCollisions:C}:{};return(0,j.jsx)(X,{scope:r,content:E,viewport:P,onViewportChange:I,itemRefCallback:ee,selectedItem:L,onItemLeave:et,itemTextRefCallback:er,focusSelectedItem:U,selectedItemText:B,position:l,isPositioned:F,searchRef:Z,children:(0,j.jsx)(N.A,{as:w.DX,allowPinchZoom:!0,children:(0,j.jsx)(f.n,{asChild:!0,trapped:A.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.m)(o,e=>{var t;null===(t=A.trigger)||void 0===t||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,j.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>A.onOpenChange(!1),children:(0,j.jsx)(en,{role:"listbox",id:A.contentId,"data-state":A.open?"open":"closed",dir:A.dir,onContextMenu:e=>e.preventDefault(),...R,...el,onPlaced:()=>G(!0),ref:D,style:{display:"flex",flexDirection:"column",outline:"none",...R.style},onKeyDown:(0,a.m)(R.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||Y(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=V().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>W(t)),e.preventDefault()}})})})})})})});Y.displayName="SelectContentImpl";var J=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:l,...a}=e,i=H($,r),u=Z($,r),[c,d]=n.useState(null),[p,f]=n.useState(null),v=(0,s.s)(t,e=>f(e)),m=M(r),h=n.useRef(!1),w=n.useRef(!0),{viewport:g,selectedItem:x,selectedItemText:S,focusSelectedItem:C}=u,k=n.useCallback(()=>{if(i.trigger&&i.valueNode&&c&&p&&g&&x&&S){let e=i.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),r=i.valueNode.getBoundingClientRect(),n=S.getBoundingClientRect();if("rtl"!==i.dir){let l=n.left-t.left,a=r.left-l,i=e.left-a,s=e.width+i,u=Math.max(s,t.width),d=o(a,[10,Math.max(10,window.innerWidth-10-u)]);c.style.minWidth=s+"px",c.style.left=d+"px"}else{let l=t.right-n.right,a=window.innerWidth-r.right-l,i=window.innerWidth-e.right-a,s=e.width+i,u=Math.max(s,t.width),d=o(a,[10,Math.max(10,window.innerWidth-10-u)]);c.style.minWidth=s+"px",c.style.right=d+"px"}let a=m(),s=window.innerHeight-20,u=g.scrollHeight,d=window.getComputedStyle(p),f=parseInt(d.borderTopWidth,10),v=parseInt(d.paddingTop,10),y=parseInt(d.borderBottomWidth,10),w=f+v+u+parseInt(d.paddingBottom,10)+y,b=Math.min(5*x.offsetHeight,w),C=window.getComputedStyle(g),k=parseInt(C.paddingTop,10),N=parseInt(C.paddingBottom,10),j=e.top+e.height/2-10,R=x.offsetHeight/2,A=f+v+(x.offsetTop+R);if(A<=j){let e=a.length>0&&x===a[a.length-1].ref.current;c.style.bottom="0px";let t=Math.max(s-j,R+(e?N:0)+(p.clientHeight-g.offsetTop-g.offsetHeight)+y);c.style.height=A+t+"px"}else{let e=a.length>0&&x===a[0].ref.current;c.style.top="0px";let t=Math.max(j,f+g.offsetTop+(e?k:0)+R);c.style.height=t+(w-A)+"px",g.scrollTop=A-j+g.offsetTop}c.style.margin="".concat(10,"px 0"),c.style.minHeight=b+"px",c.style.maxHeight=s+"px",null==l||l(),requestAnimationFrame(()=>h.current=!0)}},[m,i.trigger,i.valueNode,c,p,g,x,S,i.dir,l]);(0,b.N)(()=>k(),[k]);let[N,R]=n.useState();(0,b.N)(()=>{p&&R(window.getComputedStyle(p).zIndex)},[p]);let A=n.useCallback(e=>{e&&!0===w.current&&(k(),null==C||C(),w.current=!1)},[k,C]);return(0,j.jsx)(ee,{scope:r,contentWrapper:c,shouldExpandOnScrollRef:h,onScrollButtonChange:A,children:(0,j.jsx)("div",{ref:d,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:N},children:(0,j.jsx)(y.sG.div,{...a,ref:v,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});J.displayName="SelectItemAlignedPosition";var Q=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:l=10,...o}=e,a=L(r);return(0,j.jsx)(m.UC,{...a,...o,ref:t,align:n,collisionPadding:l,style:{boxSizing:"border-box",...o.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});Q.displayName="SelectPopperPosition";var[ee,et]=I($,{}),er="SelectViewport",en=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:l,...o}=e,i=Z(er,r),u=et(er,r),c=(0,s.s)(t,i.onViewportChange),d=n.useRef(0);return(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,j.jsx)(T.Slot,{scope:r,children:(0,j.jsx)(y.sG.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:(0,a.m)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=u;if((null==n?void 0:n.current)&&r){let e=Math.abs(d.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,l=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(l<n){let o=l+e,a=Math.min(n,o),i=o-a;r.style.height=a+"px","0px"===r.style.bottom&&(t.scrollTop=i>0?i:0,r.style.justifyContent="flex-end")}}}d.current=t.scrollTop})})})]})});en.displayName=er;var el="SelectGroup",[eo,ea]=I(el),ei=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=(0,v.B)();return(0,j.jsx)(eo,{scope:r,id:l,children:(0,j.jsx)(y.sG.div,{role:"group","aria-labelledby":l,...n,ref:t})})});ei.displayName=el;var es="SelectLabel",eu=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=ea(es,r);return(0,j.jsx)(y.sG.div,{id:l.id,...n,ref:t})});eu.displayName=es;var ec="SelectItem",[ed,ep]=I(ec),ef=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:l,disabled:o=!1,textValue:i,...u}=e,c=H(ec,r),d=Z(ec,r),p=c.value===l,[f,m]=n.useState(null!=i?i:""),[h,w]=n.useState(!1),g=(0,s.s)(t,e=>{var t;return null===(t=d.itemRefCallback)||void 0===t?void 0:t.call(d,e,l,o)}),x=(0,v.B)(),b=n.useRef("touch"),S=()=>{o||(c.onValueChange(l),c.onOpenChange(!1))};if(""===l)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,j.jsx)(ed,{scope:r,value:l,disabled:o,textId:x,isSelected:p,onItemTextChange:n.useCallback(e=>{m(t=>{var r;return t||(null!==(r=null==e?void 0:e.textContent)&&void 0!==r?r:"").trim()})},[]),children:(0,j.jsx)(T.ItemSlot,{scope:r,value:l,disabled:o,textValue:f,children:(0,j.jsx)(y.sG.div,{role:"option","aria-labelledby":x,"data-highlighted":h?"":void 0,"aria-selected":p&&h,"data-state":p?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...u,ref:g,onFocus:(0,a.m)(u.onFocus,()=>w(!0)),onBlur:(0,a.m)(u.onBlur,()=>w(!1)),onClick:(0,a.m)(u.onClick,()=>{"mouse"!==b.current&&S()}),onPointerUp:(0,a.m)(u.onPointerUp,()=>{"mouse"===b.current&&S()}),onPointerDown:(0,a.m)(u.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,a.m)(u.onPointerMove,e=>{if(b.current=e.pointerType,o){var t;null===(t=d.onItemLeave)||void 0===t||t.call(d)}else"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.m)(u.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null===(t=d.onItemLeave)||void 0===t||t.call(d)}}),onKeyDown:(0,a.m)(u.onKeyDown,e=>{var t;((null===(t=d.searchRef)||void 0===t?void 0:t.current)===""||" "!==e.key)&&(A.includes(e.key)&&S()," "===e.key&&e.preventDefault())})})})})});ef.displayName=ec;var ev="SelectItemText",em=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:o,style:a,...i}=e,u=H(ev,r),c=Z(ev,r),d=ep(ev,r),p=O(ev,r),[f,v]=n.useState(null),m=(0,s.s)(t,e=>v(e),d.onItemTextChange,e=>{var t;return null===(t=c.itemTextRefCallback)||void 0===t?void 0:t.call(c,e,d.value,d.disabled)}),h=null==f?void 0:f.textContent,w=n.useMemo(()=>(0,j.jsx)("option",{value:d.value,disabled:d.disabled,children:h},d.value),[d.disabled,d.value,h]),{onNativeOptionAdd:g,onNativeOptionRemove:x}=p;return(0,b.N)(()=>(g(w),()=>x(w)),[g,x,w]),(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)(y.sG.span,{id:d.textId,...i,ref:m}),d.isSelected&&u.valueNode&&!u.valueNodeHasChildren?l.createPortal(i.children,u.valueNode):null]})});em.displayName=ev;var eh="SelectItemIndicator",ey=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return ep(eh,r).isSelected?(0,j.jsx)(y.sG.span,{"aria-hidden":!0,...n,ref:t}):null});ey.displayName=eh;var ew="SelectScrollUpButton",eg=n.forwardRef((e,t)=>{let r=Z(ew,e.__scopeSelect),l=et(ew,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,s.s)(t,l.onScrollButtonChange);return(0,b.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){a(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,j.jsx)(eS,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});eg.displayName=ew;var ex="SelectScrollDownButton",eb=n.forwardRef((e,t)=>{let r=Z(ex,e.__scopeSelect),l=et(ex,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,s.s)(t,l.onScrollButtonChange);return(0,b.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;a(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,j.jsx)(eS,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eb.displayName=ex;var eS=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:l,...o}=e,i=Z("SelectScrollButton",r),s=n.useRef(null),u=M(r),c=n.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return n.useEffect(()=>()=>c(),[c]),(0,b.N)(()=>{var e;let t=u().find(e=>e.ref.current===document.activeElement);null==t||null===(e=t.ref.current)||void 0===e||e.scrollIntoView({block:"nearest"})},[u]),(0,j.jsx)(y.sG.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,a.m)(o.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(l,50))}),onPointerMove:(0,a.m)(o.onPointerMove,()=>{var e;null===(e=i.onItemLeave)||void 0===e||e.call(i),null===s.current&&(s.current=window.setInterval(l,50))}),onPointerLeave:(0,a.m)(o.onPointerLeave,()=>{c()})})}),eC=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,j.jsx)(y.sG.div,{"aria-hidden":!0,...n,ref:t})});eC.displayName="SelectSeparator";var ek="SelectArrow";function eN(e){return""===e||void 0===e}n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=L(r),o=H(ek,r),a=Z(ek,r);return o.open&&"popper"===a.position?(0,j.jsx)(m.i3,{...l,...n,ref:t}):null}).displayName=ek;var ej=n.forwardRef((e,t)=>{let{value:r,...l}=e,o=n.useRef(null),a=(0,s.s)(t,o),i=(0,S.Z)(r);return n.useEffect(()=>{let e=o.current,t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(i!==r&&t){let n=new Event("change",{bubbles:!0});t.call(e,r),e.dispatchEvent(n)}},[i,r]),(0,j.jsx)(C.s,{asChild:!0,children:(0,j.jsx)("select",{...l,ref:a,defaultValue:r})})});function eR(e){let t=(0,g.c)(e),r=n.useRef(""),l=n.useRef(0),o=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(l.current),""!==t&&(l.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),a=n.useCallback(()=>{r.current="",window.clearTimeout(l.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(l.current),[]),[r,o,a]}function eA(e,t,r){var n,l;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=r?e.indexOf(r):-1,i=(n=e,l=Math.max(a,0),n.map((e,t)=>n[(l+t)%n.length]));1===o.length&&(i=i.filter(e=>e!==r));let s=i.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return s!==r?s:void 0}ej.displayName="BubbleSelect";var eE=V,eT=G,eM=W,eP=U,eI=z,eD=q,eL=en,e_=ei,eH=eu,eB=ef,eO=em,eV=ey,eF=eg,eG=eb,eK=eC},1027:(e,t,r)=>{r.d(t,{F:()=>a});var n=r(3463);let l=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,o=n.$,a=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return o(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:a,defaultVariants:i}=t,s=Object.keys(a).map(e=>{let t=null==r?void 0:r[e],n=null==i?void 0:i[e];if(null===t)return null;let o=l(t)||l(n);return a[e][o]}),u=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return o(e,s,null==t?void 0:null===(n=t.compoundVariants)||void 0===n?void 0:n.reduce((e,t)=>{let{class:r,className:n,...l}=t;return Object.entries(l).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...i,...u}[t]):({...i,...u})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},1488:(e,t,r)=>{r.d(t,{i:()=>o});var n=r(2115),l=r(1524);function o({prop:e,defaultProp:t,onChange:r=()=>{}}){let[o,a]=function({defaultProp:e,onChange:t}){let r=n.useState(e),[o]=r,a=n.useRef(o),i=(0,l.c)(t);return n.useEffect(()=>{a.current!==o&&(i(o),a.current=o)},[o,a,i]),r}({defaultProp:t,onChange:r}),i=void 0!==e,s=i?e:o,u=(0,l.c)(r);return[s,n.useCallback(t=>{if(i){let r="function"==typeof t?t(e):t;r!==e&&u(r)}else a(t)},[i,e,a,u])]}},1524:(e,t,r)=>{r.d(t,{c:()=>l});var n=r(2115);function l(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},1594:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(7401).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},1902:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(7401).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},2598:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(7401).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3360:(e,t,r)=>{r.d(t,{hO:()=>s,sG:()=>i});var n=r(2115),l=r(7650),o=r(2317),a=r(5155),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=n.forwardRef((e,r)=>{let{asChild:n,...l}=e,i=n?o.DX:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(i,{...l,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function s(e,t){e&&l.flushSync(()=>e.dispatchEvent(t))}},3610:(e,t,r)=>{r.d(t,{m:()=>n});function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}},4256:(e,t,r)=>{r.d(t,{jH:()=>o});var n=r(2115);r(5155);var l=n.createContext(void 0);function o(e){let t=n.useContext(l);return e||t||"ltr"}},4267:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(7401).A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},4858:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(7401).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},6611:(e,t,r)=>{r.d(t,{N:()=>l});var n=r(2115),l=globalThis?.document?n.useLayoutEffect:()=>{}},6889:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(7401).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},7401:(e,t,r)=>{r.d(t,{A:()=>s});var n=r(2115);let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&r.indexOf(e)===t).join(" ")};var a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:l=24,strokeWidth:i=2,absoluteStrokeWidth:s,className:u="",children:c,iconNode:d,...p}=e;return(0,n.createElement)("svg",{ref:t,...a,width:l,height:l,stroke:r,strokeWidth:s?24*Number(i)/Number(l):i,className:o("lucide",u),...p},[...d.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(c)?c:[c]])}),s=(e,t)=>{let r=(0,n.forwardRef)((r,a)=>{let{className:s,...u}=r;return(0,n.createElement)(i,{ref:a,iconNode:t,className:o("lucide-".concat(l(e)),s),...u})});return r.displayName="".concat(e),r}},7668:(e,t,r)=>{r.d(t,{B:()=>s});var n,l=r(2115),o=r(6611),a=(n||(n=r.t(l,2)))["useId".toString()]||(()=>void 0),i=0;function s(e){let[t,r]=l.useState(a());return(0,o.N)(()=>{e||r(e=>e??String(i++))},[e]),e||(t?`radix-${t}`:"")}},8166:(e,t,r)=>{r.d(t,{A:()=>o});var n=r(2115),l=r(5155);function o(e,t=[]){let r=[],a=()=>{let t=r.map(e=>n.createContext(e));return function(r){let l=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:l}}),[r,l])}};return a.scopeName=e,[function(t,o){let a=n.createContext(o),i=r.length;r=[...r,o];let s=t=>{let{scope:r,children:o,...s}=t,u=r?.[e]?.[i]||a,c=n.useMemo(()=>s,Object.values(s));return(0,l.jsx)(u.Provider,{value:c,children:o})};return s.displayName=t+"Provider",[s,function(r,l){let s=l?.[e]?.[i]||a,u=n.useContext(s);if(u)return u;if(void 0!==o)return o;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let l=r.reduce((t,{useScope:r,scopeName:n})=>{let l=r(e)[`__scope${n}`];return{...t,...l}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:l}),[l])}};return r.scopeName=t.scopeName,r}(a,...t)]}},8867:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(7401).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},9741:(e,t,r)=>{r.d(t,{N:()=>s});var n=r(2115),l=r(8166),o=r(8068),a=r(2317),i=r(5155);function s(e){let t=e+"CollectionProvider",[r,s]=(0,l.A)(t),[u,c]=r(t,{collectionRef:{current:null},itemMap:new Map}),d=e=>{let{scope:t,children:r}=e,l=n.useRef(null),o=n.useRef(new Map).current;return(0,i.jsx)(u,{scope:t,itemMap:o,collectionRef:l,children:r})};d.displayName=t;let p=e+"CollectionSlot",f=n.forwardRef((e,t)=>{let{scope:r,children:n}=e,l=c(p,r),s=(0,o.s)(t,l.collectionRef);return(0,i.jsx)(a.DX,{ref:s,children:n})});f.displayName=p;let v=e+"CollectionItemSlot",m="data-radix-collection-item",h=n.forwardRef((e,t)=>{let{scope:r,children:l,...s}=e,u=n.useRef(null),d=(0,o.s)(t,u),p=c(v,r);return n.useEffect(()=>(p.itemMap.set(u,{ref:u,...s}),()=>void p.itemMap.delete(u))),(0,i.jsx)(a.DX,{[m]:"",ref:d,children:l})});return h.displayName=v,[{Provider:d,Slot:f,ItemSlot:h},function(t){let r=c(e+"CollectionConsumer",t);return n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(m,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},s]}}}]);