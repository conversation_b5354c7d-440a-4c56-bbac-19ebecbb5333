"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1102],{719:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(7401).A)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},853:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(7401).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},1594:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(7401).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},3239:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(7401).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},4081:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(7401).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},4858:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(7401).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},5844:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(7401).A)("Truck",[["path",{d:"M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2",key:"wrbu53"}],["path",{d:"M15 18H9",key:"1lyqi6"}],["path",{d:"M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14",key:"lysw3i"}],["circle",{cx:"17",cy:"18",r:"2",key:"332jqn"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}]])},6967:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(7401).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},8217:(e,t,r)=>{r.d(t,{UC:()=>Q,B8:()=>O,bL:()=>Z,l9:()=>J});var a=r(2115),n=r(3610),o=r(8166),i=r(9741),l=r(8068),c=r(7668),s=r(3360),u=r(1524),d=r(1488),f=r(4256),p=r(5155),y="rovingFocusGroup.onEntryFocus",h={bubbles:!1,cancelable:!0},v="RovingFocusGroup",[m,b,k]=(0,i.N)(v),[w,A]=(0,o.A)(v,[k]),[x,g]=w(v),C=a.forwardRef((e,t)=>(0,p.jsx)(m.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(m.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(j,{...e,ref:t})})}));C.displayName=v;var j=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:o,loop:i=!1,dir:c,currentTabStopId:v,defaultCurrentTabStopId:m,onCurrentTabStopIdChange:k,onEntryFocus:w,preventScrollOnEntryFocus:A=!1,...g}=e,C=a.useRef(null),j=(0,l.s)(t,C),F=(0,f.jH)(c),[R=null,I]=(0,d.i)({prop:v,defaultProp:m,onChange:k}),[D,T]=a.useState(!1),E=(0,u.c)(w),G=b(r),q=a.useRef(!1),[H,K]=a.useState(0);return a.useEffect(()=>{let e=C.current;if(e)return e.addEventListener(y,E),()=>e.removeEventListener(y,E)},[E]),(0,p.jsx)(x,{scope:r,orientation:o,dir:F,loop:i,currentTabStopId:R,onItemFocus:a.useCallback(e=>I(e),[I]),onItemShiftTab:a.useCallback(()=>T(!0),[]),onFocusableItemAdd:a.useCallback(()=>K(e=>e+1),[]),onFocusableItemRemove:a.useCallback(()=>K(e=>e-1),[]),children:(0,p.jsx)(s.sG.div,{tabIndex:D||0===H?-1:0,"data-orientation":o,...g,ref:j,style:{outline:"none",...e.style},onMouseDown:(0,n.m)(e.onMouseDown,()=>{q.current=!0}),onFocus:(0,n.m)(e.onFocus,e=>{let t=!q.current;if(e.target===e.currentTarget&&t&&!D){let t=new CustomEvent(y,h);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=G().filter(e=>e.focusable);M([e.find(e=>e.active),e.find(e=>e.id===R),...e].filter(Boolean).map(e=>e.ref.current),A)}}q.current=!1}),onBlur:(0,n.m)(e.onBlur,()=>T(!1))})})}),F="RovingFocusGroupItem",R=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:o=!0,active:i=!1,tabStopId:l,...u}=e,d=(0,c.B)(),f=l||d,y=g(F,r),h=y.currentTabStopId===f,v=b(r),{onFocusableItemAdd:k,onFocusableItemRemove:w}=y;return a.useEffect(()=>{if(o)return k(),()=>w()},[o,k,w]),(0,p.jsx)(m.ItemSlot,{scope:r,id:f,focusable:o,active:i,children:(0,p.jsx)(s.sG.span,{tabIndex:h?0:-1,"data-orientation":y.orientation,...u,ref:t,onMouseDown:(0,n.m)(e.onMouseDown,e=>{o?y.onItemFocus(f):e.preventDefault()}),onFocus:(0,n.m)(e.onFocus,()=>y.onItemFocus(f)),onKeyDown:(0,n.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){y.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var a;let n=(a=e.key,"rtl"!==r?a:"ArrowLeft"===a?"ArrowRight":"ArrowRight"===a?"ArrowLeft":a);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(n))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(n)))return I[n]}(e,y.orientation,y.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=v().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let a=r.indexOf(e.currentTarget);r=y.loop?function(e,t){return e.map((r,a)=>e[(t+a)%e.length])}(r,a+1):r.slice(a+1)}setTimeout(()=>M(r))}})})})});R.displayName=F;var I={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function M(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let a of e)if(a===r||(a.focus({preventScroll:t}),document.activeElement!==r))return}var D=r(7028),T="Tabs",[E,G]=(0,o.A)(T,[A]),q=A(),[H,K]=E(T),L=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,onValueChange:n,defaultValue:o,orientation:i="horizontal",dir:l,activationMode:u="automatic",...y}=e,h=(0,f.jH)(l),[v,m]=(0,d.i)({prop:a,onChange:n,defaultProp:o});return(0,p.jsx)(H,{scope:r,baseId:(0,c.B)(),value:v,onValueChange:m,orientation:i,dir:h,activationMode:u,children:(0,p.jsx)(s.sG.div,{dir:h,"data-orientation":i,...y,ref:t})})});L.displayName=T;var N="TabsList",S=a.forwardRef((e,t)=>{let{__scopeTabs:r,loop:a=!0,...n}=e,o=K(N,r),i=q(r);return(0,p.jsx)(C,{asChild:!0,...i,orientation:o.orientation,dir:o.dir,loop:a,children:(0,p.jsx)(s.sG.div,{role:"tablist","aria-orientation":o.orientation,...n,ref:t})})});S.displayName=N;var _="TabsTrigger",B=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,disabled:o=!1,...i}=e,l=K(_,r),c=q(r),u=z(l.baseId,a),d=U(l.baseId,a),f=a===l.value;return(0,p.jsx)(R,{asChild:!0,...c,focusable:!o,active:f,children:(0,p.jsx)(s.sG.button,{type:"button",role:"tab","aria-selected":f,"aria-controls":d,"data-state":f?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:u,...i,ref:t,onMouseDown:(0,n.m)(e.onMouseDown,e=>{o||0!==e.button||!1!==e.ctrlKey?e.preventDefault():l.onValueChange(a)}),onKeyDown:(0,n.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&l.onValueChange(a)}),onFocus:(0,n.m)(e.onFocus,()=>{let e="manual"!==l.activationMode;f||o||!e||l.onValueChange(a)})})})});B.displayName=_;var P="TabsContent",V=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,forceMount:o,children:i,...l}=e,c=K(P,r),u=z(c.baseId,n),d=U(c.baseId,n),f=n===c.value,y=a.useRef(f);return a.useEffect(()=>{let e=requestAnimationFrame(()=>y.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,p.jsx)(D.C,{present:o||f,children:r=>{let{present:a}=r;return(0,p.jsx)(s.sG.div,{"data-state":f?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":u,hidden:!a,id:d,tabIndex:0,...l,ref:t,style:{...e.style,animationDuration:y.current?"0s":void 0},children:a&&i})}})});function z(e,t){return"".concat(e,"-trigger-").concat(t)}function U(e,t){return"".concat(e,"-content-").concat(t)}V.displayName=P;var Z=L,O=S,J=B,Q=V}}]);