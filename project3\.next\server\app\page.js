(()=>{var e={};e.id=974,e.ids=[974],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5884:(e,t,s)=>{"use strict";s.d(t,{E:()=>i});var r=s(45512),a=s(59462);function i({className:e,...t}){return(0,r.jsx)("div",{className:(0,a.cn)("animate-pulse rounded-md bg-muted",e),...t})}},9086:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("Flame",[["path",{d:"M8.5 14.5A2.5 2.5 0 0 0 11 12c0-1.38-.5-2-1-3-1.072-2.143-.224-4.054 2-6 .5 2.5 2 4.9 4 6.5 2 1.6 3 3.5 3 5.5a7 7 0 1 1-14 0c0-1.153.433-2.294 1-3a2.5 2.5 0 0 0 2.5 2.5z",key:"96xj49"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},16873:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31757:(e,t,s)=>{Promise.resolve().then(s.bind(s,35104))},33873:e=>{"use strict";e.exports=require("path")},35104:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\page.tsx","default")},43916:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>ee});var r=s(45512),a=s(58009);s(15348);var i=s(52706),l=s(99905),n=s(87021),o=s(59462);function c({images:e,autoPlayInterval:t=5e3,className:s,onSlideChange:c,initialIndex:d=0}){let[m,u]=(0,a.useState)(d),[h,x]=(0,a.useState)(!0),[p,g]=(0,a.useState)(!1),f=(0,a.useCallback)(()=>{if(p)return;g(!0);let t=m===e.length-1?0:m+1;u(t),c&&c(t),setTimeout(()=>g(!1),500)},[e.length,m,c,p]);return(0,r.jsxs)("div",{className:(0,o.cn)("relative w-full overflow-hidden",s),onMouseEnter:()=>x(!1),onMouseLeave:()=>x(!0),children:[(0,r.jsx)("div",{className:"flex transition-transform duration-500 ease-out",style:{transform:`translateX(-${100*m}%)`},children:e.map((e,t)=>(0,r.jsx)("div",{className:"w-full flex-shrink-0 relative",style:{aspectRatio:"16/6"},children:(0,r.jsx)("div",{className:"w-full h-full",children:(0,r.jsx)("img",{src:e.url,alt:e.alt,className:"w-full h-full object-cover",style:{objectFit:"cover",width:"100%",height:"100%"}})})},t))}),(0,r.jsx)(n.$,{variant:"ghost",size:"icon",className:"absolute left-4 top-1/2 -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white rounded-full p-2 z-20",onClick:()=>{if(p)return;g(!0);let t=0===m?e.length-1:m-1;u(t),c&&c(t),setTimeout(()=>g(!1),500)},disabled:p,children:(0,r.jsx)(i.A,{className:"h-6 w-6"})}),(0,r.jsx)(n.$,{variant:"ghost",size:"icon",className:"absolute right-4 top-1/2 -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white rounded-full p-2 z-20",onClick:f,disabled:p,children:(0,r.jsx)(l.A,{className:"h-6 w-6"})}),(0,r.jsx)("div",{className:"absolute bottom-6 left-1/2 -translate-x-1/2 flex gap-3 z-20",children:e.map((e,t)=>(0,r.jsx)("button",{className:(0,o.cn)("w-3 h-3 rounded-full transition-all",m===t?"bg-white scale-125":"bg-white/50 hover:bg-white/75"),onClick:()=>{p||(g(!0),u(t),c&&c(t),setTimeout(()=>g(!1),500))},disabled:p},t))})]})}function d({className:e}){let[t,s]=(0,a.useState)([]),[i,l]=(0,a.useState)(!0),[o,d]=(0,a.useState)(null),[m,u]=(0,a.useState)(0),[h,x]=(0,a.useState)({transform:"translateX(0%) translateY(0%)"}),p=e=>{e&&(window.location.href=e)};if(i)return(0,r.jsx)("div",{className:`relative w-full h-[450px] overflow-hidden rounded-xl bg-accent/10 animate-pulse ${e}`,children:(0,r.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,r.jsx)("div",{className:"w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"})})});if((o||0===t.length)&&(s([{BannerID:1,TopTitle:"TOP TITLE",MainTitle:"main title",BottomTitle:"bottom title",LeftButtonText:"test",RightButtonText:"test right",BannerImgUrl:"https://placehold.co/1200x450/333333/FFFFFF?text=Banner+Image",LeftButtonUrl:"#",ThemeTypeID:1}]),o))return(0,r.jsx)("div",{className:`relative w-full h-[450px] overflow-hidden rounded-xl bg-accent/5 flex items-center justify-center ${e}`,children:(0,r.jsxs)("div",{className:"text-center p-8",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"Failed to load banners"}),(0,r.jsx)("p",{className:"text-muted-foreground mb-4",children:o}),(0,r.jsx)(n.$,{onClick:()=>window.location.reload(),children:"Retry"})]})});let g=t.map(e=>({url:e.BannerImgUrl,alt:e.MainTitle||"Banner",id:e.BannerID,leftButtonUrl:e.LeftButtonUrl}));return(0,r.jsx)("div",{className:"relative",onMouseMove:e=>{let t=e.clientX-window.innerWidth/1,s=e.clientY-window.innerHeight/1;x({transform:`translateX(${7+t/150}%) translateY(${1+s/150}%)`})},children:(0,r.jsx)("div",{className:"relative w-full h-full cursor-pointer",onClick:()=>p(t[m]?.LeftButtonUrl),children:(0,r.jsx)(c,{images:g,className:e,onSlideChange:e=>{u(e)},initialIndex:m,autoPlayInterval:5e3})})})}var m=s(41680);let u=(0,m.A)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);var h=s(10985),x=s(44269),p=s(71901),g=s(28531),f=s.n(g),v=s(24540),j=s(39872),N=s(15248),b=s(29e3),w=s(55785);class y extends a.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=this.props.sizeRef.current;e.height=t.offsetHeight||0,e.width=t.offsetWidth||0,e.top=t.offsetTop,e.left=t.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function C({children:e,isPresent:t}){let s=(0,a.useId)(),i=(0,a.useRef)(null),l=(0,a.useRef)({width:0,height:0,top:0,left:0}),{nonce:n}=(0,a.useContext)(w.Q);return(0,a.useInsertionEffect)(()=>{let{width:e,height:r,top:a,left:o}=l.current;if(t||!i.current||!e||!r)return;i.current.dataset.motionPopId=s;let c=document.createElement("style");return n&&(c.nonce=n),document.head.appendChild(c),c.sheet&&c.sheet.insertRule(`
          [data-motion-pop-id="${s}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${r}px !important;
            top: ${a}px !important;
            left: ${o}px !important;
          }
        `),()=>{document.head.removeChild(c)}},[t]),(0,r.jsx)(y,{isPresent:t,childRef:i,sizeRef:l,children:a.cloneElement(e,{ref:i})})}let P=({children:e,initial:t,isPresent:s,onExitComplete:i,custom:l,presenceAffectsLayout:n,mode:o})=>{let c=(0,N.M)(S),d=(0,a.useId)(),m=(0,a.useCallback)(e=>{for(let t of(c.set(e,!0),c.values()))if(!t)return;i&&i()},[c,i]),u=(0,a.useMemo)(()=>({id:d,initial:t,isPresent:s,custom:l,onExitComplete:m,register:e=>(c.set(e,!1),()=>c.delete(e))}),n?[Math.random(),m]:[s,m]);return(0,a.useMemo)(()=>{c.forEach((e,t)=>c.set(t,!1))},[s]),a.useEffect(()=>{s||c.size||!i||i()},[s]),"popLayout"===o&&(e=(0,r.jsx)(C,{isPresent:s,children:e})),(0,r.jsx)(b.t.Provider,{value:u,children:e})};function S(){return new Map}var k=s(45883);let T=e=>e.key||"";function $(e){let t=[];return a.Children.forEach(e,e=>{(0,a.isValidElement)(e)&&t.push(e)}),t}var E=s(13607);let A=({children:e,custom:t,initial:s=!0,onExitComplete:i,presenceAffectsLayout:l=!0,mode:n="sync",propagate:o=!1})=>{let[c,d]=(0,k.xQ)(o),m=(0,a.useMemo)(()=>$(e),[e]),u=o&&!c?[]:m.map(T),h=(0,a.useRef)(!0),x=(0,a.useRef)(m),p=(0,N.M)(()=>new Map),[g,f]=(0,a.useState)(m),[v,b]=(0,a.useState)(m);(0,E.E)(()=>{h.current=!1,x.current=m;for(let e=0;e<v.length;e++){let t=T(v[e]);u.includes(t)?p.delete(t):!0!==p.get(t)&&p.set(t,!1)}},[v,u.length,u.join("-")]);let w=[];if(m!==g){let e=[...m];for(let t=0;t<v.length;t++){let s=v[t],r=T(s);u.includes(r)||(e.splice(t,0,s),w.push(s))}"wait"===n&&w.length&&(e=w),b($(e)),f(m);return}let{forceRender:y}=(0,a.useContext)(j.L);return(0,r.jsx)(r.Fragment,{children:v.map(e=>{let a=T(e),g=(!o||!!c)&&(m===v||u.includes(a));return(0,r.jsx)(P,{isPresent:g,initial:(!h.current||!!s)&&void 0,custom:g?void 0:t,presenceAffectsLayout:l,mode:n,onExitComplete:g?void 0:()=>{if(!p.has(a))return;p.set(a,!0);let e=!0;p.forEach(t=>{t||(e=!1)}),e&&(null==y||y(),b(x.current),o&&(null==d||d()),i&&i())},children:e},a)})})};function I(){let[e,t]=(0,a.useState)([]),[s,i]=(0,a.useState)(!0),[c,d]=(0,a.useState)([]),[m,g]=(0,a.useState)(!0),[j,N]=(0,a.useState)(null),{t:b,primaryColor:w}=(0,p.t)(),y=e=>{N(e)};return s?(0,r.jsx)("div",{className:"p-6 space-y-4 bg-gradient-to-b from-background to-accent/5 min-h-screen border-r",children:[void 0,void 0,void 0,void 0,void 0].map((e,t)=>(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"h-8 bg-accent/10 animate-pulse rounded-lg w-3/4"}),(0,r.jsx)("div",{className:"h-6 bg-accent/5 animate-pulse rounded-lg w-1/2 ml-4"})]},t))}):(0,r.jsxs)("div",{className:"hidden md:block",children:[(0,r.jsx)(n.$,{variant:"ghost",size:"icon",className:"absolute left-4 top-4 z-50 md:hidden",onClick:()=>g(!m),children:(0,r.jsx)(u,{className:"h-6 w-6"})}),(0,r.jsx)(v.P.div,{className:"bg-background md:relative w-64 shadow-sm",style:{background:`linear-gradient(135deg, ${w}30, ${w}20, ${w}10)`},initial:{x:0},animate:{x:m?0:"-100%"},transition:{duration:.3,ease:"easeInOut"},children:(0,r.jsxs)("div",{className:"p-6 space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(h.A,{className:"h-6 w-6",style:{color:w}}),(0,r.jsx)("h2",{className:"text-xl font-semibold",style:{color:w},children:b("categories")})]}),j&&(0,r.jsx)(n.$,{variant:"ghost",size:"icon",onClick:()=>{N(null)},className:"hover:bg-accent/80",children:(0,r.jsx)(x.A,{className:"h-5 w-5"})})]}),(0,r.jsx)(A,{mode:"wait",children:j?(0,r.jsx)(v.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},transition:{duration:.2},className:"space-y-2",children:j.subcategories.map((e,t)=>(0,r.jsx)(v.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{delay:.05*t},children:(0,r.jsx)(f(),{href:`/products?category=${j.id}`,className:(0,o.cn)("block px-4 py-3 text-sm transition-all duration-200","bg-background/90 hover:bg-accent/50 hover:shadow-lg hover:scale-[1.02] rounded-lg","relative overflow-hidden group"),children:(0,r.jsxs)("span",{className:"relative z-10 flex items-center gap-2",children:[(0,r.jsx)(l.A,{className:"h-4 w-4 text-muted-foreground"}),e]})})},t))},"subcategory-list"):(0,r.jsx)(v.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},exit:{opacity:0,x:20},transition:{duration:.2},className:"space-y-2",children:[...e].reverse().map(e=>(0,r.jsx)(v.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.2},children:e.subcategories.length>0?(0,r.jsxs)("button",{onClick:()=>y(e),className:(0,o.cn)("w-full flex items-center justify-between p-4 transition-all duration-200 rounded-lg","bg-background/90 hover:bg-accent/50 hover:shadow-lg hover:scale-[1.02] relative overflow-hidden"),children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:e.name}),(0,r.jsx)(l.A,{className:"h-4 w-4"})]}):(0,r.jsx)(f(),{href:`/products?category=${e.id}`,className:(0,o.cn)("w-full flex items-center justify-between p-4 transition-all duration-200 rounded-lg","bg-background/90 hover:bg-accent/50 hover:shadow-lg hover:scale-[1.02] relative overflow-hidden"),children:(0,r.jsx)("span",{className:"text-sm font-medium",children:e.name})})},e.id))},"parent-list")})]})})]})}function M(){let e=e=>({popularCategories:"Popular Categories"})[e]||e,[t,s]=(0,a.useState)([]),[i,l]=(0,a.useState)(!0),[n,o]=(0,a.useState)(0),c=(0,a.useRef)(null),[d,m]=(0,a.useState)(!0),[u]=(0,a.useState)(5e3),h=(0,a.useRef)(null),x=Math.ceil(t.length/8),p=()=>{n<x-1?o(n+1):o(0)},g=()=>{h.current&&clearInterval(h.current),h.current=setInterval(()=>{p()},u)},f=()=>{h.current&&(clearInterval(h.current),h.current=null)},v=()=>{f(),setTimeout(()=>{d&&g()},1e4)};return i?(0,r.jsx)("section",{className:"py-8",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold mb-6",children:e("popularCategories")}),(0,r.jsx)("div",{className:"w-full flex justify-center items-center py-12",children:(0,r.jsx)("div",{className:"animate-pulse text-lg",children:"Loading popular categories..."})})]})}):t.length?(0,r.jsx)("section",{className:"py-8",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsx)("div",{className:"flex justify-between items-center mb-6",children:(0,r.jsx)("h2",{className:"text-2xl font-bold",children:e("popularCategories")})}),(0,r.jsx)("div",{className:"w-full relative overflow-hidden",onMouseEnter:()=>f(),onMouseLeave:()=>d&&g(),children:(0,r.jsx)("div",{ref:c,className:"w-full transition-transform duration-500 ease-in-out",style:{transform:`translateX(-${100*n}%)`},children:(0,r.jsx)("div",{className:"flex flex-nowrap",style:{width:`${100*x}%`},children:Array.from({length:x}).map((e,s)=>(0,r.jsx)("div",{className:"w-full flex-shrink-0 grid grid-cols-3 sm:grid-cols-3 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 sm:gap-6 md:gap-8",style:{width:`${100/x}%`},children:t.slice(8*s,(s+1)*8).map(e=>(0,r.jsx)("div",{className:"flex flex-col items-center px-2",children:(0,r.jsxs)("a",{href:`/products?category=${e.id}`,className:"group flex flex-col items-center cursor-pointer transition-all duration-300 hover:scale-105",children:[(0,r.jsx)("div",{className:"w-20 h-20 sm:w-24 sm:h-24 md:w-28 md:h-28 lg:w-32 lg:h-32 xl:w-36 xl:h-36 mb-3 relative",children:(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-50 to-blue-100 rounded-full p-1 group-hover:shadow-lg transition-all duration-300",children:(0,r.jsx)("div",{className:"w-full h-full rounded-full overflow-hidden border-2 border-white bg-white",children:(0,r.jsx)("img",{src:e.image||"/placeholder.svg?height=150&width=150",alt:e.title,width:144,height:144,className:"w-full h-full object-cover",onError:t=>{let s=t.target;if(console.error("Image load error for:",e.image),"https://admin.codemedicalapps.com/images/no-image.jpg"===e.image||e.image.includes("no-image"))s.src="/placeholder.svg?height=150&width=150";else{let e="https://admin.codemedicalapps.com/images/no-image.jpg";console.log("Trying fallback URL:",e),s.src=e,s.onerror=()=>{console.error("Fallback URL also failed, using simple placeholder"),s.src="/placeholder.svg?height=150&width=150",s.onerror=null}}}})})})}),(0,r.jsx)("h3",{className:"text-sm sm:text-base font-medium text-gray-800 text-center group-hover:text-blue-600 transition-colors duration-300 line-clamp-2",children:e.title})]})},e.id))},`page-${s}`))})})}),(0,r.jsx)("div",{className:"flex justify-center mt-4 space-x-2",children:Array.from({length:x}).map((e,t)=>(0,r.jsx)("button",{className:`h-2 rounded-full transition-all ${n===t?"w-6 bg-primary":"w-2 bg-gray-300"}`,onClick:()=>{o(t),v()},"aria-label":`Go to page ${t+1}`},`indicator-${t}`))})]})}):(0,r.jsx)("section",{className:"py-8",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold mb-6",children:e("popularCategories")}),(0,r.jsx)("div",{className:"w-full flex justify-center items-center py-12",children:(0,r.jsx)("div",{className:"text-lg text-gray-500",children:"No categories available at the moment. Please check back later."})})]})})}var R=s(97643),U=s(5884),D=s(45103),F=s(10453);let z=(0,m.A)("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]]);var L=s(16873),q=s(92557),_=s(39913),W=s(77252),B=s(10617);function O({rating:e,maxRating:t=5,size:s="md",showValue:a=!1,className:i=""}){let l=Math.max(0,Math.min(e,t)),n=l/t*100,o={sm:"h-3 w-3",md:"h-4 w-4",lg:"h-5 w-5"}[s];return(0,r.jsxs)("div",{className:`flex items-center gap-1 ${i}`,children:[(0,r.jsxs)("div",{className:"relative inline-flex",children:[(0,r.jsx)("div",{className:"flex",children:Array.from({length:t}).map((e,t)=>(0,r.jsx)(B.A,{className:`${o} text-gray-200`},`bg-${t}`))}),(0,r.jsx)("div",{className:"absolute top-0 left-0 overflow-hidden flex",style:{width:`${n}%`},children:Array.from({length:t}).map((e,t)=>(0,r.jsx)(B.A,{className:`${o} text-yellow-400`,fill:"currentColor"},`fg-${t}`))})]}),a&&(0,r.jsx)("span",{className:"text-sm font-medium ml-1",children:l.toFixed(1)})]})}var V=s(84194),Z=s(31961),J=s(91542);function X({product:e,effect:t="",layout:s=""}){let{t:i,language:l}=(0,p.t)(),o=(0,V._)(),c=(0,Z.n)(),[d,m]=(0,a.useState)(e.images[0]),[u,h]=(0,a.useState)(!1),[x,g]=(0,a.useState)(!1),v=()=>{if(e.inStock){h(!0);try{o.addToCart({id:parseInt(e.id),name:e.name,price:e.discountedPrice||e.price,discountPrice:e.discountedPrice,image:d.url,originalPrice:e.price},1,[],void 0),J.oR.success(`${e.name} added to cart`)}catch(e){console.error("Error adding to cart:",e),J.oR.error("Failed to add product to cart")}finally{h(!1)}}},j=e.discountedPrice?Math.round((e.price-e.discountedPrice)/e.price*100):0;return"list"===s?(0,r.jsx)(R.Zp,{className:"group relative overflow-hidden transition-all hover:shadow-lg",children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row",children:[(0,r.jsxs)("div",{className:"relative w-full md:w-1/3 aspect-square md:aspect-auto overflow-hidden",children:[(0,r.jsx)(f(),{href:`/product/${e.slug}`,children:(0,r.jsx)(D.default,{src:d.url.startsWith("http")?d.url:d.url.startsWith("/")?`${_.T.ADMIN_BASE_URL}${d.url.substring(1)}`:`${_.T.ADMIN_BASE_URL}${d.url}`,alt:d.alt,fill:!0,sizes:"(max-width: 640px) 100vw, (max-width: 768px) 50vw, 33vw",className:"object-cover transition-transform group-hover:scale-105",onError:e=>{e.target.src="https://placehold.co/400x400/cccccc/666666?text=Image+Not+Found"}})}),(0,r.jsxs)("div",{className:"absolute left-2 top-2 z-10 space-y-1",children:[e.isNew&&(0,r.jsx)(W.E,{variant:"secondary",className:"bg-blue-600 text-white hover:bg-blue-700 text-xs sm:text-sm font-semibold px-2 py-1 shadow-md",children:"NEW"}),j>0&&(0,r.jsxs)(W.E,{variant:"destructive",className:"text-xs sm:text-sm font-semibold px-2 py-1 shadow-md",children:["-",j,"%"]})]})]}),(0,r.jsxs)("div",{className:"p-4 md:p-6 flex-1 flex flex-col",children:[(0,r.jsxs)("div",{className:"mb-auto",children:[(0,r.jsx)(f(),{href:`/category/${e.categorySlug}`,className:"text-sm text-muted-foreground hover:text-primary",children:e.categoryName}),(0,r.jsx)(f(),{href:`/product/${e.slug}`,className:"mt-1 block text-xl font-medium hover:text-primary",children:e.name}),(0,r.jsx)("div",{className:"mt-2",children:(0,r.jsx)(O,{rating:e.rating,size:"sm"})}),(0,r.jsx)("p",{className:"mt-4 text-muted-foreground line-clamp-3",children:"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."})]}),(0,r.jsxs)("div",{className:"mt-4 flex items-center justify-between",children:[(0,r.jsx)("div",{className:"space-x-2",children:e.discountedPrice?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("span",{className:"text-xl font-bold text-primary",children:["$",e.discountedPrice.toFixed(2)]}),(0,r.jsxs)("span",{className:"text-sm text-muted-foreground line-through",children:["$",e.price.toFixed(2)]})]}):(0,r.jsxs)("span",{className:"text-xl font-bold text-primary",children:["$",e.price.toFixed(2)]})}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)(n.$,{variant:"outline",size:"sm",className:"h-9",onClick:()=>{g(!0);try{c.isInWishlist(parseInt(e.id))?(c.removeFromWishlist(parseInt(e.id)),J.oR.success(`${e.name} removed from wishlist`)):(c.addToWishlist(parseInt(e.id)),J.oR.success(`${e.name} added to wishlist`))}catch(e){console.error("Error updating wishlist:",e),J.oR.error("Failed to update wishlist")}finally{g(!1)}},children:[(0,r.jsx)(F.A,{className:"h-4 w-4 mr-2",fill:c.isInWishlist(parseInt(e.id))?"red":"none",color:c.isInWishlist(parseInt(e.id))?"red":"currentColor"}),x?"Updating...":"Wishlist"]}),(0,r.jsxs)(n.$,{variant:"default",size:"sm",className:"h-9",disabled:!e.inStock||u,onClick:v,children:[(0,r.jsx)(z,{className:"h-4 w-4 mr-2"}),u?"Adding...":"Add to Cart"]})]})]}),!e.inStock&&(0,r.jsx)("p",{className:"mt-2 text-sm text-destructive",children:"Out of Stock"})]})]})}):(0,r.jsxs)(R.Zp,{className:"group relative overflow-hidden transition-all hover:shadow-lg",children:[(0,r.jsxs)("div",{className:"absolute left-2 top-2 z-10 space-y-1",children:[e.isNew&&(0,r.jsx)(W.E,{variant:"secondary",className:"bg-blue-600 text-white hover:bg-blue-700 text-xs sm:text-sm font-semibold px-2 py-1 shadow-md",children:"NEW"}),j>0&&(0,r.jsxs)(W.E,{variant:"destructive",className:"text-xs sm:text-sm font-semibold px-2 py-1 shadow-md",children:["-",j,"%"]})]}),(0,r.jsxs)("div",{className:"relative aspect-square overflow-hidden",children:[(0,r.jsx)(f(),{href:`/product/${e.slug}`,children:(0,r.jsx)(D.default,{src:d.url.startsWith("http")?d.url:d.url.startsWith("/")?`${_.T.ADMIN_BASE_URL}${d.url.substring(1)}`:`${_.T.ADMIN_BASE_URL}${d.url}`,alt:d.alt,fill:!0,sizes:"(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw",className:"object-cover transition-transform group-hover:scale-105",onError:e=>{e.target.src="https://placehold.co/400x400/cccccc/666666?text=Image+Not+Found"}})}),(0,r.jsxs)("div",{className:`absolute right-2 top-2 z-10 flex flex-col gap-2 transition-all ${t}`,children:[(0,r.jsx)(n.$,{variant:"secondary",size:"icon",className:"h-8 w-8 bg-white/80 backdrop-blur-sm hover:bg-white",onClick:async()=>{if(e.inStock){g(!0);try{c.isInWishlist(parseInt(e.id))?(c.removeFromWishlist(parseInt(e.id)),J.oR.success("Removed from wishlist")):(c.addToWishlist(parseInt(e.id)),J.oR.success("Added to wishlist"))}catch(e){J.oR.error("Error updating wishlist")}finally{g(!1)}}},disabled:x||!e.inStock,children:(0,r.jsx)(F.A,{className:"h-4 w-4",fill:c.isInWishlist(parseInt(e.id))?"red":"none",color:c.isInWishlist(parseInt(e.id))?"red":"currentColor"})}),(0,r.jsx)(n.$,{variant:"secondary",size:"icon",className:"h-8 w-8 bg-white/80 backdrop-blur-sm hover:bg-white",onClick:()=>{},children:(0,r.jsx)(L.A,{className:"h-4 w-4"})}),(0,r.jsx)(n.$,{variant:"secondary",size:"icon",className:"h-8 w-8 bg-white/80 backdrop-blur-sm hover:bg-white",onClick:()=>{},children:(0,r.jsx)(q.A,{className:"h-4 w-4"})})]})]}),(0,r.jsxs)("div",{className:"p-4",children:[(0,r.jsx)(f(),{href:`/category/${e.categorySlug}`,className:"text-sm text-muted-foreground hover:text-primary",children:e.categoryName}),(0,r.jsx)(f(),{href:`/product/${e.slug}`,className:"mt-1 block text-lg font-medium hover:text-primary",children:e.name}),(0,r.jsx)("div",{className:"mt-1",children:(0,r.jsx)(O,{rating:e.rating,size:"sm"})}),(0,r.jsxs)("div",{className:"mt-2 flex items-center justify-between",children:[(0,r.jsx)("div",{className:"space-x-2",children:e.discountedPrice?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("span",{className:"text-lg font-bold text-primary",children:["$",e.discountedPrice.toFixed(2)]}),(0,r.jsxs)("span",{className:"text-sm text-muted-foreground line-through",children:["$",e.price.toFixed(2)]})]}):(0,r.jsxs)("span",{className:"text-lg font-bold text-primary",children:["$",e.price.toFixed(2)]})}),(0,r.jsx)(n.$,{variant:"secondary",size:"icon",className:"h-8 w-8",disabled:!e.inStock||u,onClick:v,children:u?(0,r.jsx)(q.A,{className:"h-4 w-4 animate-spin"}):(0,r.jsx)(z,{className:"h-4 w-4"})})]}),!e.inStock&&(0,r.jsx)("p",{className:"mt-2 text-sm text-destructive",children:"Out of Stock"})]})]})}function G({effect:e}){let{t}=(0,p.t)(),[i,l]=(0,a.useState)([]),[n,o]=(0,a.useState)(!0),[c,d]=(0,a.useState)(null),m=async()=>{o(!0);try{let{MakeApiCallAsync:e}=await Promise.resolve().then(s.bind(s,15348)),t=await e("get-recents-products-list",null,{requestParameters:{PageNo:1,PageSize:12,recordValueJson:"[]"}},{"Content-Type":"application/json",Accept:"application/json"},"POST",!0);if(t?.data?.data)try{let e=JSON.parse(t.data.data);if(console.log("New products data:",e),Array.isArray(e)){let t=e.map(e=>({id:e.ProductId?.toString()||"",name:e.ProductName||"",slug:(e.ProductName||"").toLowerCase().replace(/\s+/g,"-"),price:e.Price||0,discountedPrice:e.DiscountedPrice,images:e.ProductImagesJson?.map(t=>({id:t.AttachmentID?.toString()||"",url:t.AttachmentURL?`${_.T.ADMIN_BASE_URL}${t.AttachmentURL}`:"/images/placeholder.jpg",alt:e.ProductName||"Product image"}))||[],rating:e.Rating||0,categoryName:e.CategoryName||"",categorySlug:(e.CategoryName||"").toLowerCase().replace(/\s+/g,"-"),isNew:e.MarkAsNew||!1,inStock:(e.StockQuantity||0)>0}));l(t)}else console.error("Products data is not an array:",e),l([]),d("Invalid data format received from server")}catch(e){console.error("Error parsing products data:",e),l([]),d("Error processing product data")}else t?.data?.errorMessage?(console.error("API Error:",t.data.errorMessage),l([]),d(t.data.errorMessage||"An error occurred while fetching products")):(console.error("Invalid or empty response from API"),l([]),d("No data received from server"))}catch(e){console.error("Error fetching products:",e),l([]),e&&"object"==typeof e&&"message"in e?d(e.message):d("An unexpected error occurred while fetching products")}finally{o(!1)}};return c?(0,r.jsx)("div",{className:"w-full p-4 mb-4 text-center",children:(0,r.jsxs)("div",{className:"p-4 bg-red-50 border border-red-200 rounded-md",children:[(0,r.jsx)("p",{className:"text-red-600",children:c}),(0,r.jsx)("button",{onClick:()=>{d(null),m()},className:"mt-2 px-4 py-2 bg-red-100 hover:bg-red-200 text-red-700 rounded-md text-sm transition-colors",children:t("tryAgain")})]})}):(0,r.jsx)("div",{className:"w-full",children:(0,r.jsx)("div",{className:"grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4",children:n?Array.from({length:10}).map((e,t)=>(0,r.jsxs)(R.Zp,{className:"p-3 sm:p-4",children:[(0,r.jsx)(U.E,{className:"h-32 sm:h-40 w-full mb-3 sm:mb-4"}),(0,r.jsx)(U.E,{className:"h-3 sm:h-4 w-2/3 mb-2"}),(0,r.jsx)(U.E,{className:"h-3 sm:h-4 w-1/2"})]},t)):i.length>0?i.map(t=>(0,r.jsx)(X,{product:t,effect:e},t.id)):(0,r.jsx)("div",{className:"col-span-full text-center py-8",children:(0,r.jsx)("p",{className:"text-muted-foreground",children:t("noProductsFound")})})})})}let H=()=>{let[e,t]=(0,a.useState)([]),[i,l]=(0,a.useState)("https://admin.codemedicalapps.com/");(0,a.useEffect)(()=>{(async()=>{try{let{MakeApiCallAsync:e}=await Promise.resolve().then(s.bind(s,15348)),r=await e("get-web-campaign-list",null,{requestParameters:{PageNo:1,PageSize:3,recordValueJson:"[]"}},{"Content-Type":"application/json",Accept:"application/json"},"POST",!0);if(r?.data?.data)try{let e=JSON.parse(r.data.data);if(console.log("Campaign data:",e),Array.isArray(e)&&e.length>0){let s=e.map(e=>({CampaignId:e.CampaignId?.toString()||"",MainTitle:e.MainTitle||e.Title||"",DiscountTitle:e.DiscountTitle||e.SubTitle||"",CoverPictureUrl:e.CoverPictureUrl||e.ImageUrl||"/images/campaign/placeholder.jpg"}));t(s)}else t([{CampaignId:"1",MainTitle:"Summer Collection",DiscountTitle:"Up to 50% Off",CoverPictureUrl:"/images/campaign/summer.jpg"},{CampaignId:"2",MainTitle:"Winter Essentials",DiscountTitle:"Save 30%",CoverPictureUrl:"/images/campaign/winter.jpg"},{CampaignId:"3",MainTitle:"New Arrivals",DiscountTitle:"Fresh Styles",CoverPictureUrl:"/images/campaign/new.jpg"}])}catch(e){console.error("Error parsing campaign data:",e),t([{CampaignId:"1",MainTitle:"Summer Collection",DiscountTitle:"Up to 50% Off",CoverPictureUrl:"/images/campaign/summer.jpg"},{CampaignId:"2",MainTitle:"Winter Essentials",DiscountTitle:"Save 30%",CoverPictureUrl:"/images/campaign/winter.jpg"},{CampaignId:"3",MainTitle:"New Arrivals",DiscountTitle:"Fresh Styles",CoverPictureUrl:"/images/campaign/new.jpg"}])}else console.error("Invalid or empty response from API"),t([{CampaignId:"1",MainTitle:"Summer Collection",DiscountTitle:"Up to 50% Off",CoverPictureUrl:"/images/campaign/summer.jpg"},{CampaignId:"2",MainTitle:"Winter Essentials",DiscountTitle:"Save 30%",CoverPictureUrl:"/images/campaign/winter.jpg"},{CampaignId:"3",MainTitle:"New Arrivals",DiscountTitle:"Fresh Styles",CoverPictureUrl:"/images/campaign/new.jpg"}])}catch(e){console.error("Error fetching campaign data:",e),t([{CampaignId:"1",MainTitle:"Summer Collection",DiscountTitle:"Up to 50% Off",CoverPictureUrl:"/images/campaign/summer.jpg"},{CampaignId:"2",MainTitle:"Winter Essentials",DiscountTitle:"Save 30%",CoverPictureUrl:"/images/campaign/winter.jpg"},{CampaignId:"3",MainTitle:"New Arrivals",DiscountTitle:"Fresh Styles",CoverPictureUrl:"/images/campaign/new.jpg"}])}})()},[]);let n=(e,t)=>e.length<=t?e:e.substring(0,t)+"...",o=()=>"en";return(0,r.jsx)("section",{className:"py-12",children:(0,r.jsx)("div",{className:"container mx-auto px-4",children:(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:e&&e.slice(0,3).map((e,t)=>(0,r.jsxs)("div",{className:"relative overflow-hidden rounded-lg group",children:[(0,r.jsx)("div",{className:"aspect-[4/3] relative",children:(0,r.jsx)(D.default,{src:i+e.CoverPictureUrl,className:"object-cover transition-transform duration-300 group-hover:scale-105",alt:e.MainTitle,fill:!0,sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 33vw"})}),(0,r.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-black/40 p-6 text-center text-white",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-xl font-bold mb-2",children:n(e.DiscountTitle,25)}),(0,r.jsx)("h4",{className:"text-lg mb-4",children:n(e.MainTitle,35)}),(0,r.jsx)(f(),{href:`/${o()}/campaign/${e.CampaignId}/${e.MainTitle}`,className:"inline-block px-6 py-2 bg-white/20 hover:bg-white/30 rounded-full transition-colors duration-200 backdrop-blur-sm",children:"View Detail"})]})})]},t))})})})};var Q=s(9086);function Y(){let{t:e}=(0,p.t)(),t=(0,V._)(),[s,i]=(0,a.useState)(null),[l,o]=(0,a.useState)(!0),[c,d]=(0,a.useState)(!1),[m,u]=(0,a.useState)({days:0,hours:0,minutes:0,seconds:0});if(l)return(0,r.jsxs)(R.Zp,{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,r.jsx)(U.E,{className:"h-6 w-32"}),(0,r.jsx)(U.E,{className:"h-6 w-6"})]}),(0,r.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,r.jsx)(U.E,{className:"aspect-square"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(U.E,{className:"h-8 w-3/4"}),(0,r.jsx)(U.E,{className:"h-4 w-1/2"}),(0,r.jsx)(U.E,{className:"h-20 w-full"}),(0,r.jsx)("div",{className:"grid grid-cols-4 gap-2",children:Array.from({length:4}).map((e,t)=>(0,r.jsx)(U.E,{className:"h-16 w-full"},t))}),(0,r.jsx)(U.E,{className:"h-10 w-full"})]})]})]});if(!s)return null;let h=Math.round((s.price-s.discountedPrice)/s.price*100);return(0,r.jsxs)(R.Zp,{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold",children:"Today's Hot Deal"}),(0,r.jsx)(Q.A,{className:"h-6 w-6 text-red-500"})]}),(0,r.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"relative aspect-square overflow-hidden rounded-lg",children:[(0,r.jsx)(D.default,{src:s.images[0].url,alt:s.images[0].alt,fill:!0,className:"object-cover"}),(0,r.jsxs)(W.E,{variant:"destructive",className:"absolute left-2 top-2",children:["-",h,"%"]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(f(),{href:`/product/${s.slug}`,className:"block hover:text-primary",children:(0,r.jsx)("h3",{className:"text-2xl font-bold",children:s.name})}),(0,r.jsx)(O,{rating:s.rating}),(0,r.jsx)("p",{className:"text-muted-foreground",children:s.description}),(0,r.jsx)("div",{className:"grid grid-cols-4 gap-2 text-center",children:Object.entries(m).map(([e,t])=>(0,r.jsxs)("div",{className:"bg-muted p-2 rounded-lg",children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:t}),(0,r.jsx)("div",{className:"text-xs text-muted-foreground",children:e})]},e))}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("span",{className:"text-2xl font-bold text-primary",children:["$",s.discountedPrice.toFixed(2)]}),(0,r.jsxs)("span",{className:"text-lg text-muted-foreground line-through",children:["$",s.price.toFixed(2)]})]}),(0,r.jsx)(n.$,{size:"lg",className:"w-full",disabled:!s.inStock||c,onClick:()=>{if(s&&s.inStock){d(!0);try{t.addToCart({id:parseInt(s.id,10),name:s.name,price:s.discountedPrice,discountPrice:s.discountedPrice,image:s.images[0].url,originalPrice:s.price},1,[],void 0),J.oR.success(`${s.name} added to cart`)}catch(e){console.error("Error adding to cart:",e),J.oR.error("Failed to add product to cart")}finally{d(!1)}}},children:c?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(q.A,{className:"mr-2 h-5 w-5 animate-spin"}),"Adding..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(z,{className:"mr-2 h-5 w-5"}),s.inStock?"Add to Cart":"Out of Stock"]})})]})]})]})}function K({title:e="popularProducts",limit:t=8,hoverEffect:i}){let{t:l}=(0,p.t)(),[n,o]=(0,a.useState)([]),[c,d]=(0,a.useState)(!0),[m,u]=(0,a.useState)(null);return m?(0,r.jsx)("div",{className:"w-full p-4 mb-4 text-center",children:(0,r.jsxs)("div",{className:"p-4 bg-red-50 border border-red-200 rounded-md",children:[(0,r.jsx)("p",{className:"text-red-600",children:m}),(0,r.jsx)("button",{onClick:()=>{u(null),d(!0),(async()=>{try{let{MakeApiCallAsync:e}=await Promise.resolve().then(s.bind(s,15348));await e("api/v1/products/get-all-products",null,{requestParameters:{SearchTerm:"",CategoryID:null,TagID:null,ManufacturerID:null,MinPrice:null,MaxPrice:null,Rating:null,OrderByColumnName:"Price DESC",PageNo:1,PageSize:t,recordValueJson:"[]"}},{"Content-Type":"application/json",Accept:"application/json"},"POST",!0),d(!1)}catch(e){console.error("Error retrying fetch:",e),d(!1),u("Failed to fetch products. Please try again later.")}})()},className:"mt-2 px-4 py-2 bg-red-100 hover:bg-red-200 text-red-700 rounded-md text-sm transition-colors",children:"Try Again"})]})}):c?(0,r.jsx)("div",{className:"w-full",children:(0,r.jsx)("div",{className:"grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:Array.from({length:t}).map((e,t)=>(0,r.jsxs)(R.Zp,{className:"p-4",children:[(0,r.jsx)(U.E,{className:"h-48 w-full rounded-md mb-4"}),(0,r.jsx)(U.E,{className:"h-4 w-3/4 mb-2"}),(0,r.jsx)(U.E,{className:"h-4 w-1/2"})]},t))})}):0===n.length?(0,r.jsx)("div",{className:"w-full text-center py-8",children:(0,r.jsx)("p",{className:"text-muted-foreground",children:"No popular products found"})}):(0,r.jsx)("div",{className:"w-full",children:(0,r.jsx)("div",{className:"grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:n.map(e=>(0,r.jsx)(X,{product:e,effect:i},e.id))})})}function ee(){return(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row min-h-screen bg-background",children:[(0,r.jsx)("div",{className:"w-full lg:w-64 lg:flex-shrink-0 px-4 sm:px-6 lg:px-0 py-4 lg:py-0",children:(0,r.jsx)(I,{})}),(0,r.jsxs)("div",{className:"flex-1 p-4 sm:p-6",children:[(0,r.jsx)(d,{}),(0,r.jsx)(M,{}),(0,r.jsxs)("div",{className:"space-y-12 mt-8",children:[(0,r.jsxs)("section",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold mb-6",children:"New Products"}),(0,r.jsx)(G,{effect:"icon-inline"})]}),(0,r.jsx)("section",{children:(0,r.jsx)(H,{})}),(0,r.jsxs)("section",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold mb-6",children:"Today's Hot Deal"}),(0,r.jsx)(Y,{})]}),(0,r.jsx)("section",{children:(0,r.jsx)(K,{hoverEffect:"icon-inline",title:"Popular Products"})})]})]})]})}},54768:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>c});var r=s(70260),a=s(28203),i=s(25155),l=s.n(i),n=s(67292),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(t,o);let c=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,35104)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,19611)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"]}],d=["C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},77252:(e,t,s)=>{"use strict";s.d(t,{E:()=>n});var r=s(45512),a=s(21643),i=s(59462);let l=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function n({className:e,variant:t,...s}){return(0,r.jsx)("div",{className:(0,i.cn)(l({variant:t}),e),...s})}},79021:(e,t,s)=>{Promise.resolve().then(s.bind(s,43916))},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")},97643:(e,t,s)=>{"use strict";s.d(t,{Wu:()=>n,Zp:()=>l,wL:()=>o});var r=s(45512),a=s(58009),i=s(59462);let l=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));l.displayName="Card",a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...t})).displayName="CardHeader",a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("h3",{ref:s,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t})).displayName="CardTitle",a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("p",{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",e),...t})).displayName="CardDescription";let n=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)("p-6 pt-0",e),...t}));n.displayName="CardContent";let o=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)("flex items-center p-6 pt-0",e),...t}));o.displayName="CardFooter"}};var t=require("../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,320,400,609,875],()=>s(54768));module.exports=r})();