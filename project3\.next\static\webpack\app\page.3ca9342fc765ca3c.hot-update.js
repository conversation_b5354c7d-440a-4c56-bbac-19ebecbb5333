"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./lib/api-helper.ts":
/*!***************************!*\
  !*** ./lib/api-helper.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Config: () => (/* binding */ Config),\n/* harmony export */   MakeApiCallAsync: () => (/* binding */ MakeApiCallAsync)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./config */ \"(app-pages-browser)/./lib/config.ts\");\n\n\n// Configure axios defaults for HTTPS connections\naxios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].defaults.timeout = 30000; // 30 seconds timeout\n// Handle self-signed certificates for local development\nif ( true && window.location.protocol === 'https:' && _config__WEBPACK_IMPORTED_MODULE_0__.Config.ADMIN_BASE_URL.includes('localhost')) {\n    axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].defaults.httpsAgent = {\n        rejectUnauthorized: false\n    };\n}\nconst Config = {\n    ADMIN_BASE_URL: _config__WEBPACK_IMPORTED_MODULE_0__.Config.ADMIN_BASE_URL,\n    API_VERSION: 'v1',\n    DYNAMIC_METHOD_SUB_URL: 'api/v1/dynamic/dataoperation/',\n    END_POINT_NAMES: {\n        GET_CATEGORIES_LIST: 'get-categories-list',\n        SIGNUP_USER: 'signup-user',\n        GET_HOME_SCREEN_BANNER: 'get-home-screen-banner',\n        GET_RECENT_PRODUCTS: 'get-recents-products-list',\n        GET_POPULAR_PRODUCTS: 'get-popular-products-list',\n        GET_HOT_DEAL_PRODUCTS: 'get-hot-deal-products',\n        GET_CAMPAIGNS_LIST: 'get-web-campaign-list',\n        GET_PRODUCTS_LIST: 'get-products-list',\n        GET_ALL_PRODUCTS: 'api/v1/products/get-all-products',\n        GET_MANUFACTURERS_LIST: 'get-manufacturers-list',\n        GET_TAGS_LIST: 'get-tags-list',\n        GET_CURRENCY_RATE: 'get-currency-rate',\n        ..._config__WEBPACK_IMPORTED_MODULE_0__.Config.END_POINT_NAMES\n    }\n};\nconst GetTokenForHeader = async ()=>{\n    // Implement token retrieval logic here\n    // For example, from localStorage or a secure storage\n    return localStorage.getItem('token') || null;\n};\nconst GetUserIdForHeader = async ()=>{\n    // Implement user ID retrieval logic here\n    return localStorage.getItem('userId') || null;\n};\nconst MakeApiCallAsync = async function(endPointName, methodSubURL, param, headers, methodType) {\n    let loading = arguments.length > 5 && arguments[5] !== void 0 ? arguments[5] : true;\n    try {\n        // Create a copy of headers to avoid modifying the original object\n        const updatedHeaders = {\n            ...headers\n        };\n        // Check if Authorization header already exists\n        if (!updatedHeaders.hasOwnProperty('Authorization')) {\n            // If not, try to add it using the token from GetTokenForHeader\n            const token = await GetTokenForHeader();\n            if (token) {\n                updatedHeaders['Authorization'] = 'Bearer ' + token;\n            }\n        }\n        // For backward compatibility, also add Token header if it doesn't exist\n        if (!updatedHeaders.hasOwnProperty('Token')) {\n            const token = await GetTokenForHeader();\n            updatedHeaders['Token'] = token !== null && token !== void 0 ? token : \"\";\n        }\n        // Add user id in header\n        if (!updatedHeaders.hasOwnProperty('UserID')) {\n            const UserID = await GetUserIdForHeader();\n            updatedHeaders['UserID'] = UserID !== null && UserID !== void 0 ? UserID : \"\";\n        }\n        // Always ensure proper content type headers are set\n        if (!updatedHeaders.hasOwnProperty('Accept')) {\n            updatedHeaders['Accept'] = 'application/json';\n        }\n        if (!updatedHeaders.hasOwnProperty('Content-Type')) {\n            updatedHeaders['Content-Type'] = 'application/json';\n        }\n        const URL = Config['ADMIN_BASE_URL'] + (methodSubURL === null || methodSubURL == undefined ? Config['DYNAMIC_METHOD_SUB_URL'] : methodSubURL) + endPointName;\n        methodType = methodType !== null && methodType !== void 0 ? methodType : \"POST\";\n        const axiosConfig = {\n            headers: updatedHeaders,\n            responseType: 'json',\n            timeout: 30000,\n            withCredentials: false // Disable sending cookies with cross-origin requests\n        };\n        if (methodType === 'POST') {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(URL, param, axiosConfig);\n            return response;\n        } else if (methodType == 'GET') {\n            axiosConfig.params = param; // For GET requests, params should be used\n            const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(URL, axiosConfig);\n            return response;\n        } else {\n            // Return a default error response for unsupported method types\n            return {\n                data: {\n                    errorMessage: \"Unsupported method type: \".concat(methodType),\n                    status: 'method_not_supported'\n                }\n            };\n        }\n    } catch (error) {\n        console.error('API call failed:', error);\n        // Return a structured error response instead of throwing\n        // This allows components to handle errors more gracefully\n        // Create a response object with the ApiResponse interface\n        const errorResponse = {\n            data: {\n                errorMessage: 'An unexpected error occurred',\n                status: 'unknown_error'\n            }\n        };\n        // Type guard for axios error with response\n        if (error && typeof error === 'object' && 'response' in error && error.response) {\n            var _axiosError_response, _axiosError_response1;\n            // The request was made and the server responded with a status code\n            // that falls out of the range of 2xx\n            const axiosError = error;\n            const responseData = (_axiosError_response = axiosError.response) === null || _axiosError_response === void 0 ? void 0 : _axiosError_response.data;\n            errorResponse.data = {\n                errorMessage: (responseData === null || responseData === void 0 ? void 0 : responseData.errorMessage) || 'An error occurred while processing your request.',\n                status: (_axiosError_response1 = axiosError.response) === null || _axiosError_response1 === void 0 ? void 0 : _axiosError_response1.status\n            };\n        // Type guard for axios error with request but no response\n        } else if (error && typeof error === 'object' && 'request' in error) {\n            // The request was made but no response was received\n            // This is likely a network error, CORS issue, or server not running\n            const axiosError = error;\n            let networkErrorMessage = 'Network error: No response received from server.';\n            // Check if it's a CORS issue\n            if (axiosError.message && axiosError.message.includes('Network Error')) {\n                networkErrorMessage = 'Network Error: This may be due to CORS policy restrictions, server unavailability, or an invalid SSL certificate. Please check that:';\n                networkErrorMessage += '\\n1. The server is running and accessible';\n                networkErrorMessage += '\\n2. The URL is correct: ' + Config.ADMIN_BASE_URL;\n                networkErrorMessage += '\\n3. CORS is properly configured on the server';\n                networkErrorMessage += '\\n4. If using HTTPS, the SSL certificate is valid';\n            }\n            errorResponse.data = {\n                errorMessage: networkErrorMessage,\n                status: 'network_error'\n            };\n        } else {\n            // Something happened in setting up the request that triggered an Error\n            // Type guard for standard Error object\n            const errorMessage = error && typeof error === 'object' && 'message' in error ? error.message : 'An unexpected error occurred';\n            errorResponse.data = {\n                errorMessage,\n                status: 'request_error'\n            };\n        }\n        return errorResponse;\n    }\n};\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api-helper.ts\n"));

/***/ })

});