"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3414],{2523:(e,t,r)=>{r.d(t,{T:()=>o});let o={ADMIN_BASE_URL:"https://admin.codemedicalapps.com/",DYNAMIC_METHOD_SUB_URL:"api/",COMMON_CONTROLLER_SUB_URL:"api/common/",END_POINT_NAMES:{DOWNLOAD_DIGITAL_FILE:"downloadDigitalFile",GET_COUPON_CODE_DISCOUNT:"get-coupon-code-discount-value/calculate-coupon-discount"}}},2604:(e,t,r)=>{r.d(t,{U:()=>i,Y:()=>c});var o=r(5155),a=r(2115),n=r(2862);let s=(0,a.createContext)(void 0);function i(e){let{children:t}=e,[r,i]=(0,a.useState)(null),[c,u]=(0,a.useState)(!1),l=async(e,t,r)=>{if(!e.trim())return{valid:!1,message:"Please enter a coupon code",discount:0};u(!0);try{var o;let a=(null==r?void 0:r.map(e=>({ProductId:e.id,ProductName:e.name,Price:e.adjustedPrice||e.price,Quantity:e.quantity,IsDiscountAllowed:!0})))||[],s=JSON.stringify(a),c={requestParameters:{CouponCode:e.toUpperCase(),cartJsonData:s}},u=await (0,n.MakeApiCallAsync)(n.TS.END_POINT_NAMES.GET_COUPON_CODE_DISCOUNT,n.TS.COMMON_CONTROLLER_SUB_URL,c,{"Content-Type":"application/json",Accept:"application/json"},"POST");if(u&&u.data&&!u.data.errorMessage){let r;if((r="string"==typeof u.data.data?JSON.parse(u.data.data):u.data.data)&&r.DiscountValueAfterCouponAppliedWithQuantity>0){let t=r.DiscountValueAfterCouponAppliedWithQuantity,o=2===r.DiscountValueType?"percentage":"fixed",a={code:e.toUpperCase(),discount:t,type:o};return i(a),{valid:!0,message:"Coupon applied successfully!",discount:t}}if(r&&Array.isArray(r)&&r.length>0){let o=r[0];if(o&&o.DiscountValue>0){let r=0;if(1===o.DiscountValueType?r=o.DiscountValue:2===o.DiscountValueType&&(r=o.DiscountValue*t/100),r>0){let t={code:e.toUpperCase(),discount:r,type:2===o.DiscountValueType?"percentage":"fixed"};return i(t),{valid:!0,message:"Coupon applied successfully!",discount:r}}}}return{valid:!1,message:"Invalid coupon code or coupon not applicable to your cart",discount:0}}return{valid:!1,message:(null===(o=u.data)||void 0===o?void 0:o.errorMessage)||"Failed to validate coupon",discount:0}}catch(e){return console.error("Coupon validation error:",e),{valid:!1,message:"Error validating coupon. Please try again.",discount:0}}finally{u(!1)}};return(0,o.jsx)(s.Provider,{value:{appliedCoupon:r,validateCoupon:l,clearCoupon:()=>{i(null)},isLoading:c},children:t})}function c(){let e=(0,a.useContext)(s);if(void 0===e)throw Error("useCoupon must be used within a CouponProvider");return e}},2862:(e,t,r)=>{r.d(t,{$g:()=>d,MakeApiCallAsync:()=>c,TS:()=>n,XX:()=>l,k6:()=>u});var o=r(2651),a=r(2523);o.A.defaults.timeout=3e4,"https:"===window.location.protocol&&a.T.ADMIN_BASE_URL.includes("localhost")&&(o.A.defaults.httpsAgent={rejectUnauthorized:!1});let n={ADMIN_BASE_URL:a.T.ADMIN_BASE_URL,API_VERSION:"v1",DYNAMIC_METHOD_SUB_URL:"api/v1/dynamic/dataoperation/",END_POINT_NAMES:{...a.T.END_POINT_NAMES,GET_CATEGORIES_LIST:"get-categories-list",SIGNUP_USER:"signup-user",GET_USER_LOGIN:"get-user-login",GET_USER_BY_PHONE:"get-user-by-phone",RESET_PASSWORD_BY_PHONE:"reset-password-by-phone",GET_HOME_SCREEN_BANNER:"get-home-screen-banner",GET_RECENT_PRODUCTS:"get-recents-products-list",GET_POPULAR_PRODUCTS:"get-popular-products-list",GET_HOT_DEAL_PRODUCTS:"get-hot-deal-products",GET_CAMPAIGNS_LIST:"get-web-campaign-list",GET_PRODUCTS_LIST:"get-products-list",GET_ALL_PRODUCTS:"api/v1/products/get-all-products",GET_MANUFACTURERS_LIST:"get-manufacturers-list",GET_TAGS_LIST:"get-tags-list",GET_CURRENCY_RATE:"get-currency-rate",GET_COUPON_CODE_DISCOUNT:"get-coupon-code-discount-value/calculate-coupon-discount"},COMMON_CONTROLLER_SUB_URL:"api/v1/common/"},s=async()=>localStorage.getItem("token")||null,i=async()=>localStorage.getItem("userId")||null,c=async function(e,t,r,a,c){arguments.length>5&&void 0!==arguments[5]&&arguments[5];try{let u={...a};if(!u.hasOwnProperty("Authorization")){let e=await s();e&&(u.Authorization="Bearer "+e)}if(!u.hasOwnProperty("Token")){let e=await s();u.Token=null!=e?e:""}if(!u.hasOwnProperty("UserID")){let e=await i();u.UserID=null!=e?e:""}u.hasOwnProperty("Accept")||(u.Accept="application/json"),u.hasOwnProperty("Content-Type")||(u["Content-Type"]="application/json");let l=n.ADMIN_BASE_URL+(null===t||void 0==t?n.DYNAMIC_METHOD_SUB_URL:t)+e;c=null!=c?c:"POST";let d={headers:u,responseType:"json",timeout:3e4,withCredentials:!1};if("POST"===c)return await o.A.post(l,r,d);if("GET"==c)return d.params=r,await o.A.get(l,d);return{data:{errorMessage:"Unsupported method type: ".concat(c),status:"method_not_supported"}}}catch(t){console.error("API call failed:",t);let e={data:{errorMessage:"An unexpected error occurred",status:"unknown_error"}};if(t&&"object"==typeof t&&"response"in t&&t.response){var u,l;let r=null===(u=t.response)||void 0===u?void 0:u.data;e.data={errorMessage:(null==r?void 0:r.errorMessage)||"An error occurred while processing your request.",status:null===(l=t.response)||void 0===l?void 0:l.status}}else if(t&&"object"==typeof t&&"request"in t){let r="Network error: No response received from server.";t.message&&t.message.includes("Network Error")&&(r="Network Error: This may be due to CORS policy restrictions, server unavailability, or an invalid SSL certificate. Please check that:\n1. The server is running and accessible\n2. The URL is correct: "+n.ADMIN_BASE_URL+"\n3. CORS is properly configured on the server\n4. If using HTTPS, the SSL certificate is valid"),e.data={errorMessage:r,status:"network_error"}}else{let r=t&&"object"==typeof t&&"message"in t?t.message:"An unexpected error occurred";e.data={errorMessage:r,status:"request_error"}}return e}},u=async()=>{try{let e=await c("getrate","api/v1/common/",{},{},"GET");if(e&&e.data&&!e.data.errorMessage)return parseInt(e.data)||1500;return 1500}catch(e){return console.error("Error fetching currency rate:",e),1500}},l=(e,t)=>Math.round(e*t),d=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD";return"IQD"===t?"".concat(e.toLocaleString()," IQD"):"$".concat(e.toFixed(2))}},4085:(e,t,r)=>{r.d(t,{$:()=>u,r:()=>c});var o=r(5155),a=r(2115),n=r(2317),s=r(1027),i=r(9602);let c=(0,s.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-red-500 text-destructive-foreground hover:bg-red-600",outline:"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),u=a.forwardRef((e,t)=>{let{className:r,variant:a,size:s,asChild:u=!1,...l}=e,d=u?n.DX:"button";return(0,o.jsx)(d,{className:(0,i.cn)(c({variant:a,size:s,className:r})),ref:t,...l})});u.displayName="Button"},5943:(e,t,r)=>{r.d(t,{B:()=>i,H:()=>c});var o=r(5155),a=r(2115),n=r(2862);let s=(0,a.createContext)(void 0);function i(e){let{children:t}=e,[r,i]=(0,a.useState)(1500),[c,u]=(0,a.useState)(!0),l=async()=>{u(!0);try{let e=await (0,n.k6)();i(e)}catch(e){console.error("Failed to load currency rate:",e)}finally{u(!1)}},d=async()=>{await l()};return(0,a.useEffect)(()=>{l()},[]),(0,o.jsx)(s.Provider,{value:{rate:r,isLoading:c,convertToIQD:e=>(0,n.XX)(e,r),formatUSD:e=>(0,n.$g)(e,"USD"),formatIQD:e=>(0,n.$g)(e,"IQD"),refreshRate:d},children:t})}function c(){let e=(0,a.useContext)(s);if(void 0===e)throw Error("useCurrency must be used within a CurrencyProvider");return e}},7110:(e,t,r)=>{r.d(t,{Z:()=>i,t:()=>c});var o=r(5155),a=r(2115);let n={en:{menu:"Menu",newsletter:"Newsletter",enterEmail:"Enter your email",newsletterDisclaimer:"Subscribe to our newsletter to receive updates and exclusive offers",popularCategories:"Popular Categories!",settings:"Settings",language:"Language",theme:"Theme",color:"Color",save:"Save",cancel:"Cancel",close:"Close",phone:"***************",phonenumber:"Phone Number",email:"<EMAIL>",liveChat:"Live Chat",welcome:"Welcome",logout:"Logout",login:"Login",signup:"Sign Up",signUp:"Sign Up",category:"Category",categories:"Categories",filters:"Filters",clearAll:"Clear All",products:"Products",loadingCategories:"Loading categories...",home:"Home",todayDeals:"Today's Deals",followUs:"Follow Us",aboutUs:"About Us",contactUs:"Contact Us",tryAgain:"Try Again",noProductsFound:"No products found",allProducts:"All Products",quickLinks:"Quick Links",about:"About Us",contact:"Contact Us",hotDeals:"Hot Deals",customerArea:"Customer Area",myAccount:"My Account",orders:"Orders",cart:"Cart",wishlist:"Wishlist",paymentMethods:"Payment Methods",location:"Location",callUs:"Call Us",emailUs:"Email Us",subscribe:"Subscribe",name:"Name",subject:"Subject",message:"Message",sendMessage:"Send Message",sending:"Sending...",messageSent:"Message sent successfully!",messageError:"Failed to send message",contactInfo:"Contact Information",address:"Address",findUs:"Find Us"},ar:{menu:"القائمة",popularCategories:"الفئات الشائعة!",settings:"الإعدادات",language:"اللغة",theme:"المظهر",color:"اللون",save:"حفظ",cancel:"إلغاء",close:"إغلاق",phone:"***************",phonenumber:"رقم الهاتف",email:"<EMAIL>",liveChat:"محادثة مباشرة",welcome:"مرحباً",logout:"تسجيل الخروج",login:"تسجيل الدخول",signup:"تسجيل جديد",signUp:"تسجيل جديد",category:"الفئة",categories:"الفئات",products:"المنتجات",loadingCategories:"جاري تحميل الفئات...",filters:"المرشحات",clearAll:"مسح الكل",home:"الرئيسية",todayDeals:"عروض اليوم",followUs:"تابعنا",aboutUs:"من نحن",contactUs:"اتصل بنا",tryAgain:"حاول مرة أخرى",noProductsFound:"لم يتم العثور على منتجات",allProducts:"جميع المنتجات",quickLinks:"روابط سريعة",about:"من نحن",contact:"اتصل بنا",hotDeals:"عروض ساخنة",customerArea:"منطقة العملاء",myAccount:"حسابي",orders:"الطلبات",cart:"السلة",wishlist:"المفضلة",paymentMethods:"طرق الدفع",location:"الموقع",callUs:"اتصل بنا",emailUs:"راسلنا",subscribe:"اشترك",name:"الاسم",subject:"الموضوع",message:"الرسالة",sendMessage:"إرسال الرسالة",sending:"جاري الإرسال...",messageSent:"تم إرسال الرسالة بنجاح!",messageError:"فشل في إرسال الرسالة",contactInfo:"معلومات الاتصال",address:"العنوان",findUs:"موقعنا"}},s=(0,a.createContext)(void 0);function i(e){let{children:t}=e,[r,i]=(0,a.useState)("light"),[c,u]=(0,a.useState)("en"),[l,d]=(0,a.useState)("#0074b2");return(0,a.useEffect)(()=>{document.documentElement.style.setProperty("--primary",l),document.documentElement.style.setProperty("--primary-foreground","#ffffff")},[]),(0,o.jsx)(s.Provider,{value:{theme:r,language:c,primaryColor:l,toggleTheme:()=>{i("light"===r?"dark":"light")},setLanguage:e=>{u(e),document.documentElement.dir="ar"===e?"rtl":"ltr"},setPrimaryColor:e=>{d(e)},t:e=>(function(e,t){let r=n[t];return e in r?r[e]:"en"!==t&&e in n.en?n.en[e]:e})(e,c)},children:t})}function c(){let e=(0,a.useContext)(s);if(void 0===e)throw Error("useSettings must be used within a SettingsProvider");return e}},8936:(e,t,r)=>{r.d(t,{_:()=>i,e:()=>s});var o=r(5155),a=r(2115);let n=(0,a.createContext)(void 0);function s(e){let{children:t}=e,[r,s]=(0,a.useState)([]),[i,c]=(0,a.useState)(!1);(0,a.useEffect)(()=>{let e=localStorage.getItem("cart");if(e)try{s(JSON.parse(e))}catch(e){console.error("Failed to parse cart from localStorage:",e)}c(!0)},[]),(0,a.useEffect)(()=>{localStorage.setItem("cart",JSON.stringify(r))},[r]);let u=e=>{s(t=>t.filter(t=>t.id!==e))},l=r.reduce((e,t)=>e+t.quantity,0);(0,a.useEffect)(()=>{localStorage.setItem("cart",JSON.stringify(r))},[r]);let d=r.reduce((e,t)=>e+(t.discountPrice?Math.min(t.discountPrice,t.adjustedPrice):t.adjustedPrice)*t.quantity,0),p=r.reduce((e,t)=>e+(t.adjustedIqdPrice||t.iqdPrice||0)*t.quantity,0);return(0,o.jsx)(n.Provider,{value:{items:r,addToCart:function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],o=arguments.length>3?arguments[3]:void 0,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1500;s(n=>{let s=e.price,i=o||Math.round(e.price*a),c=i;r.forEach(t=>{if(t.PriceAdjustment&&t.PriceAdjustmentType){let r=e.originalPrice||e.price;switch(t.PriceAdjustmentType){case 1:s+=t.PriceAdjustment,c+=Math.round(t.PriceAdjustment*a);break;case 2:let o=r*t.PriceAdjustment/100;s+=o,c+=Math.round(o*a)}}});let u=n.findIndex(t=>{var o;return t.id===e.id&&JSON.stringify(null===(o=t.attributes)||void 0===o?void 0:o.sort((e,t)=>e.ProductAttributeID-t.ProductAttributeID))===JSON.stringify(null==r?void 0:r.sort((e,t)=>e.ProductAttributeID-t.ProductAttributeID))});if(!(u>=0))return[...n,{...e,iqdPrice:i,adjustedIqdPrice:Math.max(0,c),quantity:t,attributes:r,adjustedPrice:Math.max(0,s),originalPrice:e.originalPrice}];{let e=[...n];return e[u].quantity+=t,e}})},removeFromCart:u,updateQuantity:(e,t)=>{if(t<=0){u(e);return}s(r=>r.map(r=>r.id===e?{...r,quantity:t}:r))},clearCart:()=>{s([])},totalItems:l,subtotal:d,subtotalIQD:p,total:d,totalIQD:p,isHydrated:i},children:t})}function i(){let e=(0,a.useContext)(n);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e}},9602:(e,t,r)=>{r.d(t,{cn:()=>n});var o=r(3463),a=r(9795);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,o.$)(t))}}}]);