(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1720],{1027:(e,r,t)=>{"use strict";t.d(r,{F:()=>i});var a=t(3463);let s=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,o=a.$,i=(e,r)=>t=>{var a;if((null==r?void 0:r.variants)==null)return o(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:i,defaultVariants:l}=r,n=Object.keys(i).map(e=>{let r=null==t?void 0:t[e],a=null==l?void 0:l[e];if(null===r)return null;let o=s(r)||s(a);return i[e][o]}),c=t&&Object.entries(t).reduce((e,r)=>{let[t,a]=r;return void 0===a||(e[t]=a),e},{});return o(e,n,null==r?void 0:null===(a=r.compoundVariants)||void 0===a?void 0:a.reduce((e,r)=>{let{class:t,className:a,...s}=r;return Object.entries(s).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...l,...c}[r]):({...l,...c})[r]===t})?[...e,t,a]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}},2598:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(7401).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},4085:(e,r,t)=>{"use strict";t.d(r,{$:()=>c,r:()=>n});var a=t(5155),s=t(2115),o=t(2317),i=t(1027),l=t(9602);let n=(0,i.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-red-500 text-destructive-foreground hover:bg-red-600",outline:"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=s.forwardRef((e,r)=>{let{className:t,variant:s,size:i,asChild:c=!1,...d}=e,u=c?o.DX:"button";return(0,a.jsx)(u,{className:(0,l.cn)(n({variant:s,size:i,className:t})),ref:r,...d})});c.displayName="Button"},4858:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(7401).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},5007:(e,r,t)=>{"use strict";t.d(r,{Wu:()=>l,Zp:()=>i,wL:()=>n});var a=t(5155),s=t(2115),o=t(9602);let i=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,o.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...s})});i.displayName="Card",s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",t),...s})}).displayName="CardHeader",s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("h3",{ref:r,className:(0,o.cn)("text-2xl font-semibold leading-none tracking-tight",t),...s})}).displayName="CardTitle",s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("p",{ref:r,className:(0,o.cn)("text-sm text-muted-foreground",t),...s})}).displayName="CardDescription";let l=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,o.cn)("p-6 pt-0",t),...s})});l.displayName="CardContent";let n=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,o.cn)("flex items-center p-6 pt-0",t),...s})});n.displayName="CardFooter"},5686:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(7401).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},5882:(e,r,t)=>{Promise.resolve().then(t.bind(t,8896))},6967:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(7401).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},7110:(e,r,t)=>{"use strict";t.d(r,{Z:()=>l,t:()=>n});var a=t(5155),s=t(2115);let o={en:{menu:"Menu",newsletter:"Newsletter",enterEmail:"Enter your email",newsletterDisclaimer:"Subscribe to our newsletter to receive updates and exclusive offers",popularCategories:"Popular Categories!",settings:"Settings",language:"Language",theme:"Theme",color:"Color",save:"Save",cancel:"Cancel",close:"Close",phone:"***************",phonenumber:"Phone Number",email:"<EMAIL>",liveChat:"Live Chat",welcome:"Welcome",logout:"Logout",login:"Login",signup:"Sign Up",signUp:"Sign Up",category:"Category",categories:"Categories",filters:"Filters",clearAll:"Clear All",products:"Products",loadingCategories:"Loading categories...",home:"Home",todayDeals:"Today's Deals",followUs:"Follow Us",aboutUs:"About Us",contactUs:"Contact Us",tryAgain:"Try Again",noProductsFound:"No products found",allProducts:"All Products",quickLinks:"Quick Links",about:"About Us",contact:"Contact Us",hotDeals:"Hot Deals",customerArea:"Customer Area",myAccount:"My Account",orders:"Orders",cart:"Cart",wishlist:"Wishlist",paymentMethods:"Payment Methods",location:"Location",callUs:"Call Us",emailUs:"Email Us",subscribe:"Subscribe",name:"Name",subject:"Subject",message:"Message",sendMessage:"Send Message",sending:"Sending...",messageSent:"Message sent successfully!",messageError:"Failed to send message",contactInfo:"Contact Information",address:"Address",findUs:"Find Us"},ar:{menu:"القائمة",popularCategories:"الفئات الشائعة!",settings:"الإعدادات",language:"اللغة",theme:"المظهر",color:"اللون",save:"حفظ",cancel:"إلغاء",close:"إغلاق",phone:"***************",phonenumber:"رقم الهاتف",email:"<EMAIL>",liveChat:"محادثة مباشرة",welcome:"مرحباً",logout:"تسجيل الخروج",login:"تسجيل الدخول",signup:"تسجيل جديد",signUp:"تسجيل جديد",category:"الفئة",categories:"الفئات",products:"المنتجات",loadingCategories:"جاري تحميل الفئات...",filters:"المرشحات",clearAll:"مسح الكل",home:"الرئيسية",todayDeals:"عروض اليوم",followUs:"تابعنا",aboutUs:"من نحن",contactUs:"اتصل بنا",tryAgain:"حاول مرة أخرى",noProductsFound:"لم يتم العثور على منتجات",allProducts:"جميع المنتجات",quickLinks:"روابط سريعة",about:"من نحن",contact:"اتصل بنا",hotDeals:"عروض ساخنة",customerArea:"منطقة العملاء",myAccount:"حسابي",orders:"الطلبات",cart:"السلة",wishlist:"المفضلة",paymentMethods:"طرق الدفع",location:"الموقع",callUs:"اتصل بنا",emailUs:"راسلنا",subscribe:"اشترك",name:"الاسم",subject:"الموضوع",message:"الرسالة",sendMessage:"إرسال الرسالة",sending:"جاري الإرسال...",messageSent:"تم إرسال الرسالة بنجاح!",messageError:"فشل في إرسال الرسالة",contactInfo:"معلومات الاتصال",address:"العنوان",findUs:"موقعنا"}},i=(0,s.createContext)(void 0);function l(e){let{children:r}=e,[t,l]=(0,s.useState)("light"),[n,c]=(0,s.useState)("en"),[d,u]=(0,s.useState)("#0074b2");return(0,s.useEffect)(()=>{document.documentElement.style.setProperty("--primary",d),document.documentElement.style.setProperty("--primary-foreground","#ffffff")},[]),(0,a.jsx)(i.Provider,{value:{theme:t,language:n,primaryColor:d,toggleTheme:()=>{l("light"===t?"dark":"light")},setLanguage:e=>{c(e),document.documentElement.dir="ar"===e?"rtl":"ltr"},setPrimaryColor:e=>{u(e)},t:e=>(function(e,r){let t=o[r];return e in t?t[e]:"en"!==r&&e in o.en?o.en[e]:e})(e,n)},children:r})}function n(){let e=(0,s.useContext)(i);if(void 0===e)throw Error("useSettings must be used within a SettingsProvider");return e}},7401:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});var a=t(2115);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&t.indexOf(e)===r).join(" ")};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,a.forwardRef)((e,r)=>{let{color:t="currentColor",size:s=24,strokeWidth:l=2,absoluteStrokeWidth:n,className:c="",children:d,iconNode:u,...m}=e;return(0,a.createElement)("svg",{ref:r,...i,width:s,height:s,stroke:t,strokeWidth:n?24*Number(l)/Number(s):l,className:o("lucide",c),...m},[...u.map(e=>{let[r,t]=e;return(0,a.createElement)(r,t)}),...Array.isArray(d)?d:[d]])}),n=(e,r)=>{let t=(0,a.forwardRef)((t,i)=>{let{className:n,...c}=t;return(0,a.createElement)(l,{ref:i,iconNode:r,className:o("lucide-".concat(s(e)),n),...c})});return t.displayName="".concat(e),t}},8474:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(7401).A)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},8896:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>j});var a=t(5155),s=t(2115),o=t(9426),i=t(5007),l=t(4085),n=t(8173),c=t.n(n),d=t(7110),u=t(8936),m=t(9355);let g=(0,t(7401).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);var h=t(5686),f=t(2598),p=t(8474),y=t(591),v=t(6967),x=t(2651),P=t(814);let b=e=>{if(!e)return[];try{if(e.startsWith("[")||e.startsWith("{")){let r=JSON.parse(e);if(Array.isArray(r))return r;if(r&&"object"==typeof r)return[r]}let r=e.trim();if(r)return[{AttachmentName:r.split("/").pop()||"image",AttachmentURL:r,IsPrimary:!0}]}catch(e){console.error("Error parsing product images:",e)}return[]},N=e=>{if(!e||"string"!=typeof e)return console.warn("Invalid attachment URL:",e),"/placeholder-image.jpg";try{if(e.startsWith("http"))return e;if(e.startsWith("/uploads")||e.startsWith("/images")){let r="https://admin.codemedicalapps.com/".replace(/\/$/,""),t=e.startsWith("/")?e:"/".concat(e),a="".concat(r).concat(t);return console.log("Constructed image URL:",a),a}return console.log("Using relative image path:",e),e}catch(r){return console.error("Error constructing image URL:",r,"URL:",e),"/placeholder-image.jpg"}};function j(){let{t:e}=(0,d.t)(),r=(0,u._)(),{wishlistItems:t,removeFromWishlist:n}=(0,m.n)(),[j,w]=(0,s.useState)([]),[A,I]=(0,s.useState)(!1),k=async e=>{if(!e||0===e.length){w([]);return}let r=e.filter(e=>e&&!isNaN(Number(e)));if(0===r.length){w([]);return}I(!0);try{console.log("Fetching products for IDs:",r);let e=localStorage.getItem("cachedProducts");if(e)try{let t=JSON.parse(e).filter(e=>r.includes(e.ProductID||e.ProductId||e.id||0));if(t.length>0){console.log("Using cached products:",t.length);let e=t.map(e=>{let r="";try{if(e.ProductImagesJson&&"string"==typeof e.ProductImagesJson){let t=b(e.ProductImagesJson),a=t.find(e=>e.IsPrimary)||t[0];a&&(r=N(a.AttachmentURL))}!r&&e.ImagePath&&(r=N(e.ImagePath))}catch(e){console.error("Error processing cached product images:",e)}return{id:e.ProductID||e.ProductId||e.id||0,name:e.ProductName||e.Name||"Unnamed Product",price:e.Price||e.ProductPrice||0,originalPrice:e.OldPrice||e.OriginalPrice||e.Price||e.ProductPrice||0,image:r||"/placeholder-image.jpg",inStock:(e.StockQuantity||e.Quantity||0)>0}});w(e);return}}catch(e){console.error("Error reading from cache:",e)}console.log("Fetching products from API...");let t=r.map(async e=>{try{let r=await x.A.post("/api/product-detail",{requestParameters:{ProductId:e,recordValueJson:"[]"}});if(r.data&&r.data.data){let e=JSON.parse(r.data.data);return Array.isArray(e)?e[0]:e}return null}catch(r){return console.error("Error fetching product ".concat(e,":"),r),null}}),a=(await Promise.all(t)).filter(e=>null!==e);if(console.log("Fetched products:",a.length),console.log("Total products extracted from response:",a.length),0===a.length){console.warn("No products found in the API response."),w([]);return}let s=a.map(e=>{console.log("Processing product:",{id:e.ProductId||e.id,name:e.ProductName||e.Name,images:e.ProductImagesJson,imagePath:e.ImagePath,imageUrl:e.ImageUrl});let r="";try{if(e.ProductImagesJson)try{let t=b("string"==typeof e.ProductImagesJson?e.ProductImagesJson:JSON.stringify(e.ProductImagesJson));console.log("Parsed images:",t);let a=Array.isArray(t)&&t.length>0?t.find(e=>e.IsPrimary)||t[0]:t;if(a){let e=a.AttachmentURL||a.url||a;console.log("Using primary image:",e),r=N(e)}}catch(e){console.error("Error parsing product images:",e)}!r&&e.ImagePath&&(console.log("Falling back to ImagePath:",e.ImagePath),r=N(e.ImagePath)),!r&&e.ImageUrl&&(console.log("Falling back to ImageUrl:",e.ImageUrl),r=N(e.ImageUrl)),r||(console.warn("No valid image found for product:",e.ProductId||e.id,e),r="/placeholder-image.jpg"),console.log("Final image URL for product:",e.ProductId||e.id,r)}catch(t){console.error("Error processing product images:",t,"for product:",e.ProductId||e.id),r="/placeholder-image.jpg"}return{id:e.ProductId||e.ProductID||e.id,name:e.ProductName||e.Name||"Unnamed Product",price:e.Price||e.ProductPrice||0,originalPrice:e.OldPrice||e.OriginalPrice||e.Price||e.ProductPrice||0,image:r||"/placeholder-image.jpg",inStock:(e.StockQuantity||e.Quantity||0)>0}});console.log("Display items prepared:",s.length),w(s);try{localStorage.setItem("cachedProducts",JSON.stringify(a))}catch(e){console.error("Error caching products:",e)}}catch(m){var t,a,s,o,i,l,n,c,d,u;console.error("Error in fetchProductDetails:",m);let e="An unknown error occurred";m instanceof Error?e=m.message:m&&"object"==typeof m&&"message"in m&&(e=String(m.message)),m&&"object"==typeof m&&console.error("Error details:",{message:e,response:(null==m?void 0:null===(i=m.response)||void 0===i?void 0:i.data)||"No response data",status:null==m?void 0:null===(l=m.response)||void 0===l?void 0:l.status,statusText:null==m?void 0:null===(n=m.response)||void 0===n?void 0:n.statusText,config:{url:null==m?void 0:null===(c=m.config)||void 0===c?void 0:c.url,method:null==m?void 0:null===(d=m.config)||void 0===d?void 0:d.method,params:null==m?void 0:null===(u=m.config)||void 0===u?void 0:u.params}});let r=m&&"object"==typeof m&&"isAxiosError"in m&&(null===(a=m.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.error)?null===(o=m.response)||void 0===o?void 0:null===(s=o.data)||void 0===s?void 0:s.error:e;P.oR.error("Failed to load wishlist: "+(r||"Unknown error")),w([])}finally{I(!1)}};(0,s.useEffect)(()=>{k(t)},[t]);let C=e=>{n(e),P.oR.success("Product removed from wishlist")};return A?(0,a.jsxs)("div",{className:"container mx-auto py-12 flex flex-col items-center justify-center min-h-[50vh]",children:[(0,a.jsx)(g,{className:"h-12 w-12 animate-spin text-primary mb-4"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Loading your wishlist..."})]}):(0,a.jsxs)("div",{className:"container mx-auto py-6 px-4",children:[(0,a.jsx)(o.Qp,{className:"mb-6",children:(0,a.jsxs)(o.AB,{children:[(0,a.jsx)(o.J5,{children:(0,a.jsx)(o.w1,{asChild:!0,children:(0,a.jsx)(c(),{href:"/",children:"Home"})})}),(0,a.jsx)(o.tH,{}),(0,a.jsx)(o.tJ,{children:"Wishlist"})]})}),(0,a.jsx)("h1",{className:"text-3xl font-bold mb-8",children:"Your Wishlist"}),j.length>0?(0,a.jsx)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",children:j.map(e=>(0,a.jsxs)(i.Zp,{className:"overflow-hidden",children:[(0,a.jsxs)("div",{className:"relative aspect-square",children:[(0,a.jsx)("img",{src:e.image,alt:e.name,className:"w-full h-full object-cover",onError:e=>{let r=e.target;r.onerror=null,r.src="/placeholder-image.jpg"}}),(0,a.jsx)(l.$,{variant:"ghost",size:"icon",className:"absolute top-2 right-2 rounded-full bg-background/80 backdrop-blur-sm hover:bg-background/60",onClick:()=>C(e.id),children:(0,a.jsx)(h.A,{className:"h-4 w-4"})})]}),(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsx)("h3",{className:"font-medium text-lg mb-2 line-clamp-2",children:e.name}),(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,a.jsxs)("span",{className:"text-lg font-bold",children:["$",e.price.toFixed(2)]}),e.originalPrice&&e.originalPrice>e.price&&(0,a.jsxs)("span",{className:"text-sm text-muted-foreground line-through",children:["$",e.originalPrice.toFixed(2)]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(l.$,{variant:"outline",size:"sm",className:"flex-1",asChild:!0,children:(0,a.jsxs)(c(),{href:"/product/".concat(e.id),children:[(0,a.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"View"]})}),(0,a.jsxs)(l.$,{size:"sm",className:"flex-1",disabled:!e.inStock,onClick:()=>{r.addToCart({id:e.id,name:e.name,price:e.price,discountPrice:e.originalPrice&&e.originalPrice>e.price?e.price:void 0,originalPrice:e.originalPrice||e.price,image:e.image},1,[],void 0),P.oR.success("Added ".concat(e.name," to cart"))},children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-2"}),e.inStock?"Add to Cart":"Out of Stock"]})]})]})]},e.id))}):(0,a.jsxs)(i.Zp,{className:"p-8 text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(y.A,{className:"h-8 w-8 text-muted-foreground"})}),(0,a.jsx)("h3",{className:"text-lg font-medium mb-2",children:"Your wishlist is empty"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-6",children:"You haven't added any products to your wishlist yet."}),(0,a.jsx)(l.$,{asChild:!0,children:(0,a.jsxs)(c(),{href:"/products",children:["Continue Shopping",(0,a.jsx)(v.A,{className:"ml-2 h-4 w-4"})]})})]})]})}},8936:(e,r,t)=>{"use strict";t.d(r,{_:()=>l,e:()=>i});var a=t(5155),s=t(2115);let o=(0,s.createContext)(void 0);function i(e){let{children:r}=e,[t,i]=(0,s.useState)([]),[l,n]=(0,s.useState)(!1);(0,s.useEffect)(()=>{let e=localStorage.getItem("cart");if(e)try{i(JSON.parse(e))}catch(e){console.error("Failed to parse cart from localStorage:",e)}n(!0)},[]),(0,s.useEffect)(()=>{localStorage.setItem("cart",JSON.stringify(t))},[t]);let c=e=>{i(r=>r.filter(r=>r.id!==e))},d=t.reduce((e,r)=>e+r.quantity,0);(0,s.useEffect)(()=>{localStorage.setItem("cart",JSON.stringify(t))},[t]);let u=t.reduce((e,r)=>e+(r.discountPrice?Math.min(r.discountPrice,r.adjustedPrice):r.adjustedPrice)*r.quantity,0),m=t.reduce((e,r)=>e+(r.adjustedIqdPrice||r.iqdPrice||0)*r.quantity,0);return(0,a.jsx)(o.Provider,{value:{items:t,addToCart:function(e,r){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],a=arguments.length>3?arguments[3]:void 0,s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1500;i(o=>{let i=e.price,l=a||Math.round(e.price*s),n=l;t.forEach(r=>{if(r.PriceAdjustment&&r.PriceAdjustmentType){let t=e.originalPrice||e.price;switch(r.PriceAdjustmentType){case 1:i+=r.PriceAdjustment,n+=Math.round(r.PriceAdjustment*s);break;case 2:let a=t*r.PriceAdjustment/100;i+=a,n+=Math.round(a*s)}}});let c=o.findIndex(r=>{var a;return r.id===e.id&&JSON.stringify(null===(a=r.attributes)||void 0===a?void 0:a.sort((e,r)=>e.ProductAttributeID-r.ProductAttributeID))===JSON.stringify(null==t?void 0:t.sort((e,r)=>e.ProductAttributeID-r.ProductAttributeID))});if(!(c>=0))return[...o,{...e,iqdPrice:l,adjustedIqdPrice:Math.max(0,n),quantity:r,attributes:t,adjustedPrice:Math.max(0,i),originalPrice:e.originalPrice}];{let e=[...o];return e[c].quantity+=r,e}})},removeFromCart:c,updateQuantity:(e,r)=>{if(r<=0){c(e);return}i(t=>t.map(t=>t.id===e?{...t,quantity:r}:t))},clearCart:()=>{i([])},totalItems:d,subtotal:u,subtotalIQD:m,total:u,totalIQD:m,isHydrated:l},children:r})}function l(){let e=(0,s.useContext)(o);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e}},9355:(e,r,t)=>{"use strict";t.d(r,{Z:()=>i,n:()=>l});var a=t(5155),s=t(2115);let o=(0,s.createContext)(void 0);function i(e){let{children:r}=e,[t,i]=(0,s.useState)([]),[l,n]=(0,s.useState)(!1);return(0,s.useEffect)(()=>{let e=localStorage.getItem("wishlist");if(e)try{i(JSON.parse(e))}catch(e){console.error("Failed to parse wishlist from localStorage:",e)}n(!0)},[]),(0,s.useEffect)(()=>{localStorage.setItem("wishlist",JSON.stringify(t))},[t]),(0,a.jsx)(o.Provider,{value:{wishlistItems:t,addToWishlist:e=>{t.includes(e)||i([...t,e])},removeFromWishlist:e=>{i(t.filter(r=>r!==e))},isInWishlist:e=>t.includes(e),totalItems:t.length,isHydrated:l},children:r})}function l(){let e=(0,s.useContext)(o);if(void 0===e)throw Error("useWishlist must be used within a WishlistProvider");return e}},9426:(e,r,t)=>{"use strict";t.d(r,{AB:()=>c,J5:()=>d,Qp:()=>n,tH:()=>g,tJ:()=>m,w1:()=>u});var a=t(5155),s=t(2115),o=t(2317),i=t(6967),l=(t(4858),t(9602));let n=s.forwardRef((e,r)=>{let{...t}=e;return(0,a.jsx)("nav",{ref:r,"aria-label":"breadcrumb",...t})});n.displayName="Breadcrumb";let c=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("ol",{ref:r,className:(0,l.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",t),...s})});c.displayName="BreadcrumbList";let d=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("li",{ref:r,className:(0,l.cn)("inline-flex items-center gap-1.5",t),...s})});d.displayName="BreadcrumbItem";let u=s.forwardRef((e,r)=>{let{asChild:t,className:s,...i}=e,n=t?o.DX:"a";return(0,a.jsx)(n,{ref:r,className:(0,l.cn)("transition-colors hover:text-foreground",s),...i})});u.displayName="BreadcrumbLink";let m=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("span",{ref:r,role:"link","aria-disabled":"true","aria-current":"page",className:(0,l.cn)("font-normal text-foreground",t),...s})});m.displayName="BreadcrumbPage";let g=e=>{let{children:r,className:t,...s}=e;return(0,a.jsx)("li",{role:"presentation","aria-hidden":"true",className:(0,l.cn)("[&>svg]:size-3.5",t),...s,children:null!=r?r:(0,a.jsx)(i.A,{})})};g.displayName="BreadcrumbSeparator"},9602:(e,r,t)=>{"use strict";t.d(r,{cn:()=>o});var a=t(3463),s=t(9795);function o(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,s.QP)((0,a.$)(r))}}},e=>{var r=r=>e(e.s=r);e.O(0,[1345,2651,6764,8441,6587,7358],()=>r(5882)),_N_E=e.O()}]);