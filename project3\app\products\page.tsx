"use client"
import type React from "react"
import { useEffect, useState, Suspense, useCallback } from "react"
import { useSearchParams } from "next/navigation"
import { Filter, AlertCircle, Package, ChevronDown, ChevronUp } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { useToast } from "@/hooks/use-toast"
import ProductCard from "@/components/product-card"
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"

// Updated interface with new fields
interface Product {
  ProductId: number
  ProductName: string
  Price: number
  OldPrice?: number
  DiscountPrice?: number
  Rating: number
  ProductImageUrl?: string
  CategoryName: string
  StockQuantity: number
  TotalRecords?: number
  ProductTypeID?: number
  ProductTypeName?: string
  IQDPrice?: number
  IsDiscountAllowed?: boolean
  MarkAsNew?: boolean
  SellStartDatetimeUTC?: string
  SellEndDatetimeUTC?: string
  Attributes?: ProductAttribute[]
}

interface ProductAttribute {
  ProductAttributeID: number
  AttributeName: string
  DisplayName: string
  AttributeValueID: number
  AttributeValueText: string
}

interface AttributeFilter {
  attributeName: string
  displayName: string
  values: { id: number; text: string; count: number }[]
}

interface ProductType {
  producttypeID: number
  Name: string
}

function SearchParamsWrapper({
  children,
}: { children: (params: { searchTerm: string; categoryId: string; productTypeId: string }) => React.ReactNode }) {
  const searchParams = useSearchParams()
  const searchTerm = searchParams.get("search") || ""
  const categoryId = searchParams.get("category") || "all"
  const productTypeId = searchParams.get("productType") || "all"
  return <>{children({ searchTerm, categoryId, productTypeId })}</>
}

export default function ProductsPage() {
  return (
    <Suspense fallback={<ProductsPageSkeleton />}>
      <SearchParamsWrapper>
        {({ searchTerm, categoryId, productTypeId }) => (
          <ProductsContent
            initialSearchTerm={searchTerm}
            initialCategoryId={categoryId}
            initialProductTypeId={productTypeId}
          />
        )}
      </SearchParamsWrapper>
    </Suspense>
  )
}

function ProductsPageSkeleton() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="h-8 w-64 bg-gray-200 rounded mb-4 animate-pulse" />
      <div className="h-4 w-96 bg-gray-200 rounded mb-6 animate-pulse" />
      <div className="flex flex-col lg:flex-row gap-8">
        <div className="lg:w-1/4">
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="h-6 w-32 bg-gray-200 rounded mb-4 animate-pulse" />
            <div className="space-y-4">
              {Array(4)
                .fill(0)
                .map((_, i) => (
                  <div key={i} className="h-10 bg-gray-200 rounded animate-pulse" />
                ))}
            </div>
          </div>
        </div>
        <div className="lg:w-3/4">
          <div className="grid grid-cols-2 gap-4 sm:gap-6 sm:grid-cols-2 lg:grid-cols-3">
            {Array(12)
              .fill(0)
              .map((_, i) => (
                <div key={i} className="bg-white rounded-lg shadow overflow-hidden">
                  <div className="aspect-square bg-gray-200 animate-pulse" />
                  <div className="p-4 space-y-2">
                    <div className="h-4 bg-gray-200 rounded animate-pulse" />
                    <div className="h-4 w-3/4 bg-gray-200 rounded animate-pulse" />
                    <div className="h-6 w-1/3 bg-gray-200 rounded animate-pulse" />
                  </div>
                </div>
              ))}
          </div>
        </div>
      </div>
    </div>
  )
}

function ProductsContent({
  initialSearchTerm,
  initialCategoryId,
  initialProductTypeId,
}: {
  initialSearchTerm: string
  initialCategoryId: string
  initialProductTypeId: string
}) {
  const { toast } = useToast()
  const [products, setProducts] = useState<Product[]>([])
  const [allProducts, setAllProducts] = useState<Product[]>([]) // Store all products for attribute filtering
  const [loading, setLoading] = useState(true)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [sortOrder, setSortOrder] = useState("Price DESC")
  const [categoryFilter, setCategoryFilter] = useState<number | null>(
    initialCategoryId !== "all" ? Number.parseInt(initialCategoryId) : null,
  )
  const [productTypeFilter, setProductTypeFilter] = useState<number | null>(
    initialProductTypeId !== "all" ? Number.parseInt(initialProductTypeId) : null,
  )
  const [searchTerm, setSearchTerm] = useState(initialSearchTerm)
  const [searchInput, setSearchInput] = useState(initialSearchTerm)
  const [categories, setCategories] = useState<{ id: number; name: string }[]>([])
  const [productTypes, setProductTypes] = useState<ProductType[]>([])
  const [priceRange, setPriceRange] = useState<{ min: number | null; max: number | null }>({
    min: null,
    max: null,
  })
  const [filtersLoading, setFiltersLoading] = useState(false)
  const [apiError, setApiError] = useState<string | null>(null)
  const [attributeFilters, setAttributeFilters] = useState<AttributeFilter[]>([])
  const [selectedAttributes, setSelectedAttributes] = useState<Record<string, number[]>>({})
  const [expandedAttributes, setExpandedAttributes] = useState<Record<string, boolean>>({})
  const pageSize = 12

  // Debounce function
  function debounce<Params extends any[]>(func: (...args: Params) => any, timeout: number): (...args: Params) => void {
    let timer: NodeJS.Timeout
    return (...args: Params) => {
      clearTimeout(timer)
      timer = setTimeout(() => {
        func(...args)
      }, timeout)
    }
  }

  const debouncedSearch = useCallback(
    debounce((searchValue: string) => {
      setSearchTerm(searchValue)
      setCurrentPage(1)
    }, 500),
    [],
  )

  useEffect(() => {
    fetchProducts()
    fetchCategories()
    fetchProductTypes()
  }, [currentPage, sortOrder, categoryFilter, productTypeFilter, priceRange, searchTerm])

  // Apply attribute filters to products
  useEffect(() => {
    if (allProducts.length > 0) {
      applyAttributeFilters()
    }
  }, [selectedAttributes, allProducts])

  const applyAttributeFilters = () => {
    let filteredProducts = [...allProducts]

    // Apply attribute filters
    Object.entries(selectedAttributes).forEach(([attributeName, selectedValueIds]) => {
      if (selectedValueIds.length > 0) {
        filteredProducts = filteredProducts.filter((product) => {
          if (!product.Attributes || product.Attributes.length === 0) return false

          return product.Attributes.some(
            (attr) => attr.AttributeName === attributeName && selectedValueIds.includes(attr.AttributeValueID),
          )
        })
      }
    })

    setProducts(filteredProducts)
    setTotalPages(Math.ceil(filteredProducts.length / pageSize))
  }

  const buildAttributeFilters = (products: Product[]) => {
    const attributeMap = new Map<string, Map<number, { text: string; count: number }>>()

    products.forEach((product) => {
      if (product.Attributes && product.Attributes.length > 0) {
        product.Attributes.forEach((attr) => {
          const key = `${attr.AttributeName}|${attr.DisplayName}`

          if (!attributeMap.has(key)) {
            attributeMap.set(key, new Map())
          }

          const valueMap = attributeMap.get(key)!
          const existing = valueMap.get(attr.AttributeValueID)

          if (existing) {
            existing.count++
          } else {
            valueMap.set(attr.AttributeValueID, {
              text: attr.AttributeValueText,
              count: 1,
            })
          }
        })
      }
    })

    const filters: AttributeFilter[] = []
    attributeMap.forEach((valueMap, key) => {
      const [attributeName, displayName] = key.split("|")
      const values = Array.from(valueMap.entries()).map(([id, data]) => ({
        id,
        text: data.text,
        count: data.count,
      }))

      filters.push({
        attributeName,
        displayName,
        values: values.sort((a, b) => a.text.localeCompare(b.text)),
      })
    })

    setAttributeFilters(filters.sort((a, b) => a.displayName.localeCompare(b.displayName)))
  }

  const fetchProducts = async () => {
    setLoading(currentPage === 1)
    setFiltersLoading(currentPage > 1)
    setApiError(null)

    try {
      // Map sort order to numeric values
      const getOrderByValue = (sortOrder: string) => {
        switch (sortOrder) {
          case "Price ASC":
            return 1
          case "Price DESC":
            return 0
          case "ProductName ASC":
            return 2
          case "ProductName DESC":
            return 3
          case "Rating DESC":
            return 4
          default:
            return 0
        }
      }

      const requestData = {
        requestParameters: {
          SearchTerm: searchTerm || "",
          SizeID: null,
          ColorID: null,
          CategoryID: categoryFilter,
          TagID: null,
          ManufacturerID: null,
          producttypeId: productTypeFilter,
          MinPrice: priceRange.min,
          MaxPrice: priceRange.max,
          Rating: null,
          OrderByColumnName: getOrderByValue(sortOrder),
          PageNo: 1, // Always fetch page 1 to get all products for attribute filtering
          PageSize: 1000, // Get more products to build comprehensive attribute filters
          recordValueJson: "[]",
        },
      }

      console.log("Sending request to Next.js API route:", requestData)

      // Use Next.js API route instead of direct external API call
          const response = await fetch("/api/products/get-products", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestData),
      })

      console.log("API route response status:", response.status)

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      console.log("API route response data:", data)

      if (data && data.data) {
        let productsData = []

        try {
          if (typeof data.data === "string") {
            console.log("Parsing string data:", data.data)
            productsData = JSON.parse(data.data)
          } else if (Array.isArray(data.data)) {
            console.log("Using array data directly")
            productsData = data.data
          } else {
            console.log("Converting object to array:", data.data)
            productsData = [data.data]
          }

          console.log("Processed products data:", productsData)

          if (!Array.isArray(productsData)) {
            throw new Error("Parsed data is not an array")
          }

          if (productsData.length === 0) {
            console.log("No products found in API response")
            setProducts([])
            setAllProducts([])
            setTotalPages(0)
            return
          }

          console.log("First product in response:", productsData[0])
          console.log("Available fields:", Object.keys(productsData[0]))

          const processedProducts = productsData.map((product) => {
            const rawUrl = product.ProductImagesUrl || product.ProductImageUrl
            let imageUrl = null

            try {
              if (rawUrl) {
                let cleanUrl = rawUrl

                if (typeof rawUrl === "string" && (rawUrl.startsWith("[") || rawUrl.startsWith('"'))) {
                  try {
                    const parsed = JSON.parse(rawUrl)
                    if (Array.isArray(parsed) && parsed.length > 0) {
                      cleanUrl = parsed[0].AttachmentURL || parsed[0]
                    } else if (typeof parsed === "string") {
                      cleanUrl = parsed
                    }
                  } catch (jsonError) {
                    cleanUrl = rawUrl.replace(/^"|"/g, "")
                  }
                }

                if (typeof cleanUrl === "string" && cleanUrl.trim() !== "") {
                  cleanUrl = cleanUrl.replace(/^"|"$/g, "").trim()

                  if (cleanUrl) {
                    const decodedUrl = decodeURIComponent(cleanUrl)
                    const normalizedUrl =
                      decodedUrl.startsWith("/") || decodedUrl.startsWith("http") ? decodedUrl : `/${decodedUrl}`

                    imageUrl = normalizedUrl.startsWith("http")
                      ? normalizedUrl
                      : `https://admin.codemedicalapps.com${normalizedUrl}`
                  }
                }
              }
            } catch (error) {
              console.error("Error processing URL for product", product.ProductID || product.ProductId, ":", error)
            }

            const productId = product.ProductID || product.ProductId || product.Id || product.ID || product.id

            const normalizedProduct = {
              ...product,
              ProductId: productId,
              ProductID: productId,
              ProductName: product.ProductName || "Unnamed Product",
              Price: Number.parseFloat(product.Price) || 0,
              OldPrice: product.OldPrice ? Number.parseFloat(product.OldPrice) : undefined,
              IQDPrice: Number.parseFloat(product.IQDPrice) || 0,
              ProductTypeID: product.ProductTypeID,
              ProductTypeName: product.ProductTypeName,
              CategoryName: product.CategoryName || "Uncategorized",
              Rating: Number.parseFloat(product.Rating) || 0,
              StockQuantity: Number.parseInt(product.StockQuantity, 10) || 0,
              ProductImageUrl: imageUrl,
              IsDiscountAllowed: Boolean(product.IsDiscountAllowed),
              MarkAsNew: Boolean(product.MarkAsNew),
              SellStartDatetimeUTC: product.SellStartDatetimeUTC || undefined,
              SellEndDatetimeUTC: product.SellEndDatetimeUTC || undefined,
              Attributes: product.Attributes || [],
              ...(product.DiscountPrice && {
                DiscountPrice: Number.parseFloat(product.DiscountPrice),
              }),
            }

            return normalizedProduct
          })

          console.log("Processed products:", processedProducts)
          setAllProducts(processedProducts)
          setProducts(processedProducts)
          buildAttributeFilters(processedProducts)

          if (productsData.length > 0 && (productsData[0].TotalRecords || productsData[0].totalRecords)) {
            const totalRecords = productsData[0].TotalRecords || productsData[0].totalRecords
            setTotalPages(Math.ceil(Number.parseInt(totalRecords, 10) / pageSize))
          }
        } catch (parseError) {
          console.error("Error parsing product data:", parseError)
          console.error("Raw response data:", data)
          const err = parseError as Error
          setApiError(`Error parsing data: ${err.message || 'Unknown'}`)
          throw parseError
        }
      } else {
        console.warn("No data field in API response:", data)
        setApiError("API response missing data field")
        setProducts([])
        setAllProducts([])
      }
    } catch (error) {
      console.error("Error fetching products:", error)
      const err = error as Error
      setApiError(`API Error: ${err.message || 'Unknown'}`)
      toast({ description: "Failed to load products. Please try again.", type: "error" })

      // Don't use sample data - just show empty state
      setProducts([])
      setAllProducts([])
    } finally {
      setFiltersLoading(false)
      setLoading(false)
    }
  }

  const fetchCategories = async () => {
    try {
      const requestData = {
        requestParameters: {
          recordValueJson: "[]",
        },
      }

      // Use Next.js API route instead of direct external API call
      const response = await fetch("/api/categories", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestData),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      if (data && data.data) {
        const parsedData = JSON.parse(data.data)
        if (Array.isArray(parsedData)) {
          const formattedCategories = parsedData.map((cat: any) => ({
            id: cat.CategoryID,
            name: cat.Name,
          }))
          setCategories(formattedCategories)
        }
      }
    } catch (error) {
      console.error("Error fetching categories:", error)
      // Set some default categories as fallback
      setCategories([
        { id: 1063, name: "Surgery and its subspecialties" },
        { id: 1026, name: "Nursing" },
        { id: 1025, name: "Dentistry" },
        { id: 1043, name: "Internal medicine" },
        { id: 1024, name: "Pharmacology" },
        { id: 1058, name: "Radiology" },
      ])
    }
  }

  const fetchProductTypes = async () => {
    try {
      const requestData = {
        requestParameters: {
          recordValueJson: "[]",
        },
      }

      const response = await fetch("/api/product-types", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestData),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      if (data && data.data) {
        const parsedData = JSON.parse(data.data)
        if (Array.isArray(parsedData)) {
          setProductTypes(parsedData)
        }
      }
    } catch (error) {
      console.error("Error fetching product types:", error)
      // Set fallback product types based on your database
      setProductTypes([
        { producttypeID: 1, Name: "Courses" },
        { producttypeID: 2, Name: "Books" },
        { producttypeID: 3, Name: "Journals" },
        { producttypeID: 4, Name: "Medical Apps" },
      ])
    }
  }

  const updateURLParams = () => {
    const params = new URLSearchParams()
    if (searchTerm) params.append("search", searchTerm)
    if (categoryFilter !== null) params.append("category", categoryFilter.toString())
    if (productTypeFilter !== null) params.append("productType", productTypeFilter.toString())
    window.history.pushState({}, "", `${window.location.pathname}?${params.toString()}`)
  }

  const handleAttributeChange = (attributeName: string, valueId: number, checked: boolean) => {
    setSelectedAttributes((prev) => {
      const current = prev[attributeName] || []
      if (checked) {
        return { ...prev, [attributeName]: [...current, valueId] }
      } else {
        return { ...prev, [attributeName]: current.filter((id) => id !== valueId) }
      }
    })
  }

  const toggleAttributeExpansion = (attributeName: string) => {
    setExpandedAttributes((prev) => ({
      ...prev,
      [attributeName]: !prev[attributeName],
    }))
  }

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    window.scrollTo(0, 0)
  }

  const renderPagination = () => {
    const pages = []
    const maxVisiblePages = 5
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2))
    const endPage = Math.min(totalPages, startPage + maxVisiblePages - 1)

    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1)
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(
        <PaginationItem key={i}>
          <PaginationLink onClick={() => handlePageChange(i)} isActive={currentPage === i}>
            {i}
          </PaginationLink>
        </PaginationItem>,
      )
    }

    return (
      <Pagination className="mt-8">
        <PaginationContent>
          <PaginationItem>
            <PaginationPrevious
              onClick={() => currentPage > 1 && handlePageChange(currentPage - 1)}
              className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
            />
          </PaginationItem>

          {startPage > 1 && (
            <>
              <PaginationItem>
                <PaginationLink onClick={() => handlePageChange(1)}>1</PaginationLink>
              </PaginationItem>
              {startPage > 2 && <PaginationEllipsis />}
            </>
          )}

          {pages}

          {endPage < totalPages && (
            <>
              {endPage < totalPages - 1 && <PaginationEllipsis />}
              <PaginationItem>
                <PaginationLink onClick={() => handlePageChange(totalPages)}>{totalPages}</PaginationLink>
              </PaginationItem>
            </>
          )}

          <PaginationItem>
            <PaginationNext
              onClick={() => currentPage < totalPages && handlePageChange(currentPage + 1)}
              className={currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
            />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    )
  }

  const renderProductSkeleton = () => {
    return Array(pageSize)
      .fill(0)
      .map((_, index) => (
        <div key={`skeleton-${index}`} className="bg-white rounded-lg shadow overflow-hidden">
          <div className="aspect-square">
            <Skeleton className="h-full w-full" />
          </div>
          <div className="p-4 space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-6 w-1/3" />
          </div>
          <div className="p-4 pt-0">
            <div className="flex w-full gap-2">
              <Skeleton className="h-10 flex-1" />
              <Skeleton className="h-10 w-10" />
            </div>
          </div>
        </div>
      ))
  }

  // Get current page products
  const getCurrentPageProducts = () => {
    const startIndex = (currentPage - 1) * pageSize
    const endIndex = startIndex + pageSize
    return products.slice(startIndex, endIndex)
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">
        {categoryFilter !== null && categories.length > 0
          ? `${categories.find((c) => c.id === categoryFilter)?.name || "Category"} Products`
          : "All Products"}
      </h1>

      {/* Top Product Type Filter */}
      <div className="bg-white p-6 rounded-lg shadow mb-6">
        <div className="text-center">
          <div className="flex items-center justify-center gap-2 mb-4">
            <Package className="h-5 w-5 text-gray-600" />
            <span className="font-medium text-gray-700">Filter by Product Type</span>
          </div>
          <div className="flex justify-center gap-3 flex-wrap">
            <Button
              variant={productTypeFilter === null ? "default" : "outline"}
              size="lg"
              onClick={() => {
                setProductTypeFilter(null)
                setCurrentPage(1)
                updateURLParams()
              }}
              className="min-w-[120px]"
            >
              All Types
            </Button>
            <Button
              variant={productTypeFilter === 1 ? "default" : "outline"}
              size="lg"
              onClick={() => {
                setProductTypeFilter(1)
                setCurrentPage(1)
                updateURLParams()
              }}
              className="min-w-[120px]"
            >
              Courses
            </Button>
            <Button
              variant={productTypeFilter === 2 ? "default" : "outline"}
              size="lg"
              onClick={() => {
                setProductTypeFilter(2)
                setCurrentPage(1)
                updateURLParams()
              }}
              className="min-w-[120px]"
            >
              Books
            </Button>
            <Button
              variant={productTypeFilter === 3 ? "default" : "outline"}
              size="lg"
              onClick={() => {
                setProductTypeFilter(3)
                setCurrentPage(1)
                updateURLParams()
              }}
              className="min-w-[120px]"
            >
              Journals
            </Button>
            <Button
              variant={productTypeFilter === 4 ? "default" : "outline"}
              size="lg"
              onClick={() => {
                setProductTypeFilter(4)
                setCurrentPage(1)
                updateURLParams()
              }}
              className="min-w-[120px]"
            >
              Medical Apps
            </Button>
          </div>
        </div>
      </div>

      {/* Debug section */}
      {apiError && (
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 p-4 mb-6 rounded-md">
          <div className="flex items-center mb-2">
            <AlertCircle className="h-5 w-5 mr-2" />
            <h3 className="font-semibold">API Notice</h3>
          </div>
          <p className="text-sm">{apiError}</p>
          <div className="mt-3 flex gap-2">
            <Button size="sm" onClick={() => fetchProducts()}>
              Retry API Call
            </Button>
          </div>
        </div>
      )}

      <div className="mb-6">
        {searchTerm && (
          <p className="text-lg">
            Search results for: <span className="font-semibold">"{searchTerm}"</span>
            {categoryFilter !== null && categories.length > 0 && (
              <span> in {categories.find((c) => c.id === categoryFilter)?.name || "selected category"}</span>
            )}
            {productTypeFilter !== null && productTypes.length > 0 && (
              <span> - {productTypes.find((t) => t.producttypeID === productTypeFilter)?.Name || "selected type"}</span>
            )}
          </p>
        )}
        {!searchTerm && (categoryFilter !== null || productTypeFilter !== null) && (
          <p className="text-lg">
            Browsing:
            {categoryFilter !== null && categories.length > 0 && (
              <span className="font-semibold ml-1">
                {categories.find((c) => c.id === categoryFilter)?.name || "selected category"}
              </span>
            )}
            {productTypeFilter !== null && productTypes.length > 0 && (
              <span className="font-semibold ml-1">
                {categoryFilter !== null ? " - " : " "}
                {productTypes.find((t) => t.producttypeID === productTypeFilter)?.Name || "selected type"}
              </span>
            )}
          </p>
        )}
      </div>

      <div className="flex flex-col lg:flex-row gap-8">
        <div className="lg:w-1/4 space-y-6">
          <div className="bg-white p-4 rounded-lg shadow">
            <h2 className="text-lg font-semibold mb-4 flex items-center">
              <Filter className="mr-2 h-5 w-5" />
              Filters
            </h2>
            <div className="space-y-4">
              <div>
                <h3 className="font-medium mb-2">Category</h3>
                <Select
                  value={categoryFilter?.toString() || "all"}
                  onValueChange={(value) => {
                    const newCategoryFilter = value === "all" ? null : Number(value)
                    setCategoryFilter(newCategoryFilter)
                    setCurrentPage(1)
                    updateURLParams()
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All Categories" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id.toString()}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Attribute Filters - Hidden as per request */}

              {/* Price Range Filter - Hidden as per request */}
              <div className="hidden">
                <h3 className="font-medium mb-2">Price Range</h3>
                <div className="flex items-center space-x-2">
                  <input
                    type="number"
                    placeholder="Min"
                    className="w-full p-2 border rounded"
                    onChange={(e) =>
                      setPriceRange({
                        ...priceRange,
                        min: e.target.value ? Number.parseFloat(e.target.value) : null,
                      })
                    }
                  />
                  <span>-</span>
                  <input
                    type="number"
                    placeholder="Max"
                    className="w-full p-2 border rounded"
                    onChange={(e) =>
                      setPriceRange({
                        ...priceRange,
                        max: e.target.value ? Number.parseFloat(e.target.value) : null,
                      })
                    }
                  />
                </div>
              </div>

              <div>
                <h3 className="font-medium mb-2">Sort By</h3>
                <Select value={sortOrder} onValueChange={setSortOrder}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                      <SelectItem value="ProductName ASC">Name: A to Z</SelectItem>
                    <SelectItem value="ProductName DESC">Name: Z to A</SelectItem>
                    <SelectItem value="Rating DESC">Rating: High to Low</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <h3 className="font-medium mb-2">Search</h3>
                <input
                  type="text"
                  placeholder="Search products..."
                  value={searchInput}
                  onChange={(e) => {
                    setSearchInput(e.target.value)
                    debouncedSearch(e.target.value)
                  }}
                  className="w-full p-2 border rounded"
                />
              </div>

              <Button
                className="w-full"
                onClick={() => {
                  setCategoryFilter(null)
                  setProductTypeFilter(null)
                  setPriceRange({ min: null, max: null })
                  setSortOrder("Price DESC")
                  setCurrentPage(1)
                  setSearchTerm("")
                  setSearchInput("")
                  setSelectedAttributes({})
                  setExpandedAttributes({})
                  window.history.pushState({}, "", window.location.pathname)
                }}
                disabled={filtersLoading}
              >
                Reset Filters
              </Button>
            </div>
          </div>
        </div>

        <div className="lg:w-3/4">
          <div className="grid grid-cols-2 gap-4 sm:gap-6 sm:grid-cols-2 lg:grid-cols-3">
            {loading
              ? renderProductSkeleton()
              : getCurrentPageProducts().map((product) => {
                  if (!product.ProductId) {
                    return null
                  }
                  return <ProductCard key={product.ProductId} product={product} />
                })}
          </div>

          {!loading && products.length === 0 && (
            <div className="text-center py-12">
              <h3 className="text-xl font-semibold mb-2">
                {apiError ? "Failed to load products" : "No products found"}
              </h3>
              <p className="text-gray-500">
                {apiError
                  ? "Please check your connection and try again"
                  : "Try adjusting your filters or search criteria"}
              </p>
              {apiError && (
                <Button className="mt-4" onClick={() => fetchProducts()}>
                  Retry
                </Button>
              )}
            </div>
          )}

          {!loading && products.length > 0 && renderPagination()}
        </div>
      </div>
    </div>
  )
}
