(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4520],{241:(e,t,r)=>{"use strict";r.d(t,{dj:()=>m});var s=r(2115);let a=0,o=new Map,n=e=>{if(o.has(e))return;let t=setTimeout(()=>{o.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);o.set(e,t)},i=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?n(r):e.toasts.forEach(e=>{n(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],c={toasts:[]};function d(e){c=i(c,e),l.forEach(e=>{e(c)})}function u(e){let{...t}=e,r=(a=(a+1)%Number.MAX_SAFE_INTEGER).toString(),s=()=>d({type:"DISMISS_TOAST",toastId:r});return d({type:"ADD_TOAST",toast:{...t,id:r,open:!0,onOpenChange:e=>{e||s()}}}),{id:r,dismiss:s,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:r}})}}function m(){let[e,t]=s.useState(c);return s.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>d({type:"DISMISS_TOAST",toastId:e})}}},738:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(7401).A)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},2336:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var s=r(5155),a=r(2115),o=r(9602);let n=a.forwardRef((e,t)=>{let{className:r,type:a,...n}=e;return(0,s.jsx)("input",{type:a,className:(0,o.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...n})});n.displayName="Input"},2523:(e,t,r)=>{"use strict";r.d(t,{T:()=>s});let s={ADMIN_BASE_URL:"https://admin.codemedicalapps.com/",DYNAMIC_METHOD_SUB_URL:"api/",COMMON_CONTROLLER_SUB_URL:"api/common/",END_POINT_NAMES:{DOWNLOAD_DIGITAL_FILE:"downloadDigitalFile",GET_COUPON_CODE_DISCOUNT:"get-coupon-code-discount-value/calculate-coupon-discount"}}},2598:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(7401).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2862:(e,t,r)=>{"use strict";r.d(t,{$g:()=>u,MakeApiCallAsync:()=>l,TS:()=>o,XX:()=>d,k6:()=>c});var s=r(2651),a=r(2523);s.A.defaults.timeout=3e4,"https:"===window.location.protocol&&a.T.ADMIN_BASE_URL.includes("localhost")&&(s.A.defaults.httpsAgent={rejectUnauthorized:!1});let o={ADMIN_BASE_URL:a.T.ADMIN_BASE_URL,API_VERSION:"v1",DYNAMIC_METHOD_SUB_URL:"api/v1/dynamic/dataoperation/",END_POINT_NAMES:{...a.T.END_POINT_NAMES,GET_CATEGORIES_LIST:"get-categories-list",SIGNUP_USER:"signup-user",GET_USER_LOGIN:"get-user-login",GET_USER_BY_PHONE:"get-user-by-phone",RESET_PASSWORD_BY_PHONE:"reset-password-by-phone",GET_HOME_SCREEN_BANNER:"get-home-screen-banner",GET_RECENT_PRODUCTS:"get-recents-products-list",GET_POPULAR_PRODUCTS:"get-popular-products-list",GET_HOT_DEAL_PRODUCTS:"get-hot-deal-products",GET_CAMPAIGNS_LIST:"get-web-campaign-list",GET_PRODUCTS_LIST:"get-products-list",GET_ALL_PRODUCTS:"api/v1/products/get-all-products",GET_MANUFACTURERS_LIST:"get-manufacturers-list",GET_TAGS_LIST:"get-tags-list",GET_CURRENCY_RATE:"get-currency-rate",GET_COUPON_CODE_DISCOUNT:"get-coupon-code-discount-value/calculate-coupon-discount"},COMMON_CONTROLLER_SUB_URL:"api/v1/common/"},n=async()=>localStorage.getItem("token")||null,i=async()=>localStorage.getItem("userId")||null,l=async function(e,t,r,a,l){arguments.length>5&&void 0!==arguments[5]&&arguments[5];try{let c={...a};if(!c.hasOwnProperty("Authorization")){let e=await n();e&&(c.Authorization="Bearer "+e)}if(!c.hasOwnProperty("Token")){let e=await n();c.Token=null!=e?e:""}if(!c.hasOwnProperty("UserID")){let e=await i();c.UserID=null!=e?e:""}c.hasOwnProperty("Accept")||(c.Accept="application/json"),c.hasOwnProperty("Content-Type")||(c["Content-Type"]="application/json");let d=o.ADMIN_BASE_URL+(null===t||void 0==t?o.DYNAMIC_METHOD_SUB_URL:t)+e;l=null!=l?l:"POST";let u={headers:c,responseType:"json",timeout:3e4,withCredentials:!1};if("POST"===l)return await s.A.post(d,r,u);if("GET"==l)return u.params=r,await s.A.get(d,u);return{data:{errorMessage:"Unsupported method type: ".concat(l),status:"method_not_supported"}}}catch(t){console.error("API call failed:",t);let e={data:{errorMessage:"An unexpected error occurred",status:"unknown_error"}};if(t&&"object"==typeof t&&"response"in t&&t.response){var c,d;let r=null===(c=t.response)||void 0===c?void 0:c.data;e.data={errorMessage:(null==r?void 0:r.errorMessage)||"An error occurred while processing your request.",status:null===(d=t.response)||void 0===d?void 0:d.status}}else if(t&&"object"==typeof t&&"request"in t){let r="Network error: No response received from server.";t.message&&t.message.includes("Network Error")&&(r="Network Error: This may be due to CORS policy restrictions, server unavailability, or an invalid SSL certificate. Please check that:\n1. The server is running and accessible\n2. The URL is correct: "+o.ADMIN_BASE_URL+"\n3. CORS is properly configured on the server\n4. If using HTTPS, the SSL certificate is valid"),e.data={errorMessage:r,status:"network_error"}}else{let r=t&&"object"==typeof t&&"message"in t?t.message:"An unexpected error occurred";e.data={errorMessage:r,status:"request_error"}}return e}},c=async()=>{try{let e=await l("getrate","api/v1/common/",{},{},"GET");if(e&&e.data&&!e.data.errorMessage)return parseInt(e.data)||1500;return 1500}catch(e){return console.error("Error fetching currency rate:",e),1500}},d=(e,t)=>Math.round(e*t),u=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD";return"IQD"===t?"".concat(e.toLocaleString()," IQD"):"$".concat(e.toFixed(2))}},3360:(e,t,r)=>{"use strict";r.d(t,{hO:()=>l,sG:()=>i});var s=r(2115),a=r(7650),o=r(2317),n=r(5155),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=s.forwardRef((e,r)=>{let{asChild:s,...a}=e,i=s?o.DX:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(i,{...a,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function l(e,t){e&&a.flushSync(()=>e.dispatchEvent(t))}},4085:(e,t,r)=>{"use strict";r.d(t,{$:()=>c,r:()=>l});var s=r(5155),a=r(2115),o=r(2317),n=r(1027),i=r(9602);let l=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-red-500 text-destructive-foreground hover:bg-red-600",outline:"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef((e,t)=>{let{className:r,variant:a,size:n,asChild:c=!1,...d}=e,u=c?o.DX:"button";return(0,s.jsx)(u,{className:(0,i.cn)(l({variant:a,size:n,className:r})),ref:t,...d})});c.displayName="Button"},5007:(e,t,r)=>{"use strict";r.d(t,{Wu:()=>i,Zp:()=>n,wL:()=>l});var s=r(5155),a=r(2115),o=r(9602);let n=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,o.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});n.displayName="Card",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",r),...a})}).displayName="CardHeader",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("h3",{ref:t,className:(0,o.cn)("text-2xl font-semibold leading-none tracking-tight",r),...a})}).displayName="CardTitle",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("p",{ref:t,className:(0,o.cn)("text-sm text-muted-foreground",r),...a})}).displayName="CardDescription";let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,o.cn)("p-6 pt-0",r),...a})});i.displayName="CardContent";let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,o.cn)("flex items-center p-6 pt-0",r),...a})});l.displayName="CardFooter"},5785:(e,t,r)=>{"use strict";r.d(t,{J:()=>c});var s=r(5155),a=r(2115),o=r(6195),n=r(1027),i=r(9602);let l=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(o.b,{ref:t,className:(0,i.cn)(l(),r),...a})});c.displayName=o.b.displayName},5896:(e,t,r)=>{Promise.resolve().then(r.bind(r,6482))},6046:(e,t,r)=>{"use strict";var s=r(6658);r.o(s,"useParams")&&r.d(t,{useParams:function(){return s.useParams}}),r.o(s,"usePathname")&&r.d(t,{usePathname:function(){return s.usePathname}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},6195:(e,t,r)=>{"use strict";r.d(t,{b:()=>i});var s=r(2115),a=r(3360),o=r(5155),n=s.forwardRef((e,t)=>(0,o.jsx)(a.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null===(r=e.onMouseDown)||void 0===r||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var i=n},6227:(e,t,r)=>{"use strict";r.d(t,{J:()=>l,v:()=>i});var s=r(5155),a=r(2115),o=r(2862);let n=(0,a.createContext)(void 0);function i(e){let{children:t}=e,[r,i]=(0,a.useState)(null),[l,c]=(0,a.useState)(null),[d,u]=(0,a.useState)(!0);(0,a.useEffect)(()=>{(()=>{try{let e=localStorage.getItem("user"),t=localStorage.getItem("token");if(e&&"{}"!==e){let t=JSON.parse(e);i(t)}t&&c(t)}catch(e){console.error("Error initializing user from localStorage:",e),localStorage.removeItem("user"),localStorage.removeItem("token")}finally{u(!1)}})()},[]);let m=async(e,t)=>{try{var r;u(!0);let s=await (0,o.MakeApiCallAsync)(o.TS.END_POINT_NAMES.GET_USER_LOGIN,null,{requestParameters:{Email:e,Password:t}},{Accept:"application/json","Content-Type":"application/json"},"POST",!0);if(s&&s.data&&!s.data.errorMessage){let e;if(e="string"==typeof s.data.data?JSON.parse(s.data.data):s.data.data,!Array.isArray(e)||!(e.length>0)||"Login Successfully"!==e[0].ResponseMsg)return{success:!1,message:"Incorrect email or password!"};{let t=e[0];i(t),localStorage.setItem("user",JSON.stringify(t));let r=s.data.token||"";return c(r),localStorage.setItem("token",r),{success:!0,message:"Login successful!"}}}return{success:!1,message:(null===(r=s.data)||void 0===r?void 0:r.errorMessage)||"Login failed. Please try again."}}catch(e){return console.error("Login error:",e),{success:!1,message:"An error occurred. Please try again!"}}finally{u(!1)}},p=null!==r&&r.UserId>0;return(0,s.jsx)(n.Provider,{value:{user:r,isLoggedIn:p,isLoading:d,login:m,logout:()=>{i(null),c(null),localStorage.removeItem("user"),localStorage.removeItem("token"),localStorage.removeItem("userId")},token:l},children:t})}function l(){let e=(0,a.useContext)(n);if(void 0===e)throw Error("useUser must be used within a UserProvider");return e}},6462:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(7401).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},6482:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>w});var s=r(5155),a=r(2115),o=r(5007),n=r(4085),i=r(2336),l=r(5785),c=r(9124),d=r(6462),u=r(8686),m=r(1773),p=r(8173),g=r.n(p),f=r(6046),h=r(7110),y=r(6227),v=r(9602),S=r(738),x=r(2598);let N=a.forwardRef((e,t)=>{let{className:r,...o}=e,[l,c]=a.useState(!1);return(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(i.p,{type:l?"text":"password",className:(0,v.cn)("pr-10",r),ref:t,...o}),(0,s.jsx)(n.$,{type:"button",variant:"ghost",size:"icon",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>c(!l),"aria-label":l?"Hide password":"Show password",children:l?(0,s.jsx)(S.A,{className:"h-4 w-4","aria-hidden":"true"}):(0,s.jsx)(x.A,{className:"h-4 w-4","aria-hidden":"true"})})]})});N.displayName="PasswordInput";var b=r(241);function w(){let[e,t]=(0,a.useState)(""),[r,p]=(0,a.useState)({email:"",password:""}),{t:S}=(0,h.t)(),{login:x,isLoading:w,isLoggedIn:E}=(0,y.J)(),{toast:_}=(0,b.dj)(),A=(0,f.useRouter)(),[T,C]=(0,a.useState)({email:"",password:""}),O=()=>{let e={email:"",password:""},t=!0;return r.email.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(r.email)||(e.email="Please enter a valid email address",t=!1):(e.email="Email is required",t=!1),r.password.trim()||(e.password="Password is required",t=!1),C(e),t};(0,a.useEffect)(()=>{E&&A.push("/")},[E,A]);let P=async e=>{if(e.preventDefault(),O()){t("");try{let e=await x(r.email,r.password);e.success?(_({title:"Login Successful",description:e.message,variant:"default"}),A.push("/")):(t(e.message),_({title:"Login Failed",description:e.message,variant:"destructive"}))}catch(r){let e=r.message||"Failed to login";t(e),_({title:"Login Error",description:e,variant:"destructive"}),console.error("Error:",r)}}};return(0,s.jsx)("div",{className:"min-h-screen bg-background flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h2",{className:"mt-6 text-3xl font-extrabold",children:"Welcome Back"}),(0,s.jsx)("p",{className:"mt-2 text-sm text-muted-foreground",children:"Sign in to your account"})]}),(0,s.jsx)(o.Zp,{className:"mt-8 p-8 shadow-xl bg-card",children:(0,s.jsx)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:(0,s.jsxs)("form",{onSubmit:P,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(l.J,{className:"block text-sm font-medium mb-2",children:"Email"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(i.p,{type:"email",value:r.email,onChange:e=>{p({...r,email:e.target.value}),T.email&&O()},className:(0,v.cn)("pl-10",T.email&&"border-red-500"),required:!0,disabled:w}),(0,s.jsx)(d.A,{className:"w-4 h-4 absolute left-3 top-3 text-muted-foreground"})]}),T.email&&(0,s.jsx)("p",{className:"mt-1 text-sm text-destructive",children:T.email})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(l.J,{className:"block text-sm font-medium mb-2",children:"Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(N,{value:r.password,onChange:e=>{p({...r,password:e.target.value}),T.password&&O()},className:(0,v.cn)("pl-10",T.password&&"border-red-500"),required:!0,disabled:w}),(0,s.jsx)(u.A,{className:"w-4 h-4 absolute left-3 top-3 text-muted-foreground"})]}),T.password&&(0,s.jsx)("p",{className:"mt-1 text-sm text-destructive",children:T.password})]}),e&&(0,s.jsx)("div",{className:"text-red-500 text-sm text-center",children:e}),(0,s.jsx)(n.$,{type:"submit",className:"w-full h-12 text-lg bg-primary text-primary-foreground hover:bg-primary/90 transition-colors",disabled:w,children:w?(0,s.jsx)(m.A,{className:"w-4 h-4 animate-spin"}):"Sign In"}),(0,s.jsxs)("div",{className:"text-center text-sm space-y-2",children:[(0,s.jsx)("div",{children:(0,s.jsx)(g(),{href:"/forgot-password",className:"text-primary hover:text-primary/80 hover:underline transition-colors",children:"Forgot your password?"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-muted-foreground",children:"Don't have an account? "}),(0,s.jsx)(g(),{href:"/signup",className:"text-primary hover:text-primary/80 hover:underline transition-colors",children:"Sign up"})]})]})]})})})]})})}},7110:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i,t:()=>l});var s=r(5155),a=r(2115);let o={en:{menu:"Menu",newsletter:"Newsletter",enterEmail:"Enter your email",newsletterDisclaimer:"Subscribe to our newsletter to receive updates and exclusive offers",popularCategories:"Popular Categories!",settings:"Settings",language:"Language",theme:"Theme",color:"Color",save:"Save",cancel:"Cancel",close:"Close",phone:"***************",phonenumber:"Phone Number",email:"<EMAIL>",liveChat:"Live Chat",welcome:"Welcome",logout:"Logout",login:"Login",signup:"Sign Up",signUp:"Sign Up",category:"Category",categories:"Categories",filters:"Filters",clearAll:"Clear All",products:"Products",loadingCategories:"Loading categories...",home:"Home",todayDeals:"Today's Deals",followUs:"Follow Us",aboutUs:"About Us",contactUs:"Contact Us",tryAgain:"Try Again",noProductsFound:"No products found",allProducts:"All Products",quickLinks:"Quick Links",about:"About Us",contact:"Contact Us",hotDeals:"Hot Deals",customerArea:"Customer Area",myAccount:"My Account",orders:"Orders",cart:"Cart",wishlist:"Wishlist",paymentMethods:"Payment Methods",location:"Location",callUs:"Call Us",emailUs:"Email Us",subscribe:"Subscribe",name:"Name",subject:"Subject",message:"Message",sendMessage:"Send Message",sending:"Sending...",messageSent:"Message sent successfully!",messageError:"Failed to send message",contactInfo:"Contact Information",address:"Address",findUs:"Find Us"},ar:{menu:"القائمة",popularCategories:"الفئات الشائعة!",settings:"الإعدادات",language:"اللغة",theme:"المظهر",color:"اللون",save:"حفظ",cancel:"إلغاء",close:"إغلاق",phone:"***************",phonenumber:"رقم الهاتف",email:"<EMAIL>",liveChat:"محادثة مباشرة",welcome:"مرحباً",logout:"تسجيل الخروج",login:"تسجيل الدخول",signup:"تسجيل جديد",signUp:"تسجيل جديد",category:"الفئة",categories:"الفئات",products:"المنتجات",loadingCategories:"جاري تحميل الفئات...",filters:"المرشحات",clearAll:"مسح الكل",home:"الرئيسية",todayDeals:"عروض اليوم",followUs:"تابعنا",aboutUs:"من نحن",contactUs:"اتصل بنا",tryAgain:"حاول مرة أخرى",noProductsFound:"لم يتم العثور على منتجات",allProducts:"جميع المنتجات",quickLinks:"روابط سريعة",about:"من نحن",contact:"اتصل بنا",hotDeals:"عروض ساخنة",customerArea:"منطقة العملاء",myAccount:"حسابي",orders:"الطلبات",cart:"السلة",wishlist:"المفضلة",paymentMethods:"طرق الدفع",location:"الموقع",callUs:"اتصل بنا",emailUs:"راسلنا",subscribe:"اشترك",name:"الاسم",subject:"الموضوع",message:"الرسالة",sendMessage:"إرسال الرسالة",sending:"جاري الإرسال...",messageSent:"تم إرسال الرسالة بنجاح!",messageError:"فشل في إرسال الرسالة",contactInfo:"معلومات الاتصال",address:"العنوان",findUs:"موقعنا"}},n=(0,a.createContext)(void 0);function i(e){let{children:t}=e,[r,i]=(0,a.useState)("light"),[l,c]=(0,a.useState)("en"),[d,u]=(0,a.useState)("#0074b2");return(0,a.useEffect)(()=>{document.documentElement.style.setProperty("--primary",d),document.documentElement.style.setProperty("--primary-foreground","#ffffff")},[]),(0,s.jsx)(n.Provider,{value:{theme:r,language:l,primaryColor:d,toggleTheme:()=>{i("light"===r?"dark":"light")},setLanguage:e=>{c(e),document.documentElement.dir="ar"===e?"rtl":"ltr"},setPrimaryColor:e=>{u(e)},t:e=>(function(e,t){let r=o[t];return e in r?r[e]:"en"!==t&&e in o.en?o.en[e]:e})(e,l)},children:t})}function l(){let e=(0,a.useContext)(n);if(void 0===e)throw Error("useSettings must be used within a SettingsProvider");return e}},8686:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(7401).A)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},9602:(e,t,r)=>{"use strict";r.d(t,{cn:()=>o});var s=r(3463),a=r(9795);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[1345,2651,2446,8441,6587,7358],()=>t(5896)),_N_E=e.O()}]);