"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./contexts/coupon-context.tsx":
/*!*************************************!*\
  !*** ./contexts/coupon-context.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CouponProvider: () => (/* binding */ CouponProvider),\n/* harmony export */   useCoupon: () => (/* binding */ useCoupon)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api_helper__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api-helper */ \"(app-pages-browser)/./lib/api-helper.ts\");\n/* __next_internal_client_entry_do_not_use__ CouponProvider,useCoupon auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst CouponContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction CouponProvider(param) {\n    let { children } = param;\n    _s();\n    const [appliedCoupon, setAppliedCoupon] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const validateCoupon = async (code, amount)=>{\n        if (!code.trim()) {\n            return {\n                valid: false,\n                message: 'Please enter a coupon code',\n                discount: 0\n            };\n        }\n        setIsLoading(true);\n        try {\n            // Prepare cart data - you might need to get this from cart context\n            const cartJsonData = JSON.stringify([]);\n            const param = {\n                CouponCode: code.toUpperCase(),\n                cartJsonData: cartJsonData\n            };\n            const headers = {\n                'Content-Type': 'application/json',\n                'Accept': 'application/json'\n            };\n            const response = await (0,_lib_api_helper__WEBPACK_IMPORTED_MODULE_2__.MakeApiCallAsync)(_lib_api_helper__WEBPACK_IMPORTED_MODULE_2__.Config.END_POINT_NAMES.GET_COUPON_CODE_DISCOUNT, _lib_api_helper__WEBPACK_IMPORTED_MODULE_2__.Config.COMMON_CONTROLLER_SUB_URL, param, headers, 'POST');\n            if (response && response.data && !response.data.errorMessage) {\n                let couponData;\n                // Parse the response data\n                if (typeof response.data.data === 'string') {\n                    couponData = JSON.parse(response.data.data);\n                } else {\n                    couponData = response.data.data;\n                }\n                if (couponData && couponData.DiscountValueAfterCouponAppliedWithQuantity > 0) {\n                    const discountAmount = couponData.DiscountValueAfterCouponAppliedWithQuantity;\n                    // Create coupon object for state\n                    const coupon = {\n                        code: code.toUpperCase(),\n                        discount: discountAmount,\n                        type: 'fixed' // Assuming fixed amount from API\n                    };\n                    setAppliedCoupon(coupon);\n                    return {\n                        valid: true,\n                        message: 'Coupon applied successfully!',\n                        discount: discountAmount\n                    };\n                } else {\n                    return {\n                        valid: false,\n                        message: 'Invalid coupon code or coupon not applicable to your cart',\n                        discount: 0\n                    };\n                }\n            } else {\n                var _response_data;\n                return {\n                    valid: false,\n                    message: ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.errorMessage) || 'Failed to validate coupon',\n                    discount: 0\n                };\n            }\n        } catch (error) {\n            console.error('Coupon validation error:', error);\n            return {\n                valid: false,\n                message: 'Error validating coupon. Please try again.',\n                discount: 0\n            };\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const clearCoupon = ()=>{\n        setAppliedCoupon(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CouponContext.Provider, {\n        value: {\n            appliedCoupon,\n            validateCoupon,\n            clearCoupon,\n            isLoading\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\contexts\\\\coupon-context.tsx\",\n        lineNumber: 115,\n        columnNumber: 5\n    }, this);\n}\n_s(CouponProvider, \"kjHJqm53e+vsbGz9dHK1EJBrEqk=\");\n_c = CouponProvider;\nfunction useCoupon() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(CouponContext);\n    if (context === undefined) {\n        throw new Error('useCoupon must be used within a CouponProvider');\n    }\n    return context;\n}\n_s1(useCoupon, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"CouponProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./contexts/coupon-context.tsx\n"));

/***/ })

});