(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1778],{1641:(e,s,t)=>{"use strict";t.d(s,{Xi:()=>d,av:()=>c,j7:()=>o,tU:()=>i});var r=t(5155),a=t(2115),n=t(8217),l=t(9602);let i=n.bL,o=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(n.B8,{ref:s,className:(0,l.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",t),...a})});o.displayName=n.B8.displayName;let d=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(n.l9,{ref:s,className:(0,l.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",t),...a})});d.displayName=n.l9.displayName;let c=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(n.UC,{ref:s,className:(0,l.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",t),...a})});c.displayName=n.UC.displayName},4085:(e,s,t)=>{"use strict";t.d(s,{$:()=>d,r:()=>o});var r=t(5155),a=t(2115),n=t(2317),l=t(1027),i=t(9602);let o=(0,l.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-red-500 text-destructive-foreground hover:bg-red-600",outline:"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,s)=>{let{className:t,variant:a,size:l,asChild:d=!1,...c}=e,u=d?n.DX:"button";return(0,r.jsx)(u,{className:(0,i.cn)(o({variant:a,size:l,className:t})),ref:s,...c})});d.displayName="Button"},5007:(e,s,t)=>{"use strict";t.d(s,{Wu:()=>i,Zp:()=>l,wL:()=>o});var r=t(5155),a=t(2115),n=t(9602);let l=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...a})});l.displayName="Card",a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",t),...a})}).displayName="CardHeader",a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("h3",{ref:s,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",t),...a})}).displayName="CardTitle",a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("p",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",t),...a})}).displayName="CardDescription";let i=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,n.cn)("p-6 pt-0",t),...a})});i.displayName="CardContent";let o=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",t),...a})});o.displayName="CardFooter"},5436:(e,s,t)=>{Promise.resolve().then(t.bind(t,8964))},7110:(e,s,t)=>{"use strict";t.d(s,{Z:()=>i,t:()=>o});var r=t(5155),a=t(2115);let n={en:{menu:"Menu",newsletter:"Newsletter",enterEmail:"Enter your email",newsletterDisclaimer:"Subscribe to our newsletter to receive updates and exclusive offers",popularCategories:"Popular Categories!",settings:"Settings",language:"Language",theme:"Theme",color:"Color",save:"Save",cancel:"Cancel",close:"Close",phone:"***************",phonenumber:"Phone Number",email:"<EMAIL>",liveChat:"Live Chat",welcome:"Welcome",logout:"Logout",login:"Login",signup:"Sign Up",signUp:"Sign Up",category:"Category",categories:"Categories",filters:"Filters",clearAll:"Clear All",products:"Products",loadingCategories:"Loading categories...",home:"Home",todayDeals:"Today's Deals",followUs:"Follow Us",aboutUs:"About Us",contactUs:"Contact Us",tryAgain:"Try Again",noProductsFound:"No products found",allProducts:"All Products",quickLinks:"Quick Links",about:"About Us",contact:"Contact Us",hotDeals:"Hot Deals",customerArea:"Customer Area",myAccount:"My Account",orders:"Orders",cart:"Cart",wishlist:"Wishlist",paymentMethods:"Payment Methods",location:"Location",callUs:"Call Us",emailUs:"Email Us",subscribe:"Subscribe",name:"Name",subject:"Subject",message:"Message",sendMessage:"Send Message",sending:"Sending...",messageSent:"Message sent successfully!",messageError:"Failed to send message",contactInfo:"Contact Information",address:"Address",findUs:"Find Us"},ar:{menu:"القائمة",popularCategories:"الفئات الشائعة!",settings:"الإعدادات",language:"اللغة",theme:"المظهر",color:"اللون",save:"حفظ",cancel:"إلغاء",close:"إغلاق",phone:"***************",phonenumber:"رقم الهاتف",email:"<EMAIL>",liveChat:"محادثة مباشرة",welcome:"مرحباً",logout:"تسجيل الخروج",login:"تسجيل الدخول",signup:"تسجيل جديد",signUp:"تسجيل جديد",category:"الفئة",categories:"الفئات",products:"المنتجات",loadingCategories:"جاري تحميل الفئات...",filters:"المرشحات",clearAll:"مسح الكل",home:"الرئيسية",todayDeals:"عروض اليوم",followUs:"تابعنا",aboutUs:"من نحن",contactUs:"اتصل بنا",tryAgain:"حاول مرة أخرى",noProductsFound:"لم يتم العثور على منتجات",allProducts:"جميع المنتجات",quickLinks:"روابط سريعة",about:"من نحن",contact:"اتصل بنا",hotDeals:"عروض ساخنة",customerArea:"منطقة العملاء",myAccount:"حسابي",orders:"الطلبات",cart:"السلة",wishlist:"المفضلة",paymentMethods:"طرق الدفع",location:"الموقع",callUs:"اتصل بنا",emailUs:"راسلنا",subscribe:"اشترك",name:"الاسم",subject:"الموضوع",message:"الرسالة",sendMessage:"إرسال الرسالة",sending:"جاري الإرسال...",messageSent:"تم إرسال الرسالة بنجاح!",messageError:"فشل في إرسال الرسالة",contactInfo:"معلومات الاتصال",address:"العنوان",findUs:"موقعنا"}},l=(0,a.createContext)(void 0);function i(e){let{children:s}=e,[t,i]=(0,a.useState)("light"),[o,d]=(0,a.useState)("en"),[c,u]=(0,a.useState)("#0074b2");return(0,a.useEffect)(()=>{document.documentElement.style.setProperty("--primary",c),document.documentElement.style.setProperty("--primary-foreground","#ffffff")},[]),(0,r.jsx)(l.Provider,{value:{theme:t,language:o,primaryColor:c,toggleTheme:()=>{i("light"===t?"dark":"light")},setLanguage:e=>{d(e),document.documentElement.dir="ar"===e?"rtl":"ltr"},setPrimaryColor:e=>{u(e)},t:e=>(function(e,s){let t=n[s];return e in t?t[e]:"en"!==s&&e in n.en?n.en[e]:e})(e,o)},children:s})}function o(){let e=(0,a.useContext)(l);if(void 0===e)throw Error("useSettings must be used within a SettingsProvider");return e}},8964:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>v});var r=t(5155),a=t(2115),n=t(9426),l=t(5007),i=t(4085),o=t(1641),d=t(8173),c=t.n(d),u=t(7110),m=t(3239),f=t(5844),x=t(1594),g=t(719),p=t(853),h=t(4081),b=t(6967);function v(){let{t:e}=(0,u.t)(),[s,t]=(0,a.useState)("all"),d=[{id:"ORD-2023-1001",date:"2023-12-15",total:129.99,status:"delivered",items:[{name:"Medical Terminology Book",quantity:1,price:49.99},{name:"Anatomy Atlas",quantity:1,price:79.99}]},{id:"ORD-2023-0892",date:"2023-11-28",total:199.99,status:"processing",items:[{name:"Clinical Medicine Course",quantity:1,price:199.99}]},{id:"ORD-2023-0765",date:"2023-10-05",total:45.99,status:"cancelled",items:[{name:"Pharmacology Flashcards",quantity:1,price:45.99}]}],v="all"===s?d:d.filter(e=>e.status===s),j=e=>{switch(e){case"delivered":return(0,r.jsx)(m.A,{className:"h-5 w-5 text-green-500"});case"processing":return(0,r.jsx)(f.A,{className:"h-5 w-5 text-blue-500"});case"cancelled":return(0,r.jsx)(x.A,{className:"h-5 w-5 text-red-500"});default:return(0,r.jsx)(g.A,{className:"h-5 w-5 text-gray-500"})}},y=e=>{switch(e){case"delivered":return"Delivered";case"processing":return"Processing";case"cancelled":return"Cancelled";default:return"Unknown"}};return(0,r.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,r.jsx)(n.Qp,{className:"mb-6",children:(0,r.jsxs)(n.AB,{children:[(0,r.jsx)(n.J5,{children:(0,r.jsx)(n.w1,{asChild:!0,children:(0,r.jsx)(c(),{href:"/",children:e("home")})})}),(0,r.jsx)(n.tH,{}),(0,r.jsx)(n.J5,{children:(0,r.jsx)(n.tJ,{children:e("orders")})})]})}),(0,r.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold mb-6",children:e("orders")}),(0,r.jsx)(o.tU,{defaultValue:"all",className:"mb-6",onValueChange:t,children:(0,r.jsxs)(o.j7,{children:[(0,r.jsx)(o.Xi,{value:"all",children:"All Orders"}),(0,r.jsx)(o.Xi,{value:"processing",children:"Processing"}),(0,r.jsx)(o.Xi,{value:"delivered",children:"Delivered"}),(0,r.jsx)(o.Xi,{value:"cancelled",children:"Cancelled"})]})}),(0,r.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6",children:[(0,r.jsxs)("div",{className:"relative w-full md:w-64",children:[(0,r.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,r.jsx)("input",{type:"text",placeholder:"Search orders...",className:"w-full pl-10 pr-4 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"text-sm text-muted-foreground",children:"Sort by:"}),(0,r.jsxs)("select",{className:"border border-input rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent",children:[(0,r.jsx)("option",{value:"date-desc",children:"Date (Newest)"}),(0,r.jsx)("option",{value:"date-asc",children:"Date (Oldest)"}),(0,r.jsx)("option",{value:"total-desc",children:"Amount (High to Low)"}),(0,r.jsx)("option",{value:"total-asc",children:"Amount (Low to High)"})]})]})]}),v.length>0?(0,r.jsx)("div",{className:"space-y-4",children:v.map(e=>(0,r.jsxs)(l.Zp,{className:"overflow-hidden",children:[(0,r.jsxs)("div",{className:"border-b border-border p-4 flex flex-col md:flex-row justify-between items-start md:items-center gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("h3",{className:"font-medium",children:e.id}),(0,r.jsxs)("div",{className:"flex items-center gap-1 px-2 py-1 rounded-full bg-muted text-xs",children:[j(e.status),(0,r.jsx)("span",{children:y(e.status)})]})]}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Ordered on ",e.date]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("p",{className:"font-medium",children:["$",e.total.toFixed(2)]}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:[e.items.length," item(s)"]})]}),(0,r.jsxs)(i.$,{variant:"outline",size:"sm",className:"flex items-center gap-1",children:[(0,r.jsx)(h.A,{className:"h-4 w-4"}),"Details",(0,r.jsx)(b.A,{className:"h-4 w-4"})]})]})]}),(0,r.jsxs)("div",{className:"p-4 bg-muted/30",children:[(0,r.jsx)("h4",{className:"text-sm font-medium mb-2",children:"Order Items"}),(0,r.jsx)("div",{className:"space-y-2",children:e.items.map((e,s)=>(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-muted rounded-md flex items-center justify-center",children:(0,r.jsx)(g.A,{className:"h-4 w-4"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm",children:e.name}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Qty: ",e.quantity]})]})]}),(0,r.jsxs)("p",{className:"text-sm font-medium",children:["$",e.price.toFixed(2)]})]},s))})]})]},e.id))}):(0,r.jsx)(l.Zp,{children:(0,r.jsxs)("div",{className:"p-8 text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(g.A,{className:"h-8 w-8 text-muted-foreground"})}),(0,r.jsx)("h3",{className:"text-lg font-medium mb-2",children:"No orders found"}),(0,r.jsxs)("p",{className:"text-muted-foreground mb-4",children:["You don't have any ","all"!==s?s:""," orders yet."]}),(0,r.jsx)(i.$,{asChild:!0,children:(0,r.jsx)(c(),{href:"/",children:"Continue Shopping"})})]})})]})]})}},9426:(e,s,t)=>{"use strict";t.d(s,{AB:()=>d,J5:()=>c,Qp:()=>o,tH:()=>f,tJ:()=>m,w1:()=>u});var r=t(5155),a=t(2115),n=t(2317),l=t(6967),i=(t(4858),t(9602));let o=a.forwardRef((e,s)=>{let{...t}=e;return(0,r.jsx)("nav",{ref:s,"aria-label":"breadcrumb",...t})});o.displayName="Breadcrumb";let d=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("ol",{ref:s,className:(0,i.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",t),...a})});d.displayName="BreadcrumbList";let c=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("li",{ref:s,className:(0,i.cn)("inline-flex items-center gap-1.5",t),...a})});c.displayName="BreadcrumbItem";let u=a.forwardRef((e,s)=>{let{asChild:t,className:a,...l}=e,o=t?n.DX:"a";return(0,r.jsx)(o,{ref:s,className:(0,i.cn)("transition-colors hover:text-foreground",a),...l})});u.displayName="BreadcrumbLink";let m=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("span",{ref:s,role:"link","aria-disabled":"true","aria-current":"page",className:(0,i.cn)("font-normal text-foreground",t),...a})});m.displayName="BreadcrumbPage";let f=e=>{let{children:s,className:t,...a}=e;return(0,r.jsx)("li",{role:"presentation","aria-hidden":"true",className:(0,i.cn)("[&>svg]:size-3.5",t),...a,children:null!=s?s:(0,r.jsx)(l.A,{})})};f.displayName="BreadcrumbSeparator"},9602:(e,s,t)=>{"use strict";t.d(s,{cn:()=>n});var r=t(3463),a=t(9795);function n(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,a.QP)((0,r.$)(s))}}},e=>{var s=s=>e(e.s=s);e.O(0,[1345,7663,1102,8441,6587,7358],()=>s(5436)),_N_E=e.O()}]);