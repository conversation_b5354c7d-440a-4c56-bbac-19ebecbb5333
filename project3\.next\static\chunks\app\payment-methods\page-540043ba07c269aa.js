(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6272],{3944:(e,t,s)=>{Promise.resolve().then(s.bind(s,7242))},4858:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},5007:(e,t,s)=>{"use strict";s.d(t,{Wu:()=>d,Zp:()=>n,wL:()=>m});var a=s(5155),r=s(2115),l=s(9602);let n=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...r})});n.displayName="Card",r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",s),...r})}).displayName="CardHeader",r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("h3",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",s),...r})}).displayName="CardTitle",r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("p",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",s),...r})}).displayName="CardDescription";let d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",s),...r})});d.displayName="CardContent";let m=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",s),...r})});m.displayName="CardFooter"},6967:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},7110:(e,t,s)=>{"use strict";s.d(t,{Z:()=>d,t:()=>m});var a=s(5155),r=s(2115);let l={en:{menu:"Menu",newsletter:"Newsletter",enterEmail:"Enter your email",newsletterDisclaimer:"Subscribe to our newsletter to receive updates and exclusive offers",popularCategories:"Popular Categories!",settings:"Settings",language:"Language",theme:"Theme",color:"Color",save:"Save",cancel:"Cancel",close:"Close",phone:"***************",phonenumber:"Phone Number",email:"<EMAIL>",liveChat:"Live Chat",welcome:"Welcome",logout:"Logout",login:"Login",signup:"Sign Up",signUp:"Sign Up",category:"Category",categories:"Categories",filters:"Filters",clearAll:"Clear All",products:"Products",loadingCategories:"Loading categories...",home:"Home",todayDeals:"Today's Deals",followUs:"Follow Us",aboutUs:"About Us",contactUs:"Contact Us",tryAgain:"Try Again",noProductsFound:"No products found",allProducts:"All Products",quickLinks:"Quick Links",about:"About Us",contact:"Contact Us",hotDeals:"Hot Deals",customerArea:"Customer Area",myAccount:"My Account",orders:"Orders",cart:"Cart",wishlist:"Wishlist",paymentMethods:"Payment Methods",location:"Location",callUs:"Call Us",emailUs:"Email Us",subscribe:"Subscribe",name:"Name",subject:"Subject",message:"Message",sendMessage:"Send Message",sending:"Sending...",messageSent:"Message sent successfully!",messageError:"Failed to send message",contactInfo:"Contact Information",address:"Address",findUs:"Find Us"},ar:{menu:"القائمة",popularCategories:"الفئات الشائعة!",settings:"الإعدادات",language:"اللغة",theme:"المظهر",color:"اللون",save:"حفظ",cancel:"إلغاء",close:"إغلاق",phone:"***************",phonenumber:"رقم الهاتف",email:"<EMAIL>",liveChat:"محادثة مباشرة",welcome:"مرحباً",logout:"تسجيل الخروج",login:"تسجيل الدخول",signup:"تسجيل جديد",signUp:"تسجيل جديد",category:"الفئة",categories:"الفئات",products:"المنتجات",loadingCategories:"جاري تحميل الفئات...",filters:"المرشحات",clearAll:"مسح الكل",home:"الرئيسية",todayDeals:"عروض اليوم",followUs:"تابعنا",aboutUs:"من نحن",contactUs:"اتصل بنا",tryAgain:"حاول مرة أخرى",noProductsFound:"لم يتم العثور على منتجات",allProducts:"جميع المنتجات",quickLinks:"روابط سريعة",about:"من نحن",contact:"اتصل بنا",hotDeals:"عروض ساخنة",customerArea:"منطقة العملاء",myAccount:"حسابي",orders:"الطلبات",cart:"السلة",wishlist:"المفضلة",paymentMethods:"طرق الدفع",location:"الموقع",callUs:"اتصل بنا",emailUs:"راسلنا",subscribe:"اشترك",name:"الاسم",subject:"الموضوع",message:"الرسالة",sendMessage:"إرسال الرسالة",sending:"جاري الإرسال...",messageSent:"تم إرسال الرسالة بنجاح!",messageError:"فشل في إرسال الرسالة",contactInfo:"معلومات الاتصال",address:"العنوان",findUs:"موقعنا"}},n=(0,r.createContext)(void 0);function d(e){let{children:t}=e,[s,d]=(0,r.useState)("light"),[m,i]=(0,r.useState)("en"),[c,o]=(0,r.useState)("#0074b2");return(0,r.useEffect)(()=>{document.documentElement.style.setProperty("--primary",c),document.documentElement.style.setProperty("--primary-foreground","#ffffff")},[]),(0,a.jsx)(n.Provider,{value:{theme:s,language:m,primaryColor:c,toggleTheme:()=>{d("light"===s?"dark":"light")},setLanguage:e=>{i(e),document.documentElement.dir="ar"===e?"rtl":"ltr"},setPrimaryColor:e=>{o(e)},t:e=>(function(e,t){let s=l[t];return e in s?s[e]:"en"!==t&&e in l.en?l.en[e]:e})(e,m)},children:t})}function m(){let e=(0,r.useContext)(n);if(void 0===e)throw Error("useSettings must be used within a SettingsProvider");return e}},7242:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var a=s(5155),r=s(9426),l=s(5007),n=s(8173),d=s.n(n),m=s(7110);function i(){let{t:e,primaryColor:t}=(0,m.t)();return(0,a.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,a.jsx)(r.Qp,{className:"mb-6",children:(0,a.jsxs)(r.AB,{children:[(0,a.jsx)(r.J5,{children:(0,a.jsx)(r.w1,{asChild:!0,children:(0,a.jsx)(d(),{href:"/",children:e("home")})})}),(0,a.jsx)(r.tH,{}),(0,a.jsx)(r.J5,{children:(0,a.jsx)(r.tJ,{children:e("paymentMethods")})})]})}),(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,a.jsx)("h1",{className:"text-2xl md:text-3xl font-bold mb-6 md:mb-8 text-center",children:"Payment Methods"}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-xl md:text-2xl font-semibold mb-4 md:mb-6",style:{color:t},children:"Inside Iraq"}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,a.jsx)(l.Zp,{className:"p-4 md:p-6",children:(0,a.jsxs)("div",{className:"flex flex-col items-center text-center md:flex-row md:items-start md:text-left md:gap-4",children:[(0,a.jsx)("img",{src:"/Zaincash iraq.png",alt:"Zain Cash",className:"h-16 w-16 flex-shrink-0 mb-2 md:mb-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-base md:text-xl font-semibold mb-1 md:mb-2 break-words",children:"Zain cash (Iraq)"}),(0,a.jsx)("p",{className:"text-base md:text-lg font-medium break-all",children:"***********"})]})]})}),(0,a.jsx)(l.Zp,{className:"p-4 md:p-6",children:(0,a.jsxs)("div",{className:"flex flex-col items-center text-center md:flex-row md:items-start md:text-left md:gap-4",children:[(0,a.jsx)("img",{src:"/Qicard iraq.png",alt:"Rafidein Account",className:"h-16 w-16 flex-shrink-0 mb-2 md:mb-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-base md:text-xl font-semibold mb-1 md:mb-2 break-words",children:" Rafidain Bank"}),(0,a.jsx)("p",{className:"text-base md:text-lg font-medium break-all",children:"**********"})]})]})}),(0,a.jsx)(l.Zp,{className:"p-4 md:p-6",children:(0,a.jsxs)("div",{className:"flex flex-col items-center text-center md:flex-row md:items-start md:text-left md:gap-4",children:[(0,a.jsx)("img",{src:"/Asia pay.png",alt:"Asia Pay",className:"h-16 w-16 flex-shrink-0 mb-2 md:mb-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-base md:text-xl font-semibold mb-1 md:mb-2",children:"Asia Pay"}),(0,a.jsx)("p",{className:"text-base md:text-lg font-medium break-all",children:"***********"})]})]})})]})]}),(0,a.jsxs)("section",{children:[(0,a.jsx)("h2",{className:"text-xl md:text-2xl font-semibold mb-4 md:mb-6 mt-8 md:mt-12",style:{color:t},children:"Outside Iraq"}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,a.jsx)(l.Zp,{className:"p-4 md:p-6",children:(0,a.jsxs)("div",{className:"flex flex-col items-center text-center md:flex-row md:items-start md:text-left md:gap-4",children:[(0,a.jsx)("img",{src:"/Paypal.png",alt:"PayPal",className:"h-16 w-16 flex-shrink-0 mb-2 md:mb-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-base md:text-xl font-semibold mb-1 md:mb-2",children:"PayPal"}),(0,a.jsx)("p",{className:"mb-1 md:mb-2 text-sm md:text-base",children:"You can pay through this link\uD83D\uDC47"}),(0,a.jsx)("a",{href:"https://paypal.me/FatimahNaser?country.x=JO&locale.x=en_US",target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:text-blue-800 underline mb-2 block break-all",children:"https://paypal.me/FatimahNaser?country.x=JO&locale.x=en_US"})]})]})}),(0,a.jsx)(l.Zp,{className:"p-4 md:p-6",children:(0,a.jsxs)("div",{className:"flex flex-col items-center text-center md:flex-row md:items-start md:text-left md:gap-4",children:[(0,a.jsx)("img",{src:"/Amazon gift card.png",alt:"Amazon Gift",className:"h-16 w-16 flex-shrink-0 mb-2 md:mb-0"}),(0,a.jsxs)("div",{className:"space-y-2 w-full",children:[(0,a.jsx)("h3",{className:"text-base md:text-xl font-semibold mb-1 md:mb-2",children:"Amazon Gift"}),(0,a.jsx)("a",{href:"https://www.amazon.com/Amazon-eGift-Card-Logo-Animated/dp/B07PCMWTSG/ref=mp_s_a_1_1?adgrpid=160438626878&dib=eyJ2IjoiMSJ9.y343JC2nqCCCtAt_MaFdYdSEoDk1IL8C8OVn3MADfESEozTH6jWzFIJ4WqlXn7_W-n2IrnPR-rfE3Spk4QYVuOOL7cvbuK9Esy0CXQivH6v0c4KW6RfZeH8pYn15Gdj-s62p0V-fiHzAE12D4YOgeY2zQf3sUuAQF30eHiR7nSfSyvGj9P0M79Suz3VRAqqxS64beG-r2SJhB_Y_apq-6Q.gbfTjpxr2hWpO9dWg-U8dthgvZM21cwxR6PrsZBpG38&dib_tag=se&hvadid=692707382867&hvdev=m&hvlocphy=9211521&hvnetw=g&hvqmt=e&hvrand=15003258399388157606&hvtargid=kwd-2389411675177&hydadcr=22339_13333066&keywords=amazon%27+gift+card&mcid=90ecf431d9b83733b420d0f87065fc78&qid=1748814128&sr=8-1",target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:text-blue-800 underline block break-all",children:"Amazon eGift Card Link"}),(0,a.jsx)("p",{className:"text-gray-700 text-sm md:text-base",children:"Please choose the amount and then send it to this email\uD83D\uDC47"}),(0,a.jsx)("p",{className:"text-base md:text-lg font-medium break-all",children:"<EMAIL>"})]})]})})]})]}),(0,a.jsxs)("section",{className:"mt-8",children:[(0,a.jsx)("h2",{className:"text-xl md:text-2xl font-semibold mb-4 md:mb-6 mt-8 md:mt-12",style:{color:t},children:"Cash on Delivery"}),(0,a.jsx)(l.Zp,{className:"p-2 md:p-4",children:(0,a.jsxs)("div",{className:"flex flex-col items-center text-center md:flex-row md:items-start md:text-left",children:[(0,a.jsx)("div",{className:"w-32 h-32 md:w-40 md:h-40 rounded-full flex items-center justify-center mb-4 md:mb-0 md:mr-4",style:{backgroundColor:"".concat(t,"20")},children:(0,a.jsx)("img",{src:"/Cash on delivery.png",alt:"Cash on Delivery",className:"w-full h-full rounded-full bg-white p-2 object-contain"})}),(0,a.jsxs)("div",{className:"md:flex-1",children:[(0,a.jsx)("h3",{className:"text-base md:text-xl font-semibold mb-1 md:mb-2",children:"Cash on Delivery"}),(0,a.jsx)("p",{className:"text-muted-foreground text-sm md:text-base",children:"Pay in cash upon delivery - we offer delivery to all provinces within Iraq. Additional fees may apply depending on your location."})]})]})})]}),(0,a.jsxs)("section",{className:"mt-12",children:[(0,a.jsx)("h2",{className:"text-xl md:text-2xl font-bold mb-4 md:mb-6 mt-8 md:mt-12",children:"Payment Process"}),(0,a.jsx)(l.Zp,{className:"p-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 rounded-full bg-muted flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)("span",{className:"text-xl font-bold",children:"1"})}),(0,a.jsx)("h3",{className:"font-medium text-sm md:text-base mb-1 md:mb-2",children:"Select Products"}),(0,a.jsx)("p",{className:"text-xs md:text-sm text-muted-foreground",children:"Add items to your cart"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 rounded-full bg-muted flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)("span",{className:"text-xl font-bold",children:"2"})}),(0,a.jsx)("h3",{className:"font-medium text-sm md:text-base mb-1 md:mb-2",children:"Shipping Details"}),(0,a.jsx)("p",{className:"text-xs md:text-sm text-muted-foreground",children:"Enter your shipping information"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 rounded-full bg-muted flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)("span",{className:"text-xl font-bold",children:"3"})}),(0,a.jsx)("h3",{className:"font-medium text-sm md:text-base mb-1 md:mb-2",children:"Payment Method"}),(0,a.jsx)("p",{className:"text-xs md:text-sm text-muted-foreground",children:"Choose your payment method"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 rounded-full bg-muted flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)("span",{className:"text-xl font-bold",children:"4"})}),(0,a.jsx)("h3",{className:"font-medium text-sm md:text-base mb-1 md:mb-2",children:"Confirmation"}),(0,a.jsx)("p",{className:"text-xs md:text-sm text-muted-foreground",children:"Review and confirm your order"})]})]})})]}),(0,a.jsx)("div",{className:"mt-12 text-center",children:(0,a.jsx)("p",{className:"text-gray-700 text-sm md:text-base text-center px-4",children:"For payment support, please contact our customer service team."})})]})]})}},7401:(e,t,s)=>{"use strict";s.d(t,{A:()=>m});var a=s(2115);let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return t.filter((e,t,s)=>!!e&&s.indexOf(e)===t).join(" ")};var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,a.forwardRef)((e,t)=>{let{color:s="currentColor",size:r=24,strokeWidth:d=2,absoluteStrokeWidth:m,className:i="",children:c,iconNode:o,...x}=e;return(0,a.createElement)("svg",{ref:t,...n,width:r,height:r,stroke:s,strokeWidth:m?24*Number(d)/Number(r):d,className:l("lucide",i),...x},[...o.map(e=>{let[t,s]=e;return(0,a.createElement)(t,s)}),...Array.isArray(c)?c:[c]])}),m=(e,t)=>{let s=(0,a.forwardRef)((s,n)=>{let{className:m,...i}=s;return(0,a.createElement)(d,{ref:n,iconNode:t,className:l("lucide-".concat(r(e)),m),...i})});return s.displayName="".concat(e),s}},9426:(e,t,s)=>{"use strict";s.d(t,{AB:()=>i,J5:()=>c,Qp:()=>m,tH:()=>u,tJ:()=>x,w1:()=>o});var a=s(5155),r=s(2115),l=s(2317),n=s(6967),d=(s(4858),s(9602));let m=r.forwardRef((e,t)=>{let{...s}=e;return(0,a.jsx)("nav",{ref:t,"aria-label":"breadcrumb",...s})});m.displayName="Breadcrumb";let i=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("ol",{ref:t,className:(0,d.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",s),...r})});i.displayName="BreadcrumbList";let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("li",{ref:t,className:(0,d.cn)("inline-flex items-center gap-1.5",s),...r})});c.displayName="BreadcrumbItem";let o=r.forwardRef((e,t)=>{let{asChild:s,className:r,...n}=e,m=s?l.DX:"a";return(0,a.jsx)(m,{ref:t,className:(0,d.cn)("transition-colors hover:text-foreground",r),...n})});o.displayName="BreadcrumbLink";let x=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("span",{ref:t,role:"link","aria-disabled":"true","aria-current":"page",className:(0,d.cn)("font-normal text-foreground",s),...r})});x.displayName="BreadcrumbPage";let u=e=>{let{children:t,className:s,...r}=e;return(0,a.jsx)("li",{role:"presentation","aria-hidden":"true",className:(0,d.cn)("[&>svg]:size-3.5",s),...r,children:null!=t?t:(0,a.jsx)(n.A,{})})};u.displayName="BreadcrumbSeparator"},9602:(e,t,s)=>{"use strict";s.d(t,{cn:()=>l});var a=s(3463),r=s(9795);function l(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,a.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[1345,8441,6587,7358],()=>t(3944)),_N_E=e.O()}]);