(()=>{var e={};e.id=162,e.ids=[162],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4306:(e,r,t)=>{"use strict";t.d(r,{j:()=>o});var s=t(46722),a=t(33066);let i=(0,s.Wp)({apiKey:"AIzaSyBlr1YG3CXkyn3yUJ44xvFFCcpfSj0pwFU",authDomain:"codemedical-19ec6.firebaseapp.com",projectId:"codemedical-19ec6",storageBucket:"codemedical-19ec6.firebasestorage.app",messagingSenderId:"494556459416",appId:"1:494556459416:web:180c7e662f6e3ae4e43bed",measurementId:"G-BRC9LRF5DE"}),o=(0,a.xI)(i)},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12144:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l});var s=t(70260),a=t(28203),i=t(25155),o=t.n(i),n=t(67292),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(r,d);let l={children:["",{children:["forgot-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,93220)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\forgot-password\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,19611)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\forgot-password\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/forgot-password/page",pathname:"/forgot-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},12412:e=>{"use strict";e.exports=require("assert")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},25409:(e,r,t)=>{"use strict";t.d(r,{p:()=>o});var s=t(45512),a=t(58009),i=t(59462);let o=a.forwardRef(({className:e,type:r,...t},a)=>(0,s.jsx)("input",{type:r,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...t}));o.displayName="Input"},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},37067:e=>{"use strict";e.exports=require("node:http")},37540:e=>{"use strict";e.exports=require("node:console")},38522:e=>{"use strict";e.exports=require("node:zlib")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},47699:(e,r,t)=>{"use strict";t.d(r,{J:()=>l});var s=t(45512),a=t(58009),i=t(92405),o=t(21643),n=t(59462);let d=(0,o.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),l=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)(i.b,{ref:t,className:(0,n.cn)(d(),e),...r}));l.displayName=i.b.displayName},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},53798:(e,r,t)=>{Promise.resolve().then(t.bind(t,93220))},55591:e=>{"use strict";e.exports=require("https")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71950:(e,r,t)=>{Promise.resolve().then(t.bind(t,93036))},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},93036:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>A});var s=t(45512),a=t(58009),i=t(4306),o=t(33066),n=t(97643),d=t(87021),l=t(25409),c=t(47699),u=t(36752),p=t.n(u);t(90895);var m=t(24540),x=t(28531),f=t.n(x),h=t(79334),g=t(1734),y=t(92056);let b=(0,t(41680).A)("KeyRound",[["path",{d:"M2.586 17.414A2 2 0 0 0 2 18.828V21a1 1 0 0 0 1 1h3a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1h1a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1h.172a2 2 0 0 0 1.414-.586l.814-.814a6.5 6.5 0 1 0-4-4z",key:"1s6t7t"}],["circle",{cx:"16.5",cy:"7.5",r:".5",fill:"currentColor",key:"w0ekpg"}]]);var w=t(92557),v=t(15907),j=t(72734),N=t(21956),P=t(69208),q=t(71901),C=t(15348),S=t(65518);function A(){let[e,r]=(0,a.useState)("phone"),[t,u]=(0,a.useState)("964"),[x,A]=(0,a.useState)("iq"),[E,k]=(0,a.useState)(""),[_,R]=(0,a.useState)(null),[T,$]=(0,a.useState)(0),[F,M]=(0,a.useState)(!1),[D,I]=(0,a.useState)(""),[O,V]=(0,a.useState)(!1),[L,z]=(0,a.useState)(!1),[U,Y]=(0,a.useState)(""),[G,J]=(0,a.useState)({newPassword:"",confirmPassword:""}),{t:B}=(0,q.t)(),{toast:H}=(0,S.dj)(),W=(0,h.useRouter)(),K=()=>{$(60)},X=async e=>{if(e.preventDefault(),M(!0),I(""),!t){I("Phone number is required"),M(!1);return}if(t.length<8){I("Phone number must be at least 8 digits"),M(!1);return}if(!/^\+?[1-9]\d{1,14}$/.test(t)){I("Please enter a valid phone number"),M(!1);return}try{let e={requestParameters:{PhoneNumber:`+${t}`}},s=await (0,C.MakeApiCallAsync)(C.TS.END_POINT_NAMES.GET_USER_BY_PHONE,null,e,{Accept:"application/json","Content-Type":"application/json"},"POST",!0);if(s.data?.errorMessage){I("No account found with this phone number"),M(!1);return}let a=new o.kT(i.j,"recaptcha-container",{size:"invisible",callback:()=>{}}),n=`+${t}`,d=await (0,o.ik)(i.j,n,a);R(d),r("verification"),K(),H({title:"Verification Code Sent",description:`We've sent a verification code to ${n}`})}catch(e){I(e.message||"Failed to send verification code"),console.error("Error:",e),H({title:"Error",description:e.message||"Failed to send verification code"})}finally{M(!1)}},Z=async()=>{if(!(T>0)){M(!0),I("");try{let e=new o.kT(i.j,"recaptcha-container-resend",{size:"invisible",callback:()=>{}}),r=`+${t}`,s=await (0,o.ik)(i.j,r,e);R(s),K(),H({title:"Code Resent",description:"A new verification code has been sent to your phone"})}catch(e){I("Failed to resend verification code"),console.error("Error:",e),H({title:"Error",description:"Failed to resend verification code"})}finally{M(!1)}}},Q=async e=>{e.preventDefault(),M(!0),I("");try{if((await _.confirm(E)).user){let e={requestParameters:{PhoneNumber:`+${t}`}},s=await (0,C.MakeApiCallAsync)(C.TS.END_POINT_NAMES.GET_USER_BY_PHONE,null,e,{Accept:"application/json","Content-Type":"application/json"},"POST",!0);if(s.data&&!s.data.errorMessage){let e;e="string"==typeof s.data.data?JSON.parse(s.data.data):s.data.data,Array.isArray(e)&&e.length>0&&Y(e[0].EmailAddress||e[0].Email||"")}r("reset"),H({title:"Phone Verified",description:"Your phone number has been verified. You can now reset your password."})}}catch(e){I(e.message||"Invalid verification code"),console.error("Error:",e),H({title:"Verification Failed",description:e.message||"Invalid verification code"})}finally{M(!1)}},ee=async e=>{if(e.preventDefault(),M(!0),I(""),!G.newPassword||G.newPassword.length<6){I("Password must be at least 6 characters long"),M(!1);return}if(G.newPassword!==G.confirmPassword){I("Passwords do not match"),M(!1);return}try{let e={requestParameters:{PhoneNumber:`+${t}`,NewPassword:G.newPassword,Email:U}},r=await (0,C.MakeApiCallAsync)(C.TS.END_POINT_NAMES.RESET_PASSWORD_BY_PHONE,null,e,{Accept:"application/json","Content-Type":"application/json"},"POST",!0);if(r.data&&!r.data.errorMessage){let e;e="string"==typeof r.data.data?JSON.parse(r.data.data):r.data.data,Array.isArray(e)&&e.length>0&&"Saved Successfully"===e[0].ResponseMsg?(H({title:"Password Reset Successful",description:"Your password has been reset successfully. You can now log in with your new password."}),setTimeout(()=>{W.push("/login")},2e3)):I("Failed to reset password. Please try again.")}else I(r.data?.errorMessage||"Failed to reset password. Please try again.")}catch(e){I(e.message||"Failed to reset password"),console.error("Error:",e),H({title:"Reset Failed",description:e.message||"Failed to reset password"})}finally{M(!1)}};return(0,s.jsx)("div",{className:"min-h-screen bg-background flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("h2",{className:"mt-6 text-3xl font-extrabold",children:["phone"===e&&"Reset Password","verification"===e&&"Verify Your Phone","reset"===e&&"Create New Password"]}),(0,s.jsxs)("p",{className:"mt-2 text-sm text-muted-foreground",children:["phone"===e&&"Enter your phone number to reset your password","verification"===e&&"Enter the code we sent to your phone","reset"===e&&"Enter your new password"]})]}),(0,s.jsxs)(n.Zp,{className:"mt-8 p-8 shadow-xl bg-card/100",children:[(0,s.jsx)("div",{className:"flex justify-center mb-8",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:`w-8 h-8 rounded-full flex items-center justify-center ${"phone"===e?"bg-primary text-primary-foreground":"bg-primary/20 text-primary"}`,children:(0,s.jsx)(g.A,{className:"w-4 h-4"})}),(0,s.jsx)("div",{className:`w-16 h-1 ${"phone"===e?"bg-primary/20":"bg-primary"}`}),(0,s.jsx)("div",{className:`w-8 h-8 rounded-full flex items-center justify-center ${"verification"===e?"bg-primary text-primary-foreground":"reset"===e?"bg-primary":"bg-primary/20 text-primary"}`,children:(0,s.jsx)(y.A,{className:"w-4 h-4"})}),(0,s.jsx)("div",{className:`w-16 h-1 ${"reset"===e?"bg-primary":"bg-primary/20"}`}),(0,s.jsx)("div",{className:`w-8 h-8 rounded-full flex items-center justify-center ${"reset"===e?"bg-primary text-primary-foreground":"bg-primary/20 text-primary"}`,children:(0,s.jsx)(b,{className:"w-4 h-4"})})]})}),(0,s.jsxs)(m.P.div,{variants:{hidden:{opacity:0,x:-20},visible:{opacity:1,x:0},exit:{opacity:0,x:20}},initial:"hidden",animate:"visible",exit:"exit",transition:{duration:.3},children:["phone"===e&&(0,s.jsxs)("form",{onSubmit:X,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{className:"block text-sm font-medium mb-2 text-center",children:"Phone Number"}),(0,s.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,s.jsx)("div",{className:"w-full max-w-[300px]",children:(0,s.jsx)(p(),{country:x,value:t,onChange:e=>{u(e),I("")},enableSearch:!0,searchPlaceholder:"Search country...",containerClass:"w-full",inputClass:`w-full p-4 border rounded-lg focus:ring-2 focus:ring-primary/50 ${D?"border-destructive":""}`,buttonClass:"!border-input !bg-background hover:!bg-accent",dropdownClass:"!bg-background !border-input",disabled:F,countryCodeEditable:!1,isValid:(e,r)=>!!(e&&!(e.length<8)&&/^\+?[1-9]\d{1,14}$/.test(e))})}),D&&(0,s.jsx)("p",{className:"text-sm text-destructive",children:D})]})]}),(0,s.jsx)("div",{id:"recaptcha-container"}),(0,s.jsx)(d.$,{type:"submit",className:"w-full h-12 text-lg bg-primary text-primary-foreground hover:bg-primary/90 transition-colors flex items-center justify-center gap-2",disabled:F,children:F?(0,s.jsx)(w.A,{className:"w-4 h-4 animate-spin"}):(0,s.jsxs)(s.Fragment,{children:["Send Code ",(0,s.jsx)(v.A,{className:"w-4 h-4"})]})}),(0,s.jsxs)("div",{className:"text-center text-sm mt-4",children:[(0,s.jsx)("span",{className:"text-muted-foreground",children:"Remember your password? "}),(0,s.jsx)(f(),{href:"/login",className:"text-primary hover:text-primary/80 hover:underline transition-colors",children:"Back to Login"})]})]}),"verification"===e&&(0,s.jsxs)("form",{onSubmit:Q,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{className:"block text-sm font-medium mb-4 text-center",children:"Verification Code"}),(0,s.jsx)("div",{className:"flex justify-center items-center gap-3",children:[...Array(6)].map((e,r)=>(0,s.jsx)(l.p,{type:"text",maxLength:1,className:"w-12 h-12 text-center text-2xl font-semibold rounded-lg focus:ring-2 focus:ring-primary/50 transition-all",value:E[r]||"",onChange:e=>{let t=E.split("");t[r]=e.target.value,k(t.join("")),e.target.value&&e.target.nextElementSibling&&e.target.nextElementSibling.focus(),I("")},disabled:F},r))}),(0,s.jsx)("div",{className:"mt-4 text-center",children:(0,s.jsx)("button",{type:"button",onClick:Z,className:`text-sm ${T>0?"text-muted-foreground":"text-primary hover:underline"}`,disabled:T>0||F,children:T>0?`Resend code in ${(e=>{let r=Math.floor(e/60);return`${r}:${(e%60).toString().padStart(2,"0")}`})(T)}`:"Resend code"})}),D&&(0,s.jsx)("p",{className:"text-sm text-destructive text-center mt-2",children:D})]}),(0,s.jsx)("div",{id:"recaptcha-container-resend"}),(0,s.jsx)(d.$,{type:"submit",className:"w-full h-12 text-lg bg-primary text-primary-foreground hover:bg-primary/90 transition-colors flex items-center justify-center gap-2",disabled:F||6!==E.length,children:F?(0,s.jsx)(w.A,{className:"w-4 h-4 animate-spin"}):(0,s.jsxs)(s.Fragment,{children:["Verify ",(0,s.jsx)(y.A,{className:"w-4 h-4"})]})})]}),"reset"===e&&(0,s.jsxs)("form",{onSubmit:ee,className:"space-y-6",children:[U&&(0,s.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 mb-4",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(y.A,{className:"w-5 h-5 text-green-600 mr-2"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-green-800",children:"Account Found"}),(0,s.jsx)("p",{className:"text-sm text-green-600",children:U})]})]})}),(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{className:"block text-sm font-medium mb-2",children:"New Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(l.p,{type:O?"text":"password",value:G.newPassword,onChange:e=>{J({...G,newPassword:e.target.value}),I("")},className:"pl-10 pr-10",placeholder:"Enter new password",required:!0,minLength:6,disabled:F}),(0,s.jsx)(j.A,{className:"w-4 h-4 absolute left-3 top-3 text-muted-foreground"}),(0,s.jsx)(d.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 hover:bg-transparent",onClick:()=>V(!O),disabled:F,children:O?(0,s.jsx)(N.A,{className:"w-4 h-4 text-muted-foreground"}):(0,s.jsx)(P.A,{className:"w-4 h-4 text-muted-foreground"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{className:"block text-sm font-medium mb-2",children:"Confirm New Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(l.p,{type:L?"text":"password",value:G.confirmPassword,onChange:e=>{J({...G,confirmPassword:e.target.value}),I("")},className:"pl-10 pr-10",placeholder:"Confirm new password",required:!0,minLength:6,disabled:F}),(0,s.jsx)(j.A,{className:"w-4 h-4 absolute left-3 top-3 text-muted-foreground"}),(0,s.jsx)(d.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 hover:bg-transparent",onClick:()=>z(!L),disabled:F,children:L?(0,s.jsx)(N.A,{className:"w-4 h-4 text-muted-foreground"}):(0,s.jsx)(P.A,{className:"w-4 h-4 text-muted-foreground"})})]})]}),D&&(0,s.jsx)("p",{className:"text-sm text-destructive text-center",children:D}),(0,s.jsx)(d.$,{type:"submit",className:"w-full h-12 text-lg bg-primary text-primary-foreground hover:bg-primary/90 transition-colors",disabled:F||!G.newPassword||!G.confirmPassword,children:F?(0,s.jsx)(w.A,{className:"w-4 h-4 animate-spin"}):"Reset Password"})]})]},e)]})]})})}},93220:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\forgot-password\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\forgot-password\\page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")},97643:(e,r,t)=>{"use strict";t.d(r,{Wu:()=>n,Zp:()=>o,wL:()=>d});var s=t(45512),a=t(58009),i=t(59462);let o=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));o.displayName="Card",a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...r})).displayName="CardHeader",a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("h3",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r})).displayName="CardTitle",a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("p",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",e),...r})).displayName="CardDescription";let n=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",e),...r}));n.displayName="CardContent";let d=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",e),...r}));d.displayName="CardFooter"}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[638,320,400,151,875],()=>t(12144));module.exports=s})();