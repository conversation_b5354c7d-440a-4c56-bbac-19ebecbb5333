(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{1826:(e,t,s)=>{Promise.resolve().then(s.bind(s,6539))},6227:(e,t,s)=>{"use strict";s.d(t,{J:()=>i,v:()=>l});var a=s(5155),r=s(2115),n=s(2862);let o=(0,r.createContext)(void 0);function l(e){let{children:t}=e,[s,l]=(0,r.useState)(null),[i,c]=(0,r.useState)(null),[d,m]=(0,r.useState)(!0);(0,r.useEffect)(()=>{(()=>{try{let e=localStorage.getItem("user"),t=localStorage.getItem("token");if(e&&"{}"!==e){let t=JSON.parse(e);l(t)}t&&c(t)}catch(e){console.error("Error initializing user from localStorage:",e),localStorage.removeItem("user"),localStorage.removeItem("token")}finally{m(!1)}})()},[]);let h=async(e,t)=>{try{var s;m(!0);let a=await (0,n.MakeApiCallAsync)(n.TS.END_POINT_NAMES.GET_USER_LOGIN,null,{requestParameters:{Email:e,Password:t}},{Accept:"application/json","Content-Type":"application/json"},"POST",!0);if(a&&a.data&&!a.data.errorMessage){let e;if(e="string"==typeof a.data.data?JSON.parse(a.data.data):a.data.data,!Array.isArray(e)||!(e.length>0)||"Login Successfully"!==e[0].ResponseMsg)return{success:!1,message:"Incorrect email or password!"};{let t=e[0];l(t),localStorage.setItem("user",JSON.stringify(t));let s=a.data.token||"";return c(s),localStorage.setItem("token",s),{success:!0,message:"Login successful!"}}}return{success:!1,message:(null===(s=a.data)||void 0===s?void 0:s.errorMessage)||"Login failed. Please try again."}}catch(e){return console.error("Login error:",e),{success:!1,message:"An error occurred. Please try again!"}}finally{m(!1)}},x=null!==s&&s.UserId>0;return(0,a.jsx)(o.Provider,{value:{user:s,isLoggedIn:x,isLoading:d,login:h,logout:()=>{l(null),c(null),localStorage.removeItem("user"),localStorage.removeItem("token"),localStorage.removeItem("userId")},token:i},children:t})}function i(){let e=(0,r.useContext)(o);if(void 0===e)throw Error("useUser must be used within a UserProvider");return e}},6539:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>ep});var a=s(5155);s(9324);var r=s(2115),n=s(9840),o=s.n(n),l=s(3831),i=s(6462),c=s(6122),d=s(9271),m=s(1466),h=s(7517),x=s(853),u=s(1719),p=s(591),f=s(8474),g=s(6046),j=s(8173),b=s.n(j),v=s(8936),y=s(9355),N=s(6227),w=s(2862),S=s(1119),A=s(1027),C=s(9602);let k=r.forwardRef((e,t)=>{let{className:s,children:r,...n}=e;return(0,a.jsxs)(S.bL,{ref:t,className:(0,C.cn)("relative z-10 flex max-w-max flex-1 items-center justify-center",s),...n,children:[r,(0,a.jsx)(_,{})]})});k.displayName=S.bL.displayName;let I=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(S.B8,{ref:t,className:(0,C.cn)("group flex flex-1 list-none items-center justify-center space-x-1 bg-white",s),...r})});I.displayName=S.B8.displayName;let E=S.q7,T=(0,A.F)("group inline-flex h-10 w-max items-center justify-center rounded-md bg-white px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50");r.forwardRef((e,t)=>{let{className:s,children:r,...n}=e;return(0,a.jsxs)(S.l9,{ref:t,className:(0,C.cn)(T(),"group",s),...n,children:[r," ",(0,a.jsx)(u.A,{className:"relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180","aria-hidden":"true"})]})}).displayName=S.l9.displayName,r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(S.UC,{ref:t,className:(0,C.cn)("left-0 top-0 w-full data-[motion^=from-]:animate-in data-[motion^=to-]:animate-out data-[motion^=from-]:fade-in data-[motion^=to-]:fade-out data-[motion=from-end]:slide-in-from-right-52 data-[motion=from-start]:slide-in-from-left-52 data-[motion=to-end]:slide-out-to-right-52 data-[motion=to-start]:slide-out-to-left-52 md:absolute md:w-auto ",s),...r})}).displayName=S.UC.displayName;let P=S.N_,_=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{className:(0,C.cn)("absolute left-0 top-full flex justify-center z-50"),children:(0,a.jsx)(S.LM,{className:(0,C.cn)("origin-top-center relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full overflow-hidden rounded-md border bg-white text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 md:w-[var(--radix-navigation-menu-viewport-width)] !bg-white",s),ref:t,...r})})});_.displayName=S.LM.displayName,r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(S.C1,{ref:t,className:(0,C.cn)("top-full z-[1] flex h-1.5 items-end justify-center overflow-hidden data-[state=visible]:animate-in data-[state=hidden]:animate-out data-[state=hidden]:fade-out data-[state=visible]:fade-in",s),...r,children:(0,a.jsx)("div",{className:"relative top-[60%] h-2 w-2 rotate-45 rounded-tl-sm bg-border shadow-md"})})}).displayName=S.C1.displayName;var O=s(532);let z=O.bL,D=O.l9,L=r.forwardRef((e,t)=>{let{className:s,align:r="center",sideOffset:n=4,...o}=e;return(0,a.jsx)(O.ZL,{children:(0,a.jsx)(O.UC,{ref:t,align:r,sideOffset:n,className:(0,C.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s),...o})})});L.displayName=O.UC.displayName;var M=s(4085),R=s(7110),U=s(767);let B=["#0074b2","#194234","#2a9d8f","#81d4fa","#f295ce","#fce4ec","#b39ddb","#bcaaa4","#ffccbc","#b2dfdb","#6c9bcf","#ffd552","#39b1df","#7986cb","#003554"],$=e=>{let t=e.replace("#",""),s=parseInt(t.slice(0,2),16),a=parseInt(t.slice(2,4),16);return(.299*s+.587*a+.114*parseInt(t.slice(4,6),16))/255>.5?"#000000":"#ffffff"};function F(e){let{onColorSelect:t,onClose:s}=e,[n,o]=(0,r.useState)("#0074b2");(0,r.useEffect)(()=>{o("#0074b2"),document.documentElement.style.setProperty("--primary","#0074b2"),document.documentElement.style.setProperty("--primary-foreground",$("#0074b2"))},[]);let l=e=>{o(e),document.documentElement.style.setProperty("--primary",e),document.documentElement.style.setProperty("--primary-foreground",$(e)),t(e)};return(0,a.jsx)("div",{className:"fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"bg-card border rounded-lg shadow-lg p-6 w-[320px] space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Choose a Color"}),(0,a.jsx)(M.$,{variant:"ghost",size:"icon",onClick:s,children:(0,a.jsx)(U.A,{className:"h-4 w-4"})})]}),(0,a.jsx)("div",{className:"grid grid-cols-3 gap-2",children:B.map(e=>(0,a.jsx)("button",{className:"w-full aspect-square rounded-lg transition-all duration-200 hover:scale-105 hover:shadow-lg ".concat(n===e?"ring-2 ring-primary":""),style:{backgroundColor:e,color:$(e)},onClick:()=>l(e),children:n===e&&"✓"},e))})]})})}function J(){let e=(0,g.useRouter)(),[t,s]=(0,r.useState)([]),[n,o]=(0,r.useState)(!0),[j,S]=(0,r.useState)(!1),[A,T]=(0,r.useState)(null),[_,O]=(0,r.useState)(null),[U,B]=(0,r.useState)(null),[$,J]=(0,r.useState)(!1),[H,W]=(0,r.useState)(!1),[q,G]=(0,r.useState)(""),[Z,K]=(0,r.useState)(0),[V,Y]=(0,r.useState)(0),Q=(0,v._)(),X=(0,y.n)(),{user:ee,isLoggedIn:et,logout:es}=(0,N.J)(),{theme:ea,language:er,primaryColor:en,toggleTheme:eo,setLanguage:el,setPrimaryColor:ei,t:ec}=(0,R.t)(),ed=()=>{let t=new URLSearchParams;q&&t.append("search",q),_&&t.append("category",_.toString()),e.push("/products?".concat(t.toString()))};return(0,r.useEffect)(()=>{(async()=>{try{var e,t;let a={"Content-Type":"application/json",Accept:"application/json",Authorization:"Bearer "+localStorage.getItem("token")},r=await (0,w.MakeApiCallAsync)(w.TS.END_POINT_NAMES.GET_CATEGORIES_LIST,null,{PageNumber:1,PageSize:100,SortColumn:"Name",SortOrder:"ASC"},a,"POST",!0);if(null==r?void 0:null===(e=r.data)||void 0===e?void 0:e.data)try{let e=JSON.parse(r.data.data);if(Array.isArray(e)){let t=e.filter(e=>!e.ParentCategoryID),a=e.filter(e=>e.ParentCategoryID),r=t.map(e=>({id:e.CategoryID,name:e.Name,subcategories:a.filter(t=>t.ParentCategoryID===e.CategoryID).map(e=>e.Name)}));s(r)}else console.error("Categories data is not an array:",e),s([])}catch(e){console.error("Error parsing categories data:",e),s([])}else(null==r?void 0:null===(t=r.data)||void 0===t?void 0:t.errorMessage)?console.error("API Error:",r.data.errorMessage):console.error("Invalid or empty response from API"),s([])}catch(e){console.error("Error fetching categories:",e),s([])}finally{o(!1)}})()},[]),(0,r.useEffect)(()=>{Q&&Q.items&&K(Q.totalItems)},[Q,null==Q?void 0:Q.items]),(0,r.useEffect)(()=>{X&&Y(X.totalItems)},[X,null==X?void 0:X.wishlistItems]),(0,a.jsxs)("header",{className:"w-full",children:[(0,a.jsx)(M.$,{variant:"ghost",size:"sm",className:"fixed right-0 top-1/2 -translate-y-1/2 z-50 bg-gradient-to-r from-background/90 to-background/60 backdrop-blur-sm shadow-lg rounded-l-lg border-l border-y border-border/40 md:flex hidden hover:translate-x-1 transition-all duration-200 hover:shadow-xl group items-center justify-center w-16 h-12",onClick:()=>S(!0),children:(0,a.jsx)("div",{className:"h-8 w-8 rounded-full ring-2 ring-border/50 group-hover:ring-primary/50 transition-all duration-200 shadow-inner",style:{backgroundColor:en}})}),(0,a.jsx)("div",{className:"hidden md:block text-white py-2.5",style:{backgroundColor:en},children:(0,a.jsxs)("div",{className:"container mx-auto flex flex-col md:flex-row justify-between items-center text-sm px-4",children:[(0,a.jsxs)("div",{className:"flex md:flex-row items-start justify-start gap-4 md:gap-8",children:[(0,a.jsxs)(b(),{href:"tel:00*************",className:"flex items-center gap-2 hover:text-white/80",children:[(0,a.jsx)(l.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"text-xs md:text-sm",children:ec("phone")})]}),(0,a.jsxs)(b(),{href:"mailto:<EMAIL>",className:"flex items-center gap-2 hover:text-white/80",children:[(0,a.jsx)(i.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"text-xs md:text-sm",children:ec("email")})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 md:gap-4",children:[(0,a.jsxs)(M.$,{variant:"ghost",size:"sm",className:"text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2",onClick:()=>el("en"===er?"ar":"en"),children:[(0,a.jsx)(c.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"text-sm",children:"en"===er?"العربية":"English"})]}),(0,a.jsxs)(M.$,{variant:"ghost",size:"sm",className:"text-white hover:text-white/80 flex items-center gap-2",onClick:()=>window.open("https://wa.me/*************?text=".concat(encodeURIComponent("Hello! I would like to chat with you regarding your services.")),"_blank"),children:[(0,a.jsx)(d.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"text-sm",children:ec("liveChat")})]}),et?(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)("span",{className:"text-white text-sm",children:[ec("welcome")," ",(null==ee?void 0:ee.FirstName)||(null==ee?void 0:ee.UserName)||(null==ee?void 0:ee.Email)]}),(0,a.jsxs)(M.$,{variant:"ghost",size:"sm",className:"text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2",onClick:es,children:[(0,a.jsx)(m.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"text-sm",children:ec("logout")||"Logout"})]})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(b(),{href:"/login",children:(0,a.jsxs)(M.$,{variant:"ghost",size:"sm",className:"text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"text-sm",children:ec("login")})]})}),(0,a.jsx)(b(),{href:"/signup",children:(0,a.jsxs)(M.$,{variant:"ghost",size:"sm",className:"text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2",children:[(0,a.jsx)(h.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"text-sm",children:ec("signUp")})]})})]})]})]})}),(0,a.jsxs)("div",{className:"container mx-auto py-4 px-4",children:[(0,a.jsxs)("div",{className:"md:hidden",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("div",{className:"w-20"}),(0,a.jsx)(b(),{href:"/",className:"flex items-center gap-2",children:(0,a.jsx)("div",{className:"text-[#1B3764] flex items-center gap-2",children:(0,a.jsx)("img",{src:"".concat("https://admin.codemedicalapps.com/","content/commonImages/otherImages/18b_logo2x.png"),alt:"Logo",className:"h-12 w-auto"})})}),(0,a.jsxs)(M.$,{variant:"ghost",size:"sm",className:"flex items-center gap-2 px-3 py-2 rounded-full border border-gray-200 hover:bg-gray-50",onClick:()=>el("en"===er?"ar":"en"),children:[(0,a.jsx)("span",{className:"text-lg",children:"en"===er?"\uD83C\uDDFA\uD83C\uDDF8":"\uD83C\uDDEE\uD83C\uDDF6"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"en"===er?"EN":"AR"})]})]}),(0,a.jsx)("div",{className:"w-full",children:(0,a.jsxs)("div",{className:"flex items-center gap-2 border rounded-full px-4 py-3 bg-background shadow-sm",children:[(0,a.jsx)("input",{type:"text",placeholder:ec("products")||"البحث عن المنتجات...",className:"bg-transparent border-none focus:outline-none text-sm flex-1 placeholder:text-gray-400",value:q,onChange:e=>G(e.target.value),onKeyDown:e=>"Enter"===e.key&&ed()}),(0,a.jsx)(M.$,{variant:"ghost",className:"h-8 w-8 p-0 hover:bg-accent/80 transition-colors",style:{color:en},onClick:ed,children:(0,a.jsx)(x.A,{className:"h-4 w-4"})})]})})]}),(0,a.jsxs)("div",{className:"hidden md:flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4 flex-1",children:[(0,a.jsx)(b(),{href:"/",className:"flex items-center gap-2",children:(0,a.jsx)("div",{className:"text-[#1B3764] flex items-center gap-2",children:(0,a.jsx)("img",{src:"".concat("https://admin.codemedicalapps.com/","content/commonImages/otherImages/18b_logo2x.png"),alt:"Logo",className:"h-16 w-auto"})})}),(0,a.jsxs)("div",{className:"flex items-center gap-2 border rounded-full px-3 py-1.5 flex-1 max-w-md ml-4",children:[(0,a.jsxs)(z,{children:[(0,a.jsx)(D,{asChild:!0,children:(0,a.jsxs)(M.$,{variant:"ghost",className:"h-8 flex items-center gap-1 px-2",children:[(0,a.jsx)("span",{className:"text-muted-foreground text-sm",children:A||U||ec("category")}),(0,a.jsx)(u.A,{className:"h-4 w-4 text-muted-foreground"})]})}),(0,a.jsx)(L,{className:"w-64 p-0",align:"start",children:(0,a.jsx)("div",{className:"max-h-[300px] overflow-auto",children:n?(0,a.jsx)("div",{className:"p-4 text-center text-muted-foreground",children:ec("loadingCategories")}):(0,a.jsx)("div",{className:"grid",children:t.map(e=>(0,a.jsxs)("div",{className:"group",children:[(0,a.jsx)("button",{className:"w-full px-4 py-2 text-left hover:bg-accent",onClick:()=>{T(e.name),O(e.id),B(null)},children:e.name}),(0,a.jsx)("div",{className:"hidden group-hover:block absolute left-full top-0 w-48 bg-white shadow-lg rounded-md border",children:e.subcategories.map((t,s)=>(0,a.jsx)("button",{className:"w-full px-4 py-2 text-left hover:bg-accent",onClick:()=>{B(t),T(null),O(e.id)},children:t},s))})]},e.id))})})})]}),(0,a.jsx)("div",{className:"h-5 w-px bg-border mx-2"}),(0,a.jsx)("input",{type:"text",placeholder:ec("products"),className:"bg-transparent border-none focus:outline-none text-sm flex-1",value:q,onChange:e=>G(e.target.value),onKeyDown:e=>"Enter"===e.key&&ed()}),(0,a.jsx)(M.$,{variant:"ghost",className:"h-8 w-8 p-0",onClick:ed,children:(0,a.jsx)(x.A,{className:"h-4 w-4"})})]})]}),(0,a.jsx)("div",{className:"hidden md:block",children:(0,a.jsx)(k,{children:(0,a.jsxs)(I,{children:[(0,a.jsx)(E,{children:(0,a.jsx)(b(),{href:"/",legacyBehavior:!0,passHref:!0,children:(0,a.jsx)(P,{className:(0,C.cn)("group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"),children:ec("home")})})}),(0,a.jsx)(E,{children:(0,a.jsx)(b(),{href:"/hot-deals",legacyBehavior:!0,passHref:!0,children:(0,a.jsx)(P,{className:(0,C.cn)("group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"),children:ec("hotDeals")})})}),(0,a.jsx)(E,{children:(0,a.jsx)(b(),{href:"/products",legacyBehavior:!0,passHref:!0,children:(0,a.jsx)(P,{className:(0,C.cn)("group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"),children:ec("products")||"Products"})})}),(0,a.jsx)(E,{children:(0,a.jsx)(b(),{href:"/payment-methods",legacyBehavior:!0,passHref:!0,children:(0,a.jsx)(P,{className:(0,C.cn)("group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"),children:ec("paymentMethods")})})}),(0,a.jsx)(E,{children:(0,a.jsx)(b(),{href:"/follow-us",legacyBehavior:!0,passHref:!0,children:(0,a.jsx)(P,{className:(0,C.cn)("group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"),children:ec("followUs")})})}),(0,a.jsx)(E,{children:(0,a.jsx)(b(),{href:"/about",legacyBehavior:!0,passHref:!0,children:(0,a.jsx)(P,{className:(0,C.cn)("group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"),children:ec("aboutUs")})})}),(0,a.jsx)(E,{children:(0,a.jsx)(b(),{href:"/contact",legacyBehavior:!0,passHref:!0,children:(0,a.jsx)(P,{className:(0,C.cn)("group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"),children:ec("contactUs")})})})]})})}),(0,a.jsxs)("div",{className:"hidden md:flex items-center gap-4",children:[(0,a.jsx)(b(),{href:"/wishlist",children:(0,a.jsxs)(M.$,{variant:"ghost",size:"icon",className:"relative",children:[(0,a.jsx)(p.A,{className:"h-5 w-5",style:{color:en}}),(0,a.jsx)("span",{className:"absolute -top-1 -right-1 bg-primary text-white text-xs rounded-full h-4 w-4 flex items-center justify-center",children:V})]})}),(0,a.jsx)(b(),{href:"/cart",children:(0,a.jsxs)(M.$,{variant:"ghost",size:"icon",className:"relative",children:[(0,a.jsx)(f.A,{className:"h-5 w-5",style:{color:en}}),(0,a.jsx)("span",{className:"absolute -top-1 -right-1 bg-primary text-white text-xs rounded-full h-4 w-4 flex items-center justify-center",children:Z})]})})]})]})]}),j&&(0,a.jsx)(F,{onColorSelect:e=>{ei(e),S(!1)},onClose:()=>S(!1)})]})}var H=s(2233),W=s(8283),q=s(3348),G=s(495);function Z(){let{primaryColor:e,t}=(0,R.t)(),[s,n]=(0,r.useState)(""),[o,l]=(0,r.useState)(!1),[i,c]=(0,r.useState)({type:"",text:""}),{executeRecaptcha:m}=(0,G._Y)(),h=async e=>{if(e.preventDefault(),!m){console.error("Execute recaptcha not yet available");return}try{l(!0),c({type:"",text:""}),await m("subscribe");let e=await fetch("".concat("https://admin.codemedicalapps.com/","api/v1/dynamic/dataoperation/insert-subscriber"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({requestParameters:{SubscriberEmail:s}})}),t=await e.json();e.ok?(c({type:"success",text:"Successfully subscribed!"}),n("")):c({type:"error",text:t.message||"Failed to subscribe"})}catch(e){c({type:"error",text:"An error occurred. Please try again."})}finally{l(!1)}};return(0,a.jsx)("footer",{className:"w-full",children:(0,a.jsxs)("div",{className:"text-white py-8 sm:py-12",style:{backgroundColor:e},children:[(0,a.jsxs)("div",{className:"container mx-auto px-4 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6 sm:gap-8",children:[(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"flex items-center gap-2",children:(0,a.jsx)("img",{src:"".concat("https://admin.codemedicalapps.com/","content/commonImages/otherImages/18b_logo2x.png").replace(/\//g,"/"),alt:"Logo",className:"h-12 sm:h-16 w-auto bg-white p-2 rounded-md"})}),(0,a.jsx)("p",{className:"text-gray-300 text-sm sm:text-base",children:"We are professional team specialized in providing well known valuable medical courses, Ebooks, Printed books and popular medical accounts for all medical field staff all around the world with low cost and in short time."}),(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)(b(),{href:"https://www.facebook.com/codemedicalapps/",target:"_blank",rel:"noopener noreferrer",className:"hover:text-gray-300 transition-colors",children:(0,a.jsx)(H.A,{className:"h-5 w-5"})}),(0,a.jsx)(b(),{href:"https://t.me/codemedicalapps",target:"_blank",rel:"noopener noreferrer",className:"hover:text-gray-300 transition-colors",children:(0,a.jsx)(W.A,{className:"h-5 w-5"})}),(0,a.jsx)(b(),{href:"https://wa.me/*************",target:"_blank",rel:"noopener noreferrer",className:"hover:text-gray-300 transition-colors",children:(0,a.jsx)(d.A,{className:"h-5 w-5"})}),(0,a.jsx)(b(),{href:"https://m.me/***************",target:"_blank",rel:"noopener noreferrer",className:"hover:text-gray-300 transition-colors",children:(0,a.jsx)(q.A,{className:"h-5 w-5"})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-6 sm:gap-8 col-span-2 sm:col-span-1 md:col-span-2",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-base sm:text-lg font-semibold",children:t("quickLinks")}),(0,a.jsxs)("ul",{className:"space-y-2 sm:space-y-3 text-sm sm:text-base",children:[(0,a.jsx)("li",{children:(0,a.jsx)(b(),{href:"/about",className:"hover:text-gray-300 transition-colors",children:t("about")})}),(0,a.jsx)("li",{children:(0,a.jsx)(b(),{href:"/contact",className:"hover:text-gray-300 transition-colors",children:t("contact")})}),(0,a.jsx)("li",{children:(0,a.jsx)(b(),{href:"/hot-deals",className:"hover:text-gray-300 transition-colors",children:t("hotDeals")})}),(0,a.jsx)("li",{children:(0,a.jsx)(b(),{href:"/login",className:"hover:text-gray-300 transition-colors",children:t("login")})}),(0,a.jsx)("li",{children:(0,a.jsx)(b(),{href:"/signup",className:"hover:text-gray-300 transition-colors",children:t("signup")})})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-base sm:text-lg font-semibold",children:t("customerArea")}),(0,a.jsxs)("ul",{className:"space-y-2 sm:space-y-3 text-sm sm:text-base",children:[(0,a.jsx)("li",{children:(0,a.jsx)(b(),{href:"/account",className:"hover:text-gray-300 transition-colors",children:t("myAccount")})}),(0,a.jsx)("li",{children:(0,a.jsx)(b(),{href:"/orders",className:"hover:text-gray-300 transition-colors",children:t("orders")})}),(0,a.jsx)("li",{children:(0,a.jsx)(b(),{href:"/cart",className:"hover:text-gray-300 transition-colors",children:t("cart")})}),(0,a.jsx)("li",{children:(0,a.jsx)(b(),{href:"/wishlist",className:"hover:text-gray-300 transition-colors",children:t("wishlist")})}),(0,a.jsx)("li",{children:(0,a.jsx)(b(),{href:"/payment-methods",className:"hover:text-gray-300 transition-colors",children:t("paymentMethods")})})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-base sm:text-lg font-semibold",children:t("contact")}),(0,a.jsxs)("ul",{className:"space-y-2 sm:space-y-3 text-sm sm:text-base",children:[(0,a.jsxs)("li",{className:"flex items-center gap-2",children:[(0,a.jsxs)("span",{className:"text-gray-300",children:[t("location"),":"]}),(0,a.jsx)("span",{children:"Iraq"})]}),(0,a.jsxs)("li",{className:"flex items-center gap-2",children:[(0,a.jsxs)("span",{className:"text-gray-300",children:[t("callUs"),":"]}),(0,a.jsx)("a",{href:"tel:+*************",className:"hover:text-gray-300 transition-colors",children:"+964 ************"})]}),(0,a.jsxs)("li",{className:"flex items-center gap-2",children:[(0,a.jsxs)("span",{className:"text-gray-300",children:[t("emailUs"),":"]}),(0,a.jsx)("a",{href:"mailto:<EMAIL>",className:"hover:text-gray-300 transition-colors",children:"<EMAIL>"})]})]}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsx)("h3",{className:"text-base sm:text-lg font-semibold mb-4",children:t("newsletter")}),(0,a.jsxs)("form",{onSubmit:h,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,a.jsx)("input",{type:"email",value:s,onChange:e=>n(e.target.value),placeholder:t("enterEmail"),required:!0,className:"w-full px-4 py-2 text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-opacity-50",style:{outlineColor:e}}),(0,a.jsx)("button",{type:"submit",disabled:o,className:" px-2 py-2 border border-white rounded-md",style:{backgroundColor:e,color:"white",borderColor:"white"},children:o?(0,a.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white"}):(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("span",{children:t("subscribe")})})})]}),i.text&&(0,a.jsx)("p",{className:"text-sm text-center ".concat("success"===i.type?"text-green-400":"text-red-400"),children:i.text}),(0,a.jsx)("p",{className:"text-xs text-gray-300 text-center",children:t("newsletterDisclaimer")})]})]})]})]}),(0,a.jsx)("div",{className:"mt-6 pt-6 border-t border-white/20 text-center text-gray-300 text-sm sm:text-base px-4",children:(0,a.jsx)("p",{children:"\xa9 2024 Code Medical. All rights reserved."})})]})})}function K(e){let{className:t=""}=e;return(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",className:t,fill:"currentColor",children:(0,a.jsx)("path",{d:"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413Z"})})}function V(){return(0,a.jsx)("button",{className:"fixed bottom-20 right-6 z-50 flex h-12 w-12 items-center justify-center rounded-full bg-[#25D366] text-white shadow-md transition-all duration-300 hover:bg-[#128C7E] hover:scale-110",onClick:()=>{window.open("https://wa.me/00*************","_blank")},"aria-label":"Chat on WhatsApp",children:(0,a.jsx)(K,{className:"h-6 w-6"})})}var Y=s(6507),Q=s(5440);function X(){let e=(0,g.usePathname)(),{totalItems:t}=(0,v._)(),{totalItems:s}=(0,y.n)(),{primaryColor:n,t:o}=(0,R.t)(),[l,i]=(0,r.useState)(!1),[c,d]=(0,r.useState)(!1),[h,x]=(0,r.useState)([]),[u,j]=(0,r.useState)(!1);(0,r.useEffect)(()=>{i(!0)},[]);let N=async()=>{if(h.length>0){d(!0);return}j(!0);try{var e;let t={"Content-Type":"application/json",Accept:"application/json",Authorization:"Bearer "+localStorage.getItem("token")},s=await (0,w.MakeApiCallAsync)(w.TS.END_POINT_NAMES.GET_CATEGORIES_LIST,null,{PageNumber:1,PageSize:100,SortColumn:"Name",SortOrder:"ASC"},t,"POST",!0);if(null==s?void 0:null===(e=s.data)||void 0===e?void 0:e.data)try{let e=JSON.parse(s.data.data);if(Array.isArray(e)){let t=e.filter(e=>!e.ParentCategoryID);x(t),d(!0)}}catch(e){console.error("Error parsing categories data:",e)}}catch(e){console.error("Error fetching categories:",e)}finally{j(!1)}};if(!l)return null;let S=[{href:"/",icon:Y.A,label:o("home")||"الرئيسية",isActive:"/"===e,onClick:null},{href:"#",icon:Q.A,label:o("categories")||"التصنيفات",isActive:!1,onClick:N},{href:"/cart",icon:f.A,label:o("cart")||"سلة التسوق",isActive:"/cart"===e,badge:t||0,onClick:null},{href:"/wishlist",icon:p.A,label:o("wishlist")||"المفضلة",isActive:"/wishlist"===e,badge:s||0,onClick:null},{href:"/login",icon:m.A,label:o("login")||"حسابي",isActive:"/login"===e||"/signup"===e,onClick:null}];return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50 safe-area-pb",children:(0,a.jsx)("div",{className:"flex items-center justify-around py-2",children:S.map(e=>{let t=e.icon,s=!!e.onClick,r="flex flex-col items-center justify-center min-w-0 flex-1 py-2 px-1"+(s?" bg-transparent border-none":""),o=(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(t,{className:"h-6 w-6 mb-1",style:{color:e.isActive?n:"#6B7280"}}),void 0!==e.badge&&e.badge>0&&(0,a.jsx)("span",{className:"absolute -top-1 -right-1 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center font-medium shadow-md",style:{backgroundColor:n},children:e.badge>99?"99+":e.badge})]}),(0,a.jsx)("span",{className:"text-xs font-medium text-center leading-tight mt-1",style:{color:e.isActive?n:"#6B7280"},children:e.label})]});return s?(0,a.jsx)("button",{onClick:e.onClick||void 0,className:r,type:"button",children:o},e.href):(0,a.jsx)(b(),{href:e.href,className:r,children:o},e.href)})})}),c&&(0,a.jsx)("div",{className:"md:hidden fixed inset-0 bg-black/50 z-50 flex items-end",children:(0,a.jsxs)("div",{className:"bg-white w-full max-h-[70vh] rounded-t-xl overflow-hidden",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold",style:{color:n},children:o("categories")||"التصنيفات"}),(0,a.jsx)(M.$,{variant:"ghost",size:"sm",onClick:()=>d(!1),className:"text-gray-500 hover:text-gray-700",children:"✕"})]}),(0,a.jsx)("div",{className:"overflow-y-auto max-h-[calc(70vh-80px)]",children:u?(0,a.jsxs)("div",{className:"p-8 text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 mx-auto mb-4",style:{borderColor:n}}),(0,a.jsx)("p",{className:"text-gray-500",children:o("loadingCategories")})]}):(0,a.jsx)("div",{className:"p-4 space-y-2",children:h.map(e=>(0,a.jsx)(b(),{href:"/products?category=".concat(e.CategoryID),className:"block p-3 rounded-lg hover:bg-gray-50 transition-colors",onClick:()=>d(!1),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"font-medium text-gray-900",children:e.Name}),(0,a.jsx)("span",{className:"text-gray-400",children:"›"})]})},e.CategoryID))})})]})})]})}var ee=s(9134);let et=r.forwardRef((e,t)=>{let{className:s,title:r,description:n,action:o,...l}=e;return(0,a.jsxs)("div",{ref:t,className:(0,C.cn)("group relative flex items-center justify-between p-4 pr-8 space-x-4 overflow-hidden rounded-md border bg-background shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none",s),...l,children:[(0,a.jsxs)("div",{className:"grid gap-1",children:[r&&(0,a.jsx)("div",{className:"text-sm font-semibold",children:r}),n&&(0,a.jsx)("div",{className:"text-sm opacity-90",children:n})]}),o,(0,a.jsx)("button",{className:"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100",children:(0,a.jsx)(U.A,{className:"h-4 w-4"})})]})});et.displayName="Toast";let es=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,C.cn)("text-sm font-semibold",s),...r})});es.displayName="ToastTitle";let ea=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,C.cn)("text-sm opacity-90",s),...r})});ea.displayName="ToastDescription";let er=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("button",{ref:t,className:(0,C.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100",s),...r,children:(0,a.jsx)(U.A,{className:"h-4 w-4"})})});er.displayName="ToastClose";let en=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,C.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",s),...r})});function eo(e){let{children:t,...s}=e;return(0,a.jsx)(a.Fragment,{children:t})}function el(){let{toasts:e}=(0,ee.dj)();return(0,a.jsxs)(eo,{children:[e.map(function(e){let{id:t,title:s,description:r,action:n,...o}=e;return(0,a.jsxs)(et,{...o,children:[(0,a.jsxs)("div",{className:"grid gap-1",children:[s&&(0,a.jsx)(es,{children:s}),r&&(0,a.jsx)(ea,{children:r})]}),n,(0,a.jsx)(er,{})]},t)}),(0,a.jsx)(en,{})]})}en.displayName="ToastViewport",eo.displayName="ToastProvider";let ei={whatsappNumber:"+**********",phoneNumber:"+**********",whatsappLink:"https://wa.me/**********"},ec=(0,r.createContext)(ei);function ed(e){let{children:t}=e;return(0,a.jsx)(ec.Provider,{value:ei,children:t})}var em=s(2604),eh=s(5943);function ex(e){let{children:t}=e;return(0,a.jsx)(R.Z,{children:(0,a.jsx)(N.v,{children:(0,a.jsx)(eh.B,{children:(0,a.jsx)(v.e,{children:(0,a.jsx)(ed,{children:(0,a.jsx)(em.U,{children:(0,a.jsx)(y.Z,{children:t})})})})})})})}var eu=s(814);function ep(e){let{children:t}=e;return(0,a.jsx)("html",{lang:"en",children:(0,a.jsx)("body",{className:o().className,suppressHydrationWarning:!0,children:(0,a.jsx)(G.G3,{reCaptchaKey:"6LfS7D0rAAAAAAybL-FSr2N1pWStAiKxN_EapgHJ",scriptProps:{async:!0,defer:!0,appendTo:"body",nonce:void 0},container:{parameters:{badge:"inline",theme:"light"}},children:(0,a.jsxs)(ex,{children:[(0,a.jsx)(J,{}),(0,a.jsx)("main",{className:"min-h-screen pb-16 md:pb-0",children:t}),(0,a.jsx)(Z,{}),(0,a.jsx)(V,{}),(0,a.jsx)(X,{}),(0,a.jsx)(el,{}),(0,a.jsx)(eu.l$,{position:"top-right"})]})})})})}},9134:(e,t,s)=>{"use strict";s.d(t,{dj:()=>h});var a=s(2115);let r=0,n=new Map,o=e=>{if(n.has(e))return;let t=setTimeout(()=>{n.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);n.set(e,t)},l=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:s}=t;return s?o(s):e.toasts.forEach(e=>{o(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},i=[],c={toasts:[]};function d(e){c=l(c,e),i.forEach(e=>{e(c)})}function m(e){let{...t}=e,s=(r=(r+1)%100).toString(),a=()=>d({type:"DISMISS_TOAST",toastId:s});return d({type:"ADD_TOAST",toast:{...t,id:s,open:!0,onOpenChange:e=>{e||a()}}}),{id:s,dismiss:a,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:s}})}}function h(){let[e,t]=a.useState(c);return a.useEffect(()=>(i.push(t),()=>{let e=i.indexOf(t);e>-1&&i.splice(e,1)}),[e]),{...e,toast:m,dismiss:e=>d({type:"DISMISS_TOAST",toastId:e})}}},9324:()=>{},9355:(e,t,s)=>{"use strict";s.d(t,{Z:()=>o,n:()=>l});var a=s(5155),r=s(2115);let n=(0,r.createContext)(void 0);function o(e){let{children:t}=e,[s,o]=(0,r.useState)([]),[l,i]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{let e=localStorage.getItem("wishlist");if(e)try{o(JSON.parse(e))}catch(e){console.error("Failed to parse wishlist from localStorage:",e)}i(!0)},[]),(0,r.useEffect)(()=>{localStorage.setItem("wishlist",JSON.stringify(s))},[s]),(0,a.jsx)(n.Provider,{value:{wishlistItems:s,addToWishlist:e=>{s.includes(e)||o([...s,e])},removeFromWishlist:e=>{o(s.filter(t=>t!==e))},isInWishlist:e=>s.includes(e),totalItems:s.length,isHydrated:l},children:t})}function l(){let e=(0,r.useContext)(n);if(void 0===e)throw Error("useWishlist must be used within a WishlistProvider");return e}}},e=>{var t=t=>e(e.s=t);e.O(0,[7385,1345,2651,6764,7663,7542,2081,3414,8441,6587,7358],()=>t(1826)),_N_E=e.O()}]);