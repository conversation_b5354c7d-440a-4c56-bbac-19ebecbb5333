/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/video-proxy/route";
exports.ids = ["app/api/video-proxy/route"];
exports.modules = {

/***/ "(rsc)/./app/api/video-proxy/route.ts":
/*!**************************************!*\
  !*** ./app/api/video-proxy/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nasync function GET(request) {\n    try {\n        const url = request.nextUrl.searchParams.get('url');\n        if (!url) {\n            return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse('Missing URL parameter', {\n                status: 400\n            });\n        }\n        const videoResponse = await fetch(url, {\n            headers: {\n                'Range': request.headers.get('range') || 'bytes=0-',\n                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'\n            }\n        });\n        if (!videoResponse.ok) {\n            return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse('Failed to fetch video', {\n                status: videoResponse.status\n            });\n        }\n        const headers = new Headers();\n        // Copy content type and content length\n        headers.set('Content-Type', videoResponse.headers.get('Content-Type') || 'video/mp4');\n        headers.set('Content-Length', videoResponse.headers.get('Content-Length') || '');\n        headers.set('Accept-Ranges', 'bytes');\n        // Copy range headers for streaming\n        if (videoResponse.headers.get('Content-Range')) {\n            headers.set('Content-Range', videoResponse.headers.get('Content-Range') || '');\n        }\n        // Set CORS headers\n        headers.set('Access-Control-Allow-Origin', '*');\n        headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS');\n        headers.set('Access-Control-Allow-Headers', 'Range');\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(videoResponse.body, {\n            status: videoResponse.status,\n            headers\n        });\n    } catch (error) {\n        console.error('Video proxy error:', error);\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse('Internal Server Error', {\n            status: 500\n        });\n    }\n}\nasync function OPTIONS() {\n    const headers = new Headers();\n    headers.set('Access-Control-Allow-Origin', '*');\n    headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS');\n    headers.set('Access-Control-Allow-Headers', 'Range');\n    return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(null, {\n        status: 204,\n        headers\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/video-proxy/route.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvideo-proxy%2Froute&page=%2Fapi%2Fvideo-proxy%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvideo-proxy%2Froute.ts&appDir=C%3A%5CUsers%5Cyhyasoft%5CDownloads%5Cec%5C.NET%208%20Version%20-%20Latest%5Cproject%5Ccodemedical%5Cproject3%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyhyasoft%5CDownloads%5Cec%5C.NET%208%20Version%20-%20Latest%5Cproject%5Ccodemedical%5Cproject3&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvideo-proxy%2Froute&page=%2Fapi%2Fvideo-proxy%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvideo-proxy%2Froute.ts&appDir=C%3A%5CUsers%5Cyhyasoft%5CDownloads%5Cec%5C.NET%208%20Version%20-%20Latest%5Cproject%5Ccodemedical%5Cproject3%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyhyasoft%5CDownloads%5Cec%5C.NET%208%20Version%20-%20Latest%5Cproject%5Ccodemedical%5Cproject3&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_yhyasoft_Downloads_ec_NET_8_Version_Latest_project_codemedical_project3_app_api_video_proxy_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/video-proxy/route.ts */ \"(rsc)/./app/api/video-proxy/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/video-proxy/route\",\n        pathname: \"/api/video-proxy\",\n        filename: \"route\",\n        bundlePath: \"app/api/video-proxy/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\api\\\\video-proxy\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_yhyasoft_Downloads_ec_NET_8_Version_Latest_project_codemedical_project3_app_api_video_proxy_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvideo-proxy%2Froute&page=%2Fapi%2Fvideo-proxy%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvideo-proxy%2Froute.ts&appDir=C%3A%5CUsers%5Cyhyasoft%5CDownloads%5Cec%5C.NET%208%20Version%20-%20Latest%5Cproject%5Ccodemedical%5Cproject3%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyhyasoft%5CDownloads%5Cec%5C.NET%208%20Version%20-%20Latest%5Cproject%5Ccodemedical%5Cproject3&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvideo-proxy%2Froute&page=%2Fapi%2Fvideo-proxy%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvideo-proxy%2Froute.ts&appDir=C%3A%5CUsers%5Cyhyasoft%5CDownloads%5Cec%5C.NET%208%20Version%20-%20Latest%5Cproject%5Ccodemedical%5Cproject3%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyhyasoft%5CDownloads%5Cec%5C.NET%208%20Version%20-%20Latest%5Cproject%5Ccodemedical%5Cproject3&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();