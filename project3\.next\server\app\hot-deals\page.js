(()=>{var e={};e.id=416,e.ids=[416],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9086:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("Flame",[["path",{d:"M8.5 14.5A2.5 2.5 0 0 0 11 12c0-1.38-.5-2-1-3-1.072-2.143-.224-4.054 2-6 .5 2.5 2 4.9 4 6.5 2 1.6 3 3.5 3 5.5a7 7 0 1 1-14 0c0-1.153.433-2.294 1-3a2.5 2.5 0 0 0 2.5 2.5z",key:"96xj49"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},12720:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>m,tree:()=>n});var r=t(70260),a=t(28203),i=t(25155),o=t.n(i),l=t(67292),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(s,d);let n={children:["",{children:["hot-deals",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,19034)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\hot-deals\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,19611)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\hot-deals\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/hot-deals/page",pathname:"/hot-deals",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:n}})},19034:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\hot-deals\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\hot-deals\\page.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36708:(e,s,t)=>{Promise.resolve().then(t.bind(t,19034))},44324:(e,s,t)=>{Promise.resolve().then(t.bind(t,63566))},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63566:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>p});var r=t(45512),a=t(58009),i=t(39913),o=t(28531),l=t.n(o),d=t(99905),n=t(9086),c=t(71901);function p(){let{t:e,primaryColor:s}=(0,c.t)(),[t,o]=(0,a.useState)([]),[p,m]=(0,a.useState)(!0);return(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsxs)("div",{className:"flex items-center text-sm mb-6",children:[(0,r.jsx)(l(),{href:"/",className:"text-gray-500 hover:text-primary",children:"Home"}),(0,r.jsx)(d.A,{className:"h-4 w-4 mx-2 text-gray-400"}),(0,r.jsx)("span",{className:"font-medium",children:"Hot Deals"})]}),(0,r.jsxs)("div",{className:"mb-8 flex items-center",children:[(0,r.jsx)(n.A,{className:"h-8 w-8 mr-3",style:{color:s}}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold",style:{color:s},children:"Hot Deals"}),(0,r.jsx)("p",{className:"text-gray-600 mt-2",children:"Special offers and discounts on our best products"})]})]}),p?(0,r.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:[void 0,void 0,void 0,void 0].map((e,s)=>(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[(0,r.jsx)("div",{className:"h-48 bg-gray-200 animate-pulse"}),(0,r.jsxs)("div",{className:"p-4 space-y-2",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse w-2/3"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse w-1/3"})]})]},s))}):t.length>0?(0,r.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:t.map(e=>(0,r.jsxs)(l(),{href:`/product/${e.id}`,className:"bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:scale-105",children:[(0,r.jsxs)("div",{className:"relative h-48 bg-gray-100",children:[(0,r.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:e.image?(0,r.jsx)("img",{src:e.image.startsWith("http")?e.image:e.image.startsWith("/")?`${i.T.ADMIN_BASE_URL.replace(/\/$/,"")}${e.image}`:`${i.T.ADMIN_BASE_URL.replace(/\/$/,"")}/${e.image}`,alt:e.name,className:"w-full h-full object-cover",onError:s=>{let t=s.target;t.onerror=null,t.src=`/products/book${e.id}.jpg`}}):(0,r.jsx)("div",{className:"w-32 h-32 bg-gray-200 flex items-center justify-center text-gray-500",children:"Product Image"})}),e.discountPrice&&(0,r.jsxs)("div",{className:"absolute top-2 right-2 px-2 py-1 text-xs font-bold text-white rounded",style:{backgroundColor:s},children:[Math.round((e.price-e.discountPrice)/e.price*100),"% OFF"]})]}),(0,r.jsxs)("div",{className:"p-4",children:[(0,r.jsx)("h3",{className:"font-medium text-gray-900 mb-1",children:e.name}),(0,r.jsxs)("div",{className:"flex items-center mb-2",children:[(0,r.jsx)("div",{className:"flex",children:[void 0,void 0,void 0,void 0,void 0].map((s,t)=>(0,r.jsx)("svg",{className:`w-4 h-4 ${t<Math.floor(e.rating)?"text-yellow-400":"text-gray-300"}`,fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})},t))}),(0,r.jsxs)("span",{className:"text-xs text-gray-500 ml-1",children:["(",e.rating,")"]})]}),(0,r.jsx)("div",{className:"flex items-center",children:e.discountPrice&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("span",{className:"text-lg font-bold",style:{color:s},children:["$",e.discountPrice.toFixed(2)]}),(0,r.jsxs)("span",{className:"text-sm text-gray-500 line-through ml-2",children:["$",e.price.toFixed(2)]})]})})]})]},e.id))}):(0,r.jsx)("div",{className:"text-center py-12",children:(0,r.jsx)("p",{className:"text-gray-500",children:"No deals found"})})]})}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")},99905:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[638,320,875],()=>t(12720));module.exports=r})();