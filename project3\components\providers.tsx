'use client';

import { Setting<PERSON>Provider } from '@/contexts/settings-context';
import { CartProvider } from '@/contexts/cart-context';
import { ContactProvider } from '@/contexts/contact-info';
import { CouponProvider } from '@/contexts/coupon-context';
import { WishlistProvider } from '@/contexts/wishlist-context';
import { CurrencyProvider } from '@/contexts/currency-context';

interface ProvidersProps {
  children: React.ReactNode;
}

export function Providers({ children }: ProvidersProps) {
  return (
    <SettingsProvider>
      <CurrencyProvider>
        <CartProvider>
          <ContactProvider>
            <CouponProvider>
              <WishlistProvider>
                {children}
              </WishlistProvider>
            </CouponProvider>
          </ContactProvider>
        </CartProvider>
      </CurrencyProvider>
    </SettingsProvider>
  );
}