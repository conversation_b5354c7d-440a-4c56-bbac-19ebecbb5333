@model Entities.MainModels.SalesModel

<div class="modal-dialog modal-lg">
    <div class="modal-content">
        <div class="modal-header bg-purple-300">
            <h6 class="modal-title">Edit Shipping Details</h6>
            <button type="button" class="close" data-dismiss="modal">×</button>
        </div>

        <form id="edit-shipping-form">
            <div class="modal-body">
                <div class="form-row">
                    <div class="form-group col-md-6">
                        <label for="edit_addresslineone">Address Line 1</label>
                        <input type="text" class="form-control" id="edit_addresslineone" name="AddressLine1" value="@Model?.OrderShippingMasterData?.AddressLineOne" required>
                    </div>
                    <div class="form-group col-md-6">
                        <label for="edit_addresslinetwo">Address Line 2</label>
                        <input type="text" class="form-control" id="edit_addresslinetwo" name="AddressLine2" value="@Model?.OrderShippingMasterData?.AddressLineTwo">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group col-md-4">
                        <label for="edit_country">Country</label>
                        <input type="text" class="form-control" id="edit_country" name="Country" value="@Model?.OrderShippingMasterData?.CountryName" required>
                    </div>
                    <div class="form-group col-md-4">
                        <label for="edit_state">State</label>
                        <input type="text" class="form-control" id="edit_state" name="State" value="@Model?.OrderShippingMasterData?.StateName">
                    </div>
                    <div class="form-group col-md-4">
                        <label for="edit_city">City</label>
                        <input type="text" class="form-control" id="edit_city" name="City" value="@Model?.OrderShippingMasterData?.CityName">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group col-md-4">
                        <label for="edit_postalcode">Postal Code</label>
                        <input type="text" class="form-control" id="edit_postalcode" name="ZipCode" value="@Model?.OrderShippingMasterData?.PostalCode" >
                    </div>
                    <div class="form-group col-md-4">
                        <label for="edit_email">Email</label>
                        <input type="email" class="form-control" id="edit_email" name="EmailAddress" value="@Model?.OrderObj?.CustomerEmailAddress" required>
                    </div>
                    <div class="form-group col-md-4">
                        <label for="edit_mobile">Mobile</label>
                        <input type="text" class="form-control" id="edit_mobile" name="PhoneNumber" value="@Model?.OrderObj?.CustomerMobileNo" required>
                    </div>
                </div>
                <input type="hidden" name="OrderId" value="@Model?.OrderObj?.OrderId">
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-link" data-dismiss="modal">Close</button>
                <button type="submit" class="btn bg-purple-300">Save changes</button>
            </div>
        </form>
    </div>
</div>

