'use client';

import { Home, Search, Package, User, ShoppingCart, Heart, Grid3X3 } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useCart } from '@/contexts/cart-context';
import { useWishlist } from '@/contexts/wishlist-context';
import { useSettings } from '@/contexts/settings-context';
import { useState, useEffect } from 'react';
import { Button } from './button';
import { MakeApiCallAsync, Config } from '@/lib/api-helper';

export function MobileBottomNav() {
  const pathname = usePathname();
  const { totalItems: cartCount } = useCart();
  const { totalItems: wishlistCount } = useWishlist();
  const { primaryColor, t } = useSettings();
  const [mounted, setMounted] = useState(false);
  const [showCategories, setShowCategories] = useState(false);
  const [allCategories, setAllCategories] = useState<any[]>([]);
  const [parentCategories, setParentCategories] = useState<any[]>([]);
  const [selectedParent, setSelectedParent] = useState<any>(null);
  const [isLoadingCategories, setIsLoadingCategories] = useState(false);
  const [view, setView] = useState<'parent' | 'child'>('parent');

  useEffect(() => {
    setMounted(true);
  }, []);

  // Fetch categories from API
  const fetchCategories = async () => {
    if (parentCategories.length > 0) {
      setShowCategories(true);
      return;
    }

    setIsLoadingCategories(true);
    try {
      const param = {
        "PageNumber": 1,
        "PageSize": 100,
        "SortColumn": "Name",
        "SortOrder": "ASC"
      };
      const headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer ' + localStorage.getItem('token')
      };
      const categoriesResponse = await MakeApiCallAsync(Config.END_POINT_NAMES.GET_CATEGORIES_LIST, null, param, headers, "POST", true);

      if (categoriesResponse?.data?.data) {
        try {
          const parsedData = JSON.parse(categoriesResponse.data.data);
          if (Array.isArray(parsedData)) {
            setAllCategories(parsedData);
            // Get parent categories (those without a ParentCategoryID)
            const parents = parsedData.filter(cat => !cat.ParentCategoryID);
            setParentCategories(parents);
            setShowCategories(true);
          }
        } catch (parseError) {
          console.error('Error parsing categories data:', parseError);
        }
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    } finally {
      setIsLoadingCategories(false);
    }
  };

  if (!mounted) return null;

  const navItems = [
    {
      href: '/',
      icon: Home,
      label: t('home') || 'الرئيسية',
      isActive: pathname === '/',
      onClick: null
    },
    {
      href: '#',
      icon: Grid3X3,
      label: t('categories') || 'التصنيفات',
      isActive: false,
      onClick: fetchCategories
    },
    {
      href: '/cart',
      icon: ShoppingCart,
      label: t('cart') || 'سلة التسوق',
      isActive: pathname === '/cart',
      badge: cartCount || 0,
      onClick: null
    },
    {
      href: '/wishlist',
      icon: Heart,
      label: t('wishlist') || 'المفضلة',
      isActive: pathname === '/wishlist',
      badge: wishlistCount || 0,
      onClick: null
    },
    {
      href: '/login',
      icon: User,
      label: t('login') || 'حسابي',
      isActive: pathname === '/login' || pathname === '/signup',
      onClick: null
    }
  ];

  return (
    <>
      {/* Mobile Navigation Bar */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50 safe-area-pb">
        <div className="flex items-center justify-around py-2">
          {navItems.map((item) => {
            const Icon = item.icon;
            const isButton = !!item.onClick;
            const className = "flex flex-col items-center justify-center min-w-0 flex-1 py-2 px-1" + (isButton ? " bg-transparent border-none" : "");
            
            const content = (
              <>
                <div className="relative">
                  <Icon
                    className="h-6 w-6 mb-1"
                    style={{
                      color: item.isActive ? primaryColor : '#6B7280'
                    }}
                  />
                  {item.badge !== undefined && item.badge > 0 && (
                    <span
                      className="absolute -top-1 -right-1 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center font-medium shadow-md"
                      style={{ backgroundColor: primaryColor }}
                    >
                      {item.badge > 99 ? '99+' : item.badge}
                    </span>
                  )}
                </div>
                <span
                  className="text-xs font-medium text-center leading-tight mt-1"
                  style={{
                    color: item.isActive ? primaryColor : '#6B7280'
                  }}
                >
                  {item.label}
                </span>
              </>
            );

            return isButton ? (
              <button
                key={item.href}
                onClick={item.onClick || undefined}
                className={className}
                type="button"
              >
                {content}
              </button>
            ) : (
              <Link
                key={item.href}
                href={item.href}
                className={className}
              >
                {content}
              </Link>
            );
          })}
        </div>
      </div>

      {/* Categories Modal */}
      {showCategories && (
        <div className="md:hidden fixed inset-0 bg-black/50 z-50 flex items-end">
          <div className="bg-white w-full max-h-[80vh] rounded-t-xl overflow-hidden">
            <div className="flex items-center justify-between p-4 border-b">
              <div className="flex items-center">
                {view === 'child' && (
                  <button 
                    onClick={() => {
                      setView('parent');
                      setSelectedParent(null);
                    }}
                    className="mr-2 p-1"
                  >
                    ←
                  </button>
                )}
                <h2 className="text-lg font-semibold" style={{ color: primaryColor }}>
                  {view === 'parent' ? (t('categories') || 'التصنيفات') : selectedParent?.Name}
                </h2>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setShowCategories(false);
                  setView('parent');
                  setSelectedParent(null);
                }}
                className="text-gray-500 hover:text-gray-700"
              >
                ✕
              </Button>
            </div>
            <div className="overflow-y-auto max-h-[calc(80vh-80px)]">
              {isLoadingCategories ? (
                <div className="p-8 text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 mx-auto mb-4" style={{ borderColor: primaryColor }}></div>
                  <p className="text-gray-500">Loading...</p>
                </div>
              ) : view === 'parent' ? (
                <div className="divide-y">
                  {parentCategories.map((category) => {
                    const hasChildren = allCategories.some(cat => cat.ParentCategoryID === category.CategoryID);
                    return (
                      <div key={category.CategoryID} className="border-b border-gray-100">
                        <button
                          onClick={() => {
                            if (hasChildren) {
                              setSelectedParent(category);
                              setView('child');
                            } else {
                              window.location.href = `/products?category=${category.CategoryID}`;
                            }
                          }}
                          className="w-full text-left p-4 flex justify-between items-center hover:bg-gray-50 transition-colors"
                        >
                          <span>{category.Name}</span>
                          {hasChildren && <span>›</span>}
                        </button>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <div className="divide-y">
                  {/* View All in Parent Category */}
                  <Link
                    href={`/products?category=${selectedParent.CategoryID}`}
                    className="block p-4 font-medium text-center hover:bg-gray-50"
                    style={{ color: primaryColor }}
                    onClick={() => setShowCategories(false)}
                  >
                    {'View All in'} {selectedParent.Name}
                  </Link>
                  
                  {/* Child Categories */}
                  {allCategories
                    .filter(cat => cat.ParentCategoryID === selectedParent.CategoryID)
                    .map((child) => (
                      <Link
                        key={child.CategoryID}
                        href={`/products?category=${child.CategoryID}`}
                        className="block p-4 pl-8 border-b border-gray-100 hover:bg-gray-50 transition-colors"
                        onClick={() => setShowCategories(false)}
                      >
                        {child.Name}
                      </Link>
                    ))}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </>
  );
}
