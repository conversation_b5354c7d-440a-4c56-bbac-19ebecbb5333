'use client';

import { Home, Search, Package, User, ShoppingCart, Heart } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useCart } from '@/contexts/cart-context';
import { useWishlist } from '@/contexts/wishlist-context';
import { useSettings } from '@/contexts/settings-context';
import { useState, useEffect } from 'react';

export function MobileBottomNav() {
  const pathname = usePathname();
  const { totalItems: cartCount } = useCart();
  const { totalItems: wishlistCount } = useWishlist();
  const { primaryColor, t } = useSettings();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  const navItems = [
    {
      href: '/',
      icon: Home,
      label: t('home') || 'الرئيسية',
      isActive: pathname === '/'
    },
    {
      href: '/products',
      icon: Package,
      label: t('products') || 'التصنيفات',
      isActive: pathname === '/products'
    },
    {
      href: '/cart',
      icon: ShoppingCart,
      label: t('cart') || 'سلة التسوق',
      isActive: pathname === '/cart',
      badge: cartCount
    },
    {
      href: '/wishlist',
      icon: Heart,
      label: t('wishlist') || 'المفضلة',
      isActive: pathname === '/wishlist',
      badge: wishlistCount
    },
    {
      href: '/login',
      icon: User,
      label: t('login') || 'حسابي',
      isActive: pathname === '/login' || pathname === '/signup'
    }
  ];

  return (
    <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50 safe-area-pb">
      <div className="flex items-center justify-around py-2">
        {navItems.map((item) => {
          const Icon = item.icon;
          return (
            <Link
              key={item.href}
              href={item.href}
              className="flex flex-col items-center justify-center min-w-0 flex-1 py-2 px-1"
            >
              <div className="relative">
                <Icon
                  className="h-6 w-6 mb-1"
                  style={{
                    color: item.isActive ? primaryColor : '#6B7280'
                  }}
                />
                {item.badge && item.badge > 0 && (
                  <span
                    className="absolute -top-2 -right-2 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium"
                    style={{ backgroundColor: primaryColor }}
                  >
                    {item.badge > 99 ? '99+' : item.badge}
                  </span>
                )}
              </div>
              <span
                className="text-xs font-medium text-center leading-tight"
                style={{
                  color: item.isActive ? primaryColor : '#6B7280'
                }}
              >
                {item.label}
              </span>
            </Link>
          );
        })}
      </div>
    </div>
  );
}
