'use client';

import { Home, Search, Package, User, ShoppingCart, Heart, Grid3X3 } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useCart } from '@/contexts/cart-context';
import { useWishlist } from '@/contexts/wishlist-context';
import { useSettings } from '@/contexts/settings-context';
import { useState, useEffect } from 'react';
import { Button } from './button';
import { MakeApiCallAsync, Config } from '@/lib/api-helper';

export function MobileBottomNav() {
  const pathname = usePathname();
  const { totalItems: cartCount } = useCart();
  const { totalItems: wishlistCount } = useWishlist();
  const { primaryColor, t } = useSettings();
  const [mounted, setMounted] = useState(false);
  const [showCategories, setShowCategories] = useState(false);
  const [categories, setCategories] = useState<any[]>([]);
  const [isLoadingCategories, setIsLoadingCategories] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Fetch categories from API
  const fetchCategories = async () => {
    if (categories.length > 0) {
      setShowCategories(true);
      return;
    }

    setIsLoadingCategories(true);
    try {
      const param = {
        "PageNumber": 1,
        "PageSize": 100,
        "SortColumn": "Name",
        "SortOrder": "ASC"
      };
      const headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer ' + localStorage.getItem('token')
      };
      const categoriesResponse = await MakeApiCallAsync(Config.END_POINT_NAMES.GET_CATEGORIES_LIST, null, param, headers, "POST", true);

      if (categoriesResponse?.data?.data) {
        try {
          const parsedData = JSON.parse(categoriesResponse.data.data);
          if (Array.isArray(parsedData)) {
            // Filter parent categories only
            const parentCategories = parsedData.filter(cat => !cat.ParentCategoryID);
            setCategories(parentCategories);
            setShowCategories(true);
          }
        } catch (parseError) {
          console.error('Error parsing categories data:', parseError);
        }
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    } finally {
      setIsLoadingCategories(false);
    }
  };

  if (!mounted) return null;

  const navItems = [
    {
      href: '/',
      icon: Home,
      label: t('home') || 'الرئيسية',
      isActive: pathname === '/',
      onClick: null
    },
    {
      href: '#',
      icon: Grid3X3,
      label: t('categories') || 'التصنيفات',
      isActive: false,
      onClick: fetchCategories
    },
    {
      href: '/cart',
      icon: ShoppingCart,
      label: t('cart') || 'سلة التسوق',
      isActive: pathname === '/cart',
      badge: cartCount || 0,
      onClick: null
    },
    {
      href: '/wishlist',
      icon: Heart,
      label: t('wishlist') || 'المفضلة',
      isActive: pathname === '/wishlist',
      badge: wishlistCount || 0,
      onClick: null
    },
    {
      href: '/login',
      icon: User,
      label: t('login') || 'حسابي',
      isActive: pathname === '/login' || pathname === '/signup',
      onClick: null
    }
  ];

  return (
    <>
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50 safe-area-pb">
        <div className="flex items-center justify-around py-2">
          {navItems.map((item) => {
            const Icon = item.icon;
            const Component = item.onClick ? 'button' : Link;
            const props = item.onClick
              ? { onClick: item.onClick, className: "flex flex-col items-center justify-center min-w-0 flex-1 py-2 px-1 bg-transparent border-none" }
              : { href: item.href, className: "flex flex-col items-center justify-center min-w-0 flex-1 py-2 px-1" };

            return (
              <Component
                key={item.href}
                {...props}
              >
                <div className="relative">
                  <Icon
                    className="h-6 w-6 mb-1"
                    style={{
                      color: item.isActive ? primaryColor : '#6B7280'
                    }}
                  />
                  {item.badge !== undefined && (
                    <span
                      className="absolute -top-1 -right-1 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center font-medium shadow-md"
                      style={{ backgroundColor: primaryColor }}
                    >
                      {item.badge > 99 ? '99+' : item.badge}
                    </span>
                  )}
                </div>
                <span
                  className="text-xs font-medium text-center leading-tight mt-1"
                  style={{
                    color: item.isActive ? primaryColor : '#6B7280'
                  }}
                >
                  {item.label}
                </span>
              </Component>
            );
          })}
        </div>
      </div>

      {/* Categories Modal */}
      {showCategories && (
        <div className="md:hidden fixed inset-0 bg-black/50 z-50 flex items-end">
          <div className="bg-white w-full max-h-[70vh] rounded-t-xl overflow-hidden">
            <div className="flex items-center justify-between p-4 border-b">
              <h2 className="text-lg font-semibold" style={{ color: primaryColor }}>
                {t('categories') || 'التصنيفات'}
              </h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowCategories(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                ✕
              </Button>
            </div>
            <div className="overflow-y-auto max-h-[calc(70vh-80px)]">
              {isLoadingCategories ? (
                <div className="p-8 text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 mx-auto mb-4" style={{ borderColor: primaryColor }}></div>
                  <p className="text-gray-500">{t('loading') || 'جاري التحميل...'}</p>
                </div>
              ) : (
                <div className="p-4 space-y-2">
                  {categories.map((category) => (
                    <Link
                      key={category.CategoryID}
                      href={`/products?category=${category.CategoryID}`}
                      className="block p-3 rounded-lg hover:bg-gray-50 transition-colors"
                      onClick={() => setShowCategories(false)}
                    >
                      <div className="flex items-center justify-between">
                        <span className="font-medium text-gray-900">{category.Name}</span>
                        <span className="text-gray-400">›</span>
                      </div>
                    </Link>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </>
  );
}
