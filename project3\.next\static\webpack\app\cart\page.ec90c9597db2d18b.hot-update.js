"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/cart/page",{

/***/ "(app-pages-browser)/./contexts/coupon-context.tsx":
/*!*************************************!*\
  !*** ./contexts/coupon-context.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CouponProvider: () => (/* binding */ CouponProvider),\n/* harmony export */   useCoupon: () => (/* binding */ useCoupon)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api_helper__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api-helper */ \"(app-pages-browser)/./lib/api-helper.ts\");\n/* __next_internal_client_entry_do_not_use__ CouponProvider,useCoupon auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst CouponContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction CouponProvider(param) {\n    let { children } = param;\n    _s();\n    const [appliedCoupon, setAppliedCoupon] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const validateCoupon = async (code, amount, cartItems)=>{\n        if (!code.trim()) {\n            return {\n                valid: false,\n                message: 'Please enter a coupon code',\n                discount: 0\n            };\n        }\n        setIsLoading(true);\n        try {\n            // Prepare cart data from cart items\n            const cartData = (cartItems === null || cartItems === void 0 ? void 0 : cartItems.map((item)=>({\n                    ProductId: item.id,\n                    ProductName: item.name,\n                    Price: item.adjustedPrice || item.price,\n                    Quantity: item.quantity,\n                    IsDiscountAllowed: true\n                }))) || [];\n            const cartJsonData = JSON.stringify(cartData);\n            const param = {\n                CouponCode: code.toUpperCase(),\n                cartJsonData: cartJsonData\n            };\n            const headers = {\n                'Content-Type': 'application/json',\n                'Accept': 'application/json'\n            };\n            const response = await (0,_lib_api_helper__WEBPACK_IMPORTED_MODULE_2__.MakeApiCallAsync)(_lib_api_helper__WEBPACK_IMPORTED_MODULE_2__.Config.END_POINT_NAMES.GET_COUPON_CODE_DISCOUNT, _lib_api_helper__WEBPACK_IMPORTED_MODULE_2__.Config.COMMON_CONTROLLER_SUB_URL, param, headers, 'POST');\n            if (response && response.data && !response.data.errorMessage) {\n                let couponData;\n                // Parse the response data\n                if (typeof response.data.data === 'string') {\n                    couponData = JSON.parse(response.data.data);\n                } else {\n                    couponData = response.data.data;\n                }\n                if (couponData && couponData.DiscountValueAfterCouponAppliedWithQuantity > 0) {\n                    const discountAmount = couponData.DiscountValueAfterCouponAppliedWithQuantity;\n                    // Create coupon object for state\n                    const coupon = {\n                        code: code.toUpperCase(),\n                        discount: discountAmount,\n                        type: 'fixed' // Assuming fixed amount from API\n                    };\n                    setAppliedCoupon(coupon);\n                    return {\n                        valid: true,\n                        message: 'Coupon applied successfully!',\n                        discount: discountAmount\n                    };\n                } else {\n                    return {\n                        valid: false,\n                        message: 'Invalid coupon code or coupon not applicable to your cart',\n                        discount: 0\n                    };\n                }\n            } else {\n                var _response_data;\n                return {\n                    valid: false,\n                    message: ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.errorMessage) || 'Failed to validate coupon',\n                    discount: 0\n                };\n            }\n        } catch (error) {\n            console.error('Coupon validation error:', error);\n            return {\n                valid: false,\n                message: 'Error validating coupon. Please try again.',\n                discount: 0\n            };\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const clearCoupon = ()=>{\n        setAppliedCoupon(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CouponContext.Provider, {\n        value: {\n            appliedCoupon,\n            validateCoupon,\n            clearCoupon,\n            isLoading\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\contexts\\\\coupon-context.tsx\",\n        lineNumber: 123,\n        columnNumber: 5\n    }, this);\n}\n_s(CouponProvider, \"kjHJqm53e+vsbGz9dHK1EJBrEqk=\");\n_c = CouponProvider;\nfunction useCoupon() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(CouponContext);\n    if (context === undefined) {\n        throw new Error('useCoupon must be used within a CouponProvider');\n    }\n    return context;\n}\n_s1(useCoupon, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"CouponProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./contexts/coupon-context.tsx\n"));

/***/ })

});