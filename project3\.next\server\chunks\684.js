"use strict";exports.id=684,exports.ids=[684],exports.modules={6004:(e,t,r)=>{r.d(t,{A:()=>i});var n=r(58009),o=r(45512);function i(e,t=[]){let r=[],a=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return a.scopeName=e,[function(t,i){let a=n.createContext(i),u=r.length;r=[...r,i];let l=t=>{let{scope:r,children:i,...l}=t,s=r?.[e]?.[u]||a,c=n.useMemo(()=>l,Object.values(l));return(0,o.jsx)(s.Provider,{value:c,children:i})};return l.displayName=t+"Provider",[l,function(r,o){let l=o?.[e]?.[u]||a,s=n.useContext(l);if(s)return s;if(void 0!==i)return i;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(a,...t)]}},13024:(e,t,r)=>{r.d(t,{i:()=>i});var n=r(58009),o=r(92828);function i({prop:e,defaultProp:t,onChange:r=()=>{}}){let[i,a]=function({defaultProp:e,onChange:t}){let r=n.useState(e),[i]=r,a=n.useRef(i),u=(0,o.c)(t);return n.useEffect(()=>{a.current!==i&&(u(i),a.current=i)},[i,a,u]),r}({defaultProp:t,onChange:r}),u=void 0!==e,l=u?e:i,s=(0,o.c)(r);return[l,n.useCallback(t=>{if(u){let r="function"==typeof t?t(e):t;r!==e&&s(r)}else a(t)},[u,e,a,s])]}},14494:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(41680).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},30096:(e,t,r)=>{r.d(t,{B:()=>l});var n,o=r(58009),i=r(49397),a=(n||(n=r.t(o,2)))["useId".toString()]||(()=>void 0),u=0;function l(e){let[t,r]=o.useState(a());return(0,i.N)(()=>{e||r(e=>e??String(u++))},[e]),e||(t?`radix-${t}`:"")}},30830:(e,t,r)=>{r.d(t,{hO:()=>l,sG:()=>u});var n=r(58009),o=r(55740),i=r(12705),a=r(45512),u=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=n.forwardRef((e,r)=>{let{asChild:n,...o}=e,u=n?i.DX:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(u,{...o,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function l(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},31412:(e,t,r)=>{r.d(t,{m:()=>n});function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}},39217:(e,t,r)=>{r.d(t,{N:()=>l});var n=r(58009),o=r(6004),i=r(29952),a=r(12705),u=r(45512);function l(e){let t=e+"CollectionProvider",[r,l]=(0,o.A)(t),[s,c]=r(t,{collectionRef:{current:null},itemMap:new Map}),d=e=>{let{scope:t,children:r}=e,o=n.useRef(null),i=n.useRef(new Map).current;return(0,u.jsx)(s,{scope:t,itemMap:i,collectionRef:o,children:r})};d.displayName=t;let f=e+"CollectionSlot",m=n.forwardRef((e,t)=>{let{scope:r,children:n}=e,o=c(f,r),l=(0,i.s)(t,o.collectionRef);return(0,u.jsx)(a.DX,{ref:l,children:n})});m.displayName=f;let p=e+"CollectionItemSlot",v="data-radix-collection-item",y=n.forwardRef((e,t)=>{let{scope:r,children:o,...l}=e,s=n.useRef(null),d=(0,i.s)(t,s),f=c(p,r);return n.useEffect(()=>(f.itemMap.set(s,{ref:s,...l}),()=>void f.itemMap.delete(s))),(0,u.jsx)(a.DX,{[v]:"",ref:d,children:o})});return y.displayName=p,[{Provider:d,Slot:m,ItemSlot:y},function(t){let r=c(e+"CollectionConsumer",t);return n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${v}]`));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},l]}},49397:(e,t,r)=>{r.d(t,{N:()=>o});var n=r(58009),o=globalThis?.document?n.useLayoutEffect:()=>{}},59018:(e,t,r)=>{r.d(t,{jH:()=>i});var n=r(58009);r(45512);var o=n.createContext(void 0);function i(e){let t=n.useContext(o);return e||t||"ltr"}},66351:(e,t,r)=>{r.d(t,{UC:()=>Q,B8:()=>X,bL:()=>z,l9:()=>J});var n=r(58009),o=r(31412),i=r(6004),a=r(39217),u=r(29952),l=r(30096),s=r(30830),c=r(92828),d=r(13024),f=r(59018),m=r(45512),p="rovingFocusGroup.onEntryFocus",v={bubbles:!1,cancelable:!0},y="RovingFocusGroup",[w,b,N]=(0,a.N)(y),[g,h]=(0,i.A)(y,[N]),[x,R]=g(y),A=n.forwardRef((e,t)=>(0,m.jsx)(w.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,m.jsx)(w.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,m.jsx)(M,{...e,ref:t})})}));A.displayName=y;var M=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:i,loop:a=!1,dir:l,currentTabStopId:y,defaultCurrentTabStopId:w,onCurrentTabStopIdChange:N,onEntryFocus:g,preventScrollOnEntryFocus:h=!1,...R}=e,A=n.useRef(null),M=(0,u.s)(t,A),I=(0,f.jH)(l),[C=null,T]=(0,d.i)({prop:y,defaultProp:w,onChange:N}),[j,D]=n.useState(!1),S=(0,c.c)(g),F=b(r),O=n.useRef(!1),[P,L]=n.useState(0);return n.useEffect(()=>{let e=A.current;if(e)return e.addEventListener(p,S),()=>e.removeEventListener(p,S)},[S]),(0,m.jsx)(x,{scope:r,orientation:i,dir:I,loop:a,currentTabStopId:C,onItemFocus:n.useCallback(e=>T(e),[T]),onItemShiftTab:n.useCallback(()=>D(!0),[]),onFocusableItemAdd:n.useCallback(()=>L(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>L(e=>e-1),[]),children:(0,m.jsx)(s.sG.div,{tabIndex:j||0===P?-1:0,"data-orientation":i,...R,ref:M,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{O.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!O.current;if(e.target===e.currentTarget&&t&&!j){let t=new CustomEvent(p,v);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=F().filter(e=>e.focusable);E([e.find(e=>e.active),e.find(e=>e.id===C),...e].filter(Boolean).map(e=>e.ref.current),h)}}O.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>D(!1))})})}),I="RovingFocusGroupItem",C=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:i=!0,active:a=!1,tabStopId:u,...c}=e,d=(0,l.B)(),f=u||d,p=R(I,r),v=p.currentTabStopId===f,y=b(r),{onFocusableItemAdd:N,onFocusableItemRemove:g}=p;return n.useEffect(()=>{if(i)return N(),()=>g()},[i,N,g]),(0,m.jsx)(w.ItemSlot,{scope:r,id:f,focusable:i,active:a,children:(0,m.jsx)(s.sG.span,{tabIndex:v?0:-1,"data-orientation":p.orientation,...c,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i?p.onItemFocus(f):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>p.onItemFocus(f)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){p.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return T[o]}(e,p.orientation,p.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=y().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=p.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>E(r))}})})})});C.displayName=I;var T={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function E(e,t=!1){let r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var j=r(49397),D=e=>{let{present:t,children:r}=e,o=function(e){var t,r;let[o,i]=n.useState(),a=n.useRef({}),u=n.useRef(e),l=n.useRef("none"),[s,c]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>r[e][t]??e,t));return n.useEffect(()=>{let e=S(a.current);l.current="mounted"===s?e:"none"},[s]),(0,j.N)(()=>{let t=a.current,r=u.current;if(r!==e){let n=l.current,o=S(t);e?c("MOUNT"):"none"===o||t?.display==="none"?c("UNMOUNT"):r&&n!==o?c("ANIMATION_OUT"):c("UNMOUNT"),u.current=e}},[e,c]),(0,j.N)(()=>{if(o){let e;let t=o.ownerDocument.defaultView??window,r=r=>{let n=S(a.current).includes(r.animationName);if(r.target===o&&n&&(c("ANIMATION_END"),!u.current)){let r=o.style.animationFillMode;o.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=r)})}},n=e=>{e.target===o&&(l.current=S(a.current))};return o.addEventListener("animationstart",n),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{t.clearTimeout(e),o.removeEventListener("animationstart",n),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}c("ANIMATION_END")},[o,c]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:n.useCallback(e=>{e&&(a.current=getComputedStyle(e)),i(e)},[])}}(t),i="function"==typeof r?r({present:o.isPresent}):n.Children.only(r),a=(0,u.s)(o.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof r||o.isPresent?n.cloneElement(i,{ref:a}):null};function S(e){return e?.animationName||"none"}D.displayName="Presence";var F="Tabs",[O,P]=(0,i.A)(F,[h]),L=h(),[U,_]=O(F),k=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,onValueChange:o,defaultValue:i,orientation:a="horizontal",dir:u,activationMode:c="automatic",...p}=e,v=(0,f.jH)(u),[y,w]=(0,d.i)({prop:n,onChange:o,defaultProp:i});return(0,m.jsx)(U,{scope:r,baseId:(0,l.B)(),value:y,onValueChange:w,orientation:a,dir:v,activationMode:c,children:(0,m.jsx)(s.sG.div,{dir:v,"data-orientation":a,...p,ref:t})})});k.displayName=F;var G="TabsList",$=n.forwardRef((e,t)=>{let{__scopeTabs:r,loop:n=!0,...o}=e,i=_(G,r),a=L(r);return(0,m.jsx)(A,{asChild:!0,...a,orientation:i.orientation,dir:i.dir,loop:n,children:(0,m.jsx)(s.sG.div,{role:"tablist","aria-orientation":i.orientation,...o,ref:t})})});$.displayName=G;var K="TabsTrigger",B=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,disabled:i=!1,...a}=e,u=_(K,r),l=L(r),c=W(u.baseId,n),d=q(u.baseId,n),f=n===u.value;return(0,m.jsx)(C,{asChild:!0,...l,focusable:!i,active:f,children:(0,m.jsx)(s.sG.button,{type:"button",role:"tab","aria-selected":f,"aria-controls":d,"data-state":f?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:c,...a,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():u.onValueChange(n)}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&u.onValueChange(n)}),onFocus:(0,o.m)(e.onFocus,()=>{let e="manual"!==u.activationMode;f||i||!e||u.onValueChange(n)})})})});B.displayName=K;var V="TabsContent",H=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:o,forceMount:i,children:a,...u}=e,l=_(V,r),c=W(l.baseId,o),d=q(l.baseId,o),f=o===l.value,p=n.useRef(f);return n.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,m.jsx)(D,{present:i||f,children:({present:r})=>(0,m.jsx)(s.sG.div,{"data-state":f?"active":"inactive","data-orientation":l.orientation,role:"tabpanel","aria-labelledby":c,hidden:!r,id:d,tabIndex:0,...u,ref:t,style:{...e.style,animationDuration:p.current?"0s":void 0},children:r&&a})})});function W(e,t){return`${e}-trigger-${t}`}function q(e,t){return`${e}-content-${t}`}H.displayName=V;var z=k,X=$,J=B,Q=H},92828:(e,t,r)=>{r.d(t,{c:()=>o});var n=r(58009);function o(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},99905:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(41680).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])}};