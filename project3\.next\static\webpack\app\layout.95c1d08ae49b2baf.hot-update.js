"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"92cf5224aa2b\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xceWh5YXNvZnRcXERvd25sb2Fkc1xcZWNcXC5ORVQgOCBWZXJzaW9uIC0gTGF0ZXN0XFxwcm9qZWN0XFxjb2RlbWVkaWNhbFxccHJvamVjdDNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI5MmNmNTIyNGFhMmJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/ui/header.tsx":
/*!**********************************!*\
  !*** ./components/ui/header.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_Mail_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,Mail,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_Mail_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,Mail,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_Mail_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,Mail,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_Mail_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,Mail,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_Mail_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,Mail,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_Mail_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,Mail,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_Mail_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,Mail,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_Mail_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,Mail,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_Mail_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,Mail,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_Mail_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,Mail,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_Mail_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,Mail,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_Mail_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,Mail,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_Mail_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,Mail,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _contexts_cart_context__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/cart-context */ \"(app-pages-browser)/./contexts/cart-context.tsx\");\n/* harmony import */ var _contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/wishlist-context */ \"(app-pages-browser)/./contexts/wishlist-context.tsx\");\n/* harmony import */ var _lib_api_helper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api-helper */ \"(app-pages-browser)/./lib/api-helper.ts\");\n/* harmony import */ var _components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/navigation-menu */ \"(app-pages-browser)/./components/ui/navigation-menu.tsx\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./components/ui/popover.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var _button__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _contexts_settings_context__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/contexts/settings-context */ \"(app-pages-browser)/./contexts/settings-context.tsx\");\n/* harmony import */ var _color_picker__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./color-picker */ \"(app-pages-browser)/./components/ui/color-picker.tsx\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Header() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(true);\n    const [showColorPicker, setShowColorPicker] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null);\n    const [selectedCategoryId, setSelectedCategoryId] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null);\n    const [selectedSubcategory, setSelectedSubcategory] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null);\n    const [showMobileMenu, setShowMobileMenu] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [showMobileCategories, setShowMobileCategories] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)('');\n    const [cartCount, setCartCount] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    const [wishlistCount, setWishlistCount] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    const cart = (0,_contexts_cart_context__WEBPACK_IMPORTED_MODULE_5__.useCart)();\n    const wishlist = (0,_contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_6__.useWishlist)();\n    const { theme, language, primaryColor, toggleTheme, setLanguage, setPrimaryColor, t } = (0,_contexts_settings_context__WEBPACK_IMPORTED_MODULE_12__.useSettings)();\n    const handleSearch = ()=>{\n        // Navigate to products page with search parameters\n        const params = new URLSearchParams();\n        if (searchTerm) {\n            params.append('search', searchTerm);\n        }\n        if (selectedCategoryId) {\n            params.append('category', selectedCategoryId.toString());\n        }\n        router.push(\"/products?\".concat(params.toString()));\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"Header.useEffect\": ()=>{\n            const fetchCategories = {\n                \"Header.useEffect.fetchCategories\": async ()=>{\n                    try {\n                        var _categoriesResponse_data, _categoriesResponse_data1;\n                        const param = {\n                            \"PageNumber\": 1,\n                            \"PageSize\": 100,\n                            \"SortColumn\": \"Name\",\n                            \"SortOrder\": \"ASC\"\n                        };\n                        const headers = {\n                            'Content-Type': 'application/json',\n                            'Accept': 'application/json',\n                            'Authorization': 'Bearer ' + localStorage.getItem('token')\n                        };\n                        const categoriesResponse = await (0,_lib_api_helper__WEBPACK_IMPORTED_MODULE_7__.MakeApiCallAsync)(_lib_api_helper__WEBPACK_IMPORTED_MODULE_7__.Config.END_POINT_NAMES.GET_CATEGORIES_LIST, null, param, headers, \"POST\", true);\n                        if (categoriesResponse === null || categoriesResponse === void 0 ? void 0 : (_categoriesResponse_data = categoriesResponse.data) === null || _categoriesResponse_data === void 0 ? void 0 : _categoriesResponse_data.data) {\n                            try {\n                                const parsedData = JSON.parse(categoriesResponse.data.data);\n                                if (Array.isArray(parsedData)) {\n                                    // Create a map of parent categories\n                                    const parentCategories = parsedData.filter({\n                                        \"Header.useEffect.fetchCategories.parentCategories\": (cat)=>!cat.ParentCategoryID\n                                    }[\"Header.useEffect.fetchCategories.parentCategories\"]);\n                                    const childCategories = parsedData.filter({\n                                        \"Header.useEffect.fetchCategories.childCategories\": (cat)=>cat.ParentCategoryID\n                                    }[\"Header.useEffect.fetchCategories.childCategories\"]);\n                                    // Format parent categories with their children\n                                    const formattedCategories = parentCategories.map({\n                                        \"Header.useEffect.fetchCategories.formattedCategories\": (parent)=>({\n                                                id: parent.CategoryID,\n                                                name: parent.Name,\n                                                subcategories: childCategories.filter({\n                                                    \"Header.useEffect.fetchCategories.formattedCategories\": (child)=>child.ParentCategoryID === parent.CategoryID\n                                                }[\"Header.useEffect.fetchCategories.formattedCategories\"]).map({\n                                                    \"Header.useEffect.fetchCategories.formattedCategories\": (child)=>child.Name\n                                                }[\"Header.useEffect.fetchCategories.formattedCategories\"])\n                                            })\n                                    }[\"Header.useEffect.fetchCategories.formattedCategories\"]);\n                                    setCategories(formattedCategories);\n                                } else {\n                                    console.error('Categories data is not an array:', parsedData);\n                                    setCategories([]);\n                                }\n                            } catch (parseError) {\n                                console.error('Error parsing categories data:', parseError);\n                                setCategories([]);\n                            }\n                        } else if (categoriesResponse === null || categoriesResponse === void 0 ? void 0 : (_categoriesResponse_data1 = categoriesResponse.data) === null || _categoriesResponse_data1 === void 0 ? void 0 : _categoriesResponse_data1.errorMessage) {\n                            console.error('API Error:', categoriesResponse.data.errorMessage);\n                            setCategories([]);\n                        } else {\n                            console.error('Invalid or empty response from API');\n                            setCategories([]);\n                        }\n                    } catch (error) {\n                        console.error('Error fetching categories:', error);\n                        setCategories([]);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"Header.useEffect.fetchCategories\"];\n            fetchCategories();\n        }\n    }[\"Header.useEffect\"], []);\n    // Update cart count when cart items change\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"Header.useEffect\": ()=>{\n            if (cart && cart.items) {\n                setCartCount(cart.totalItems);\n            }\n        }\n    }[\"Header.useEffect\"], [\n        cart,\n        cart === null || cart === void 0 ? void 0 : cart.items\n    ]);\n    // Update wishlist count when wishlist items change\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"Header.useEffect\": ()=>{\n            if (wishlist) {\n                setWishlistCount(wishlist.totalItems);\n            }\n        }\n    }[\"Header.useEffect\"], [\n        wishlist,\n        wishlist === null || wishlist === void 0 ? void 0 : wishlist.wishlistItems\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"jsx-984f48e2305591f3\" + \" \" + \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"984f48e2305591f3\",\n                children: \".grecaptcha-badge{visibility:hidden!important}\"\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                variant: \"ghost\",\n                size: \"sm\",\n                className: \"fixed right-0 top-1/2 -translate-y-1/2 z-50 bg-gradient-to-r from-background/90 to-background/60 backdrop-blur-sm shadow-lg rounded-l-lg border-l border-y border-border/40 md:flex hidden hover:translate-x-1 transition-all duration-200 hover:shadow-xl group items-center justify-center w-16 h-12\",\n                onClick: ()=>setShowColorPicker(true),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        backgroundColor: primaryColor\n                    },\n                    className: \"jsx-984f48e2305591f3\" + \" \" + \"h-8 w-8 rounded-full ring-2 ring-border/50 group-hover:ring-primary/50 transition-all duration-200 shadow-inner\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    backgroundColor: primaryColor\n                },\n                className: \"jsx-984f48e2305591f3\" + \" \" + \"text-white py-2.5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-984f48e2305591f3\" + \" \" + \"container mx-auto flex flex-col md:flex-row justify-between items-center text-sm px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-984f48e2305591f3\" + \" \" + \"hidden md:flex md:flex-row items-start justify-start gap-4 md:gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"tel:009647836071686\",\n                                    className: \"flex items-center gap-2 hover:text-white/80\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_Mail_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-984f48e2305591f3\" + \" \" + \"text-xs md:text-sm\",\n                                            children: t('phone')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"mailto:<EMAIL>\",\n                                    className: \"flex items-center gap-2 hover:text-white/80\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_Mail_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-984f48e2305591f3\" + \" \" + \"text-xs md:text-sm\",\n                                            children: t('email')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-984f48e2305591f3\" + \" \" + \"flex items-center gap-2 md:gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    className: \"text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2\",\n                                    onClick: ()=>setLanguage(language === 'en' ? 'ar' : 'en'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_Mail_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-984f48e2305591f3\" + \" \" + \"text-sm\",\n                                            children: language === 'en' ? 'العربية' : 'English'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    className: \"text-white hover:text-white/80 flex items-center gap-2\",\n                                    onClick: ()=>window.open(\"https://wa.me/9647836071686?text=\".concat(encodeURIComponent('Hello! I would like to chat with you regarding your services.')), '_blank'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_Mail_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-984f48e2305591f3\" + \" \" + \"text-sm\",\n                                            children: t('liveChat')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/login\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_Mail_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-984f48e2305591f3\" + \" \" + \"text-sm\",\n                                                children: t('login')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/signup\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_Mail_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-984f48e2305591f3\" + \" \" + \"text-sm\",\n                                                children: t('signUp')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                lineNumber: 173,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-984f48e2305591f3\" + \" \" + \"container mx-auto py-4 px-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-984f48e2305591f3\" + \" \" + \"md:hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-984f48e2305591f3\" + \" \" + \"flex items-center justify-center mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/\",\n                                    className: \"flex items-center gap-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-984f48e2305591f3\" + \" \" + \"text-[#1B3764] flex items-center gap-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"\".concat(\"https://admin.codemedicalapps.com/\", \"content/commonImages/otherImages/18b_logo2x.png\"),\n                                            alt: \"Logo\",\n                                            className: \"jsx-984f48e2305591f3\" + \" \" + \"h-12 w-auto\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-984f48e2305591f3\" + \" \" + \"w-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-984f48e2305591f3\" + \" \" + \"flex items-center gap-2 border rounded-full px-4 py-3 bg-background shadow-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: t('searchProducts') || 'البحث عن المنتجات...',\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                            onKeyDown: (e)=>e.key === 'Enter' && handleSearch(),\n                                            className: \"jsx-984f48e2305591f3\" + \" \" + \"bg-transparent border-none focus:outline-none text-sm flex-1 placeholder:text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            variant: \"ghost\",\n                                            className: \"h-8 w-8 p-0 hover:bg-accent/80 transition-colors\",\n                                            style: {\n                                                color: primaryColor\n                                            },\n                                            onClick: handleSearch,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_Mail_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-984f48e2305591f3\" + \" \" + \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-984f48e2305591f3\" + \" \" + \"flex items-center gap-4 flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/\",\n                                        className: \"flex items-center gap-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-984f48e2305591f3\" + \" \" + \"text-[#1B3764] flex items-center gap-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"\".concat(\"https://admin.codemedicalapps.com/\", \"content/commonImages/otherImages/18b_logo2x.png\"),\n                                                alt: \"Logo\",\n                                                className: \"jsx-984f48e2305591f3\" + \" \" + \"h-12 md:h-16 w-auto\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-984f48e2305591f3\" + \" \" + \"hidden md:flex items-center gap-2 border rounded-full px-3 py-1.5 flex-1 max-w-md ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.Popover, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverTrigger, {\n                                                        asChild: true,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                            variant: \"ghost\",\n                                                            className: \"h-8 flex items-center gap-1 px-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-984f48e2305591f3\" + \" \" + \"text-muted-foreground text-sm\",\n                                                                    children: selectedCategory || selectedSubcategory || t('category')\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                    lineNumber: 265,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_Mail_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                    lineNumber: 266,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                            lineNumber: 264,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverContent, {\n                                                        className: \"w-64 p-0\",\n                                                        align: \"start\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-984f48e2305591f3\" + \" \" + \"max-h-[300px] overflow-auto\",\n                                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-984f48e2305591f3\" + \" \" + \"p-4 text-center text-muted-foreground\",\n                                                                children: t('loadingCategories')\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                lineNumber: 272,\n                                                                columnNumber: 23\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-984f48e2305591f3\" + \" \" + \"grid\",\n                                                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-984f48e2305591f3\" + \" \" + \"group\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>{\n                                                                                    setSelectedCategory(category.name);\n                                                                                    setSelectedCategoryId(category.id);\n                                                                                    setSelectedSubcategory(null);\n                                                                                },\n                                                                                className: \"jsx-984f48e2305591f3\" + \" \" + \"w-full px-4 py-2 text-left hover:bg-accent\",\n                                                                                children: category.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                                lineNumber: 277,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-984f48e2305591f3\" + \" \" + \"hidden group-hover:block absolute left-full top-0 w-48 bg-white shadow-lg rounded-md border\",\n                                                                                children: category.subcategories.map((sub, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>{\n                                                                                            setSelectedSubcategory(sub);\n                                                                                            setSelectedCategory(null);\n                                                                                            // Keep the parent category ID for search purposes\n                                                                                            setSelectedCategoryId(category.id);\n                                                                                        },\n                                                                                        className: \"jsx-984f48e2305591f3\" + \" \" + \"w-full px-4 py-2 text-left hover:bg-accent\",\n                                                                                        children: sub\n                                                                                    }, index, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                                        lineNumber: 286,\n                                                                                        columnNumber: 33\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                                lineNumber: 284,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, category.id, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                        lineNumber: 276,\n                                                                        columnNumber: 27\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                lineNumber: 274,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-984f48e2305591f3\" + \" \" + \"h-5 w-px bg-border mx-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: t('products'),\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                onKeyDown: (e)=>e.key === 'Enter' && handleSearch(),\n                                                className: \"jsx-984f48e2305591f3\" + \" \" + \"bg-transparent border-none focus:outline-none text-sm flex-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                className: \"h-8 w-8 p-0\",\n                                                onClick: handleSearch,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_Mail_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-984f48e2305591f3\" + \" \" + \"hidden md:block\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenu, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuList, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"/\",\n                                                    legacyBehavior: true,\n                                                    passHref: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuLink, {\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50\"),\n                                                        children: t('home')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"/hot-deals\",\n                                                    legacyBehavior: true,\n                                                    passHref: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuLink, {\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50\"),\n                                                        children: t('hotDeals')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"/products\",\n                                                    legacyBehavior: true,\n                                                    passHref: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuLink, {\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50\"),\n                                                        children: t('products') || 'Products'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"/payment-methods\",\n                                                    legacyBehavior: true,\n                                                    passHref: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuLink, {\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50\"),\n                                                        children: t('paymentMethods')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 354,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"/follow-us\",\n                                                    legacyBehavior: true,\n                                                    passHref: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuLink, {\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50\"),\n                                                        children: t('followUs')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"/about\",\n                                                    legacyBehavior: true,\n                                                    passHref: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuLink, {\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50\"),\n                                                        children: t('aboutUs')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"/contact\",\n                                                    legacyBehavior: true,\n                                                    passHref: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuLink, {\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50\"),\n                                                        children: t('contactUs')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    transform: showMobileMenu ? 'translateX(0)' : 'translateX(100%)'\n                                },\n                                className: \"jsx-984f48e2305591f3\" + \" \" + \"md:hidden fixed inset-0   z-50 translate-x-full transition-transform duration-300\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        backgroundColor: \"#fff\"\n                                    },\n                                    className: \"jsx-984f48e2305591f3\" + \" \" + \"fixed right-0 top-0 h-full w-3/4  shadow-xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-984f48e2305591f3\" + \" \" + \"flex items-center justify-between p-4 border-b\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"jsx-984f48e2305591f3\" + \" \" + \"font-semibold\",\n                                                    children: t('menu')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"icon\",\n                                                    onClick: ()=>setShowMobileMenu(false),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_Mail_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 399,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                            className: \"jsx-984f48e2305591f3\" + \" \" + \"p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-984f48e2305591f3\" + \" \" + \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                        href: \"/\",\n                                                        onClick: ()=>setShowMobileMenu(false),\n                                                        className: \"block py-2 hover:text-primary\",\n                                                        children: t('home')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                        href: \"/hot-deals\",\n                                                        onClick: ()=>setShowMobileMenu(false),\n                                                        className: \"block py-2 hover:text-primary\",\n                                                        children: t('hotDeals')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 405,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                        href: \"/products\",\n                                                        onClick: ()=>setShowMobileMenu(false),\n                                                        className: \"block py-2 hover:text-primary\",\n                                                        children: t('products') || 'Products'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 406,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                        href: \"/payment-methods\",\n                                                        onClick: ()=>setShowMobileMenu(false),\n                                                        className: \"block py-2 hover:text-primary\",\n                                                        children: t('paymentMethods')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 407,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                        href: \"/follow-us\",\n                                                        onClick: ()=>setShowMobileMenu(false),\n                                                        className: \"block py-2 hover:text-primary\",\n                                                        children: t('followUs')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 408,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                        href: \"/about\",\n                                                        onClick: ()=>setShowMobileMenu(false),\n                                                        className: \"block py-2 hover:text-primary\",\n                                                        children: t('aboutUs')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 409,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                        href: \"/contact\",\n                                                        onClick: ()=>setShowMobileMenu(false),\n                                                        className: \"block py-2 hover:text-primary\",\n                                                        children: t('contactUs')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-984f48e2305591f3\" + \" \" + \"border-t my-4 pt-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                                href: \"/login\",\n                                                                onClick: ()=>setShowMobileMenu(false),\n                                                                className: \"flex items-center py-2 hover:text-primary\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_Mail_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                        lineNumber: 414,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    t('login')\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                                href: \"/signup\",\n                                                                onClick: ()=>setShowMobileMenu(false),\n                                                                className: \"flex items-center py-2 hover:text-primary\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_Mail_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                        lineNumber: 418,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    t('signUp')\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                lineNumber: 417,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-984f48e2305591f3\" + \" \" + \"md:hidden fixed bottom-24 left-6 z-40 flex flex-col items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        className: \"h-14 w-14 rounded-full shadow-lg flex flex-col items-center justify-center\",\n                                        style: {\n                                            backgroundColor: primaryColor\n                                        },\n                                        onClick: ()=>setShowMobileCategories(!showMobileCategories),\n                                        children: showMobileCategories ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_Mail_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_Mail_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"jsx-984f48e2305591f3\" + \" \" + \"text-xs font-medium mt-1 bg-background/80 px-2 py-1 rounded-full shadow-sm\",\n                                        children: t('categories')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                lineNumber: 428,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-984f48e2305591f3\" + \" \" + \"md:hidden fixed inset-0 bg-background/80 backdrop-blur-sm z-30 transition-opacity duration-300 \".concat(showMobileCategories ? 'opacity-100' : 'opacity-0 pointer-events-none'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-984f48e2305591f3\" + \" \" + \"fixed left-1/2 -translate-x-1/2 bottom-40 w-5/6 max-h-[60vh] bg-white rounded-lg shadow-xl overflow-y-auto transition-transform duration-300 \".concat(showMobileCategories ? 'translate-y-0' : 'translate-y-full'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-984f48e2305591f3\" + \" \" + \"p-4 border-b\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                style: {\n                                                    color: primaryColor\n                                                },\n                                                className: \"jsx-984f48e2305591f3\" + \" \" + \"font-semibold text-center\",\n                                                children: t('categories')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 450,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-984f48e2305591f3\" + \" \" + \"p-4\",\n                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-984f48e2305591f3\" + \" \" + \"p-4 text-center text-muted-foreground\",\n                                                children: t('loadingCategories')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 455,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-984f48e2305591f3\" + \" \" + \"space-y-2\",\n                                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-984f48e2305591f3\" + \" \" + \"mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                style: {\n                                                                    color: primaryColor\n                                                                },\n                                                                onClick: ()=>{\n                                                                    setSelectedCategory(category.name);\n                                                                    setSelectedCategoryId(category.id);\n                                                                    setSelectedSubcategory(null);\n                                                                    setShowMobileCategories(false);\n                                                                    // Navigate to products page with this category\n                                                                    const params = new URLSearchParams();\n                                                                    params.append('category', category.id.toString());\n                                                                    router.push(\"/products?\".concat(params.toString()));\n                                                                },\n                                                                className: \"jsx-984f48e2305591f3\" + \" \" + \"font-medium py-2 border-b w-full text-left\",\n                                                                children: category.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                lineNumber: 460,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            category.subcategories.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-984f48e2305591f3\" + \" \" + \"ml-4 mt-2 space-y-1\",\n                                                                children: category.subcategories.map((sub, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>{\n                                                                            setSelectedSubcategory(sub);\n                                                                            setSelectedCategory(null);\n                                                                            setSelectedCategoryId(category.id);\n                                                                            setShowMobileCategories(false);\n                                                                            // Navigate to products page with this category\n                                                                            const params = new URLSearchParams();\n                                                                            params.append('category', category.id.toString());\n                                                                            router.push(\"/products?\".concat(params.toString()));\n                                                                        },\n                                                                        className: \"jsx-984f48e2305591f3\" + \" \" + \"block py-1.5 text-sm text-muted-foreground hover:text-primary w-full text-left\",\n                                                                        children: sub\n                                                                    }, index, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                        lineNumber: 479,\n                                                                        columnNumber: 31\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                lineNumber: 477,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, category.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 453,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-984f48e2305591f3\" + \" \" + \"flex items-center gap-2 md:gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/wishlist\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"icon\",\n                                            className: \"relative  md:inline-flex\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_Mail_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    className: \"h-5 w-5\",\n                                                    style: {\n                                                        color: primaryColor\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-984f48e2305591f3\" + \" \" + \"absolute -top-1 -right-1 bg-primary text-white text-xs rounded-full h-4 w-4 flex items-center justify-center\",\n                                                    children: wishlistCount\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 511,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 509,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/cart\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"icon\",\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_Mail_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    className: \"h-5 w-5\",\n                                                    style: {\n                                                        color: primaryColor\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 518,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-984f48e2305591f3\" + \" \" + \"absolute -top-1 -right-1 bg-primary text-white text-xs rounded-full h-4 w-4 flex items-center justify-center\",\n                                                    children: cartCount\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 519,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 517,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 516,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        className: \"md:hidden\",\n                                        onClick: ()=>setShowMobileMenu(true),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_Mail_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 525,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 524,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                lineNumber: 507,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                lineNumber: 216,\n                columnNumber: 7\n            }, this),\n            showColorPicker && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_color_picker__WEBPACK_IMPORTED_MODULE_13__.ColorPicker, {\n                onColorSelect: (color)=>{\n                    setPrimaryColor(color);\n                    setShowColorPicker(false);\n                },\n                onClose: ()=>setShowColorPicker(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                lineNumber: 531,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n        lineNumber: 160,\n        columnNumber: 5\n    }, this);\n}\n_s(Header, \"uLv9Yi/mrNDTE+zWECTgE8zMIDY=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _contexts_cart_context__WEBPACK_IMPORTED_MODULE_5__.useCart,\n        _contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_6__.useWishlist,\n        _contexts_settings_context__WEBPACK_IMPORTED_MODULE_12__.useSettings\n    ];\n});\n_c = Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ui/header.tsx\n"));

/***/ })

});