"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2446],{1027:(t,e,i)=>{i.d(e,{F:()=>o});var s=i(3463);let n=t=>"boolean"==typeof t?`${t}`:0===t?"0":t,r=s.$,o=(t,e)=>i=>{var s;if((null==e?void 0:e.variants)==null)return r(t,null==i?void 0:i.class,null==i?void 0:i.className);let{variants:o,defaultVariants:a}=e,l=Object.keys(o).map(t=>{let e=null==i?void 0:i[t],s=null==a?void 0:a[t];if(null===e)return null;let r=n(e)||n(s);return o[t][r]}),u=i&&Object.entries(i).reduce((t,e)=>{let[i,s]=e;return void 0===s||(t[i]=s),t},{});return r(t,l,null==e?void 0:null===(s=e.compoundVariants)||void 0===s?void 0:s.reduce((t,e)=>{let{class:i,className:s,...n}=e;return Object.entries(n).every(t=>{let[e,i]=t;return Array.isArray(i)?i.includes({...a,...u}[e]):({...a,...u})[e]===i})?[...t,i,s]:t},[]),null==i?void 0:i.class,null==i?void 0:i.className)}},1773:(t,e,i)=>{i.d(e,{A:()=>s});let s=(0,i(7401).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},4710:(t,e,i)=>{i.d(e,{L:()=>s});let s=(0,i(2115).createContext)({})},5087:(t,e,i)=>{i.d(e,{xQ:()=>r});var s=i(2115),n=i(9656);function r(t=!0){let e=(0,s.useContext)(n.t);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:o,register:a}=e,l=(0,s.useId)();(0,s.useEffect)(()=>{t&&a(l)},[t]);let u=(0,s.useCallback)(()=>t&&o&&o(l),[l,o,t]);return!i&&o?[!1,u]:[!0]}},5403:(t,e,i)=>{i.d(e,{E:()=>n});var s=i(2115);let n=i(5687).B?s.useLayoutEffect:s.useEffect},5687:(t,e,i)=>{i.d(e,{B:()=>s});let s="undefined"!=typeof window},7249:(t,e,i)=>{i.d(e,{Q:()=>s});let s=(0,i(2115).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},7401:(t,e,i)=>{i.d(e,{A:()=>l});var s=i(2115);let n=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),r=function(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];return e.filter((t,e,i)=>!!t&&i.indexOf(t)===e).join(" ")};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=(0,s.forwardRef)((t,e)=>{let{color:i="currentColor",size:n=24,strokeWidth:a=2,absoluteStrokeWidth:l,className:u="",children:h,iconNode:d,...c}=t;return(0,s.createElement)("svg",{ref:e,...o,width:n,height:n,stroke:i,strokeWidth:l?24*Number(a)/Number(n):a,className:r("lucide",u),...c},[...d.map(t=>{let[e,i]=t;return(0,s.createElement)(e,i)}),...Array.isArray(h)?h:[h]])}),l=(t,e)=>{let i=(0,s.forwardRef)((i,o)=>{let{className:l,...u}=i;return(0,s.createElement)(a,{ref:o,iconNode:e,className:r("lucide-".concat(n(t)),l),...u})});return i.displayName="".concat(t),i}},9124:(t,e,i)=>{let s;function n(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}i.d(e,{P:()=>rf});let r=t=>Array.isArray(t);function o(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let s=0;s<i;s++)if(e[s]!==t[s])return!1;return!0}function a(t){return"string"==typeof t||Array.isArray(t)}function l(t){let e=[{},{}];return null==t||t.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function u(t,e,i,s){if("function"==typeof e){let[n,r]=l(s);e=e(void 0!==i?i:t.custom,n,r)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[n,r]=l(s);e=e(void 0!==i?i:t.custom,n,r)}return e}function h(t,e,i){let s=t.getProps();return u(s,e,void 0!==i?i:s.custom,t)}let d=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],c=["initial",...d];function p(t){let e;return()=>(void 0===e&&(e=t()),e)}let m=p(()=>void 0!==window.ScrollTimeline);class f{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>"finished"in t?t.finished:t))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let i=0;i<this.animations.length;i++)this.animations[i][t]=e}attachTimeline(t,e){let i=this.animations.map(i=>m()&&i.attachTimeline?i.attachTimeline(t):"function"==typeof e?e(i):void 0);return()=>{i.forEach((t,e)=>{t&&t(),this.animations[e].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach(e=>e[t]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class v extends f{then(t,e){return Promise.all(this.animations).then(t).catch(e)}}function g(t,e){return t?t[e]||t.default||t:void 0}function y(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function x(t){return"function"==typeof t}function w(t,e){t.timeline=e,t.onfinish=null}let P=t=>Array.isArray(t)&&"number"==typeof t[0],T={linearEasing:void 0},b=function(t,e){let i=p(t);return()=>{var t;return null!==(t=T[e])&&void 0!==t?t:i()}}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),A=(t,e,i)=>{let s=e-t;return 0===s?1:(i-t)/s},S=(t,e,i=10)=>{let s="",n=Math.max(Math.round(e/i),2);for(let e=0;e<n;e++)s+=t(A(0,n-1,e))+", ";return`linear(${s.substring(0,s.length-2)})`},V=([t,e,i,s])=>`cubic-bezier(${t}, ${e}, ${i}, ${s})`,E={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:V([0,.65,.55,1]),circOut:V([.55,0,1,.45]),backIn:V([.31,.01,.66,-.59]),backOut:V([.33,1.53,.69,.99])},M={x:!1,y:!1};function C(t,e){let i=function(t,e,i){var s;if(t instanceof Element)return[t];if("string"==typeof t){let e=document,i=(s=void 0,e.querySelectorAll(t));return i?Array.from(i):[]}return Array.from(t)}(t),s=new AbortController;return[i,{passive:!0,...e,signal:s.signal},()=>s.abort()]}function D(t){return e=>{"touch"===e.pointerType||M.x||M.y||t(e)}}let k=(t,e)=>!!e&&(t===e||k(t,e.parentElement)),R=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,L=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),j=new WeakSet;function F(t){return e=>{"Enter"===e.key&&t(e)}}function B(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let O=(t,e)=>{let i=t.currentTarget;if(!i)return;let s=F(()=>{if(j.has(i))return;B(i,"down");let t=F(()=>{B(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>B(i,"cancel"),e)});i.addEventListener("keydown",s,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",s),e)};function I(t){return R(t)&&!(M.x||M.y)}let U=t=>1e3*t,N=t=>t/1e3,$=t=>t,W=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],z=new Set(W),H=new Set(["width","height","top","left","right","bottom",...W]),Y=t=>!!(t&&"object"==typeof t&&t.mix&&t.toValue),X=t=>r(t)?t[t.length-1]||0:t,K={skipAnimations:!1,useManualTiming:!1},q=["read","resolveKeyframes","update","preRender","render","postRender"];function G(t,e){let i=!1,s=!0,n={delta:0,timestamp:0,isProcessing:!1},r=()=>i=!0,o=q.reduce((t,e)=>(t[e]=function(t){let e=new Set,i=new Set,s=!1,n=!1,r=new WeakSet,o={delta:0,timestamp:0,isProcessing:!1};function a(e){r.has(e)&&(l.schedule(e),t()),e(o)}let l={schedule:(t,n=!1,o=!1)=>{let a=o&&s?e:i;return n&&r.add(t),a.has(t)||a.add(t),t},cancel:t=>{i.delete(t),r.delete(t)},process:t=>{if(o=t,s){n=!0;return}s=!0,[e,i]=[i,e],e.forEach(a),e.clear(),s=!1,n&&(n=!1,l.process(t))}};return l}(r),t),{}),{read:a,resolveKeyframes:l,update:u,preRender:h,render:d,postRender:c}=o,p=()=>{let r=K.useManualTiming?n.timestamp:performance.now();i=!1,n.delta=s?1e3/60:Math.max(Math.min(r-n.timestamp,40),1),n.timestamp=r,n.isProcessing=!0,a.process(n),l.process(n),u.process(n),h.process(n),d.process(n),c.process(n),n.isProcessing=!1,i&&e&&(s=!1,t(p))},m=()=>{i=!0,s=!0,n.isProcessing||t(p)};return{schedule:q.reduce((t,e)=>{let s=o[e];return t[e]=(t,e=!1,n=!1)=>(i||m(),s.schedule(t,e,n)),t},{}),cancel:t=>{for(let e=0;e<q.length;e++)o[q[e]].cancel(t)},state:n,steps:o}}let{schedule:_,cancel:Z,state:Q,steps:J}=G("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:$,!0);function tt(){s=void 0}let te={now:()=>(void 0===s&&te.set(Q.isProcessing||K.useManualTiming?Q.timestamp:performance.now()),s),set:t=>{s=t,queueMicrotask(tt)}};function ti(t,e){-1===t.indexOf(e)&&t.push(e)}function ts(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class tn{constructor(){this.subscriptions=[]}add(t){return ti(this.subscriptions,t),()=>ts(this.subscriptions,t)}notify(t,e,i){let s=this.subscriptions.length;if(s){if(1===s)this.subscriptions[0](t,e,i);else for(let n=0;n<s;n++){let s=this.subscriptions[n];s&&s(t,e,i)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let tr=t=>!isNaN(parseFloat(t)),to={current:void 0};class ta{constructor(t,e={}){this.version="11.18.2",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=te.now();this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),e&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=te.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=tr(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new tn);let i=this.events[t].add(e);return"change"===t?()=>{i(),_.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return to.current&&to.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=te.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function tl(t,e){return new ta(t,e)}let tu=t=>!!(t&&t.getVelocity);function th(t,e){let i=t.getValue("willChange");if(tu(i)&&i.add)return i.add(e)}let td=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),tc="data-"+td("framerAppearId"),tp={current:!1},tm=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function tf(t,e,i,s){if(t===e&&i===s)return $;let n=e=>(function(t,e,i,s,n){let r,o;let a=0;do(r=tm(o=e+(i-e)/2,s,n)-t)>0?i=o:e=o;while(Math.abs(r)>1e-7&&++a<12);return o})(e,0,1,t,i);return t=>0===t||1===t?t:tm(n(t),e,s)}let tv=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,tg=t=>e=>1-t(1-e),ty=tf(.33,1.53,.69,.99),tx=tg(ty),tw=tv(tx),tP=t=>(t*=2)<1?.5*tx(t):.5*(2-Math.pow(2,-10*(t-1))),tT=t=>1-Math.sin(Math.acos(t)),tb=tg(tT),tA=tv(tT),tS=t=>/^0[^.\s]+$/u.test(t),tV=(t,e,i)=>i>e?e:i<t?t:i,tE={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},tM={...tE,transform:t=>tV(0,1,t)},tC={...tE,default:1},tD=t=>Math.round(1e5*t)/1e5,tk=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,tR=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tL=(t,e)=>i=>!!("string"==typeof i&&tR.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),tj=(t,e,i)=>s=>{if("string"!=typeof s)return s;let[n,r,o,a]=s.match(tk);return{[t]:parseFloat(n),[e]:parseFloat(r),[i]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},tF=t=>tV(0,255,t),tB={...tE,transform:t=>Math.round(tF(t))},tO={test:tL("rgb","red"),parse:tj("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:s=1})=>"rgba("+tB.transform(t)+", "+tB.transform(e)+", "+tB.transform(i)+", "+tD(tM.transform(s))+")"},tI={test:tL("#"),parse:function(t){let e="",i="",s="",n="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),s=t.substring(5,7),n=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),s=t.substring(3,4),n=t.substring(4,5),e+=e,i+=i,s+=s,n+=n),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(s,16),alpha:n?parseInt(n,16)/255:1}},transform:tO.transform},tU=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),tN=tU("deg"),t$=tU("%"),tW=tU("px"),tz=tU("vh"),tH=tU("vw"),tY={...t$,parse:t=>t$.parse(t)/100,transform:t=>t$.transform(100*t)},tX={test:tL("hsl","hue"),parse:tj("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:s=1})=>"hsla("+Math.round(t)+", "+t$.transform(tD(e))+", "+t$.transform(tD(i))+", "+tD(tM.transform(s))+")"},tK={test:t=>tO.test(t)||tI.test(t)||tX.test(t),parse:t=>tO.test(t)?tO.parse(t):tX.test(t)?tX.parse(t):tI.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?tO.transform(t):tX.transform(t)},tq=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tG="number",t_="color",tZ=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tQ(t){let e=t.toString(),i=[],s={color:[],number:[],var:[]},n=[],r=0,o=e.replace(tZ,t=>(tK.test(t)?(s.color.push(r),n.push(t_),i.push(tK.parse(t))):t.startsWith("var(")?(s.var.push(r),n.push("var"),i.push(t)):(s.number.push(r),n.push(tG),i.push(parseFloat(t))),++r,"${}")).split("${}");return{values:i,split:o,indexes:s,types:n}}function tJ(t){return tQ(t).values}function t0(t){let{split:e,types:i}=tQ(t),s=e.length;return t=>{let n="";for(let r=0;r<s;r++)if(n+=e[r],void 0!==t[r]){let e=i[r];e===tG?n+=tD(t[r]):e===t_?n+=tK.transform(t[r]):n+=t[r]}return n}}let t1=t=>"number"==typeof t?0:t,t5={test:function(t){var e,i;return isNaN(t)&&"string"==typeof t&&((null===(e=t.match(tk))||void 0===e?void 0:e.length)||0)+((null===(i=t.match(tq))||void 0===i?void 0:i.length)||0)>0},parse:tJ,createTransformer:t0,getAnimatableNone:function(t){let e=tJ(t);return t0(t)(e.map(t1))}},t2=new Set(["brightness","contrast","saturate","opacity"]);function t3(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[s]=i.match(tk)||[];if(!s)return t;let n=i.replace(s,""),r=+!!t2.has(e);return s!==i&&(r*=100),e+"("+r+n+")"}let t9=/\b([a-z-]*)\(.*?\)/gu,t4={...t5,getAnimatableNone:t=>{let e=t.match(t9);return e?e.map(t3).join(" "):t}},t6={...tE,transform:Math.round},t7={borderWidth:tW,borderTopWidth:tW,borderRightWidth:tW,borderBottomWidth:tW,borderLeftWidth:tW,borderRadius:tW,radius:tW,borderTopLeftRadius:tW,borderTopRightRadius:tW,borderBottomRightRadius:tW,borderBottomLeftRadius:tW,width:tW,maxWidth:tW,height:tW,maxHeight:tW,top:tW,right:tW,bottom:tW,left:tW,padding:tW,paddingTop:tW,paddingRight:tW,paddingBottom:tW,paddingLeft:tW,margin:tW,marginTop:tW,marginRight:tW,marginBottom:tW,marginLeft:tW,backgroundPositionX:tW,backgroundPositionY:tW,rotate:tN,rotateX:tN,rotateY:tN,rotateZ:tN,scale:tC,scaleX:tC,scaleY:tC,scaleZ:tC,skew:tN,skewX:tN,skewY:tN,distance:tW,translateX:tW,translateY:tW,translateZ:tW,x:tW,y:tW,z:tW,perspective:tW,transformPerspective:tW,opacity:tM,originX:tY,originY:tY,originZ:tW,zIndex:t6,size:tW,fillOpacity:tM,strokeOpacity:tM,numOctaves:t6},t8={...t7,color:tK,backgroundColor:tK,outlineColor:tK,fill:tK,stroke:tK,borderColor:tK,borderTopColor:tK,borderRightColor:tK,borderBottomColor:tK,borderLeftColor:tK,filter:t4,WebkitFilter:t4},et=t=>t8[t];function ee(t,e){let i=et(t);return i!==t4&&(i=t5),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let ei=new Set(["auto","none","0"]),es=t=>t===tE||t===tW,en=(t,e)=>parseFloat(t.split(", ")[e]),er=(t,e)=>(i,{transform:s})=>{if("none"===s||!s)return 0;let n=s.match(/^matrix3d\((.+)\)$/u);if(n)return en(n[1],e);{let e=s.match(/^matrix\((.+)\)$/u);return e?en(e[1],t):0}},eo=new Set(["x","y","z"]),ea=W.filter(t=>!eo.has(t)),el={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:er(4,13),y:er(5,14)};el.translateX=el.x,el.translateY=el.y;let eu=new Set,eh=!1,ed=!1;function ec(){if(ed){let t=Array.from(eu).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return ea.forEach(i=>{let s=t.getValue(i);void 0!==s&&(e.push([i,s.get()]),s.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{var s;null===(s=t.getValue(e))||void 0===s||s.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}ed=!1,eh=!1,eu.forEach(t=>t.complete()),eu.clear()}function ep(){eu.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(ed=!0)})}class em{constructor(t,e,i,s,n,r=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=s,this.element=n,this.isAsync=r}scheduleResolve(){this.isScheduled=!0,this.isAsync?(eu.add(this),eh||(eh=!0,_.read(ep),_.resolveKeyframes(ec))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:s}=this;for(let n=0;n<t.length;n++)if(null===t[n]){if(0===n){let n=null==s?void 0:s.get(),r=t[t.length-1];if(void 0!==n)t[0]=n;else if(i&&e){let s=i.readValue(e,r);null!=s&&(t[0]=s)}void 0===t[0]&&(t[0]=r),s&&void 0===n&&s.set(t[0])}else t[n]=t[n-1]}}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),eu.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,eu.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}let ef=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),ev=t=>e=>"string"==typeof e&&e.startsWith(t),eg=ev("--"),ey=ev("var(--"),ex=t=>!!ey(t)&&ew.test(t.split("/*")[0].trim()),ew=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,eP=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,eT=t=>e=>e.test(t),eb=[tE,tW,t$,tN,tH,tz,{test:t=>"auto"===t,parse:t=>t}],eA=t=>eb.find(eT(t));class eS extends em{constructor(t,e,i,s,n){super(t,e,i,s,n,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let s=t[i];if("string"==typeof s&&ex(s=s.trim())){let n=function t(e,i,s=1){$(s<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[n,r]=function(t){let e=eP.exec(t);if(!e)return[,];let[,i,s,n]=e;return[`--${null!=i?i:s}`,n]}(e);if(!n)return;let o=window.getComputedStyle(i).getPropertyValue(n);if(o){let t=o.trim();return ef(t)?parseFloat(t):t}return ex(r)?t(r,i,s+1):r}(s,e.current);void 0!==n&&(t[i]=n),i===t.length-1&&(this.finalKeyframe=s)}}if(this.resolveNoneKeyframes(),!H.has(i)||2!==t.length)return;let[s,n]=t,r=eA(s),o=eA(n);if(r!==o){if(es(r)&&es(o))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else this.needsMeasurement=!0}}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var s;("number"==typeof(s=t[e])?0===s:null===s||"none"===s||"0"===s||tS(s))&&i.push(e)}i.length&&function(t,e,i){let s,n=0;for(;n<t.length&&!s;){let e=t[n];"string"==typeof e&&!ei.has(e)&&tQ(e).values.length&&(s=t[n]),n++}if(s&&i)for(let n of e)t[n]=ee(i,s)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=el[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let s=e[e.length-1];void 0!==s&&t.getValue(i,s).jump(s,!1)}measureEndState(){var t;let{element:e,name:i,unresolvedKeyframes:s}=this;if(!e||!e.current)return;let n=e.getValue(i);n&&n.jump(this.measuredOrigin,!1);let r=s.length-1,o=s[r];s[r]=el[i](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),(null===(t=this.removedTransforms)||void 0===t?void 0:t.length)&&this.removedTransforms.forEach(([t,i])=>{e.getValue(t).set(i)}),this.resolveNoneKeyframes()}}let eV=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(t5.test(t)||"0"===t)&&!t.startsWith("url(")),eE=t=>null!==t;function eM(t,{repeat:e,repeatType:i="loop"},s){let n=t.filter(eE),r=e&&"loop"!==i&&e%2==1?0:n.length-1;return r&&void 0!==s?s:n[r]}class eC{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:s=0,repeatDelay:n=0,repeatType:r="loop",...o}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=te.now(),this.options={autoplay:t,delay:e,type:i,repeat:s,repeatDelay:n,repeatType:r,...o},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt}get resolved(){return this._resolved||this.hasAttemptedResolve||(ep(),ec()),this._resolved}onKeyframesResolved(t,e){this.resolvedAt=te.now(),this.hasAttemptedResolve=!0;let{name:i,type:s,velocity:n,delay:r,onComplete:o,onUpdate:a,isGenerator:l}=this.options;if(!l&&!function(t,e,i,s){let n=t[0];if(null===n)return!1;if("display"===e||"visibility"===e)return!0;let r=t[t.length-1],o=eV(n,e),a=eV(r,e);return $(o===a,`You are trying to animate ${e} from "${n}" to "${r}". ${n} is not an animatable value - to enable this animation set ${n} to a value animatable to ${r} via the \`style\` property.`),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||x(i))&&s)}(t,i,s,n)){if(tp.current||!r){a&&a(eM(t,this.options,e)),o&&o(),this.resolveFinishedPromise();return}this.options.duration=0}let u=this.initPlayback(t,e);!1!==u&&(this._resolved={keyframes:t,finalKeyframe:e,...u},this.onPostResolved())}onPostResolved(){}then(t,e){return this.currentFinishedPromise.then(t,e)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}}let eD=(t,e,i)=>t+(e-t)*i;function ek(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function eR(t,e){return i=>i>0?e:t}let eL=(t,e,i)=>{let s=t*t,n=i*(e*e-s)+s;return n<0?0:Math.sqrt(n)},ej=[tI,tO,tX],eF=t=>ej.find(e=>e.test(t));function eB(t){let e=eF(t);if($(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===tX&&(i=function({hue:t,saturation:e,lightness:i,alpha:s}){t/=360,i/=100;let n=0,r=0,o=0;if(e/=100){let s=i<.5?i*(1+e):i+e-i*e,a=2*i-s;n=ek(a,s,t+1/3),r=ek(a,s,t),o=ek(a,s,t-1/3)}else n=r=o=i;return{red:Math.round(255*n),green:Math.round(255*r),blue:Math.round(255*o),alpha:s}}(i)),i}let eO=(t,e)=>{let i=eB(t),s=eB(e);if(!i||!s)return eR(t,e);let n={...i};return t=>(n.red=eL(i.red,s.red,t),n.green=eL(i.green,s.green,t),n.blue=eL(i.blue,s.blue,t),n.alpha=eD(i.alpha,s.alpha,t),tO.transform(n))},eI=(t,e)=>i=>e(t(i)),eU=(...t)=>t.reduce(eI),eN=new Set(["none","hidden"]);function e$(t,e){return i=>eD(t,e,i)}function eW(t){return"number"==typeof t?e$:"string"==typeof t?ex(t)?eR:tK.test(t)?eO:eY:Array.isArray(t)?ez:"object"==typeof t?tK.test(t)?eO:eH:eR}function ez(t,e){let i=[...t],s=i.length,n=t.map((t,i)=>eW(t)(t,e[i]));return t=>{for(let e=0;e<s;e++)i[e]=n[e](t);return i}}function eH(t,e){let i={...t,...e},s={};for(let n in i)void 0!==t[n]&&void 0!==e[n]&&(s[n]=eW(t[n])(t[n],e[n]));return t=>{for(let e in s)i[e]=s[e](t);return i}}let eY=(t,e)=>{let i=t5.createTransformer(e),s=tQ(t),n=tQ(e);return s.indexes.var.length===n.indexes.var.length&&s.indexes.color.length===n.indexes.color.length&&s.indexes.number.length>=n.indexes.number.length?eN.has(t)&&!n.values.length||eN.has(e)&&!s.values.length?function(t,e){return eN.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):eU(ez(function(t,e){var i;let s=[],n={color:0,var:0,number:0};for(let r=0;r<e.values.length;r++){let o=e.types[r],a=t.indexes[o][n[o]],l=null!==(i=t.values[a])&&void 0!==i?i:0;s[r]=l,n[o]++}return s}(s,n),n.values),i):($(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),eR(t,e))};function eX(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?eD(t,e,i):eW(t)(t,e)}function eK(t,e,i){var s,n;let r=Math.max(e-5,0);return s=i-t(r),(n=e-r)?1e3/n*s:0}let eq={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function eG(t,e){return t*Math.sqrt(1-e*e)}let e_=["duration","bounce"],eZ=["stiffness","damping","mass"];function eQ(t,e){return e.some(e=>void 0!==t[e])}function eJ(t=eq.visualDuration,e=eq.bounce){let i;let s="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:n,restDelta:r}=s,o=s.keyframes[0],a=s.keyframes[s.keyframes.length-1],l={done:!1,value:o},{stiffness:u,damping:h,mass:d,duration:c,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:eq.velocity,stiffness:eq.stiffness,damping:eq.damping,mass:eq.mass,isResolvedFromDuration:!1,...t};if(!eQ(t,eZ)&&eQ(t,e_)){if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),s=i*i,n=2*tV(.05,1,1-(t.bounce||0))*Math.sqrt(s);e={...e,mass:eq.mass,stiffness:s,damping:n}}else{let i=function({duration:t=eq.duration,bounce:e=eq.bounce,velocity:i=eq.velocity,mass:s=eq.mass}){let n,r;$(t<=U(eq.maxDuration),"Spring duration must be 10 seconds or less");let o=1-e;o=tV(eq.minDamping,eq.maxDamping,o),t=tV(eq.minDuration,eq.maxDuration,N(t)),o<1?(n=e=>{let s=e*o,n=s*t;return .001-(s-i)/eG(e,o)*Math.exp(-n)},r=e=>{let s=e*o*t,r=Math.pow(o,2)*Math.pow(e,2)*t,a=Math.exp(-s),l=eG(Math.pow(e,2),o);return(s*i+i-r)*a*(-n(e)+.001>0?-1:1)/l}):(n=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),r=e=>t*t*(i-e)*Math.exp(-e*t));let a=function(t,e,i){let s=i;for(let i=1;i<12;i++)s-=t(s)/e(s);return s}(n,r,5/t);if(t=U(t),isNaN(a))return{stiffness:eq.stiffness,damping:eq.damping,duration:t};{let e=Math.pow(a,2)*s;return{stiffness:e,damping:2*o*Math.sqrt(s*e),duration:t}}}(t);(e={...e,...i,mass:eq.mass}).isResolvedFromDuration=!0}}return e}({...s,velocity:-N(s.velocity||0)}),f=p||0,v=h/(2*Math.sqrt(u*d)),g=a-o,x=N(Math.sqrt(u/d)),w=5>Math.abs(g);if(n||(n=w?eq.restSpeed.granular:eq.restSpeed.default),r||(r=w?eq.restDelta.granular:eq.restDelta.default),v<1){let t=eG(x,v);i=e=>a-Math.exp(-v*x*e)*((f+v*x*g)/t*Math.sin(t*e)+g*Math.cos(t*e))}else if(1===v)i=t=>a-Math.exp(-x*t)*(g+(f+x*g)*t);else{let t=x*Math.sqrt(v*v-1);i=e=>{let i=Math.exp(-v*x*e),s=Math.min(t*e,300);return a-i*((f+v*x*g)*Math.sinh(s)+t*g*Math.cosh(s))/t}}let P={calculatedDuration:m&&c||null,next:t=>{let e=i(t);if(m)l.done=t>=c;else{let s=0;v<1&&(s=0===t?U(f):eK(i,t,e));let o=Math.abs(s)<=n,u=Math.abs(a-e)<=r;l.done=o&&u}return l.value=l.done?a:e,l},toString:()=>{let t=Math.min(y(P),2e4),e=S(e=>P.next(t*e).value,t,30);return t+"ms "+e}};return P}function e0({keyframes:t,velocity:e=0,power:i=.8,timeConstant:s=325,bounceDamping:n=10,bounceStiffness:r=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:h}){let d,c;let p=t[0],m={done:!1,value:p},f=t=>void 0!==a&&t<a||void 0!==l&&t>l,v=t=>void 0===a?l:void 0===l?a:Math.abs(a-t)<Math.abs(l-t)?a:l,g=i*e,y=p+g,x=void 0===o?y:o(y);x!==y&&(g=x-p);let w=t=>-g*Math.exp(-t/s),P=t=>x+w(t),T=t=>{let e=w(t),i=P(t);m.done=Math.abs(e)<=u,m.value=m.done?x:i},b=t=>{f(m.value)&&(d=t,c=eJ({keyframes:[m.value,v(m.value)],velocity:eK(P,t,m.value),damping:n,stiffness:r,restDelta:u,restSpeed:h}))};return b(0),{calculatedDuration:null,next:t=>{let e=!1;return(c||void 0!==d||(e=!0,T(t),b(t)),void 0!==d&&t>=d)?c.next(t-d):(e||T(t),m)}}}let e1=tf(.42,0,1,1),e5=tf(0,0,.58,1),e2=tf(.42,0,.58,1),e3=t=>Array.isArray(t)&&"number"!=typeof t[0],e9={linear:$,easeIn:e1,easeInOut:e2,easeOut:e5,circIn:tT,circInOut:tA,circOut:tb,backIn:tx,backInOut:tw,backOut:ty,anticipate:tP},e4=t=>{if(P(t)){$(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,s,n]=t;return tf(e,i,s,n)}return"string"==typeof t?($(void 0!==e9[t],`Invalid easing type '${t}'`),e9[t]):t};function e6({duration:t=300,keyframes:e,times:i,ease:s="easeInOut"}){let n=e3(s)?s.map(e4):e4(s),r={done:!1,value:e[0]},o=function(t,e,{clamp:i=!0,ease:s,mixer:n}={}){let r=t.length;if($(r===e.length,"Both input and output ranges must be the same length"),1===r)return()=>e[0];if(2===r&&e[0]===e[1])return()=>e[1];let o=t[0]===t[1];t[0]>t[r-1]&&(t=[...t].reverse(),e=[...e].reverse());let a=function(t,e,i){let s=[],n=i||eX,r=t.length-1;for(let i=0;i<r;i++){let r=n(t[i],t[i+1]);e&&(r=eU(Array.isArray(e)?e[i]||$:e,r)),s.push(r)}return s}(e,s,n),l=a.length,u=i=>{if(o&&i<t[0])return e[0];let s=0;if(l>1)for(;s<t.length-2&&!(i<t[s+1]);s++);let n=A(t[s],t[s+1],i);return a[s](n)};return i?e=>u(tV(t[0],t[r-1],e)):u}((i&&i.length===e.length?i:function(t){let e=[0];return function(t,e){let i=t[t.length-1];for(let s=1;s<=e;s++){let n=A(0,e,s);t.push(eD(i,1,n))}}(e,t.length-1),e}(e)).map(e=>e*t),e,{ease:Array.isArray(n)?n:e.map(()=>n||e2).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(r.value=o(e),r.done=e>=t,r)}}let e7=t=>{let e=({timestamp:e})=>t(e);return{start:()=>_.update(e,!0),stop:()=>Z(e),now:()=>Q.isProcessing?Q.timestamp:te.now()}},e8={decay:e0,inertia:e0,tween:e6,keyframes:e6,spring:eJ},it=t=>t/100;class ie extends eC{constructor(t){super(t),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.teardown();let{onStop:t}=this.options;t&&t()};let{name:e,motionValue:i,element:s,keyframes:n}=this.options,r=(null==s?void 0:s.KeyframeResolver)||em;this.resolver=new r(n,(t,e)=>this.onKeyframesResolved(t,e),e,i,s),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(t){let e,i;let{type:s="keyframes",repeat:n=0,repeatDelay:r=0,repeatType:o,velocity:a=0}=this.options,l=x(s)?s:e8[s]||e6;l!==e6&&"number"!=typeof t[0]&&(e=eU(it,eX(t[0],t[1])),t=[0,100]);let u=l({...this.options,keyframes:t});"mirror"===o&&(i=l({...this.options,keyframes:[...t].reverse(),velocity:-a})),null===u.calculatedDuration&&(u.calculatedDuration=y(u));let{calculatedDuration:h}=u,d=h+r;return{generator:u,mirroredGenerator:i,mapPercentToKeyframes:e,calculatedDuration:h,resolvedDuration:d,totalDuration:d*(n+1)-r}}onPostResolved(){let{autoplay:t=!0}=this.options;this.play(),"paused"!==this.pendingPlayState&&t?this.state=this.pendingPlayState:this.pause()}tick(t,e=!1){let{resolved:i}=this;if(!i){let{keyframes:t}=this.options;return{done:!0,value:t[t.length-1]}}let{finalKeyframe:s,generator:n,mirroredGenerator:r,mapPercentToKeyframes:o,keyframes:a,calculatedDuration:l,totalDuration:u,resolvedDuration:h}=i;if(null===this.startTime)return n.next(0);let{delay:d,repeat:c,repeatType:p,repeatDelay:m,onUpdate:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-u/this.speed,this.startTime)),e?this.currentTime=t:null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;let v=this.currentTime-d*(this.speed>=0?1:-1),g=this.speed>=0?v<0:v>u;this.currentTime=Math.max(v,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=u);let y=this.currentTime,x=n;if(c){let t=Math.min(this.currentTime,u)/h,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,c+1))%2&&("reverse"===p?(i=1-i,m&&(i-=m/h)):"mirror"===p&&(x=r)),y=tV(0,1,i)*h}let w=g?{done:!1,value:a[0]}:x.next(y);o&&(w.value=o(w.value));let{done:P}=w;g||null===l||(P=this.speed>=0?this.currentTime>=u:this.currentTime<=0);let T=null===this.holdTime&&("finished"===this.state||"running"===this.state&&P);return T&&void 0!==s&&(w.value=eM(a,this.options,s)),f&&f(w.value),T&&this.finish(),w}get duration(){let{resolved:t}=this;return t?N(t.calculatedDuration):0}get time(){return N(this.currentTime)}set time(t){t=U(t),this.currentTime=t,null!==this.holdTime||0===this.speed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=N(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;let{driver:t=e7,onPlay:e,startTime:i}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),e&&e();let s=this.driver.now();null!==this.holdTime?this.startTime=s-this.holdTime:this.startTime?"finished"===this.state&&(this.startTime=s):this.startTime=null!=i?i:this.calcStartTime(),"finished"===this.state&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var t;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=null!==(t=this.currentTime)&&void 0!==t?t:0}complete(){"running"!==this.state&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";let{onComplete:t}=this.options;t&&t()}cancel(){null!==this.cancelTime&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}}let ii=new Set(["opacity","clipPath","filter","transform"]),is=p(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),ir={anticipate:tP,backInOut:tw,circInOut:tA};class io extends eC{constructor(t){super(t);let{name:e,motionValue:i,element:s,keyframes:n}=this.options;this.resolver=new eS(n,(t,e)=>this.onKeyframesResolved(t,e),e,i,s),this.resolver.scheduleResolve()}initPlayback(t,e){var i;let{duration:s=300,times:n,ease:r,type:o,motionValue:a,name:l,startTime:u}=this.options;if(!a.owner||!a.owner.current)return!1;if("string"==typeof r&&b()&&r in ir&&(r=ir[r]),x((i=this.options).type)||"spring"===i.type||!function t(e){return!!("function"==typeof e&&b()||!e||"string"==typeof e&&(e in E||b())||P(e)||Array.isArray(e)&&e.every(t))}(i.ease)){let{onComplete:e,onUpdate:i,motionValue:a,element:l,...u}=this.options,h=function(t,e){let i=new ie({...e,keyframes:t,repeat:0,delay:0,isGenerator:!0}),s={done:!1,value:t[0]},n=[],r=0;for(;!s.done&&r<2e4;)n.push((s=i.sample(r)).value),r+=10;return{times:void 0,keyframes:n,duration:r-10,ease:"linear"}}(t,u);1===(t=h.keyframes).length&&(t[1]=t[0]),s=h.duration,n=h.times,r=h.ease,o="keyframes"}let h=function(t,e,i,{delay:s=0,duration:n=300,repeat:r=0,repeatType:o="loop",ease:a="easeInOut",times:l}={}){let u={[e]:i};l&&(u.offset=l);let h=function t(e,i){if(e)return"function"==typeof e&&b()?S(e,i):P(e)?V(e):Array.isArray(e)?e.map(e=>t(e,i)||E.easeOut):E[e]}(a,n);return Array.isArray(h)&&(u.easing=h),t.animate(u,{delay:s,duration:n,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:r+1,direction:"reverse"===o?"alternate":"normal"})}(a.owner.current,l,t,{...this.options,duration:s,times:n,ease:r});return h.startTime=null!=u?u:this.calcStartTime(),this.pendingTimeline?(w(h,this.pendingTimeline),this.pendingTimeline=void 0):h.onfinish=()=>{let{onComplete:i}=this.options;a.set(eM(t,this.options,e)),i&&i(),this.cancel(),this.resolveFinishedPromise()},{animation:h,duration:s,times:n,type:o,ease:r,keyframes:t}}get duration(){let{resolved:t}=this;if(!t)return 0;let{duration:e}=t;return N(e)}get time(){let{resolved:t}=this;if(!t)return 0;let{animation:e}=t;return N(e.currentTime||0)}set time(t){let{resolved:e}=this;if(!e)return;let{animation:i}=e;i.currentTime=U(t)}get speed(){let{resolved:t}=this;if(!t)return 1;let{animation:e}=t;return e.playbackRate}set speed(t){let{resolved:e}=this;if(!e)return;let{animation:i}=e;i.playbackRate=t}get state(){let{resolved:t}=this;if(!t)return"idle";let{animation:e}=t;return e.playState}get startTime(){let{resolved:t}=this;if(!t)return null;let{animation:e}=t;return e.startTime}attachTimeline(t){if(this._resolved){let{resolved:e}=this;if(!e)return $;let{animation:i}=e;w(i,t)}else this.pendingTimeline=t;return $}play(){if(this.isStopped)return;let{resolved:t}=this;if(!t)return;let{animation:e}=t;"finished"===e.playState&&this.updateFinishedPromise(),e.play()}pause(){let{resolved:t}=this;if(!t)return;let{animation:e}=t;e.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.resolveFinishedPromise(),this.updateFinishedPromise();let{resolved:t}=this;if(!t)return;let{animation:e,keyframes:i,duration:s,type:n,ease:r,times:o}=t;if("idle"===e.playState||"finished"===e.playState)return;if(this.time){let{motionValue:t,onUpdate:e,onComplete:a,element:l,...u}=this.options,h=new ie({...u,keyframes:i,duration:s,type:n,ease:r,times:o,isGenerator:!0}),d=U(this.time);t.setWithVelocity(h.sample(d-10).value,h.sample(d).value,10)}let{onStop:a}=this.options;a&&a(),this.cancel()}complete(){let{resolved:t}=this;t&&t.animation.finish()}cancel(){let{resolved:t}=this;t&&t.animation.cancel()}static supports(t){let{motionValue:e,name:i,repeatDelay:s,repeatType:n,damping:r,type:o}=t;if(!e||!e.owner||!(e.owner.current instanceof HTMLElement))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return is()&&i&&ii.has(i)&&!a&&!l&&!s&&"mirror"!==n&&0!==r&&"inertia"!==o}}let ia={type:"spring",stiffness:500,damping:25,restSpeed:10},il=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),iu={type:"keyframes",duration:.8},ih={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},id=(t,{keyframes:e})=>e.length>2?iu:z.has(t)?t.startsWith("scale")?il(e[1]):ia:ih,ic=(t,e,i,s={},n,r)=>o=>{let a=g(s,t)||{},l=a.delay||s.delay||0,{elapsed:u=0}=s;u-=U(l);let h={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-u,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:r?void 0:n};!function({when:t,delay:e,delayChildren:i,staggerChildren:s,staggerDirection:n,repeat:r,repeatType:o,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(a)&&(h={...h,...id(t,h)}),h.duration&&(h.duration=U(h.duration)),h.repeatDelay&&(h.repeatDelay=U(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let d=!1;if(!1!==h.type&&(0!==h.duration||h.repeatDelay)||(h.duration=0,0!==h.delay||(d=!0)),(tp.current||K.skipAnimations)&&(d=!0,h.duration=0,h.delay=0),d&&!r&&void 0!==e.get()){let t=eM(h.keyframes,a);if(void 0!==t)return _.update(()=>{h.onUpdate(t),h.onComplete()}),new v([])}return!r&&io.supports(h)?new io(h):new ie(h)};function ip(t,e,{delay:i=0,transitionOverride:s,type:n}={}){var r;let{transition:o=t.getDefaultTransition(),transitionEnd:a,...l}=e;s&&(o=s);let u=[],d=n&&t.animationState&&t.animationState.getState()[n];for(let e in l){let s=t.getValue(e,null!==(r=t.latestValues[e])&&void 0!==r?r:null),n=l[e];if(void 0===n||d&&function({protectedKeys:t,needsAnimating:e},i){let s=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,s}(d,e))continue;let a={delay:i,...g(o||{},e)},h=!1;if(window.MotionHandoffAnimation){let i=t.props[tc];if(i){let t=window.MotionHandoffAnimation(i,e,_);null!==t&&(a.startTime=t,h=!0)}}th(t,e),s.start(ic(e,s,n,t.shouldReduceMotion&&H.has(e)?{type:!1}:a,t,h));let c=s.animation;c&&u.push(c)}return a&&Promise.all(u).then(()=>{_.update(()=>{a&&function(t,e){let{transitionEnd:i={},transition:s={},...n}=h(t,e)||{};for(let e in n={...n,...i}){let i=X(n[e]);t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,tl(i))}}(t,a)})}),u}function im(t,e,i={}){var s;let n=h(t,e,"exit"===i.type?null===(s=t.presenceContext)||void 0===s?void 0:s.custom:void 0),{transition:r=t.getDefaultTransition()||{}}=n||{};i.transitionOverride&&(r=i.transitionOverride);let o=n?()=>Promise.all(ip(t,n,i)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(s=0)=>{let{delayChildren:n=0,staggerChildren:o,staggerDirection:a}=r;return function(t,e,i=0,s=0,n=1,r){let o=[],a=(t.variantChildren.size-1)*s,l=1===n?(t=0)=>t*s:(t=0)=>a-t*s;return Array.from(t.variantChildren).sort(iv).forEach((t,s)=>{t.notify("AnimationStart",e),o.push(im(t,e,{...r,delay:i+l(s)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(o)}(t,e,n+s,o,a,i)}:()=>Promise.resolve(),{when:l}=r;if(!l)return Promise.all([o(),a(i.delay)]);{let[t,e]="beforeChildren"===l?[o,a]:[a,o];return t().then(()=>e())}}function iv(t,e){return t.sortNodePosition(e)}let ig=c.length,iy=[...d].reverse(),ix=d.length;function iw(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function iP(){return{animate:iw(!0),whileInView:iw(),whileHover:iw(),whileTap:iw(),whileDrag:iw(),whileFocus:iw(),exit:iw()}}class iT{constructor(t){this.isMounted=!1,this.node=t}update(){}}class ib extends iT{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let s;if(t.notify("AnimationStart",e),Array.isArray(e))s=Promise.all(e.map(e=>im(t,e,i)));else if("string"==typeof e)s=im(t,e,i);else{let n="function"==typeof e?h(t,e,i.custom):e;s=Promise.all(ip(t,n,i))}return s.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=iP(),s=!0,l=e=>(i,s)=>{var n;let r=h(t,s,"exit"===e?null===(n=t.presenceContext)||void 0===n?void 0:n.custom:void 0);if(r){let{transition:t,transitionEnd:e,...s}=r;i={...i,...s,...e}}return i};function u(u){let{props:h}=t,d=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<ig;t++){let s=c[t],n=e.props[s];(a(n)||!1===n)&&(i[s]=n)}return i}(t.parent)||{},p=[],m=new Set,f={},v=1/0;for(let e=0;e<ix;e++){var g,y;let c=iy[e],x=i[c],w=void 0!==h[c]?h[c]:d[c],P=a(w),T=c===u?x.isActive:null;!1===T&&(v=e);let b=w===d[c]&&w!==h[c]&&P;if(b&&s&&t.manuallyAnimateOnMount&&(b=!1),x.protectedKeys={...f},!x.isActive&&null===T||!w&&!x.prevProp||n(w)||"boolean"==typeof w)continue;let A=(g=x.prevProp,"string"==typeof(y=w)?y!==g:!!Array.isArray(y)&&!o(y,g)),S=A||c===u&&x.isActive&&!b&&P||e>v&&P,V=!1,E=Array.isArray(w)?w:[w],M=E.reduce(l(c),{});!1===T&&(M={});let{prevResolvedValues:C={}}=x,D={...C,...M},k=e=>{S=!0,m.has(e)&&(V=!0,m.delete(e)),x.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in D){let e=M[t],i=C[t];if(f.hasOwnProperty(t))continue;let s=!1;(r(e)&&r(i)?o(e,i):e===i)?void 0!==e&&m.has(t)?k(t):x.protectedKeys[t]=!0:null!=e?k(t):m.add(t)}x.prevProp=w,x.prevResolvedValues=M,x.isActive&&(f={...f,...M}),s&&t.blockInitialAnimation&&(S=!1);let R=!(b&&A)||V;S&&R&&p.push(...E.map(t=>({animation:t,options:{type:c}})))}if(m.size){let e={};m.forEach(i=>{let s=t.getBaseTarget(i),n=t.getValue(i);n&&(n.liveStyle=!0),e[i]=null!=s?s:null}),p.push({animation:e})}let x=!!p.length;return s&&(!1===h.initial||h.initial===h.animate)&&!t.manuallyAnimateOnMount&&(x=!1),s=!1,x?e(p):Promise.resolve()}return{animateChanges:u,setActive:function(e,s){var n;if(i[e].isActive===s)return Promise.resolve();null===(n=t.variantChildren)||void 0===n||n.forEach(t=>{var i;return null===(i=t.animationState)||void 0===i?void 0:i.setActive(e,s)}),i[e].isActive=s;let r=u(e);for(let t in i)i[t].protectedKeys={};return r},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=iP(),s=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();n(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),null===(t=this.unmountControls)||void 0===t||t.call(this)}}let iA=0;class iS extends iT{constructor(){super(...arguments),this.id=iA++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let s=this.node.animationState.setActive("exit",!t);e&&!t&&s.then(()=>e(this.id))}mount(){let{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}function iV(t,e,i,s={passive:!0}){return t.addEventListener(e,i,s),()=>t.removeEventListener(e,i)}function iE(t){return{point:{x:t.pageX,y:t.pageY}}}let iM=t=>e=>R(e)&&t(e,iE(e));function iC(t,e,i,s){return iV(t,e,iM(i),s)}let iD=(t,e)=>Math.abs(t-e);class ik{constructor(t,e,{transformPagePoint:i,contextWindow:s,dragSnapToOrigin:n=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=ij(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(iD(t.x,e.x)**2+iD(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:s}=t,{timestamp:n}=Q;this.history.push({...s,timestamp:n});let{onStart:r,onMove:o}=this.handlers;e||(r&&r(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=iR(e,this.transformPagePoint),_.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:s,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let r=ij("pointercancel"===t.type?this.lastMoveEventInfo:iR(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,r),s&&s(t,r)},!R(t))return;this.dragSnapToOrigin=n,this.handlers=e,this.transformPagePoint=i,this.contextWindow=s||window;let r=iR(iE(t),this.transformPagePoint),{point:o}=r,{timestamp:a}=Q;this.history=[{...o,timestamp:a}];let{onSessionStart:l}=e;l&&l(t,ij(r,this.history)),this.removeListeners=eU(iC(this.contextWindow,"pointermove",this.handlePointerMove),iC(this.contextWindow,"pointerup",this.handlePointerUp),iC(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),Z(this.updatePoint)}}function iR(t,e){return e?{point:e(t.point)}:t}function iL(t,e){return{x:t.x-e.x,y:t.y-e.y}}function ij({point:t},e){return{point:t,delta:iL(t,iF(e)),offset:iL(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,s=null,n=iF(t);for(;i>=0&&(s=t[i],!(n.timestamp-s.timestamp>U(.1)));)i--;if(!s)return{x:0,y:0};let r=N(n.timestamp-s.timestamp);if(0===r)return{x:0,y:0};let o={x:(n.x-s.x)/r,y:(n.y-s.y)/r};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,.1)}}function iF(t){return t[t.length-1]}function iB(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}function iO(t){return t.max-t.min}function iI(t,e,i,s=.5){t.origin=s,t.originPoint=eD(e.min,e.max,t.origin),t.scale=iO(i)/iO(e),t.translate=eD(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function iU(t,e,i,s){iI(t.x,e.x,i.x,s?s.originX:void 0),iI(t.y,e.y,i.y,s?s.originY:void 0)}function iN(t,e,i){t.min=i.min+e.min,t.max=t.min+iO(e)}function i$(t,e,i){t.min=e.min-i.min,t.max=t.min+iO(e)}function iW(t,e,i){i$(t.x,e.x,i.x),i$(t.y,e.y,i.y)}function iz(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function iH(t,e){let i=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,s]=[s,i]),{min:i,max:s}}function iY(t,e,i){return{min:iX(t,e),max:iX(t,i)}}function iX(t,e){return"number"==typeof t?t:t[e]||0}let iK=()=>({translate:0,scale:1,origin:0,originPoint:0}),iq=()=>({x:iK(),y:iK()}),iG=()=>({min:0,max:0}),i_=()=>({x:iG(),y:iG()});function iZ(t){return[t("x"),t("y")]}function iQ({top:t,left:e,right:i,bottom:s}){return{x:{min:e,max:i},y:{min:t,max:s}}}function iJ(t){return void 0===t||1===t}function i0({scale:t,scaleX:e,scaleY:i}){return!iJ(t)||!iJ(e)||!iJ(i)}function i1(t){return i0(t)||i5(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function i5(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function i2(t,e,i,s,n){return void 0!==n&&(t=s+n*(t-s)),s+i*(t-s)+e}function i3(t,e=0,i=1,s,n){t.min=i2(t.min,e,i,s,n),t.max=i2(t.max,e,i,s,n)}function i9(t,{x:e,y:i}){i3(t.x,e.translate,e.scale,e.originPoint),i3(t.y,i.translate,i.scale,i.originPoint)}function i4(t,e){t.min=t.min+e,t.max=t.max+e}function i6(t,e,i,s,n=.5){let r=eD(t.min,t.max,n);i3(t,e,i,r,s)}function i7(t,e){i6(t.x,e.x,e.scaleX,e.scale,e.originX),i6(t.y,e.y,e.scaleY,e.scale,e.originY)}function i8(t,e){return iQ(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:s.y,right:s.x}}(t.getBoundingClientRect(),e))}let st=({current:t})=>t?t.ownerDocument.defaultView:null,se=new WeakMap;class si{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=i_(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:s}=this.getProps();this.panSession=new ik(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(iE(t).point)},onStart:(t,e)=>{var i;let{drag:s,dragPropagation:n,onDragStart:r}=this.getProps();if(s&&!n&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===(i=s)||"y"===i?M[i]?null:(M[i]=!0,()=>{M[i]=!1}):M.x||M.y?null:(M.x=M.y=!0,()=>{M.x=M.y=!1}),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),iZ(t=>{let e=this.getAxisMotionValue(t).get()||0;if(t$.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let s=i.layout.layoutBox[t];s&&(e=iO(s)*(parseFloat(e)/100))}}this.originPoint[t]=e}),r&&_.postRender(()=>r(t,e)),th(this.visualElement,"transform");let{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:s,onDirectionLock:n,onDrag:r}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:o}=e;if(s&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),r&&r(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>iZ(t=>{var e;return"paused"===this.getAnimationState(t)&&(null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:s,contextWindow:st(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:s}=e;this.startAnimation(s);let{onDragEnd:n}=this.getProps();n&&_.postRender(()=>n(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:s}=this.getProps();if(!i||!ss(t,s,this.currentDirection))return;let n=this.getAxisMotionValue(t),r=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(r=function(t,{min:e,max:i},s){return void 0!==e&&t<e?t=s?eD(e,t,s.min):Math.max(t,e):void 0!==i&&t>i&&(t=s?eD(i,t,s.max):Math.min(t,i)),t}(r,this.constraints[t],this.elastic[t])),n.set(r)}resolveConstraints(){var t;let{dragConstraints:e,dragElastic:i}=this.getProps(),s=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(t=this.visualElement.projection)||void 0===t?void 0:t.layout,n=this.constraints;e&&iB(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&s?this.constraints=function(t,{top:e,left:i,bottom:s,right:n}){return{x:iz(t.x,i,n),y:iz(t.y,e,s)}}(s.layoutBox,e):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:iY(t,"left","right"),y:iY(t,"top","bottom")}}(i),n!==this.constraints&&s&&this.constraints&&!this.hasMutatedConstraints&&iZ(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(s.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!iB(e))return!1;let s=e.current;$(null!==s,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let r=function(t,e,i){let s=i8(t,i),{scroll:n}=e;return n&&(i4(s.x,n.offset.x),i4(s.y,n.offset.y)),s}(s,n.root,this.visualElement.getTransformPagePoint()),o={x:iH((t=n.layout.layoutBox).x,r.x),y:iH(t.y,r.y)};if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=iQ(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:s,dragTransition:n,dragSnapToOrigin:r,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(iZ(o=>{if(!ss(o,e,this.currentDirection))return;let l=a&&a[o]||{};r&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[o]:0,bounceStiffness:s?200:1e6,bounceDamping:s?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return th(this.visualElement,t),i.start(ic(t,i,0,e,this.visualElement,!1))}stopAnimation(){iZ(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){iZ(t=>{var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.pause()})}getAnimationState(t){var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){iZ(e=>{let{drag:i}=this.getProps();if(!ss(e,i,this.currentDirection))return;let{projection:s}=this.visualElement,n=this.getAxisMotionValue(e);if(s&&s.layout){let{min:i,max:r}=s.layout.layoutBox[e];n.set(t[e]-eD(i,r,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!iB(e)||!i||!this.constraints)return;this.stopAnimation();let s={x:0,y:0};iZ(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();s[t]=function(t,e){let i=.5,s=iO(t),n=iO(e);return n>s?i=A(e.min,e.max-s,t.min):s>n&&(i=A(t.min,t.max-n,e.min)),tV(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),iZ(e=>{if(!ss(e,t,null))return;let i=this.getAxisMotionValue(e),{min:n,max:r}=this.constraints[e];i.set(eD(n,r,s[e]))})}addListeners(){if(!this.visualElement.current)return;se.set(this.visualElement,this);let t=iC(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();iB(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,s=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),_.read(e);let n=iV(window,"resize",()=>this.scalePositionWithinConstraints()),r=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(iZ(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{n(),t(),s(),r&&r()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:s=!1,dragConstraints:n=!1,dragElastic:r=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:s,dragConstraints:n,dragElastic:r,dragMomentum:o}}}function ss(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class sn extends iT{constructor(t){super(t),this.removeGroupControls=$,this.removeListeners=$,this.controls=new si(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||$}unmount(){this.removeGroupControls(),this.removeListeners()}}let sr=t=>(e,i)=>{t&&_.postRender(()=>t(e,i))};class so extends iT{constructor(){super(...arguments),this.removePointerDownListener=$}onPointerDown(t){this.session=new ik(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:st(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:s}=this.node.getProps();return{onSessionStart:sr(t),onStart:sr(e),onMove:i,onEnd:(t,e)=>{delete this.session,s&&_.postRender(()=>s(t,e))}}}mount(){this.removePointerDownListener=iC(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var sa,sl,su=i(5155),sh=i(2115),sd=i(5087),sc=i(4710);let sp=(0,sh.createContext)({}),sm={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function sf(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let sv={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!tW.test(t))return t;t=parseFloat(t)}let i=sf(t,e.target.x),s=sf(t,e.target.y);return`${i}% ${s}%`}},sg={},{schedule:sy,cancel:sx}=G(queueMicrotask,!1);class sw extends sh.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:s}=this.props,{projection:n}=t;Object.assign(sg,sT),n&&(e.group&&e.group.add(n),i&&i.register&&s&&i.register(n),n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),sm.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:s,isPresent:n}=this.props,r=i.projection;return r&&(r.isPresent=n,s||t.layoutDependency!==e||void 0===e?r.willUpdate():this.safeToRemove(),t.isPresent===n||(n?r.promote():r.relegate()||_.postRender(()=>{let t=r.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),sy.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:s}=t;s&&(s.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(s),i&&i.deregister&&i.deregister(s))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function sP(t){let[e,i]=(0,sd.xQ)(),s=(0,sh.useContext)(sc.L);return(0,su.jsx)(sw,{...t,layoutGroup:s,switchLayoutGroup:(0,sh.useContext)(sp),isPresent:e,safeToRemove:i})}let sT={borderRadius:{...sv,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:sv,borderTopRightRadius:sv,borderBottomLeftRadius:sv,borderBottomRightRadius:sv,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let s=t5.parse(t);if(s.length>5)return t;let n=t5.createTransformer(t),r=+("number"!=typeof s[0]),o=i.x.scale*e.x,a=i.y.scale*e.y;s[0+r]/=o,s[1+r]/=a;let l=eD(o,a,.5);return"number"==typeof s[2+r]&&(s[2+r]/=l),"number"==typeof s[3+r]&&(s[3+r]/=l),n(s)}}},sb=(t,e)=>t.depth-e.depth;class sA{constructor(){this.children=[],this.isDirty=!1}add(t){ti(this.children,t),this.isDirty=!0}remove(t){ts(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(sb),this.isDirty=!1,this.children.forEach(t)}}function sS(t){let e=tu(t)?t.get():t;return Y(e)?e.toValue():e}let sV=["TopLeft","TopRight","BottomLeft","BottomRight"],sE=sV.length,sM=t=>"string"==typeof t?parseFloat(t):t,sC=t=>"number"==typeof t||tW.test(t);function sD(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let sk=sL(0,.5,tb),sR=sL(.5,.95,$);function sL(t,e,i){return s=>s<t?0:s>e?1:i(A(t,e,s))}function sj(t,e){t.min=e.min,t.max=e.max}function sF(t,e){sj(t.x,e.x),sj(t.y,e.y)}function sB(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function sO(t,e,i,s,n){return t-=e,t=s+1/i*(t-s),void 0!==n&&(t=s+1/n*(t-s)),t}function sI(t,e,[i,s,n],r,o){!function(t,e=0,i=1,s=.5,n,r=t,o=t){if(t$.test(e)&&(e=parseFloat(e),e=eD(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=eD(r.min,r.max,s);t===r&&(a-=e),t.min=sO(t.min,e,i,a,n),t.max=sO(t.max,e,i,a,n)}(t,e[i],e[s],e[n],e.scale,r,o)}let sU=["x","scaleX","originX"],sN=["y","scaleY","originY"];function s$(t,e,i,s){sI(t.x,e,sU,i?i.x:void 0,s?s.x:void 0),sI(t.y,e,sN,i?i.y:void 0,s?s.y:void 0)}function sW(t){return 0===t.translate&&1===t.scale}function sz(t){return sW(t.x)&&sW(t.y)}function sH(t,e){return t.min===e.min&&t.max===e.max}function sY(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function sX(t,e){return sY(t.x,e.x)&&sY(t.y,e.y)}function sK(t){return iO(t.x)/iO(t.y)}function sq(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class sG{constructor(){this.members=[]}add(t){ti(this.members,t),t.scheduleRender()}remove(t){if(ts(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e;let i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:s}=t.options;!1===s&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let s_={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},sZ="undefined"!=typeof window&&void 0!==window.MotionDebug,sQ=["","X","Y","Z"],sJ={visibility:"hidden"},s0=0;function s1(t,e,i,s){let{latestValues:n}=e;n[t]&&(i[t]=n[t],e.setStaticValue(t,0),s&&(s[t]=0))}function s5({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:s,resetTransform:n}){return class{constructor(t={},i=null==e?void 0:e()){this.id=s0++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,sZ&&(s_.totalNodes=s_.resolvedTargetDeltas=s_.recalculatedProjection=0),this.nodes.forEach(s9),this.nodes.forEach(ni),this.nodes.forEach(ns),this.nodes.forEach(s4),sZ&&window.MotionDebug.record(s_)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new sA)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new tn),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e,i=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=e instanceof SVGElement&&"svg"!==e.tagName,this.instance=e;let{layoutId:s,layout:n,visualElement:r}=this.options;if(r&&!r.current&&r.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),i&&(n||s)&&(this.isLayoutDirty=!0),t){let i;let s=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=te.now(),s=({timestamp:n})=>{let r=n-i;r>=250&&(Z(s),t(r-e))};return _.read(s,!0),()=>Z(s)}(s,250),sm.hasAnimatedSinceResize&&(sm.hasAnimatedSinceResize=!1,this.nodes.forEach(ne))})}s&&this.root.registerSharedNode(s,this),!1!==this.options.animate&&r&&(s||n)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeTargetChanged:i,layout:s})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let n=this.options.transition||r.getDefaultTransition()||nu,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=r.getProps(),l=!this.targetLayout||!sX(this.targetLayout,s)||i,u=!e&&i;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,u);let e={...g(n,"layout"),onPlay:o,onComplete:a};(r.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||ne(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=s})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,Z(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(nn),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let s=i.props[tc];if(window.MotionHasOptimisedAnimation(s,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(s,"transform",_,!(t||i))}let{parent:n}=e;n&&!n.hasCheckedOptimisedAppear&&t(n)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let s=this.getTransformTemplate();this.prevTransformTemplateValue=s?s(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(s7);return}this.isUpdating||this.nodes.forEach(s8),this.isUpdating=!1,this.nodes.forEach(nt),this.nodes.forEach(s2),this.nodes.forEach(s3),this.clearAllSnapshots();let t=te.now();Q.delta=tV(0,1e3/60,t-Q.timestamp),Q.timestamp=t,Q.isProcessing=!0,J.update.process(Q),J.preRender.process(Q),J.render.process(Q),Q.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,sy.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(s6),this.sharedNodes.forEach(nr)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,_.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){_.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=i_(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e){let e=s(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!n)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!sz(this.projectionDelta),i=this.getTransformTemplate(),s=i?i(this.latestValues,""):void 0,r=s!==this.prevTransformTemplateValue;t&&(e||i1(this.latestValues)||r)&&(n(this.instance,s),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),s=this.removeElementScroll(i);return t&&(s=this.removeTransform(s)),nc((e=s).x),nc(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:s,latestValues:{},source:this.id}}measurePageBox(){var t;let{visualElement:e}=this.options;if(!e)return i_();let i=e.measureViewportBox();if(!((null===(t=this.scroll)||void 0===t?void 0:t.wasRoot)||this.path.some(nm))){let{scroll:t}=this.root;t&&(i4(i.x,t.offset.x),i4(i.y,t.offset.y))}return i}removeElementScroll(t){var e;let i=i_();if(sF(i,t),null===(e=this.scroll)||void 0===e?void 0:e.wasRoot)return i;for(let e=0;e<this.path.length;e++){let s=this.path[e],{scroll:n,options:r}=s;s!==this.root&&n&&r.layoutScroll&&(n.wasRoot&&sF(i,t),i4(i.x,n.offset.x),i4(i.y,n.offset.y))}return i}applyTransform(t,e=!1){let i=i_();sF(i,t);for(let t=0;t<this.path.length;t++){let s=this.path[t];!e&&s.options.layoutScroll&&s.scroll&&s!==s.root&&i7(i,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),i1(s.latestValues)&&i7(i,s.latestValues)}return i1(this.latestValues)&&i7(i,this.latestValues),i}removeTransform(t){let e=i_();sF(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!i1(i.latestValues))continue;i0(i.latestValues)&&i.updateSnapshot();let s=i_();sF(s,i.measurePageBox()),s$(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,s)}return i1(this.latestValues)&&s$(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==Q.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){var e,i,s,n;let r=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=r.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=r.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=r.isSharedProjectionDirty);let o=!!this.resumingFrom||this!==r;if(!(t||o&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty)||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:a,layoutId:l}=this.options;if(this.layout&&(a||l)){if(this.resolvedRelativeTargetAt=Q.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=i_(),this.relativeTargetOrigin=i_(),iW(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),sF(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=i_(),this.targetWithTransforms=i_()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),i=this.target,s=this.relativeTarget,n=this.relativeParent.target,iN(i.x,s.x,n.x),iN(i.y,s.y,n.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):sF(this.target,this.layout.layoutBox),i9(this.target,this.targetDelta)):sF(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=i_(),this.relativeTargetOrigin=i_(),iW(this.relativeTargetOrigin,this.target,t.target),sF(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}sZ&&s_.resolvedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||i0(this.parent.latestValues)||i5(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var t;let e=this.getLead(),i=!!this.resumingFrom||this!==e,s=!0;if((this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty))&&(s=!1),i&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(s=!1),this.resolvedRelativeTargetAt===Q.timestamp&&(s=!1),s)return;let{layout:n,layoutId:r}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||r))return;sF(this.layoutCorrected,this.layout.layoutBox);let o=this.treeScale.x,a=this.treeScale.y;!function(t,e,i,s=!1){let n,r;let o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){r=(n=i[a]).projectionDelta;let{visualElement:o}=n.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(s&&n.options.layoutScroll&&n.scroll&&n!==n.root&&i7(t,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,i9(t,r)),s&&i1(n.latestValues)&&i7(t,n.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,i),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=i_());let{target:l}=e;if(!l){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(sB(this.prevProjectionDelta.x,this.projectionDelta.x),sB(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),iU(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===o&&this.treeScale.y===a&&sq(this.projectionDelta.x,this.prevProjectionDelta.x)&&sq(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),sZ&&s_.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){var e;if(null===(e=this.options.visualElement)||void 0===e||e.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=iq(),this.projectionDelta=iq(),this.projectionDeltaWithTransform=iq()}setAnimationOrigin(t,e=!1){let i;let s=this.snapshot,n=s?s.latestValues:{},r={...this.latestValues},o=iq();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=i_(),l=(s?s.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,d=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(nl));this.animationProgress=0,this.mixTargetDelta=e=>{let s=e/1e3;if(no(o.x,t.x,s),no(o.y,t.y,s),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,c,p,m,f,v;if(iW(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=a,v=s,na(p.x,m.x,f.x,v),na(p.y,m.y,f.y,v),i&&(u=this.relativeTarget,c=i,sH(u.x,c.x)&&sH(u.y,c.y)))this.isProjectionDirty=!1;i||(i=i_()),sF(i,this.relativeTarget)}l&&(this.animationValues=r,function(t,e,i,s,n,r){n?(t.opacity=eD(0,void 0!==i.opacity?i.opacity:1,sk(s)),t.opacityExit=eD(void 0!==e.opacity?e.opacity:1,0,sR(s))):r&&(t.opacity=eD(void 0!==e.opacity?e.opacity:1,void 0!==i.opacity?i.opacity:1,s));for(let n=0;n<sE;n++){let r=`border${sV[n]}Radius`,o=sD(e,r),a=sD(i,r);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||sC(o)===sC(a)?(t[r]=Math.max(eD(sM(o),sM(a),s),0),(t$.test(a)||t$.test(o))&&(t[r]+="%")):t[r]=a)}(e.rotate||i.rotate)&&(t.rotate=eD(e.rotate||0,i.rotate||0,s))}(r,n,this.latestValues,s,d,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=s},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(Z(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=_.update(()=>{sm.hasAnimatedSinceResize=!0,this.currentAnimation=function(t,e,i){let s=tu(0)?0:tl(t);return s.start(ic("",s,1e3,i)),s.animation}(0,0,{...t,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onComplete:()=>{t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:s,latestValues:n}=t;if(e&&i&&s){if(this!==t&&this.layout&&s&&np(this.options.animationType,this.layout.layoutBox,s.layoutBox)){i=this.target||i_();let e=iO(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let s=iO(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+s}sF(e,i),i7(e,n),iU(this.projectionDeltaWithTransform,this.layoutCorrected,e,n)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new sG),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){var t;let{layoutId:e}=this.options;return e&&(null===(t=this.getStack())||void 0===t?void 0:t.lead)||this}getPrevLead(){var t;let{layoutId:e}=this.options;return e?null===(t=this.getStack())||void 0===t?void 0:t.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let s=this.getStack();s&&s.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let s={};i.z&&s1("z",t,s,this.animationValues);for(let e=0;e<sQ.length;e++)s1(`rotate${sQ[e]}`,t,s,this.animationValues),s1(`skew${sQ[e]}`,t,s,this.animationValues);for(let e in t.render(),s)t.setStaticValue(e,s[e]),this.animationValues&&(this.animationValues[e]=s[e]);t.scheduleRender()}getProjectionStyles(t){var e,i;if(!this.instance||this.isSVG)return;if(!this.isVisible)return sJ;let s={visibility:""},n=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,s.opacity="",s.pointerEvents=sS(null==t?void 0:t.pointerEvents)||"",s.transform=n?n(this.latestValues,""):"none",s;let r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=sS(null==t?void 0:t.pointerEvents)||""),this.hasProjected&&!i1(this.latestValues)&&(e.transform=n?n({},""):"none",this.hasProjected=!1),e}let o=r.animationValues||r.latestValues;this.applyTransformsToTarget(),s.transform=function(t,e,i){let s="",n=t.x.translate/e.x,r=t.y.translate/e.y,o=(null==i?void 0:i.z)||0;if((n||r||o)&&(s=`translate3d(${n}px, ${r}px, ${o}px) `),(1!==e.x||1!==e.y)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:n,rotateY:r,skewX:o,skewY:a}=i;t&&(s=`perspective(${t}px) ${s}`),e&&(s+=`rotate(${e}deg) `),n&&(s+=`rotateX(${n}deg) `),r&&(s+=`rotateY(${r}deg) `),o&&(s+=`skewX(${o}deg) `),a&&(s+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(s+=`scale(${a}, ${l})`),s||"none"}(this.projectionDeltaWithTransform,this.treeScale,o),n&&(s.transform=n(o,s.transform));let{x:a,y:l}=this.projectionDelta;for(let t in s.transformOrigin=`${100*a.origin}% ${100*l.origin}% 0`,r.animationValues?s.opacity=r===this?null!==(i=null!==(e=o.opacity)&&void 0!==e?e:this.latestValues.opacity)&&void 0!==i?i:1:this.preserveOpacity?this.latestValues.opacity:o.opacityExit:s.opacity=r===this?void 0!==o.opacity?o.opacity:"":void 0!==o.opacityExit?o.opacityExit:0,sg){if(void 0===o[t])continue;let{correct:e,applyTo:i}=sg[t],n="none"===s.transform?o[t]:e(o[t],r);if(i){let t=i.length;for(let e=0;e<t;e++)s[i[e]]=n}else s[t]=n}return this.options.layoutId&&(s.pointerEvents=r===this?sS(null==t?void 0:t.pointerEvents)||"":"none"),s}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>{var e;return null===(e=t.currentAnimation)||void 0===e?void 0:e.stop()}),this.root.nodes.forEach(s7),this.root.sharedNodes.clear()}}}function s2(t){t.updateLayout()}function s3(t){var e;let i=(null===(e=t.resumeFrom)||void 0===e?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&i&&t.hasListeners("didUpdate")){let{layoutBox:e,measuredBox:s}=t.layout,{animationType:n}=t.options,r=i.source!==t.layout.source;"size"===n?iZ(t=>{let s=r?i.measuredBox[t]:i.layoutBox[t],n=iO(s);s.min=e[t].min,s.max=s.min+n}):np(n,i.layoutBox,e)&&iZ(s=>{let n=r?i.measuredBox[s]:i.layoutBox[s],o=iO(e[s]);n.max=n.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[s].max=t.relativeTarget[s].min+o)});let o=iq();iU(o,e,i.layoutBox);let a=iq();r?iU(a,t.applyTransform(s,!0),i.measuredBox):iU(a,e,i.layoutBox);let l=!sz(o),u=!1;if(!t.resumeFrom){let s=t.getClosestProjectingParent();if(s&&!s.resumeFrom){let{snapshot:n,layout:r}=s;if(n&&r){let o=i_();iW(o,i.layoutBox,n.layoutBox);let a=i_();iW(a,e,r.layoutBox),sX(o,a)||(u=!0),s.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=s)}}}t.notifyListeners("didUpdate",{layout:e,snapshot:i,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function s9(t){sZ&&s_.totalNodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function s4(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function s6(t){t.clearSnapshot()}function s7(t){t.clearMeasurements()}function s8(t){t.isLayoutDirty=!1}function nt(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function ne(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function ni(t){t.resolveTargetDelta()}function ns(t){t.calcProjection()}function nn(t){t.resetSkewAndRotation()}function nr(t){t.removeLeadSnapshot()}function no(t,e,i){t.translate=eD(e.translate,0,i),t.scale=eD(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function na(t,e,i,s){t.min=eD(e.min,i.min,s),t.max=eD(e.max,i.max,s)}function nl(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let nu={duration:.45,ease:[.4,0,.1,1]},nh=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),nd=nh("applewebkit/")&&!nh("chrome/")?Math.round:$;function nc(t){t.min=nd(t.min),t.max=nd(t.max)}function np(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(sK(e)-sK(i)))}function nm(t){var e;return t!==t.root&&(null===(e=t.scroll)||void 0===e?void 0:e.wasRoot)}let nf=s5({attachResizeListener:(t,e)=>iV(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),nv={current:void 0},ng=s5({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!nv.current){let t=new nf({});t.mount(window),t.setOptions({layoutScroll:!0}),nv.current=t}return nv.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function ny(t,e,i){let{props:s}=t;t.animationState&&s.whileHover&&t.animationState.setActive("whileHover","Start"===i);let n=s["onHover"+i];n&&_.postRender(()=>n(e,iE(e)))}class nx extends iT{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[s,n,r]=C(t,i),o=D(t=>{let{target:i}=t,s=e(t);if("function"!=typeof s||!i)return;let r=D(t=>{s(t),i.removeEventListener("pointerleave",r)});i.addEventListener("pointerleave",r,n)});return s.forEach(t=>{t.addEventListener("pointerenter",o,n)}),r}(t,t=>(ny(this.node,t,"Start"),t=>ny(this.node,t,"End"))))}unmount(){}}class nw extends iT{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=eU(iV(this.node.current,"focus",()=>this.onFocus()),iV(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function nP(t,e,i){let{props:s}=t;t.animationState&&s.whileTap&&t.animationState.setActive("whileTap","Start"===i);let n=s["onTap"+("End"===i?"":i)];n&&_.postRender(()=>n(e,iE(e)))}class nT extends iT{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[s,n,r]=C(t,i),o=t=>{let s=t.currentTarget;if(!I(t)||j.has(s))return;j.add(s);let r=e(t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),I(t)&&j.has(s)&&(j.delete(s),"function"==typeof r&&r(t,{success:e}))},a=t=>{o(t,i.useGlobalTarget||k(s,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,n),window.addEventListener("pointercancel",l,n)};return s.forEach(t=>{!L.has(t.tagName)&&-1===t.tabIndex&&null===t.getAttribute("tabindex")&&(t.tabIndex=0),(i.useGlobalTarget?window:t).addEventListener("pointerdown",o,n),t.addEventListener("focus",t=>O(t,n),n)}),r}(t,t=>(nP(this.node,t,"Start"),(t,{success:e})=>nP(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let nb=new WeakMap,nA=new WeakMap,nS=t=>{let e=nb.get(t.target);e&&e(t)},nV=t=>{t.forEach(nS)},nE={some:0,all:1};class nM extends iT{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:s="some",once:n}=t,r={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof s?s:nE[s]};return function(t,e,i){let s=function({root:t,...e}){let i=t||document;nA.has(i)||nA.set(i,{});let s=nA.get(i),n=JSON.stringify(e);return s[n]||(s[n]=new IntersectionObserver(nV,{root:t,...e})),s[n]}(e);return nb.set(t,i),s.observe(t),()=>{nb.delete(t),s.unobserve(t)}}(this.node.current,r,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,n&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:s}=this.node.getProps(),r=e?i:s;r&&r(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let nC=(0,sh.createContext)({strict:!1});var nD=i(7249);let nk=(0,sh.createContext)({});function nR(t){return n(t.animate)||c.some(e=>a(t[e]))}function nL(t){return!!(nR(t)||t.variants)}function nj(t){return Array.isArray(t)?t.join(" "):t}var nF=i(5687);let nB={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},nO={};for(let t in nB)nO[t]={isEnabled:e=>nB[t].some(t=>!!e[t])};let nI=Symbol.for("motionComponentSymbol");var nU=i(9656),nN=i(5403);let n$=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function nW(t){if("string"!=typeof t||t.includes("-"));else if(n$.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var nz=i(9234);let nH=t=>(e,i)=>{let s=(0,sh.useContext)(nk),r=(0,sh.useContext)(nU.t),o=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e,onUpdate:i},s,r,o){let a={latestValues:function(t,e,i,s){let r={},o=s(t,{});for(let t in o)r[t]=sS(o[t]);let{initial:a,animate:l}=t,h=nR(t),d=nL(t);e&&d&&!h&&!1!==t.inherit&&(void 0===a&&(a=e.initial),void 0===l&&(l=e.animate));let c=!!i&&!1===i.initial,p=(c=c||!1===a)?l:a;if(p&&"boolean"!=typeof p&&!n(p)){let e=Array.isArray(p)?p:[p];for(let i=0;i<e.length;i++){let s=u(t,e[i]);if(s){let{transitionEnd:t,transition:e,...i}=s;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=c?e.length-1:0;e=e[t]}null!==e&&(r[t]=e)}for(let e in t)r[e]=t[e]}}}return r}(s,r,o,t),renderState:e()};return i&&(a.onMount=t=>i({props:s,current:t,...a}),a.onUpdate=t=>i(t)),a})(t,e,s,r);return i?o():(0,nz.M)(o)},nY=(t,e)=>e&&"number"==typeof t?e.transform(t):t,nX={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},nK=W.length;function nq(t,e,i){let{style:s,vars:n,transformOrigin:r}=t,o=!1,a=!1;for(let t in e){let i=e[t];if(z.has(t)){o=!0;continue}if(eg(t)){n[t]=i;continue}{let e=nY(i,t7[t]);t.startsWith("origin")?(a=!0,r[t]=e):s[t]=e}}if(!e.transform&&(o||i?s.transform=function(t,e,i){let s="",n=!0;for(let r=0;r<nK;r++){let o=W[r],a=t[o];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===+!!o.startsWith("scale"):0===parseFloat(a))||i){let t=nY(a,t7[o]);if(!l){n=!1;let e=nX[o]||o;s+=`${e}(${t}) `}i&&(e[o]=t)}}return s=s.trim(),i?s=i(e,n?"":s):n&&(s="none"),s}(e,t.transform,i):s.transform&&(s.transform="none")),a){let{originX:t="50%",originY:e="50%",originZ:i=0}=r;s.transformOrigin=`${t} ${e} ${i}`}}let nG={offset:"stroke-dashoffset",array:"stroke-dasharray"},n_={offset:"strokeDashoffset",array:"strokeDasharray"};function nZ(t,e,i){return"string"==typeof t?t:tW.transform(e+i*t)}function nQ(t,{attrX:e,attrY:i,attrScale:s,originX:n,originY:r,pathLength:o,pathSpacing:a=1,pathOffset:l=0,...u},h,d){if(nq(t,u,d),h){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:c,style:p,dimensions:m}=t;c.transform&&(m&&(p.transform=c.transform),delete c.transform),m&&(void 0!==n||void 0!==r||p.transform)&&(p.transformOrigin=function(t,e,i){let s=nZ(e,t.x,t.width),n=nZ(i,t.y,t.height);return`${s} ${n}`}(m,void 0!==n?n:.5,void 0!==r?r:.5)),void 0!==e&&(c.x=e),void 0!==i&&(c.y=i),void 0!==s&&(c.scale=s),void 0!==o&&function(t,e,i=1,s=0,n=!0){t.pathLength=1;let r=n?nG:n_;t[r.offset]=tW.transform(-s);let o=tW.transform(e),a=tW.transform(i);t[r.array]=`${o} ${a}`}(c,o,a,l,!1)}let nJ=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),n0=()=>({...nJ(),attrs:{}}),n1=t=>"string"==typeof t&&"svg"===t.toLowerCase();function n5(t,{style:e,vars:i},s,n){for(let r in Object.assign(t.style,e,n&&n.getProjectionStyles(s)),i)t.style.setProperty(r,i[r])}let n2=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function n3(t,e,i,s){for(let i in n5(t,e,void 0,s),e.attrs)t.setAttribute(n2.has(i)?i:td(i),e.attrs[i])}function n9(t,{layout:e,layoutId:i}){return z.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!sg[t]||"opacity"===t)}function n4(t,e,i){var s;let{style:n}=t,r={};for(let o in n)(tu(n[o])||e.style&&tu(e.style[o])||n9(o,t)||(null===(s=null==i?void 0:i.getValue(o))||void 0===s?void 0:s.liveStyle)!==void 0)&&(r[o]=n[o]);return r}function n6(t,e,i){let s=n4(t,e,i);for(let i in t)(tu(t[i])||tu(e[i]))&&(s[-1!==W.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return s}let n7=["x","y","width","height","cx","cy","r"],n8={useVisualState:nH({scrapeMotionValuesFromProps:n6,createRenderState:n0,onUpdate:({props:t,prevProps:e,current:i,renderState:s,latestValues:n})=>{if(!i)return;let r=!!t.drag;if(!r){for(let t in n)if(z.has(t)){r=!0;break}}if(!r)return;let o=!e;if(e)for(let i=0;i<n7.length;i++){let s=n7[i];t[s]!==e[s]&&(o=!0)}o&&_.read(()=>{!function(t,e){try{e.dimensions="function"==typeof t.getBBox?t.getBBox():t.getBoundingClientRect()}catch(t){e.dimensions={x:0,y:0,width:0,height:0}}}(i,s),_.render(()=>{nQ(s,n,n1(i.tagName),t.transformTemplate),n3(i,s)})})}})},rt={useVisualState:nH({scrapeMotionValuesFromProps:n4,createRenderState:nJ})};function re(t,e,i){for(let s in e)tu(e[s])||n9(s,i)||(t[s]=e[s])}let ri=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function rs(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||ri.has(t)}let rn=t=>!rs(t);try{!function(t){t&&(rn=e=>e.startsWith("on")?!rs(e):t(e))}(require("@emotion/is-prop-valid").default)}catch(t){}let rr={current:null},ro={current:!1},ra=[...eb,tK,t5],rl=t=>ra.find(eT(t)),ru=new WeakMap,rh=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class rd{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:s,blockInitialAnimation:n,visualState:r},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=em,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=te.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,_.render(this.render,!1,!0))};let{latestValues:a,renderState:l,onUpdate:u}=r;this.onUpdate=u,this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=s,this.options=o,this.blockInitialAnimation=!!n,this.isControllingVariants=nR(e),this.isVariantNode=nL(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:h,...d}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in d){let e=d[t];void 0!==a[t]&&tu(e)&&e.set(a[t],!1)}}mount(t){this.current=t,ru.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),ro.current||function(){if(ro.current=!0,nF.B){if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>rr.current=t.matches;t.addListener(e),e()}else rr.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||rr.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in ru.delete(this.current),this.projection&&this.projection.unmount(),Z(this.notifyUpdate),Z(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let s=z.has(t),n=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&_.preRender(this.notifyUpdate),s&&this.projection&&(this.projection.isTransformDirty=!0)}),r=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{n(),r(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in nO){let e=nO[t];if(!e)continue;let{isEnabled:i,Feature:s}=e;if(!this.features[t]&&s&&i(this.props)&&(this.features[t]=new s(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):i_()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<rh.length;e++){let i=rh[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let s=t["on"+i];s&&(this.propEventSubscriptions[i]=this.on(i,s))}this.prevMotionValues=function(t,e,i){for(let s in e){let n=e[s],r=i[s];if(tu(n))t.addValue(s,n);else if(tu(r))t.addValue(s,tl(n,{owner:t}));else if(r!==n){if(t.hasValue(s)){let e=t.getValue(s);!0===e.liveStyle?e.jump(n):e.hasAnimated||e.set(n)}else{let e=t.getStaticValue(s);t.addValue(s,tl(void 0!==e?e:n,{owner:t}))}}}for(let s in i)void 0===e[s]&&t.removeValue(s);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=tl(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){var i;let s=void 0===this.latestValues[t]&&this.current?null!==(i=this.getBaseTargetFromProps(this.props,t))&&void 0!==i?i:this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=s&&("string"==typeof s&&(ef(s)||tS(s))?s=parseFloat(s):!rl(s)&&t5.test(e)&&(s=ee(t,e)),this.setBaseTarget(t,tu(s)?s.get():s)),tu(s)?s.get():s}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){var e;let i;let{initial:s}=this.props;if("string"==typeof s||"object"==typeof s){let n=u(this.props,s,null===(e=this.presenceContext)||void 0===e?void 0:e.custom);n&&(i=n[t])}if(s&&void 0!==i)return i;let n=this.getBaseTargetFromProps(this.props,t);return void 0===n||tu(n)?void 0!==this.initialValues[t]&&void 0===i?void 0:this.baseTarget[t]:n}on(t,e){return this.events[t]||(this.events[t]=new tn),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class rc extends rd{constructor(){super(...arguments),this.KeyframeResolver=eS}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;tu(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}class rp extends rc{constructor(){super(...arguments),this.type="html",this.renderInstance=n5}readValueFromInstance(t,e){if(z.has(e)){let t=et(e);return t&&t.default||0}{let i=window.getComputedStyle(t),s=(eg(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof s?s.trim():s}}measureInstanceViewportBox(t,{transformPagePoint:e}){return i8(t,e)}build(t,e,i){nq(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return n4(t,e,i)}}class rm extends rc{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=i_}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(z.has(e)){let t=et(e);return t&&t.default||0}return e=n2.has(e)?e:td(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return n6(t,e,i)}build(t,e,i){nQ(t,e,this.isSVGTag,i.transformTemplate)}renderInstance(t,e,i,s){n3(t,e,i,s)}mount(t){this.isSVGTag=n1(t.tagName),super.mount(t)}}let rf=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,s)=>"create"===s?t:(e.has(s)||e.set(s,t(s)),e.get(s))})}((sa={animation:{Feature:ib},exit:{Feature:iS},inView:{Feature:nM},tap:{Feature:nT},focus:{Feature:nw},hover:{Feature:nx},pan:{Feature:so},drag:{Feature:sn,ProjectionNode:ng,MeasureLayout:sP},layout:{ProjectionNode:ng,MeasureLayout:sP}},sl=(t,e)=>nW(t)?new rm(e):new rp(e,{allowProjection:t!==sh.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function(t){var e,i;let{preloadedFeatures:s,createVisualElement:n,useRender:r,useVisualState:o,Component:l}=t;function u(t,e){var i,s,u;let h;let d={...(0,sh.useContext)(nD.Q),...t,layoutId:function(t){let{layoutId:e}=t,i=(0,sh.useContext)(sc.L).id;return i&&void 0!==e?i+"-"+e:e}(t)},{isStatic:c}=d,p=function(t){let{initial:e,animate:i}=function(t,e){if(nR(t)){let{initial:e,animate:i}=t;return{initial:!1===e||a(e)?e:void 0,animate:a(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,sh.useContext)(nk));return(0,sh.useMemo)(()=>({initial:e,animate:i}),[nj(e),nj(i)])}(t),m=o(t,c);if(!c&&nF.B){s=0,u=0,(0,sh.useContext)(nC).strict;let t=function(t){let{drag:e,layout:i}=nO;if(!e&&!i)return{};let s={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?s.MeasureLayout:void 0,ProjectionNode:s.ProjectionNode}}(d);h=t.MeasureLayout,p.visualElement=function(t,e,i,s,n){var r,o;let{visualElement:a}=(0,sh.useContext)(nk),l=(0,sh.useContext)(nC),u=(0,sh.useContext)(nU.t),h=(0,sh.useContext)(nD.Q).reducedMotion,d=(0,sh.useRef)(null);s=s||l.renderer,!d.current&&s&&(d.current=s(t,{visualState:e,parent:a,props:i,presenceContext:u,blockInitialAnimation:!!u&&!1===u.initial,reducedMotionConfig:h}));let c=d.current,p=(0,sh.useContext)(sp);c&&!c.projection&&n&&("html"===c.type||"svg"===c.type)&&function(t,e,i,s){let{layoutId:n,layout:r,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:n,layout:r,alwaysMeasureLayout:!!o||a&&iB(a),visualElement:t,animationType:"string"==typeof r?r:"both",initialPromotionConfig:s,layoutScroll:l,layoutRoot:u})}(d.current,i,n,p);let m=(0,sh.useRef)(!1);(0,sh.useInsertionEffect)(()=>{c&&m.current&&c.update(i,u)});let f=i[tc],v=(0,sh.useRef)(!!f&&!(null===(r=window.MotionHandoffIsComplete)||void 0===r?void 0:r.call(window,f))&&(null===(o=window.MotionHasOptimisedAnimation)||void 0===o?void 0:o.call(window,f)));return(0,nN.E)(()=>{c&&(m.current=!0,window.MotionIsMounted=!0,c.updateFeatures(),sy.render(c.render),v.current&&c.animationState&&c.animationState.animateChanges())}),(0,sh.useEffect)(()=>{c&&(!v.current&&c.animationState&&c.animationState.animateChanges(),v.current&&(queueMicrotask(()=>{var t;null===(t=window.MotionHandoffMarkAsComplete)||void 0===t||t.call(window,f)}),v.current=!1))}),c}(l,m,d,n,t.ProjectionNode)}return(0,su.jsxs)(nk.Provider,{value:p,children:[h&&p.visualElement?(0,su.jsx)(h,{visualElement:p.visualElement,...d}):null,r(l,t,(i=p.visualElement,(0,sh.useCallback)(t=>{t&&m.onMount&&m.onMount(t),i&&(t?i.mount(t):i.unmount()),e&&("function"==typeof e?e(t):iB(e)&&(e.current=t))},[i])),m,c,p.visualElement)]})}s&&function(t){for(let e in t)nO[e]={...nO[e],...t[e]}}(s),u.displayName="motion.".concat("string"==typeof l?l:"create(".concat(null!==(i=null!==(e=l.displayName)&&void 0!==e?e:l.name)&&void 0!==i?i:"",")"));let h=(0,sh.forwardRef)(u);return h[nI]=l,h}({...nW(t)?n8:rt,preloadedFeatures:sa,useRender:function(t=!1){return(e,i,s,{latestValues:n},r)=>{let o=(nW(e)?function(t,e,i,s){let n=(0,sh.useMemo)(()=>{let i=n0();return nQ(i,e,n1(s),t.transformTemplate),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};re(e,t.style,t),n.style={...e,...n.style}}return n}:function(t,e){let i={},s=function(t,e){let i=t.style||{},s={};return re(s,i,t),Object.assign(s,function({transformTemplate:t},e){return(0,sh.useMemo)(()=>{let i=nJ();return nq(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),s}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=s,i})(i,n,r,e),a=function(t,e,i){let s={};for(let n in t)("values"!==n||"object"!=typeof t.values)&&(rn(n)||!0===i&&rs(n)||!e&&!rs(n)||t.draggable&&n.startsWith("onDrag"))&&(s[n]=t[n]);return s}(i,"string"==typeof e,t),l=e!==sh.Fragment?{...a,...o,ref:s}:{},{children:u}=i,h=(0,sh.useMemo)(()=>tu(u)?u.get():u,[u]);return(0,sh.createElement)(e,{...l,children:h})}}(e),createVisualElement:sl,Component:t})}))},9234:(t,e,i)=>{i.d(e,{M:()=>n});var s=i(2115);function n(t){let e=(0,s.useRef)(null);return null===e.current&&(e.current=t()),e.current}},9656:(t,e,i)=>{i.d(e,{t:()=>s});let s=(0,i(2115).createContext)(null)}}]);