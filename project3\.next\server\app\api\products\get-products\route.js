(()=>{var e={};e.id=140,e.ids=[140],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13181:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>m,routeModule:()=>i,serverHooks:()=>g,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{POST:()=>p});var o=t(42706),a=t(28203),u=t(45994),c=t(65859),n=t(39187);async function p(e){try{let r=await e.json();console.log("Incoming request to get-products:",r);let t="[]";r.productIds&&Array.isArray(r.productIds)&&r.productIds.length>0&&(t=JSON.stringify(r.productIds.map(e=>({columnName:"ProductId",columnValue:e,operator:"Equal",logicalOperator:"OR"}))));let s={requestParameters:{SearchTerm:r.requestParameters?.SearchTerm||"",CategoryID:r.requestParameters?.CategoryID||null,TagID:null,ManufacturerID:null,MinPrice:r.requestParameters?.MinPrice||null,MaxPrice:r.requestParameters?.MaxPrice||null,Rating:null,OrderByColumnName:r.requestParameters?.OrderByColumnName!==void 0?r.requestParameters.OrderByColumnName:0,PageNo:r.requestParameters?.PageNo||1,PageSize:r.requestParameters?.PageSize||1e3,recordValueJson:t,producttypeId:r.requestParameters?.producttypeId||null}},o=await c.A.post("https://admin.codemedicalapps.com/api/v1/dynamic/dataoperation/get-all-products",s,{headers:{"Content-Type":"application/json",Accept:"application/json"}});if(console.log("API response status:",o.status),console.log("API response data structure:",Object.keys(o.data)),o.data&&o.data.data)try{let e=JSON.parse(o.data.data);if(Array.isArray(e)&&e.length>0){console.log("First product structure:",Object.keys(e[0])),console.log("First product sample:",e[0]);let r=e.map(e=>e.ProductID).slice(0,10);console.log("Available Product IDs (first 10):",r),console.log("Total products found:",e.length)}else console.log("No products found in response")}catch(e){console.log("Error parsing products data:",e)}return n.NextResponse.json(o.data)}catch(e){return console.error("Error fetching products:",e.message),console.error("Request that caused error:",body),e.response&&(console.error("Error response status:",e.response.status),console.error("Error response data:",e.response.data)),n.NextResponse.json({error:"Failed to fetch products",message:e.message},{status:e.response?.status||500})}}let i=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/products/get-products/route",pathname:"/api/products/get-products",filename:"route",bundlePath:"app/api/products/get-products/route"},resolvedPagePath:"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\api\\products\\get-products\\route.js",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:g}=i;function m(){return(0,u.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[638,452,859],()=>t(13181));module.exports=s})();