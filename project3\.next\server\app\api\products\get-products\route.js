/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/products/get-products/route";
exports.ids = ["app/api/products/get-products/route"];
exports.modules = {

/***/ "(rsc)/./app/api/products/get-products/route.js":
/*!************************************************!*\
  !*** ./app/api/products/get-products/route.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(rsc)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n\nasync function POST(request) {\n    try {\n        // Parse the request body\n        const body1 = await request.json();\n        // Log the incoming request for debugging\n        console.log('Incoming request to get-products:', body1);\n        // Prepare the request parameters for the external API\n        let recordValueJson = '[]';\n        // If productIds are provided, create a filter for them\n        if (body1.productIds && Array.isArray(body1.productIds) && body1.productIds.length > 0) {\n            recordValueJson = JSON.stringify(body1.productIds.map((id)=>({\n                    columnName: 'ProductId',\n                    columnValue: id,\n                    operator: 'Equal',\n                    logicalOperator: 'OR'\n                })));\n        }\n        const data = {\n            requestParameters: {\n                SearchTerm: body1.requestParameters?.SearchTerm || '',\n                CategoryID: body1.requestParameters?.CategoryID || null,\n                TagID: null,\n                ManufacturerID: null,\n                MinPrice: body1.requestParameters?.MinPrice || null,\n                MaxPrice: body1.requestParameters?.MaxPrice || null,\n                Rating: null,\n                OrderByColumnName: body1.requestParameters?.OrderByColumnName !== undefined ? body1.requestParameters.OrderByColumnName : 0,\n                PageNo: body1.requestParameters?.PageNo || 1,\n                PageSize: body1.requestParameters?.PageSize || 1000,\n                recordValueJson: recordValueJson,\n                producttypeId: body1.requestParameters?.producttypeId || null\n            }\n        };\n        // Make the request to the external API\n        const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post('https://admin.codemedicalapps.com/api/v1/dynamic/dataoperation/get-all-products', data, {\n            headers: {\n                'Content-Type': 'application/json',\n                'Accept': 'application/json'\n            }\n        });\n        // Log the response for debugging\n        console.log('API response status:', response.status);\n        console.log('API response data structure:', Object.keys(response.data));\n        // Parse and log product IDs for debugging\n        if (response.data && response.data.data) {\n            try {\n                const products = JSON.parse(response.data.data);\n                if (Array.isArray(products) && products.length > 0) {\n                    // Log the structure of the first product to understand the data format\n                    console.log('First product structure:', Object.keys(products[0]));\n                    console.log('First product sample:', products[0]);\n                    const productIds = products.map((p)=>p.ProductID).slice(0, 10); // Log first 10 product IDs (note: ProductID not ProductId)\n                    console.log('Available Product IDs (first 10):', productIds);\n                    console.log('Total products found:', products.length);\n                } else {\n                    console.log('No products found in response');\n                }\n            } catch (parseError) {\n                console.log('Error parsing products data:', parseError);\n            }\n        }\n        // Return the response data\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response.data);\n    } catch (error) {\n        console.error('Error fetching products:', error.message);\n        console.error('Request that caused error:', body);\n        if (error.response) {\n            console.error('Error response status:', error.response.status);\n            console.error('Error response data:', error.response.data);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch products',\n            message: error.message\n        }, {\n            status: error.response?.status || 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/products/get-products/route.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproducts%2Fget-products%2Froute&page=%2Fapi%2Fproducts%2Fget-products%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Fget-products%2Froute.js&appDir=C%3A%5CUsers%5Cyhyasoft%5CDownloads%5Cec%5C.NET%208%20Version%20-%20Latest%5Cproject%5Ccodemedical%5Cproject3%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyhyasoft%5CDownloads%5Cec%5C.NET%208%20Version%20-%20Latest%5Cproject%5Ccodemedical%5Cproject3&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproducts%2Fget-products%2Froute&page=%2Fapi%2Fproducts%2Fget-products%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Fget-products%2Froute.js&appDir=C%3A%5CUsers%5Cyhyasoft%5CDownloads%5Cec%5C.NET%208%20Version%20-%20Latest%5Cproject%5Ccodemedical%5Cproject3%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyhyasoft%5CDownloads%5Cec%5C.NET%208%20Version%20-%20Latest%5Cproject%5Ccodemedical%5Cproject3&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_yhyasoft_Downloads_ec_NET_8_Version_Latest_project_codemedical_project3_app_api_products_get_products_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/products/get-products/route.js */ \"(rsc)/./app/api/products/get-products/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/products/get-products/route\",\n        pathname: \"/api/products/get-products\",\n        filename: \"route\",\n        bundlePath: \"app/api/products/get-products/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\api\\\\products\\\\get-products\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Users_yhyasoft_Downloads_ec_NET_8_Version_Latest_project_codemedical_project3_app_api_products_get_products_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproducts%2Fget-products%2Froute&page=%2Fapi%2Fproducts%2Fget-products%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Fget-products%2Froute.js&appDir=C%3A%5CUsers%5Cyhyasoft%5CDownloads%5Cec%5C.NET%208%20Version%20-%20Latest%5Cproject%5Ccodemedical%5Cproject3%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyhyasoft%5CDownloads%5Cec%5C.NET%208%20Version%20-%20Latest%5Cproject%5Ccodemedical%5Cproject3&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/axios","vendor-chunks/mime-db","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/get-intrinsic","vendor-chunks/form-data","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproducts%2Fget-products%2Froute&page=%2Fapi%2Fproducts%2Fget-products%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Fget-products%2Froute.js&appDir=C%3A%5CUsers%5Cyhyasoft%5CDownloads%5Cec%5C.NET%208%20Version%20-%20Latest%5Cproject%5Ccodemedical%5Cproject3%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyhyasoft%5CDownloads%5Cec%5C.NET%208%20Version%20-%20Latest%5Cproject%5Ccodemedical%5Cproject3&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();