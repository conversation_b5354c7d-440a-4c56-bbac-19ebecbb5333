using System.ComponentModel.DataAnnotations;

namespace Entities.CommonModels
{
    public class ChangePasswordModel
    {
        [Required(ErrorMessage = "New password is required")]
        [MinLength(6, ErrorMessage = "Password must be at least 6 characters long")]
        public string NewPassword { get; set; }

        [Required(ErrorMessage = "Confirm password is required")]
        [Compare("NewPassword", ErrorMessage = "The new password and confirmation password do not match")]
        public string ConfirmPassword { get; set; }

        public int UserId { get; set; }
    }
}