'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { 
  Menu, 
  Search, 
  X,
  ShoppingCart,
  Heart,
  User,
  UserPlus,
  Home,
  GraduationCap,
  BookOpen,
  BookMarked,
  Smartphone,
  MessageCircle,
  ChevronDown
} from 'lucide-react';
import { useSettings } from '@/contexts/settings-context';
import { useCart } from '@/contexts/cart-context';
import { useWishlist } from '@/contexts/wishlist-context';
import { useUser } from '@/contexts/user-context';
import { Button } from './button';
import { toast } from 'sonner';
import { MakeApiCallAsync } from '@/lib/api-helper';

type Category = {
  id: number;
  name: string;
  subcategories: string[];
};

type NavItem = {
  name: string;
  path: string;
  icon: React.ReactNode;
};

export function Header() {
  const router = useRouter();
  const { theme, language, setLanguage, t } = useSettings();
  const { items: cartItems } = useCart();
  const { items: wishlistItems } = useWishlist();
  const { user, isLoggedIn, logout } = useUser();
  
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);

  // Navigation items
  const navItems: NavItem[] = [
    { name: 'Home', path: '/', icon: <Home className="h-5 w-5 mr-2" /> },
    { name: 'Courses', path: '/courses', icon: <GraduationCap className="h-5 w-5 mr-2" /> },
    { name: 'Books', path: '/books', icon: <BookOpen className="h-5 w-5 mr-2" /> },
    { name: 'Journals', path: '/journals', icon: <BookMarked className="h-5 w-5 mr-2" /> },
    { name: 'Medical Apps', path: '/apps', icon: <Smartphone className="h-5 w-5 mr-2" /> },
    { name: 'About Us', path: '/about', icon: <User className="h-5 w-5 mr-2" /> },
    { name: 'Contact', path: '/contact', icon: <MessageCircle className="h-5 w-5 mr-2" /> },
  ];

  // Language options
  const languages = [
    { code: 'en' as const, name: 'English', flag: '🇬🇧' },
    { code: 'ar' as const, name: 'العربية', flag: '🇮🇶' },
  ];

  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await MakeApiCallAsync({
          url: '/api/categories',
          method: 'GET',
        });
        setCategories(response.data);
      } catch (error) {
        console.error('Error fetching categories:', error);
        toast.error('Failed to load categories');
      } finally {
        setIsLoading(false);
      }
    };

    fetchCategories();
  }, []);

  // Handle search
  const handleSearch = () => {
    if (!searchTerm.trim()) return;
    router.push(`/products?search=${encodeURIComponent(searchTerm)}`);
  };

  // Toggle mobile menu
  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  // Handle language change
  const handleLanguageChange = (lang: 'en' | 'ar') => {
    setLanguage(lang);
  };

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      {/* Top Bar */}
      <div className="border-b">
        <div className="container flex h-14 items-center justify-between px-4">
          {/* Mobile menu button */}
          <div className="md:hidden">
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={toggleMobileMenu}
              aria-label="Toggle menu"
            >
              {isMobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </Button>
          </div>

          {/* Logo - Center on mobile, left on desktop */}
          <div className="flex-1 md:flex-none">
            <Link href="/" className="flex items-center justify-center md:justify-start">
              <img 
                src={`${process.env.NEXT_PUBLIC_ADMIN_BASE_URL}content/commonImages/otherImages/18b_logo2x.png`} 
                alt="Logo" 
                className="h-12 w-auto" 
              />
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-6">
            {navItems.slice(0, 5).map((item) => (
              <Link
                key={item.path}
                href={item.path}
                className="text-sm font-medium hover:text-primary transition-colors"
              >
                {item.name}
              </Link>
            ))}
          </nav>

          {/* Right side icons */}
          <div className="flex items-center space-x-2">
            {/* Language Selector */}
            <div className="relative">
              <Button
                variant="ghost"
                size="sm"
                className="gap-1"
                onClick={() => handleLanguageChange(language === 'en' ? 'ar' : 'en')}
              >
                <span className="text-lg">
                  {language === 'en' ? '🇬🇧' : '🇮🇶'}
                </span>
                <span className="text-sm">
                  {language === 'en' ? 'EN' : 'AR'}
                </span>
              </Button>
            </div>

            {/* Wishlist */}
            <Button variant="ghost" size="icon" asChild>
              <Link href="/wishlist" className="relative">
                <Heart className="h-5 w-5" />
                {wishlistItems.length > 0 && (
                  <span className="absolute -top-1 -right-1 bg-primary text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                    {wishlistItems.length}
                  </span>
                )}
              </Link>
            </Button>

            {/* Cart */}
            <Button variant="ghost" size="icon" asChild>
              <Link href="/cart" className="relative">
                <ShoppingCart className="h-5 w-5" />
                {cartItems.length > 0 && (
                  <span className="absolute -top-1 -right-1 bg-primary text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                    {cartItems.length}
                  </span>
                )}
              </Link>
            </Button>

            {/* User Account */}
            {isLoggedIn ? (
              <Button variant="ghost" size="icon" asChild>
                <Link href="/account">
                  <User className="h-5 w-5" />
                </Link>
              </Button>
            ) : (
              <Button variant="ghost" size="sm" asChild>
                <Link href="/login">
                  <User className="h-5 w-5 mr-1" />
                  {t('login')}
                </Link>
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Search Bar */}
      <div className="border-b">
        <div className="container flex h-16 items-center px-4">
          <div className="relative flex-1 max-w-2xl mx-auto">
            <div className="relative flex items-center">
              <input
                type="text"
                placeholder={t('searchProducts') || 'Search products...'}
                className="w-full rounded-full border border-input bg-background px-4 py-2 pl-10 text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
              />
              <Search className="absolute left-3 h-4 w-4 text-muted-foreground" />
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <div className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm md:hidden">
          <div className="h-full w-4/5 max-w-sm bg-background border-r">
            <div className="flex flex-col h-full">
              <div className="p-4 border-b">
                <div className="flex items-center justify-between">
                  <h2 className="text-lg font-semibold">Menu</h2>
                  <Button variant="ghost" size="icon" onClick={toggleMobileMenu}>
                    <X className="h-5 w-5" />
                  </Button>
                </div>
              </div>
              <nav className="flex-1 overflow-y-auto p-4 space-y-2">
                {navItems.map((item) => (
                  <Link
                    key={item.path}
                    href={item.path}
                    className="flex items-center p-2 rounded-md hover:bg-accent"
                    onClick={toggleMobileMenu}
                  >
                    {item.icon}
                    <span>{item.name}</span>
                  </Link>
                ))}
              </nav>
              <div className="p-4 border-t">
                <div className="space-y-2">
                  {isLoggedIn ? (
                    <>
                      <Button variant="outline" className="w-full" asChild>
                        <Link href="/account">
                          <User className="h-4 w-4 mr-2" />
                          {t('myAccount')}
                        </Link>
                      </Button>
                      <Button variant="outline" className="w-full" onClick={logout}>
                        {t('logout')}
                      </Button>
                    </>
                  ) : (
                    <>
                      <Button variant="outline" className="w-full" asChild>
                        <Link href="/login">
                          {t('login')}
                        </Link>
                      </Button>
                      <Button className="w-full" asChild>
                        <Link href="/signup">
                          {t('signUp')}
                        </Link>
                      </Button>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </header>
  );
}
