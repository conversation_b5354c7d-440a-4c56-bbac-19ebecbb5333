(()=>{var e={};e.id=584,e.ids=[584],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4934:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\test-coupon\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\test-coupon\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},15868:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=r(70260),o=r(28203),a=r(25155),n=r.n(a),i=r(67292),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let d={children:["",{children:["test-coupon",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,4934)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\test-coupon\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,19611)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\test-coupon\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/test-coupon/page",pathname:"/test-coupon",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},38332:(e,t,r)=>{Promise.resolve().then(r.bind(r,4934))},48060:(e,t,r)=>{Promise.resolve().then(r.bind(r,93978))},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},93978:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var s=r(45512),o=r(58009),a=r(15348),n=r(87021),i=r(97643);function l(){let[e,t]=(0,o.useState)("test123"),[r,l]=(0,o.useState)(null),[d,c]=(0,o.useState)(!1),p=async()=>{c(!0),l(null);try{let t={requestParameters:{CouponCode:e.toUpperCase(),cartJsonData:JSON.stringify([{ProductId:1,ProductName:"Test Product",Price:100,Quantity:1,IsDiscountAllowed:!0}])}};console.log("Testing coupon with params:",t);let r=await (0,a.MakeApiCallAsync)(a.TS.END_POINT_NAMES.GET_COUPON_CODE_DISCOUNT,a.TS.COMMON_CONTROLLER_SUB_URL,t,{"Content-Type":"application/json",Accept:"application/json"},"POST");console.log("API Response:",r),l(r)}catch(e){console.error("Error testing coupon:",e),l({error:e instanceof Error?e.message:"Unknown error occurred"})}finally{c(!1)}};return(0,s.jsx)("div",{className:"container mx-auto p-6",children:(0,s.jsxs)(i.Zp,{className:"max-w-2xl mx-auto p-6",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Test Coupon API"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Coupon Code"}),(0,s.jsx)("input",{type:"text",value:e,onChange:e=>t(e.target.value),className:"w-full p-2 border rounded-md",placeholder:"Enter coupon code"})]}),(0,s.jsx)(n.$,{onClick:p,disabled:d,className:"w-full",children:d?"Testing...":"Test Coupon"}),r&&(0,s.jsxs)("div",{className:"mt-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"API Response:"}),(0,s.jsx)("pre",{className:"bg-gray-100 p-4 rounded-md overflow-auto text-sm",children:JSON.stringify(r,null,2)})]})]}),(0,s.jsxs)("div",{className:"mt-8 p-4 bg-blue-50 rounded-md",children:[(0,s.jsx)("h4",{className:"font-semibold text-blue-800 mb-2",children:"Test Instructions:"}),(0,s.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1",children:[(0,s.jsx)("li",{children:'• Default test coupon code is "test123"'}),(0,s.jsxs)("li",{children:["• The API endpoint is: ",a.TS.COMMON_CONTROLLER_SUB_URL+a.TS.END_POINT_NAMES.GET_COUPON_CODE_DISCOUNT]}),(0,s.jsx)("li",{children:"• Check the browser console for detailed logs"}),(0,s.jsx)("li",{children:"• Make sure you have valid coupons in the database"})]})]})]})})}},94735:e=>{"use strict";e.exports=require("events")},97643:(e,t,r)=>{"use strict";r.d(t,{Wu:()=>i,Zp:()=>n,wL:()=>l});var s=r(45512),o=r(58009),a=r(59462);let n=o.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));n.displayName="Card",o.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",e),...t})).displayName="CardHeader",o.forwardRef(({className:e,...t},r)=>(0,s.jsx)("h3",{ref:r,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t})).displayName="CardTitle",o.forwardRef(({className:e,...t},r)=>(0,s.jsx)("p",{ref:r,className:(0,a.cn)("text-sm text-muted-foreground",e),...t})).displayName="CardDescription";let i=o.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,a.cn)("p-6 pt-0",e),...t}));i.displayName="CardContent";let l=o.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,a.cn)("flex items-center p-6 pt-0",e),...t}));l.displayName="CardFooter"}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[638,551,875],()=>r(15868));module.exports=s})();