(()=>{var e={};e.id=220,e.ids=[220],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4643:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(41680).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},7678:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\about\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\about\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13392:(e,s,r)=>{Promise.resolve().then(r.bind(r,7678))},14494:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(41680).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},23120:(e,s,r)=>{Promise.resolve().then(r.bind(r,94172))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37778:(e,s,r)=>{"use strict";r.d(s,{AB:()=>c,J5:()=>n,Qp:()=>d,tH:()=>u,tJ:()=>x,w1:()=>m});var t=r(45512),a=r(58009),l=r(12705),i=r(99905),o=(r(14494),r(59462));let d=a.forwardRef(({...e},s)=>(0,t.jsx)("nav",{ref:s,"aria-label":"breadcrumb",...e}));d.displayName="Breadcrumb";let c=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("ol",{ref:r,className:(0,o.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",e),...s}));c.displayName="BreadcrumbList";let n=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("li",{ref:r,className:(0,o.cn)("inline-flex items-center gap-1.5",e),...s}));n.displayName="BreadcrumbItem";let m=a.forwardRef(({asChild:e,className:s,...r},a)=>{let i=e?l.DX:"a";return(0,t.jsx)(i,{ref:a,className:(0,o.cn)("transition-colors hover:text-foreground",s),...r})});m.displayName="BreadcrumbLink";let x=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("span",{ref:r,role:"link","aria-disabled":"true","aria-current":"page",className:(0,o.cn)("font-normal text-foreground",e),...s}));x.displayName="BreadcrumbPage";let u=({children:e,className:s,...r})=>(0,t.jsx)("li",{role:"presentation","aria-hidden":"true",className:(0,o.cn)("[&>svg]:size-3.5",s),...r,children:e??(0,t.jsx)(i.A,{})});u.displayName="BreadcrumbSeparator"},43464:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(41680).A)("Award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},48857:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(41680).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94172:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>f});var t=r(45512),a=r(37778),l=r(97643),i=r(28531),o=r.n(i),d=r(71901),c=r(43464),n=r(41680);let m=(0,n.A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),x=(0,n.A)("BookOpen",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]]);var u=r(10453),p=r(4643),h=r(48857);function f(){let{t:e,primaryColor:s}=(0,d.t)();return(0,t.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,t.jsx)(a.Qp,{className:"mb-6",children:(0,t.jsxs)(a.AB,{children:[(0,t.jsx)(a.J5,{children:(0,t.jsx)(a.w1,{asChild:!0,children:(0,t.jsx)(o(),{href:"/",children:e("home")})})}),(0,t.jsx)(a.tH,{}),(0,t.jsx)(a.J5,{children:(0,t.jsx)(a.tJ,{children:e("about")})})]})}),(0,t.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold mb-6",children:e("about")}),(0,t.jsxs)("div",{className:"relative rounded-lg overflow-hidden mb-12",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-primary/90 to-primary/70 z-10"}),(0,t.jsx)("img",{src:"https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",alt:"Medical books and stethoscope",className:"w-full h-64 md:h-96 object-cover"}),(0,t.jsx)("div",{className:"absolute inset-0 z-20 flex items-center justify-center text-center p-6",children:(0,t.jsxs)("div",{className:"max-w-3xl",children:[(0,t.jsx)("h2",{className:"text-2xl md:text-4xl font-bold text-white mb-4 drop-shadow-lg [text-shadow:2px_2px_4px_#000]",children:"We are Code Medical"}),(0,t.jsx)("p",{className:"text-white/90 text-lg md:text-xl drop-shadow-md [text-shadow:1px_1px_2px_#000]",children:"A professional team specialized in providing well-known valuable medical courses, eBooks, printed books, and popular medical accounts for all medical field staff around the world at low cost and in short time."})]})})]}),(0,t.jsx)(l.Zp,{className:"mb-12 overflow-hidden",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2",children:[(0,t.jsxs)("div",{className:"p-8 md:p-12 flex flex-col justify-center",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold mb-6",children:"Our Mission"}),(0,t.jsx)("p",{className:"text-muted-foreground mb-6",children:"At Code Medical, our mission is to make high-quality medical education accessible to healthcare professionals worldwide. We believe that knowledge should not be limited by geographical boundaries or financial constraints."}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"We strive to provide comprehensive, up-to-date, and practical resources that help medical professionals enhance their skills, stay current with the latest developments, and deliver better patient care."})]}),(0,t.jsx)("div",{className:"bg-gray-100 flex items-center justify-center p-8",children:(0,t.jsx)("img",{src:"https://images.unsplash.com/photo-1505751172876-fa1923c5c528?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",alt:"Medical professionals",className:"rounded-lg max-h-80 object-cover shadow-lg"})})]})}),(0,t.jsx)("h2",{className:"text-2xl font-bold mb-6",children:"Our Values"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-12",children:[(0,t.jsx)(l.Zp,{children:(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsx)("div",{className:"w-12 h-12 rounded-full flex items-center justify-center mb-4",style:{backgroundColor:`${s}20`},children:(0,t.jsx)(c.A,{className:"h-6 w-6",style:{color:s}})}),(0,t.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"Excellence"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"We are committed to excellence in everything we do, from the quality of our educational materials to our customer service."})]})}),(0,t.jsx)(l.Zp,{children:(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsx)("div",{className:"w-12 h-12 rounded-full flex items-center justify-center mb-4",style:{backgroundColor:`${s}20`},children:(0,t.jsx)(m,{className:"h-6 w-6",style:{color:s}})}),(0,t.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"Accessibility"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"We believe in making medical education accessible to all, regardless of location or financial constraints."})]})}),(0,t.jsx)(l.Zp,{children:(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsx)("div",{className:"w-12 h-12 rounded-full flex items-center justify-center mb-4",style:{backgroundColor:`${s}20`},children:(0,t.jsx)(x,{className:"h-6 w-6",style:{color:s}})}),(0,t.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"Innovation"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"We continuously seek innovative ways to deliver medical education and improve the learning experience."})]})})]}),(0,t.jsx)("h2",{className:"text-2xl font-bold mb-6",children:"Why Choose Code Medical"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-12",children:[(0,t.jsx)(l.Zp,{children:(0,t.jsxs)("div",{className:"p-6 flex",children:[(0,t.jsx)("div",{className:"mr-4",children:(0,t.jsx)("div",{className:"w-10 h-10 rounded-full flex items-center justify-center",style:{backgroundColor:`${s}20`},children:(0,t.jsx)(u.A,{className:"h-5 w-5",style:{color:s}})})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Quality Resources"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Our educational materials are developed by experienced medical professionals and undergo rigorous quality checks."})]})]})}),(0,t.jsx)(l.Zp,{children:(0,t.jsxs)("div",{className:"p-6 flex",children:[(0,t.jsx)("div",{className:"mr-4",children:(0,t.jsx)("div",{className:"w-10 h-10 rounded-full flex items-center justify-center",style:{backgroundColor:`${s}20`},children:(0,t.jsx)(p.A,{className:"h-5 w-5",style:{color:s}})})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Fast Delivery"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"We ensure quick delivery of our products, whether digital or physical, to save your valuable time."})]})]})}),(0,t.jsx)(l.Zp,{children:(0,t.jsxs)("div",{className:"p-6 flex",children:[(0,t.jsx)("div",{className:"mr-4",children:(0,t.jsx)("div",{className:"w-10 h-10 rounded-full flex items-center justify-center",style:{backgroundColor:`${s}20`},children:(0,t.jsx)(h.A,{className:"h-5 w-5",style:{color:s}})})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Global Reach"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"We serve medical professionals worldwide, with resources tailored to different regions and specialties."})]})]})}),(0,t.jsx)(l.Zp,{children:(0,t.jsxs)("div",{className:"p-6 flex",children:[(0,t.jsx)("div",{className:"mr-4",children:(0,t.jsx)("div",{className:"w-10 h-10 rounded-full flex items-center justify-center",style:{backgroundColor:`${s}20`},children:(0,t.jsx)(m,{className:"h-5 w-5",style:{color:s}})})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Customer Support"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Our dedicated customer support team is always ready to assist you with any queries or concerns."})]})]})})]})]})]})}},94735:e=>{"use strict";e.exports=require("events")},95184:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>n,routeModule:()=>x,tree:()=>c});var t=r(70260),a=r(28203),l=r(25155),i=r.n(l),o=r(67292),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(s,d);let c={children:["",{children:["about",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,7678)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\about\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,19611)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"]}]}.children,n=["C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\about\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/about/page",pathname:"/about",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},97643:(e,s,r)=>{"use strict";r.d(s,{Wu:()=>o,Zp:()=>i,wL:()=>d});var t=r(45512),a=r(58009),l=r(59462);let i=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));i.displayName="Card",a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",e),...s})).displayName="CardHeader",a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("h3",{ref:r,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s})).displayName="CardTitle",a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("p",{ref:r,className:(0,l.cn)("text-sm text-muted-foreground",e),...s})).displayName="CardDescription";let o=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,l.cn)("p-6 pt-0",e),...s}));o.displayName="CardContent";let d=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,l.cn)("flex items-center p-6 pt-0",e),...s}));d.displayName="CardFooter"},99905:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(41680).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[638,551,875],()=>r(95184));module.exports=t})();