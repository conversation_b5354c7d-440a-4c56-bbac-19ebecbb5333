(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1298],{1284:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>N});var a=r(5155),t=r(2115),n=r(9426),l=r(5007),i=r(4085),o=r(2336),d=r(5785),c=r(1641),m=r(8173),u=r.n(m),f=r(7110),h=r(1466),x=r(719),p=r(6233),g=r(591),w=r(5236),j=r(7780),b=r(738),v=r(2598);function N(){let{t:e}=(0,f.t)(),[s,r]=(0,t.useState)(!1),[m,N]=(0,t.useState)(!1),[y,C]=(0,t.useState)(!1),[P,A]=(0,t.useState)({firstName:"<PERSON>",lastName:"Doe",email:"<EMAIL>",phone:"***************",currentPassword:"",newPassword:"",confirmPassword:""}),U=e=>{let{name:s,value:r}=e.target;A(e=>({...e,[s]:r}))},[k,S]=(0,t.useState)(""),F=e=>{if(e.preventDefault(),N(!0),S(""),P.newPassword!==P.confirmPassword){S("New password and confirm password do not match"),N(!1);return}if(P.newPassword&&P.newPassword.length<8){S("Password must be at least 8 characters long"),N(!1);return}setTimeout(()=>{N(!1),C(!0),A(e=>({...e,currentPassword:"",newPassword:"",confirmPassword:""})),setTimeout(()=>C(!1),3e3)},1500)};return(0,a.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,a.jsx)(n.Qp,{className:"mb-6",children:(0,a.jsxs)(n.AB,{children:[(0,a.jsx)(n.J5,{children:(0,a.jsx)(n.w1,{asChild:!0,children:(0,a.jsx)(u(),{href:"/",children:e("home")})})}),(0,a.jsx)(n.tH,{}),(0,a.jsx)(n.J5,{children:(0,a.jsx)(n.tJ,{children:e("myAccount")})})]})}),(0,a.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold mb-6",children:e("myAccount")}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-[250px_1fr] gap-6",children:[(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsx)(l.Zp,{children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex flex-col items-center text-center mb-6",children:[(0,a.jsx)("div",{className:"w-20 h-20 rounded-full bg-primary/10 flex items-center justify-center mb-4",children:(0,a.jsx)(h.A,{className:"h-10 w-10 text-primary"})}),(0,a.jsx)("h3",{className:"font-medium",children:"John Doe"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"<EMAIL>"})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(i.$,{variant:"ghost",className:"w-full justify-start",asChild:!0,children:(0,a.jsxs)(u(),{href:"/account",children:[(0,a.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"Profile"]})}),(0,a.jsx)(i.$,{variant:"ghost",className:"w-full justify-start",asChild:!0,children:(0,a.jsxs)(u(),{href:"/orders",children:[(0,a.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Orders"]})}),(0,a.jsx)(i.$,{variant:"ghost",className:"w-full justify-start",asChild:!0,children:(0,a.jsxs)(u(),{href:"/payment-methods",children:[(0,a.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"Payment Methods"]})}),(0,a.jsx)(i.$,{variant:"ghost",className:"w-full justify-start",asChild:!0,children:(0,a.jsxs)(u(),{href:"/wishlist",children:[(0,a.jsx)(g.A,{className:"mr-2 h-4 w-4"}),"Wishlist"]})}),(0,a.jsxs)(i.$,{variant:"ghost",className:"w-full justify-start text-red-500 hover:text-red-600 hover:bg-red-50",children:[(0,a.jsx)(w.A,{className:"mr-2 h-4 w-4"}),"Logout"]})]})]})})}),(0,a.jsx)("div",{children:(0,a.jsxs)(c.tU,{defaultValue:"profile",children:[(0,a.jsxs)(c.j7,{className:"mb-6",children:[(0,a.jsx)(c.Xi,{value:"profile",children:"Profile Information"}),(0,a.jsx)(c.Xi,{value:"password",children:"Change Password"})]}),(0,a.jsx)(c.av,{value:"profile",children:(0,a.jsx)(l.Zp,{children:(0,a.jsxs)("form",{onSubmit:F,className:"p-6",children:[y&&(0,a.jsx)("div",{className:"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6",children:"Profile updated successfully!"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"firstName",children:"First Name"}),(0,a.jsx)(o.p,{id:"firstName",name:"firstName",value:P.firstName,onChange:U})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"lastName",children:"Last Name"}),(0,a.jsx)(o.p,{id:"lastName",name:"lastName",value:P.lastName,onChange:U})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"email",children:"Email Address"}),(0,a.jsx)(o.p,{id:"email",name:"email",type:"email",value:P.email,onChange:U})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"phone",children:"Phone Number"}),(0,a.jsx)(o.p,{id:"phone",name:"phone",value:P.phone,onChange:U})]})]}),(0,a.jsx)(i.$,{type:"submit",disabled:m,children:m?(0,a.jsxs)("span",{className:"flex items-center gap-2",children:[(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Updating..."]}):(0,a.jsxs)("span",{className:"flex items-center gap-2",children:[(0,a.jsx)(j.A,{className:"h-4 w-4"}),"Save Changes"]})})]})})}),(0,a.jsx)(c.av,{value:"password",children:(0,a.jsx)(l.Zp,{children:(0,a.jsxs)("form",{onSubmit:F,className:"p-6",children:[y&&(0,a.jsx)("div",{className:"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6",children:"Password updated successfully!"}),(0,a.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"currentPassword",children:"Current Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(o.p,{id:"currentPassword",name:"currentPassword",type:s?"text":"password",value:P.currentPassword,onChange:U}),(0,a.jsx)(i.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3",onClick:()=>r(!s),children:s?(0,a.jsx)(b.A,{className:"h-4 w-4"}):(0,a.jsx)(v.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"newPassword",children:"New Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(o.p,{id:"newPassword",name:"newPassword",type:s?"text":"password",value:P.newPassword,onChange:U}),(0,a.jsx)(i.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3",onClick:()=>r(!s),children:s?(0,a.jsx)(b.A,{className:"h-4 w-4"}):(0,a.jsx)(v.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"confirmPassword",children:"Confirm New Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(o.p,{id:"confirmPassword",name:"confirmPassword",type:s?"text":"password",value:P.confirmPassword,onChange:U}),(0,a.jsx)(i.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3",onClick:()=>r(!s),children:s?(0,a.jsx)(b.A,{className:"h-4 w-4"}):(0,a.jsx)(v.A,{className:"h-4 w-4"})})]})]})]}),(0,a.jsx)(i.$,{type:"submit",disabled:m||!P.currentPassword||!P.newPassword||!P.confirmPassword||P.newPassword!==P.confirmPassword,children:m?(0,a.jsxs)("span",{className:"flex items-center gap-2",children:[(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Updating..."]}):(0,a.jsxs)("span",{className:"flex items-center gap-2",children:[(0,a.jsx)(j.A,{className:"h-4 w-4"}),"Update Password"]})}),k&&(0,a.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6",children:k})]})})})]})})]})]})]})}},1641:(e,s,r)=>{"use strict";r.d(s,{Xi:()=>d,av:()=>c,j7:()=>o,tU:()=>i});var a=r(5155),t=r(2115),n=r(8217),l=r(9602);let i=n.bL,o=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)(n.B8,{ref:s,className:(0,l.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",r),...t})});o.displayName=n.B8.displayName;let d=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)(n.l9,{ref:s,className:(0,l.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",r),...t})});d.displayName=n.l9.displayName;let c=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)(n.UC,{ref:s,className:(0,l.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",r),...t})});c.displayName=n.UC.displayName},2336:(e,s,r)=>{"use strict";r.d(s,{p:()=>l});var a=r(5155),t=r(2115),n=r(9602);let l=t.forwardRef((e,s)=>{let{className:r,type:t,...l}=e;return(0,a.jsx)("input",{type:t,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:s,...l})});l.displayName="Input"},4085:(e,s,r)=>{"use strict";r.d(s,{$:()=>d,r:()=>o});var a=r(5155),t=r(2115),n=r(2317),l=r(1027),i=r(9602);let o=(0,l.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-red-500 text-destructive-foreground hover:bg-red-600",outline:"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=t.forwardRef((e,s)=>{let{className:r,variant:t,size:l,asChild:d=!1,...c}=e,m=d?n.DX:"button";return(0,a.jsx)(m,{className:(0,i.cn)(o({variant:t,size:l,className:r})),ref:s,...c})});d.displayName="Button"},4922:(e,s,r)=>{Promise.resolve().then(r.bind(r,1284))},5007:(e,s,r)=>{"use strict";r.d(s,{Wu:()=>i,Zp:()=>l,wL:()=>o});var a=r(5155),t=r(2115),n=r(9602);let l=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...t})});l.displayName="Card",t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",r),...t})}).displayName="CardHeader",t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("h3",{ref:s,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",r),...t})}).displayName="CardTitle",t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("p",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",r),...t})}).displayName="CardDescription";let i=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("p-6 pt-0",r),...t})});i.displayName="CardContent";let o=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",r),...t})});o.displayName="CardFooter"},5785:(e,s,r)=>{"use strict";r.d(s,{J:()=>d});var a=r(5155),t=r(2115),n=r(6195),l=r(1027),i=r(9602);let o=(0,l.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)(n.b,{ref:s,className:(0,i.cn)(o(),r),...t})});d.displayName=n.b.displayName},7110:(e,s,r)=>{"use strict";r.d(s,{Z:()=>i,t:()=>o});var a=r(5155),t=r(2115);let n={en:{menu:"Menu",newsletter:"Newsletter",enterEmail:"Enter your email",newsletterDisclaimer:"Subscribe to our newsletter to receive updates and exclusive offers",popularCategories:"Popular Categories!",settings:"Settings",language:"Language",theme:"Theme",color:"Color",save:"Save",cancel:"Cancel",close:"Close",phone:"***************",phonenumber:"Phone Number",email:"<EMAIL>",liveChat:"Live Chat",welcome:"Welcome",logout:"Logout",login:"Login",signup:"Sign Up",signUp:"Sign Up",category:"Category",categories:"Categories",filters:"Filters",clearAll:"Clear All",products:"Products",loadingCategories:"Loading categories...",home:"Home",todayDeals:"Today's Deals",followUs:"Follow Us",aboutUs:"About Us",contactUs:"Contact Us",tryAgain:"Try Again",noProductsFound:"No products found",allProducts:"All Products",quickLinks:"Quick Links",about:"About Us",contact:"Contact Us",hotDeals:"Hot Deals",customerArea:"Customer Area",myAccount:"My Account",orders:"Orders",cart:"Cart",wishlist:"Wishlist",paymentMethods:"Payment Methods",location:"Location",callUs:"Call Us",emailUs:"Email Us",subscribe:"Subscribe",name:"Name",subject:"Subject",message:"Message",sendMessage:"Send Message",sending:"Sending...",messageSent:"Message sent successfully!",messageError:"Failed to send message",contactInfo:"Contact Information",address:"Address",findUs:"Find Us"},ar:{menu:"القائمة",popularCategories:"الفئات الشائعة!",settings:"الإعدادات",language:"اللغة",theme:"المظهر",color:"اللون",save:"حفظ",cancel:"إلغاء",close:"إغلاق",phone:"***************",phonenumber:"رقم الهاتف",email:"<EMAIL>",liveChat:"محادثة مباشرة",welcome:"مرحباً",logout:"تسجيل الخروج",login:"تسجيل الدخول",signup:"تسجيل جديد",signUp:"تسجيل جديد",category:"الفئة",categories:"الفئات",products:"المنتجات",loadingCategories:"جاري تحميل الفئات...",filters:"المرشحات",clearAll:"مسح الكل",home:"الرئيسية",todayDeals:"عروض اليوم",followUs:"تابعنا",aboutUs:"من نحن",contactUs:"اتصل بنا",tryAgain:"حاول مرة أخرى",noProductsFound:"لم يتم العثور على منتجات",allProducts:"جميع المنتجات",quickLinks:"روابط سريعة",about:"من نحن",contact:"اتصل بنا",hotDeals:"عروض ساخنة",customerArea:"منطقة العملاء",myAccount:"حسابي",orders:"الطلبات",cart:"السلة",wishlist:"المفضلة",paymentMethods:"طرق الدفع",location:"الموقع",callUs:"اتصل بنا",emailUs:"راسلنا",subscribe:"اشترك",name:"الاسم",subject:"الموضوع",message:"الرسالة",sendMessage:"إرسال الرسالة",sending:"جاري الإرسال...",messageSent:"تم إرسال الرسالة بنجاح!",messageError:"فشل في إرسال الرسالة",contactInfo:"معلومات الاتصال",address:"العنوان",findUs:"موقعنا"}},l=(0,t.createContext)(void 0);function i(e){let{children:s}=e,[r,i]=(0,t.useState)("light"),[o,d]=(0,t.useState)("en"),[c,m]=(0,t.useState)("#0074b2");return(0,t.useEffect)(()=>{document.documentElement.style.setProperty("--primary",c),document.documentElement.style.setProperty("--primary-foreground","#ffffff")},[]),(0,a.jsx)(l.Provider,{value:{theme:r,language:o,primaryColor:c,toggleTheme:()=>{i("light"===r?"dark":"light")},setLanguage:e=>{d(e),document.documentElement.dir="ar"===e?"rtl":"ltr"},setPrimaryColor:e=>{m(e)},t:e=>(function(e,s){let r=n[s];return e in r?r[e]:"en"!==s&&e in n.en?n.en[e]:e})(e,o)},children:s})}function o(){let e=(0,t.useContext)(l);if(void 0===e)throw Error("useSettings must be used within a SettingsProvider");return e}},9426:(e,s,r)=>{"use strict";r.d(s,{AB:()=>d,J5:()=>c,Qp:()=>o,tH:()=>f,tJ:()=>u,w1:()=>m});var a=r(5155),t=r(2115),n=r(2317),l=r(6967),i=(r(4858),r(9602));let o=t.forwardRef((e,s)=>{let{...r}=e;return(0,a.jsx)("nav",{ref:s,"aria-label":"breadcrumb",...r})});o.displayName="Breadcrumb";let d=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("ol",{ref:s,className:(0,i.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",r),...t})});d.displayName="BreadcrumbList";let c=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("li",{ref:s,className:(0,i.cn)("inline-flex items-center gap-1.5",r),...t})});c.displayName="BreadcrumbItem";let m=t.forwardRef((e,s)=>{let{asChild:r,className:t,...l}=e,o=r?n.DX:"a";return(0,a.jsx)(o,{ref:s,className:(0,i.cn)("transition-colors hover:text-foreground",t),...l})});m.displayName="BreadcrumbLink";let u=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("span",{ref:s,role:"link","aria-disabled":"true","aria-current":"page",className:(0,i.cn)("font-normal text-foreground",r),...t})});u.displayName="BreadcrumbPage";let f=e=>{let{children:s,className:r,...t}=e;return(0,a.jsx)("li",{role:"presentation","aria-hidden":"true",className:(0,i.cn)("[&>svg]:size-3.5",r),...t,children:null!=s?s:(0,a.jsx)(l.A,{})})};f.displayName="BreadcrumbSeparator"},9602:(e,s,r)=>{"use strict";r.d(s,{cn:()=>n});var a=r(3463),t=r(9795);function n(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return(0,t.QP)((0,a.$)(s))}}},e=>{var s=s=>e(e.s=s);e.O(0,[1345,7663,303,8441,6587,7358],()=>s(4922)),_N_E=e.O()}]);