"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./contexts/coupon-context.tsx":
/*!*************************************!*\
  !*** ./contexts/coupon-context.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CouponProvider: () => (/* binding */ CouponProvider),\n/* harmony export */   useCoupon: () => (/* binding */ useCoupon)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ CouponProvider,useCoupon auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst CouponContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction CouponProvider(param) {\n    let { children } = param;\n    _s();\n    const [appliedCoupon, setAppliedCoupon] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const validateCoupon = (code, amount)=>{\n        const coupon = availableCoupons.find((c)=>c.code === code.toUpperCase());\n        if (!coupon) {\n            return {\n                valid: false,\n                message: 'Invalid coupon code',\n                discount: 0\n            };\n        }\n        if (coupon.minAmount && amount < coupon.minAmount) {\n            return {\n                valid: false,\n                message: \"Minimum purchase amount of $\".concat(coupon.minAmount, \" required\"),\n                discount: 0\n            };\n        }\n        if (coupon.expiryDate && new Date() > coupon.expiryDate) {\n            return {\n                valid: false,\n                message: 'Coupon has expired',\n                discount: 0\n            };\n        }\n        setAppliedCoupon(coupon);\n        const discountAmount = coupon.type === 'percentage' ? amount * coupon.discount / 100 : coupon.discount;\n        return {\n            valid: true,\n            message: 'Coupon applied successfully',\n            discount: discountAmount\n        };\n    };\n    const clearCoupon = ()=>{\n        setAppliedCoupon(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CouponContext.Provider, {\n        value: {\n            appliedCoupon,\n            validateCoupon,\n            clearCoupon\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\contexts\\\\coupon-context.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, this);\n}\n_s(CouponProvider, \"rjyNwj8JntUJ5sJK/G82Jl3Out4=\");\n_c = CouponProvider;\nfunction useCoupon() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(CouponContext);\n    if (context === undefined) {\n        throw new Error('useCoupon must be used within a CouponProvider');\n    }\n    return context;\n}\n_s1(useCoupon, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"CouponProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./contexts/coupon-context.tsx\n"));

/***/ })

});