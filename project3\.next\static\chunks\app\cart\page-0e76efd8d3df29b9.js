(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4005],{1027:(e,s,t)=>{"use strict";t.d(s,{F:()=>i});var r=t(3463);let l=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=r.$,i=(e,s)=>t=>{var r;if((null==s?void 0:s.variants)==null)return a(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:i,defaultVariants:n}=s,d=Object.keys(i).map(e=>{let s=null==t?void 0:t[e],r=null==n?void 0:n[e];if(null===s)return null;let a=l(s)||l(r);return i[e][a]}),c=t&&Object.entries(t).reduce((e,s)=>{let[t,r]=s;return void 0===r||(e[t]=r),e},{});return a(e,d,null==s?void 0:null===(r=s.compoundVariants)||void 0===r?void 0:r.reduce((e,s)=>{let{class:t,className:r,...l}=s;return Object.entries(l).every(e=>{let[s,t]=e;return Array.isArray(t)?t.includes({...n,...c}[s]):({...n,...c})[s]===t})?[...e,t,r]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}},4807:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(7401).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},4858:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(7401).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},5007:(e,s,t)=>{"use strict";t.d(s,{Wu:()=>n,Zp:()=>i,wL:()=>d});var r=t(5155),l=t(2115),a=t(9602);let i=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,r.jsx)("div",{ref:s,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...l})});i.displayName="Card",l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,r.jsx)("div",{ref:s,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",t),...l})}).displayName="CardHeader",l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,r.jsx)("h3",{ref:s,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",t),...l})}).displayName="CardTitle",l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,r.jsx)("p",{ref:s,className:(0,a.cn)("text-sm text-muted-foreground",t),...l})}).displayName="CardDescription";let n=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,r.jsx)("div",{ref:s,className:(0,a.cn)("p-6 pt-0",t),...l})});n.displayName="CardContent";let d=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,r.jsx)("div",{ref:s,className:(0,a.cn)("flex items-center p-6 pt-0",t),...l})});d.displayName="CardFooter"},5686:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(7401).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},5887:(e,s,t)=>{Promise.resolve().then(t.bind(t,6061))},6061:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>b});var r=t(5155),l=t(2115),a=t(9426),i=t(5007),n=t(4085),d=t(8173),c=t.n(d),o=t(7110),m=t(8936),x=t(2604),u=t(5943),h=t(4807),f=t(7401);let p=(0,f.A)("Minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]),j=(0,f.A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);var g=t(5686),y=t(8474),N=t(8897),v=t.n(N);function b(){let{t:e,primaryColor:s}=(0,o.t)(),{items:t,removeFromCart:d,updateQuantity:f,totalItems:N,subtotal:b,subtotalIQD:w,total:k,totalIQD:C}=(0,m._)(),{validateCoupon:A,appliedCoupon:P,clearCoupon:S,isLoading:R}=(0,x.Y)(),{formatIQD:M,formatUSD:B}=(0,u.H)(),[D,E]=(0,l.useState)(""),[q,$]=(0,l.useState)(!1);return(0,r.jsxs)("div",{className:"container mx-auto py-4 sm:py-6 md:py-8 px-4",children:[(0,r.jsx)(a.Qp,{className:"mb-4 sm:mb-6",children:(0,r.jsxs)(a.AB,{children:[(0,r.jsx)(a.J5,{children:(0,r.jsx)(a.w1,{asChild:!0,children:(0,r.jsx)(c(),{href:"/",children:e("home")})})}),(0,r.jsx)(a.tH,{}),(0,r.jsx)(a.J5,{children:(0,r.jsx)(a.tJ,{children:e("cart")})})]})}),(0,r.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 sm:mb-6 gap-2",children:[(0,r.jsx)("h1",{className:"text-2xl sm:text-3xl font-bold",children:e("cart")}),(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsxs)("p",{className:"text-muted-foreground",children:[N," ",1===N?"item":"items"]}),(0,r.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>$(!q),className:"flex items-center gap-2",children:[(0,r.jsx)(h.A,{className:"h-4 w-4"}),q?"Show IQD":"Show USD"]})]})]}),t.length>0?(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsx)("div",{className:"lg:col-span-2 space-y-4",children:t.map(e=>(0,r.jsx)(i.Zp,{className:"overflow-hidden hover:shadow-lg transition-shadow duration-300 border-l-4",style:{borderLeftColor:s},children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row",children:[(0,r.jsxs)("div",{className:"w-full sm:w-40 h-40 bg-gradient-to-br from-gray-50 to-gray-100 relative overflow-hidden",children:[(0,r.jsx)("img",{src:e.image||"/products/book".concat(e.id,".jpg"),alt:e.name,className:"w-full h-full object-cover hover:scale-105 transition-transform duration-300"}),(0,r.jsxs)("div",{className:"absolute top-2 right-2 bg-white/90 backdrop-blur-sm rounded-full px-2 py-1 text-xs font-medium",style:{color:s},children:["#",e.id]})]}),(0,r.jsxs)("div",{className:"flex-1 p-6 flex flex-col sm:flex-row justify-between bg-gradient-to-r from-white to-gray-50/50",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"font-semibold text-lg mb-3 text-gray-800 leading-tight",children:e.name}),(0,r.jsx)("div",{className:"mb-4 p-3 bg-white rounded-lg border border-gray-100 shadow-sm",children:q?(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"text-2xl font-bold",style:{color:s},children:B(e.adjustedPrice)}),(0,r.jsx)("div",{className:"bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium",children:"USD"})]}),(0,r.jsxs)("div",{className:"text-sm text-muted-foreground flex items-center gap-1",children:[(0,r.jsx)("span",{children:"≈"}),(0,r.jsx)("span",{children:M(e.adjustedIqdPrice||e.iqdPrice||0)})]})]}):(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"text-2xl font-bold",style:{color:s},children:M(e.adjustedIqdPrice||e.iqdPrice||0)}),(0,r.jsx)("div",{className:"bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium",children:"IQD"})]}),(0,r.jsxs)("div",{className:"text-sm text-muted-foreground flex items-center gap-1",children:[(0,r.jsx)("span",{children:"≈"}),(0,r.jsx)("span",{children:B(e.adjustedPrice)})]})]})}),e.discountPrice&&e.discountPrice<(e.originalPrice||e.price)&&(0,r.jsxs)("div",{className:"text-sm text-green-600 mb-2 flex items-center gap-2",children:[(0,r.jsx)("span",{className:"bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium",children:"SALE"}),(0,r.jsxs)("span",{children:["Original: ",q?B(e.originalPrice):M(Math.round(1500*e.originalPrice))]})]}),e.attributes&&e.attributes.length>0&&(0,r.jsxs)("div",{className:"mt-2 space-y-1 pt-2 border-t border-gray-100",children:[(0,r.jsx)("div",{className:"text-xs font-medium text-muted-foreground uppercase tracking-wide mb-1",children:"Selected Options:"}),e.attributes.map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,r.jsxs)("span",{children:[(0,r.jsxs)("span",{className:"font-medium",children:[e.DisplayName||e.AttributeName,":"]})," ",e.AttributeValueText]}),e.PriceAdjustment&&(0,r.jsxs)("span",{className:"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full",children:[1===e.PriceAdjustmentType?"+":"",q?"$".concat(e.PriceAdjustment):"".concat(Math.round(1500*e.PriceAdjustment).toLocaleString()," IQD"),2===e.PriceAdjustmentType?"%":""]})]},s))]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-4 mt-4 sm:mt-0",children:[(0,r.jsxs)("div",{className:"flex items-center bg-gray-50 rounded-lg border border-gray-200 overflow-hidden",children:[(0,r.jsx)("button",{className:"p-3 hover:bg-gray-100 transition-colors text-gray-600 hover:text-gray-800",onClick:()=>f(e.id,e.quantity-1),children:(0,r.jsx)(p,{className:"h-4 w-4"})}),(0,r.jsx)("span",{className:"px-4 py-3 font-medium bg-white border-x border-gray-200 min-w-[3rem] text-center",children:e.quantity}),(0,r.jsx)("button",{className:"p-3 hover:bg-gray-100 transition-colors text-gray-600 hover:text-gray-800",onClick:()=>f(e.id,e.quantity+1),children:(0,r.jsx)(j,{className:"h-4 w-4"})})]}),(0,r.jsx)("button",{className:"p-3 text-red-500 hover:bg-red-50 rounded-lg transition-colors border border-red-200 hover:border-red-300",onClick:()=>d(e.id),title:"Remove from cart",children:(0,r.jsx)(g.A,{className:"h-5 w-5"})})]})]})]})},e.id))}),(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsx)(i.Zp,{className:"sticky top-4 shadow-lg border-0 bg-gradient-to-br from-white to-gray-50",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-bold text-gray-800 flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-2 h-6 rounded-full",style:{backgroundColor:s}}),"Order Summary"]}),(0,r.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>$(!q),className:"text-xs border-2 hover:scale-105 transition-transform",style:{borderColor:s,color:s},children:q?"Show IQD":"Show USD"})]}),(0,r.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Subtotal"}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("div",{className:"font-medium",children:q?B(b):M(w)}),(0,r.jsxs)("div",{className:"text-xs text-muted-foreground",children:["≈ ",q?M(w):B(b)]})]})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Shipping"}),(0,r.jsx)("span",{className:"text-green-600 font-medium",children:"Free"})]}),P&&(0,r.jsxs)("div",{className:"flex justify-between text-green-600",children:[(0,r.jsxs)("span",{children:["Discount (",P.code,")"]}),(0,r.jsxs)("span",{children:["-",q?B(P.discount):M(Math.round(1500*P.discount))]})]}),(0,r.jsx)("div",{className:"border-t pt-4 mt-4",children:(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-lg font-bold",children:"Total"}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("div",{className:"text-xl font-bold",style:{color:s},children:q?B(k-(P?P.discount:0)):M(C-(P?Math.round(1500*P.discount):0))}),(0,r.jsxs)("div",{className:"text-sm text-muted-foreground",children:["≈ ",q?M(C-(P?Math.round(1500*P.discount):0)):B(k-(P?P.discount:0))]})]})]})}),(0,r.jsxs)("div",{className:"mt-4",children:[(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)("input",{type:"text",placeholder:"Enter coupon code",className:"flex-1 p-2 border rounded-md",value:D,onChange:e=>E(e.target.value)}),(0,r.jsx)(n.$,{onClick:async()=>{if(D)try{let e=await A(D,b,t);e.valid?(v().fire({title:"Success!",text:e.message,icon:"success",timer:2e3,showConfirmButton:!1}),E("")):v().fire({title:"Error",text:e.message,icon:"error",timer:2e3,showConfirmButton:!1})}catch(e){v().fire({title:"Error",text:"Failed to validate coupon. Please try again.",icon:"error",timer:2e3,showConfirmButton:!1})}},variant:"outline",disabled:R,children:R?"Validating...":"Apply"})]}),P&&(0,r.jsxs)("div",{className:"flex items-center justify-between mt-2 text-sm",children:[(0,r.jsxs)("span",{className:"text-green-600",children:["Coupon ",P.code," applied"]}),(0,r.jsx)("button",{onClick:()=>{S(),v().fire({title:"Coupon Removed",text:"Coupon has been removed successfully",icon:"info",timer:2e3,showConfirmButton:!1})},className:"text-red-500 hover:underline",children:"Remove"})]})]})]}),(0,r.jsx)(n.$,{className:"w-full py-6",style:{backgroundColor:s},asChild:!0,children:(0,r.jsx)(c(),{href:"/checkout",children:"Proceed to Checkout"})}),(0,r.jsx)("div",{className:"mt-4",children:(0,r.jsx)(c(),{href:"/",className:"text-sm text-center block hover:underline",children:"Continue Shopping"})})]})})})]}):(0,r.jsx)(i.Zp,{children:(0,r.jsxs)("div",{className:"p-8 text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(y.A,{className:"h-8 w-8 text-muted-foreground"})}),(0,r.jsx)("h3",{className:"text-lg font-medium mb-2",children:"Your cart is empty"}),(0,r.jsx)("p",{className:"text-muted-foreground mb-4",children:"Add items to your cart to proceed to checkout"}),(0,r.jsx)(n.$,{asChild:!0,children:(0,r.jsx)(c(),{href:"/",children:"Continue Shopping"})})]})})]})]})}},6967:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(7401).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},7401:(e,s,t)=>{"use strict";t.d(s,{A:()=>d});var r=t(2115);let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=function(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return s.filter((e,s,t)=>!!e&&t.indexOf(e)===s).join(" ")};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=(0,r.forwardRef)((e,s)=>{let{color:t="currentColor",size:l=24,strokeWidth:n=2,absoluteStrokeWidth:d,className:c="",children:o,iconNode:m,...x}=e;return(0,r.createElement)("svg",{ref:s,...i,width:l,height:l,stroke:t,strokeWidth:d?24*Number(n)/Number(l):n,className:a("lucide",c),...x},[...m.map(e=>{let[s,t]=e;return(0,r.createElement)(s,t)}),...Array.isArray(o)?o:[o]])}),d=(e,s)=>{let t=(0,r.forwardRef)((t,i)=>{let{className:d,...c}=t;return(0,r.createElement)(n,{ref:i,iconNode:s,className:a("lucide-".concat(l(e)),d),...c})});return t.displayName="".concat(e),t}},8474:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(7401).A)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},9426:(e,s,t)=>{"use strict";t.d(s,{AB:()=>c,J5:()=>o,Qp:()=>d,tH:()=>u,tJ:()=>x,w1:()=>m});var r=t(5155),l=t(2115),a=t(2317),i=t(6967),n=(t(4858),t(9602));let d=l.forwardRef((e,s)=>{let{...t}=e;return(0,r.jsx)("nav",{ref:s,"aria-label":"breadcrumb",...t})});d.displayName="Breadcrumb";let c=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,r.jsx)("ol",{ref:s,className:(0,n.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",t),...l})});c.displayName="BreadcrumbList";let o=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,r.jsx)("li",{ref:s,className:(0,n.cn)("inline-flex items-center gap-1.5",t),...l})});o.displayName="BreadcrumbItem";let m=l.forwardRef((e,s)=>{let{asChild:t,className:l,...i}=e,d=t?a.DX:"a";return(0,r.jsx)(d,{ref:s,className:(0,n.cn)("transition-colors hover:text-foreground",l),...i})});m.displayName="BreadcrumbLink";let x=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,r.jsx)("span",{ref:s,role:"link","aria-disabled":"true","aria-current":"page",className:(0,n.cn)("font-normal text-foreground",t),...l})});x.displayName="BreadcrumbPage";let u=e=>{let{children:s,className:t,...l}=e;return(0,r.jsx)("li",{role:"presentation","aria-hidden":"true",className:(0,n.cn)("[&>svg]:size-3.5",t),...l,children:null!=s?s:(0,r.jsx)(i.A,{})})};u.displayName="BreadcrumbSeparator"}},e=>{var s=s=>e(e.s=s);e.O(0,[8320,1345,2651,3414,8441,6587,7358],()=>s(5887)),_N_E=e.O()}]);