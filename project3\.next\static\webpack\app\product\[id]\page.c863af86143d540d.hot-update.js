"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/product/[id]/page",{

/***/ "(app-pages-browser)/./app/product/[id]/product-details-client.tsx":
/*!*****************************************************!*\
  !*** ./app/product/[id]/product-details-client.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/breadcrumb */ \"(app-pages-browser)/./components/ui/breadcrumb.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _contexts_cart_context__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/contexts/cart-context */ \"(app-pages-browser)/./contexts/cart-context.tsx\");\n/* harmony import */ var _contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/contexts/wishlist-context */ \"(app-pages-browser)/./contexts/wishlist-context.tsx\");\n/* harmony import */ var _contexts_currency_context__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/contexts/currency-context */ \"(app-pages-browser)/./contexts/currency-context.tsx\");\n/* harmony import */ var _components_products_product_media_gallery__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/products/product-media-gallery */ \"(app-pages-browser)/./components/products/product-media-gallery.tsx\");\n/* harmony import */ var _product_loading__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./product-loading */ \"(app-pages-browser)/./app/product/[id]/product-loading.tsx\");\n/* harmony import */ var _product_error__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./product-error */ \"(app-pages-browser)/./app/product/[id]/product-error.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper function to construct image URL\nconst constructImageUrl = (attachmentUrl)=>{\n    if (!attachmentUrl) return \"/placeholder.svg?height=400&width=400\";\n    if (attachmentUrl.startsWith(\"http\")) {\n        return attachmentUrl;\n    }\n    const baseUrl = \"https://admin.codemedicalapps.com\";\n    const normalizedAttachmentUrl = attachmentUrl.startsWith(\"/\") ? attachmentUrl : \"/\".concat(attachmentUrl);\n    return \"\".concat(baseUrl).concat(normalizedAttachmentUrl);\n};\nfunction ProductDetailsClient(param) {\n    let { productId } = param;\n    var _product_Rating;\n    _s();\n    const cart = (0,_contexts_cart_context__WEBPACK_IMPORTED_MODULE_8__.useCart)();\n    const wishlist = (0,_contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_9__.useWishlist)();\n    const { rate } = (0,_contexts_currency_context__WEBPACK_IMPORTED_MODULE_10__.useCurrency)();\n    const [product, setProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [activeImage, setActiveImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [videoLinks, setVideoLinks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedVideoIndex, setSelectedVideoIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [addingToCart, setAddingToCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [addingToWishlist, setAddingToWishlist] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"description\");\n    const [isAnimating, setIsAnimating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [animationType, setAnimationType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedAttributes, setSelectedAttributes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"ProductDetailsClient.useState\": ()=>{\n            // Initialize with first option selected for each attribute if none selected\n            const initial = {};\n            if (product === null || product === void 0 ? void 0 : product.AttributesJson) {\n                product.AttributesJson.forEach({\n                    \"ProductDetailsClient.useState\": (attr)=>{\n                        const key = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n                        initial[key] = true; // Select first option by default\n                    }\n                }[\"ProductDetailsClient.useState\"]);\n            }\n            return initial;\n        }\n    }[\"ProductDetailsClient.useState\"]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductDetailsClient.useEffect\": ()=>{\n            fetchProduct();\n        }\n    }[\"ProductDetailsClient.useEffect\"], [\n        productId\n    ]);\n    const fetchProduct = async ()=>{\n        setLoading(true);\n        setError(null);\n        try {\n            // Try direct API call first, then fallback to proxy if CORS issues\n            const requestBody = {\n                requestParameters: {\n                    ProductId: Number.parseInt(productId, 10),\n                    recordValueJson: \"[]\"\n                }\n            };\n            console.log(\"Fetching product with ID:\", productId, \"Request body:\", requestBody);\n            let response;\n            try {\n                // Try direct API call first\n                response = await axios__WEBPACK_IMPORTED_MODULE_14__[\"default\"].post(\"https://admin.codemedicalapps.com/api/v1/dynamic/dataoperation/get-product_detail\", requestBody, {\n                    headers: {\n                        Accept: \"application/json\",\n                        \"Content-Type\": \"application/json\"\n                    }\n                });\n                console.log(\"Direct API response:\", response.data);\n            } catch (directError) {\n                console.log(\"Direct API failed, trying proxy route:\", directError);\n                // Fallback to proxy route\n                response = await axios__WEBPACK_IMPORTED_MODULE_14__[\"default\"].post(\"/api/product-detail\", requestBody, {\n                    headers: {\n                        Accept: \"application/json\",\n                        \"Content-Type\": \"application/json\"\n                    }\n                });\n                console.log(\"Proxy API response:\", response.data);\n            }\n            if (response.data) {\n                // Check if response.data has a nested data property (from API proxy)\n                const responseData = response.data.data ? response.data : response.data;\n                if (responseData && responseData.data) {\n                    try {\n                        // Parse the response data\n                        const parsedData = JSON.parse(responseData.data);\n                        console.log(\"Parsed product data:\", parsedData);\n                        if (parsedData) {\n                            // The API might return an array with one item or a single object\n                            const productData = Array.isArray(parsedData) ? parsedData[0] : parsedData;\n                            if (productData) {\n                                // Ensure AttributesJson is properly parsed if it's a string\n                                if (productData.AttributesJson && typeof productData.AttributesJson === 'string') {\n                                    try {\n                                        productData.AttributesJson = JSON.parse(productData.AttributesJson);\n                                    } catch (e) {\n                                        console.error('Error parsing AttributesJson:', e);\n                                        productData.AttributesJson = [];\n                                    }\n                                } else if (!productData.AttributesJson) {\n                                    productData.AttributesJson = [];\n                                }\n                                console.log('Product data with attributes:', productData);\n                                setProduct(productData);\n                                // Set active image\n                                if (productData.ProductImagesJson && productData.ProductImagesJson.length > 0) {\n                                    const primaryImage = productData.ProductImagesJson.find((img)=>img.IsPrimary) || productData.ProductImagesJson[0];\n                                    setActiveImage(constructImageUrl(primaryImage.AttachmentURL));\n                                }\n                                // Handle comma-separated video links\n                                if (productData.VideoLink) {\n                                    console.log(\"Video links found:\", productData.VideoLink);\n                                    const links = productData.VideoLink.split(\",\").map((link)=>link.trim());\n                                    const processedLinks = links.map((link)=>constructVideoUrl(link));\n                                    setVideoLinks(processedLinks);\n                                    setSelectedVideoIndex(0);\n                                }\n                                // Set initial quantity based on product minimum order quantity\n                                if (productData.OrderMinimumQuantity > 0) {\n                                    setQuantity(productData.OrderMinimumQuantity);\n                                }\n                            } else {\n                                console.error(\"No product data found in parsed response\");\n                                setError(\"Product with ID \".concat(productId, \" not found. Please check if this product exists.\"));\n                            }\n                        } else {\n                            console.error(\"Invalid product data format - parsedData is null/undefined\");\n                            setError(\"Invalid product data format\");\n                        }\n                    } catch (parseError) {\n                        console.error(\"Error parsing product data:\", parseError, \"Raw data:\", responseData.data);\n                        setError(\"Error parsing product data\");\n                    }\n                } else {\n                    console.error(\"No data property in API response:\", response.data);\n                    setError(\"No data in API response\");\n                }\n            } else {\n                console.error(\"Empty response from API\");\n                setError(\"Empty response from server\");\n            }\n        } catch (error) {\n            console.error(\"Error fetching product:\", error);\n            // More detailed error handling\n            if (error.response) {\n                var _error_response_data;\n                // Server responded with error status\n                console.error(\"Server error:\", error.response.status, error.response.data);\n                setError(\"Server error: \".concat(error.response.status, \" - \").concat(((_error_response_data = error.response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Unknown error'));\n            } else if (error.request) {\n                // Request was made but no response received\n                console.error(\"Network error:\", error.request);\n                setError(\"Network error - please check your connection\");\n            } else {\n                // Something else happened\n                console.error(\"Request setup error:\", error.message);\n                setError(\"Error: \".concat(error.message));\n            }\n        } finally{\n            setLoading(false);\n        }\n    };\n    const constructVideoUrl = (videoLink)=>{\n        if (!videoLink) return \"\";\n        if (videoLink.includes('youtube.com') || videoLink.includes('youtu.be')) {\n            return videoLink;\n        }\n        // For MP4 videos, use a proxy URL to handle CORS\n        if (videoLink.startsWith('http')) {\n            return \"/api/video-proxy?url=\".concat(encodeURIComponent(videoLink));\n        }\n        const baseUrl = \"https://admin.codemedicalapps.com\";\n        const normalizedVideoLink = videoLink.startsWith('/') ? videoLink : \"/\".concat(videoLink);\n        return \"/api/video-proxy?url=\".concat(encodeURIComponent(\"\".concat(baseUrl).concat(normalizedVideoLink)));\n    };\n    // Group attributes by ProductAttributeID\n    const groupedAttributes = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductDetailsClient.useMemo[groupedAttributes]\": ()=>{\n            if (!(product === null || product === void 0 ? void 0 : product.AttributesJson)) return {};\n            return product.AttributesJson.reduce({\n                \"ProductDetailsClient.useMemo[groupedAttributes]\": (groups, attr)=>{\n                    const groupId = attr.ProductAttributeID;\n                    if (!groups[groupId]) {\n                        groups[groupId] = [];\n                    }\n                    groups[groupId].push(attr);\n                    return groups;\n                }\n            }[\"ProductDetailsClient.useMemo[groupedAttributes]\"], {});\n        }\n    }[\"ProductDetailsClient.useMemo[groupedAttributes]\"], [\n        product === null || product === void 0 ? void 0 : product.AttributesJson\n    ]);\n    // Handle attribute selection with conditional behavior\n    const handleAttributeChange = (attr, isChecked, isRadioGroup)=>{\n        setSelectedAttributes((prev)=>{\n            const newState = {\n                ...prev\n            };\n            const attrKey = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n            // For radio groups, uncheck all other attributes in the same group\n            if (isRadioGroup && isChecked) {\n                Object.keys(prev).forEach((key)=>{\n                    if (key.startsWith(\"\".concat(attr.ProductAttributeID, \"_\")) && key !== attrKey) {\n                        newState[key] = false;\n                    }\n                });\n            }\n            // Set the selected attribute\n            // For checkboxes, toggle the state\n            // For radio buttons, always set to true (since we already unset others if needed)\n            newState[attrKey] = isRadioGroup ? true : !prev[attrKey];\n            return newState;\n        });\n    };\n    // Render price with all price-related information\n    const renderPrice = ()=>{\n        if (!product) return null;\n        const showDiscount = product.DiscountPrice && product.DiscountPrice < product.Price;\n        const adjustedPrice = calculateAdjustedPrice();\n        const showAdjustedPrice = adjustedPrice !== product.Price && adjustedPrice !== (product.DiscountPrice || product.Price);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-baseline gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-3xl font-bold text-primary\",\n                            children: [\n                                \"$\",\n                                showDiscount ? (product.DiscountPrice || 0).toFixed(2) : adjustedPrice.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 354,\n                            columnNumber: 11\n                        }, this),\n                        showDiscount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg text-gray-400 line-through\",\n                            children: [\n                                \"$\",\n                                product.Price.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 360,\n                            columnNumber: 13\n                        }, this),\n                        showAdjustedPrice && !showDiscount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg text-gray-400 line-through\",\n                            children: [\n                                \"$\",\n                                product.Price.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 367,\n                            columnNumber: 13\n                        }, this),\n                        showDiscount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"ml-2 px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded\",\n                            children: [\n                                Math.round((product.Price - (product.DiscountPrice || 0)) / product.Price * 100),\n                                \"% OFF\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 374,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 353,\n                    columnNumber: 9\n                }, this),\n                product.PriceIQD && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-1 text-lg font-medium text-gray-600\",\n                    children: [\n                        product.PriceIQD.toLocaleString(),\n                        \" IQD\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 382,\n                    columnNumber: 11\n                }, this),\n                product.PointNo && product.PointNo > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800\",\n                        children: [\n                            \"Buy & Earn \",\n                            product.PointNo,\n                            \" $ credit\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                        lineNumber: 390,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 389,\n                    columnNumber: 11\n                }, this),\n                product.OldPrice && product.OldPrice > product.Price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-1 text-sm text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"line-through\",\n                            children: [\n                                \"$\",\n                                product.OldPrice.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 399,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"ml-2 text-green-600\",\n                            children: [\n                                Math.round((product.OldPrice - (product.DiscountPrice || product.Price)) / product.OldPrice * 100),\n                                \"% OFF\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 400,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 398,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n            lineNumber: 351,\n            columnNumber: 7\n        }, this);\n    };\n    // Calculate adjusted price based on selected attributes\n    const calculateAdjustedPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ProductDetailsClient.useCallback[calculateAdjustedPrice]\": ()=>{\n            if (!product) return 0;\n            let adjustedPrice = product.Price;\n            if (product.AttributesJson && product.AttributesJson.length > 0) {\n                product.AttributesJson.forEach({\n                    \"ProductDetailsClient.useCallback[calculateAdjustedPrice]\": (attr)=>{\n                        const attrKey = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n                        if (selectedAttributes[attrKey] && typeof attr.PriceAdjustment === 'number' && typeof attr.PriceAdjustmentType === 'number') {\n                            switch(attr.PriceAdjustmentType){\n                                case 1:\n                                    adjustedPrice += attr.PriceAdjustment;\n                                    break;\n                                case 2:\n                                    adjustedPrice += product.Price * attr.PriceAdjustment / 100;\n                                    break;\n                            }\n                        }\n                    }\n                }[\"ProductDetailsClient.useCallback[calculateAdjustedPrice]\"]);\n            }\n            return Math.max(0, adjustedPrice); // Ensure price doesn't go below 0\n        }\n    }[\"ProductDetailsClient.useCallback[calculateAdjustedPrice]\"], [\n        product,\n        selectedAttributes\n    ]);\n    const adjustedPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductDetailsClient.useMemo[adjustedPrice]\": ()=>calculateAdjustedPrice()\n    }[\"ProductDetailsClient.useMemo[adjustedPrice]\"], [\n        calculateAdjustedPrice\n    ]);\n    // Render product badges\n    const renderBadges = ()=>{\n        if (!product) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"absolute top-4 left-4 z-10 flex flex-col gap-2\",\n            children: [\n                product.IsDiscountAllowed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    className: \"bg-red-500 hover:bg-red-600 text-white text-sm font-bold px-3 py-1\",\n                    children: \"SALE\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 446,\n                    columnNumber: 11\n                }, this),\n                product.MarkAsNew && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    className: \"bg-green-500 hover:bg-green-600 text-white text-sm font-bold px-3 py-1\",\n                    children: \"NEW\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 451,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n            lineNumber: 444,\n            columnNumber: 7\n        }, this);\n    };\n    // Combine images and videos into a single media array for the gallery\n    const mediaItems = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductDetailsClient.useMemo[mediaItems]\": ()=>{\n            var _product_ProductImagesJson;\n            const items = [];\n            // Add product images\n            if (product === null || product === void 0 ? void 0 : (_product_ProductImagesJson = product.ProductImagesJson) === null || _product_ProductImagesJson === void 0 ? void 0 : _product_ProductImagesJson.length) {\n                product.ProductImagesJson.forEach({\n                    \"ProductDetailsClient.useMemo[mediaItems]\": (img)=>{\n                        items.push({\n                            type: 'image',\n                            url: constructImageUrl(img.AttachmentURL),\n                            alt: (product === null || product === void 0 ? void 0 : product.ProductName) || 'Product image',\n                            thumbnail: constructImageUrl(img.AttachmentURL)\n                        });\n                    }\n                }[\"ProductDetailsClient.useMemo[mediaItems]\"]);\n            }\n            // Add videos\n            videoLinks.forEach({\n                \"ProductDetailsClient.useMemo[mediaItems]\": (videoUrl, index)=>{\n                    items.push({\n                        type: 'video',\n                        url: videoUrl,\n                        alt: \"\".concat((product === null || product === void 0 ? void 0 : product.ProductName) || 'Product', \" - Video \").concat(index + 1),\n                        thumbnail: activeImage || '' // Use the active image as video thumbnail\n                    });\n                }\n            }[\"ProductDetailsClient.useMemo[mediaItems]\"]);\n            return items;\n        }\n    }[\"ProductDetailsClient.useMemo[mediaItems]\"], [\n        product,\n        videoLinks,\n        activeImage\n    ]);\n    const animateCounter = (type)=>{\n        setAnimationType(type);\n        setIsAnimating(true);\n        setTimeout(()=>setIsAnimating(false), 300);\n    };\n    const incrementQuantity = ()=>{\n        if (product) {\n            const maxQuantity = product.OrderMaximumQuantity > 0 ? Math.min(product.OrderMaximumQuantity, product.StockQuantity) : product.StockQuantity;\n            if (quantity < maxQuantity) {\n                setQuantity((prev)=>prev + 1);\n                animateCounter('increment');\n            } else {\n                // Visual feedback when max quantity is reached\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.info(\"Maximum quantity of \".concat(maxQuantity, \" reached\"));\n            }\n        }\n    };\n    const decrementQuantity = ()=>{\n        if (product) {\n            const minQuantity = product.OrderMinimumQuantity > 0 ? product.OrderMinimumQuantity : 1;\n            if (quantity > minQuantity) {\n                setQuantity((prev)=>prev - 1);\n                animateCounter('decrement');\n            } else {\n                // Visual feedback when min quantity is reached\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.info(\"Minimum quantity is \".concat(minQuantity));\n            }\n        }\n    };\n    // Dynamic button styles based on state\n    const getButtonStyles = (type)=>{\n        const baseStyles = 'flex items-center justify-center w-10 h-10 rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';\n        const disabledStyles = 'bg-gray-100 text-gray-400 cursor-not-allowed';\n        if (type === 'increment') {\n            const isMax = product && quantity >= (product.OrderMaximumQuantity > 0 ? Math.min(product.OrderMaximumQuantity, product.StockQuantity) : product.StockQuantity);\n            return \"\".concat(baseStyles, \" \").concat(isMax ? disabledStyles : 'bg-primary text-white hover:bg-primary/90 focus:ring-primary/50');\n        } else {\n            const isMin = product && quantity <= (product.OrderMinimumQuantity > 0 ? product.OrderMinimumQuantity : 1);\n            return \"\".concat(baseStyles, \" \").concat(isMin ? disabledStyles : 'bg-primary text-white hover:bg-primary/90 focus:ring-primary/50');\n        }\n    };\n    // Counter display with animation\n    const CounterDisplay = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative flex items-center justify-center w-16\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-lg font-medium transition-all duration-200 \".concat(isAnimating ? 'scale-125 text-primary' : 'scale-100'),\n                    children: quantity\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 541,\n                    columnNumber: 7\n                }, this),\n                isAnimating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"absolute text-xs font-bold text-primary transition-all duration-200 \".concat(animationType === 'increment' ? '-top-6' : 'top-6'),\n                    children: animationType === 'increment' ? '+1' : '-1'\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 549,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n            lineNumber: 540,\n            columnNumber: 5\n        }, this);\n    // Early return if product is not loaded yet\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_product_loading__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n            lineNumber: 562,\n            columnNumber: 12\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_product_error__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n            error: error,\n            retry: fetchProduct\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n            lineNumber: 566,\n            columnNumber: 12\n        }, this);\n    }\n    if (!product) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold mb-4\",\n                    children: \"Product Not Found\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 572,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mb-6\",\n                    children: [\n                        'The product with ID \"',\n                        productId,\n                        '\" could not be found. It may not exist in the database.'\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 573,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/products\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 577,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"View All Products\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 576,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 575,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500\",\n                            children: \"Check the products list to find available product IDs\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 581,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 574,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n            lineNumber: 571,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto py-8 px-4 w-full max-w-[1200px] overflow-x-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.Breadcrumb, {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbList, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbLink, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    children: \"Home\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 596,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 595,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 594,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbSeparator, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 599,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbLink, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/products\",\n                                    children: \"Products\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 602,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 601,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 600,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbSeparator, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 605,\n                            columnNumber: 11\n                        }, this),\n                        product.CategoryName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbItem, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbLink, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/products/category/\".concat(product.CategoryID),\n                                            children: product.CategoryName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                            lineNumber: 610,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 609,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 608,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbSeparator, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 613,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbPage, {\n                                children: product.ProductName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 617,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 616,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 593,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                lineNumber: 592,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col lg:flex-row gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:w-1/2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_products_product_media_gallery__WEBPACK_IMPORTED_MODULE_11__.ProductMediaGallery, {\n                            media: mediaItems,\n                            className: \"w-full rounded-lg overflow-hidden\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 625,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                        lineNumber: 624,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:w-1/2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-bold mb-2\",\n                                children: product.ProductName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 633,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex\",\n                                        children: [\n                                            ...Array(5)\n                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-4 h-4 \".concat(i < Math.floor(product.Rating || 0) ? \"text-yellow-400 fill-yellow-400\" : \"text-gray-300\")\n                                            }, i, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 639,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 637,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-500 ml-2\",\n                                        children: [\n                                            \"(\",\n                                            product.Rating || 0,\n                                            \") \",\n                                            product.TotalReviews || 0,\n                                            \" reviews\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 649,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 636,\n                                columnNumber: 11\n                            }, this),\n                            renderPrice(),\n                            product.ShortDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"prose prose-sm max-w-none mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: product.ShortDescription\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 660,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 659,\n                                columnNumber: 13\n                            }, this),\n                            product.DisplayStockQuantity && product.StockQuantity > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: [\n                                        product.StockQuantity,\n                                        \" available\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 667,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 666,\n                                columnNumber: 13\n                            }, this),\n                            product.StockQuantity <= 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-red-600\",\n                                    children: \"Out of Stock\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 672,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 671,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6 border-t border-gray-200 pt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                        children: \"Product Details\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 678,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mb-4\",\n                                        children: \"Choose your preferences from the options below.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 679,\n                                        columnNumber: 13\n                                    }, this),\n                                    Object.entries(groupedAttributes).length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: Object.entries(groupedAttributes).map((param)=>{\n                                            let [groupId, attributes] = param;\n                                            var _attributes_, _attributes_1;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: [\n                                                            ((_attributes_ = attributes[0]) === null || _attributes_ === void 0 ? void 0 : _attributes_.DisplayName) || ((_attributes_1 = attributes[0]) === null || _attributes_1 === void 0 ? void 0 : _attributes_1.AttributeName),\n                                                            \":\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 684,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2 pl-4\",\n                                                        children: attributes.map((attr)=>{\n                                                            const attrKey = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n                                                            const isSelected = !!selectedAttributes[attrKey];\n                                                            const isRadioGroup = attributes.length > 1;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center h-5\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: isRadioGroup ? \"radio\" : \"checkbox\",\n                                                                            id: \"attr-\".concat(attrKey),\n                                                                            name: \"attr-group-\".concat(groupId),\n                                                                            className: \"h-4 w-4 \".concat(isRadioGroup ? 'rounded-full' : 'rounded', \" border-gray-300 text-primary focus:ring-primary\"),\n                                                                            checked: isSelected,\n                                                                            onChange: (e)=>handleAttributeChange(attr, e.target.checked, isRadioGroup)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                            lineNumber: 696,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 695,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"ml-3 text-sm\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            htmlFor: \"attr-\".concat(attrKey),\n                                                                            className: \"font-medium \".concat(isSelected ? 'text-primary' : 'text-gray-700'),\n                                                                            children: [\n                                                                                attr.AttributeValueText,\n                                                                                (attr.PriceAdjustment || attr.PriceAdjustment === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"ml-2 text-sm font-normal text-green-600\",\n                                                                                    children: [\n                                                                                        \"(\",\n                                                                                        attr.PriceAdjustmentType === 1 ? '+' : '',\n                                                                                        \"$\",\n                                                                                        attr.PriceAdjustment,\n                                                                                        \" \",\n                                                                                        attr.PriceAdjustmentType === 2 ? '%' : '',\n                                                                                        \")\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                                    lineNumber: 712,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                            lineNumber: 706,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 705,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, attrKey, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 694,\n                                                                columnNumber: 27\n                                                            }, this);\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 687,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, \"attr-group-\".concat(groupId), true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 683,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 681,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"No additional product details available.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 726,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 677,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"mr-4 text-sm font-medium\",\n                                                children: \"Quantity:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 733,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: decrementQuantity,\n                                                        className: getButtonStyles('decrement'),\n                                                        disabled: quantity <= (product.OrderMinimumQuantity || 1),\n                                                        \"aria-label\": \"Decrease quantity\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-5 w-5\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 742,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 741,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 735,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CounterDisplay, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 746,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: incrementQuantity,\n                                                        className: getButtonStyles('increment'),\n                                                        disabled: product.OrderMaximumQuantity > 0 ? quantity >= Math.min(product.OrderMaximumQuantity, product.StockQuantity) : quantity >= product.StockQuantity,\n                                                        \"aria-label\": \"Increase quantity\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-5 w-5\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 759,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 758,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 748,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 734,\n                                                columnNumber: 15\n                                            }, this),\n                                            product.OrderMinimumQuantity > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-3 text-xs text-gray-500\",\n                                                children: [\n                                                    \"Min: \",\n                                                    product.OrderMinimumQuantity\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 765,\n                                                columnNumber: 17\n                                            }, this),\n                                            product.OrderMaximumQuantity > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-3 text-xs text-gray-500\",\n                                                children: [\n                                                    \"Max: \",\n                                                    Math.min(product.OrderMaximumQuantity, product.StockQuantity)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 771,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 732,\n                                        columnNumber: 13\n                                    }, this),\n                                    product.DisplayStockQuantity && product.StockQuantity > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 w-full bg-gray-200 rounded-full h-2.5\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-500 h-2.5 rounded-full transition-all duration-500 ease-out\",\n                                            style: {\n                                                width: \"\".concat(Math.min(100, quantity / product.StockQuantity * 100), \"%\"),\n                                                backgroundColor: quantity > product.StockQuantity * 0.8 ? '#ef4444' : '#10b981'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                            lineNumber: 780,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 779,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 731,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"flex-1 flex items-center justify-center gap-2 py-3 px-4 rounded-md bg-primary text-white hover:bg-primary/90 disabled:opacity-50 disabled:pointer-events-none\",\n                                        disabled: product.StockQuantity <= 0 || addingToCart,\n                                        onClick: ()=>{\n                                            if (!product) return;\n                                            setAddingToCart(true);\n                                            try {\n                                                // Get the first product image or use a placeholder\n                                                const productImage = product.ProductImagesJson && product.ProductImagesJson.length > 0 ? constructImageUrl(product.ProductImagesJson[0].AttachmentURL) : '/placeholder.jpg';\n                                                // Get selected attributes\n                                                const selectedAttrs = (product.AttributesJson || []).filter((attr)=>selectedAttributes[\"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID)]);\n                                                // Add to cart using the cart context with attributes and adjusted price\n                                                cart.addToCart({\n                                                    id: product.ProductId,\n                                                    name: product.ProductName,\n                                                    price: product.DiscountPrice || product.Price,\n                                                    discountPrice: product.DiscountPrice,\n                                                    image: productImage,\n                                                    originalPrice: product.Price\n                                                }, quantity, selectedAttrs, product.PriceIQD, rate // Pass currency rate as the fifth parameter\n                                                );\n                                                // Show success toast\n                                                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"\".concat(quantity, \" \\xd7 \").concat(product.ProductName, \" added to your cart\"));\n                                            } catch (error) {\n                                                console.error('Error adding to cart:', error);\n                                                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Failed to add product to cart. Please try again.\");\n                                            } finally{\n                                                setAddingToCart(false);\n                                            }\n                                        },\n                                        children: [\n                                            addingToCart ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-5 w-5 border-2 border-current border-t-transparent rounded-full animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 840,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 842,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: addingToCart ? \"Adding...\" : \"Add to Cart\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 844,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 794,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"flex items-center justify-center gap-2 py-3 px-4 rounded-md border border-input bg-transparent hover:bg-accent hover:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none\",\n                                        disabled: addingToWishlist,\n                                        onClick: ()=>{\n                                            if (!product) return;\n                                            setAddingToWishlist(true);\n                                            try {\n                                                // Check if product is already in wishlist\n                                                const isAlreadyInWishlist = wishlist.isInWishlist(product.ProductId);\n                                                if (isAlreadyInWishlist) {\n                                                    // Remove from wishlist if already there\n                                                    wishlist.removeFromWishlist(product.ProductId);\n                                                    sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"\".concat(product.ProductName, \" removed from wishlist\"));\n                                                } else {\n                                                    // Add to wishlist\n                                                    wishlist.addToWishlist(product.ProductId);\n                                                    sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"\".concat(product.ProductName, \" added to wishlist\"));\n                                                }\n                                            } catch (error) {\n                                                console.error('Error updating wishlist:', error);\n                                                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error('Failed to update wishlist. Please try again.');\n                                            } finally{\n                                                setAddingToWishlist(false);\n                                            }\n                                        },\n                                        children: [\n                                            addingToWishlist ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-5 w-5 border-2 border-current border-t-transparent rounded-full animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 878,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-5 w-5\",\n                                                fill: product && wishlist.isInWishlist(product.ProductId) ? \"currentColor\" : \"none\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 880,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only md:not-sr-only md:inline\",\n                                                children: addingToWishlist ? \"Updating...\" : product && wishlist.isInWishlist(product.ProductId) ? \"Remove\" : \"Wishlist\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 885,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 848,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"flex items-center justify-center gap-2 py-3 px-4 rounded-md border border-input bg-transparent hover:bg-accent hover:text-accent-foreground\",\n                                        onClick: ()=>{\n                                            if (navigator.share) {\n                                                navigator.share({\n                                                    title: (product === null || product === void 0 ? void 0 : product.MetaTitle) || (product === null || product === void 0 ? void 0 : product.ProductName),\n                                                    text: (product === null || product === void 0 ? void 0 : product.MetaDescription) || \"Check out this product: \".concat(product === null || product === void 0 ? void 0 : product.ProductName),\n                                                    url: window.location.href\n                                                }).catch((err)=>console.error(\"Error sharing:\", err));\n                                            } else {\n                                                // Fallback - copy to clipboard\n                                                navigator.clipboard.writeText(window.location.href);\n                                                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Product link copied to clipboard\");\n                                            }\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 911,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only md:not-sr-only md:inline\",\n                                                children: \"Share\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 912,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 892,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 792,\n                                columnNumber: 11\n                            }, this),\n                            product.MetaKeywords && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium text-gray-900 mb-3\",\n                                        children: \"Product Tags\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 919,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: product.MetaKeywords.split(\",\").map((keyword, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                variant: \"secondary\",\n                                                className: \"text-xs bg-white/70 hover:bg-white transition-colors\",\n                                                children: keyword.trim()\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 922,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 920,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 918,\n                                columnNumber: 13\n                            }, this),\n                            product.MetaDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 p-6 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border border-green-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium text-gray-900 mb-3 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"h-5 w-5 text-green-600 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 934,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"About This Product\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 933,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-700 leading-relaxed\",\n                                        children: product.MetaDescription\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 937,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 932,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                        lineNumber: 632,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                lineNumber: 622,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.Tabs, {\n                    defaultValue: \"description\",\n                    className: \"w-full\",\n                    value: activeTab,\n                    onValueChange: setActiveTab,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsList, {\n                            className: \"grid w-full grid-cols-3 mb-6 gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                    value: \"description\",\n                                    className: \"shadow-sm hover:shadow transition-shadow\",\n                                    children: \"Description\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 953,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                    value: \"reviews\",\n                                    className: \"shadow-sm hover:shadow transition-shadow\",\n                                    children: \"Reviews\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 954,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                    value: \"shipping\",\n                                    className: \"shadow-sm hover:shadow transition-shadow\",\n                                    children: \"Shipping & Returns\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 955,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 952,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                            value: \"description\",\n                            className: \"mt-4 bg-white rounded-lg shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8\",\n                                children: product.FullDescription ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"prose max-w-none\",\n                                    dangerouslySetInnerHTML: {\n                                        __html: product.FullDescription\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 961,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 italic\",\n                                    children: \"No description available for this product.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 966,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 959,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 958,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                            value: \"reviews\",\n                            className: \"mt-4 bg-white rounded-lg shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row sm:items-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        1,\n                                                        2,\n                                                        3,\n                                                        4,\n                                                        5\n                                                    ].map((star)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-6 h-6 \".concat(star <= Math.floor(product.Rating || 0) ? \"text-yellow-400 fill-yellow-400\" : \"text-gray-300\")\n                                                        }, star, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 977,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                    lineNumber: 975,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: ((_product_Rating = product.Rating) === null || _product_Rating === void 0 ? void 0 : _product_Rating.toFixed(1)) || '0.0'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 988,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" out of 5\",\n                                                        product.TotalReviews ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \" • \",\n                                                                product.TotalReviews,\n                                                                \" review\",\n                                                                product.TotalReviews !== 1 ? 's' : ''\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 990,\n                                                            columnNumber: 23\n                                                        }, this) : null\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                    lineNumber: 987,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                            lineNumber: 974,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 p-4 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium mb-2\",\n                                                    children: \"Customer Reviews\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                    lineNumber: 996,\n                                                    columnNumber: 19\n                                                }, this),\n                                                product.TotalReviews ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-8 text-gray-500\",\n                                                        children: \"Reviews will be displayed here\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 1000,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                    lineNumber: 998,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-500 mb-4\",\n                                                            children: \"No reviews yet\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 1006,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            children: \"Be the first to review\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 1007,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                    lineNumber: 1005,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                            lineNumber: 995,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 973,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 972,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 971,\n                            columnNumber: 1\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                            value: \"shipping\",\n                            className: \"mt-4 bg-white rounded-lg shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Shipping and delivery information will be provided during checkout based on your location.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                            lineNumber: 1020,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 border rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-6 w-6 text-primary mb-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 1023,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium mb-1\",\n                                                            children: \"Fast Delivery\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 1024,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: [\n                                                                \"Estimated delivery time: \",\n                                                                product.EstimatedShippingDays || '3-5',\n                                                                \" business days\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 1025,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                    lineNumber: 1022,\n                                                    columnNumber: 19\n                                                }, this),\n                                                product.IsReturnAble && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 border rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            className: \"h-6 w-6 text-primary mb-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 1029,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium mb-1\",\n                                                            children: \"Easy Returns\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 1030,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"Hassle-free returns within 30 days\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 1031,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                    lineNumber: 1028,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                            lineNumber: 1021,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 1019,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 1018,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 1017,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 946,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                lineNumber: 945,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n        lineNumber: 590,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductDetailsClient, \"v8S2esSUAPWpffHNwAOg155SZtw=\", false, function() {\n    return [\n        _contexts_cart_context__WEBPACK_IMPORTED_MODULE_8__.useCart,\n        _contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_9__.useWishlist,\n        _contexts_currency_context__WEBPACK_IMPORTED_MODULE_10__.useCurrency\n    ];\n});\n_c = ProductDetailsClient;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProductDetailsClient);\nvar _c;\n$RefreshReg$(_c, \"ProductDetailsClient\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/product/[id]/product-details-client.tsx\n"));

/***/ })

});