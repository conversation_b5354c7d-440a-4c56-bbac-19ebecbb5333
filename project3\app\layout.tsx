'use client';

import './globals.css';
import React from 'react';
import { Inter } from 'next/font/google';
import { Header } from '@/components/ui/header';
import { Footer } from '@/components/ui/footer';
import { WhatsAppButton } from '@/components/ui/whatsapp-button';
import { MobileBottomNav } from '@/components/ui/mobile-bottom-nav';
import { ToastProvider } from '@/components/ui/toast'; // Updated import
import { Providers } from '@/components/providers';
import { GoogleReCaptchaProvider } from 'react-google-recaptcha-v3';
import { Toaster as SonnerToaster } from 'sonner';

const inter = Inter({ subsets: ['latin'] });



export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={inter.className} suppressHydrationWarning>
        <GoogleReCaptchaProvider
          reCaptchaKey={process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY || '6LfS7D0rAAAAAAybL-FSr2N1pWStAiKxN_EapgHJ'}
          scriptProps={{
            async: true,
            defer: true,
            appendTo: 'body',
            nonce: undefined,
          }}
          container={{
            parameters: {
              badge: 'inline',
              theme: 'light'
            }
          }}
        >
          <Providers>
            <ToastProvider> {/* Added ToastProvider */}
              <Header />
              <main className="min-h-screen pb-16 md:pb-0">
                {children}
              </main>
              <Footer />
              <WhatsAppButton />
              <MobileBottomNav />
              {/* <Toaster /> Old Toaster removed */}
              <SonnerToaster position="top-right" />
            </ToastProvider> {/* Closing ToastProvider */}
          </Providers>
        </GoogleReCaptchaProvider>
      </body>
    </html>
  );
}