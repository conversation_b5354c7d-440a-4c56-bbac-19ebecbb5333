@model Entities.MainModels.UserManagementModel

@{
    ViewData["Title"] = "Reset User Password";
    ViewData["EntityId"] = Model?.PageBasicInfoObj?.EntityId ?? 0;
}

<!-- Page header -->
@{
    PageHeader pageHeader = new PageHeader
    {
        PageTitle = "Reset User Password",
        ShowAddNewButton = false,
        ShowActionsButton = false,
        ShowExportToPdfButton = false,
        ShowExportToExcelButton = false,
        ShowGoBackButton = true,
    };
}
@await Html.PartialAsync("~/Views/Common/_PageHeader.cshtml", pageHeader)
<!-- /page header -->

<div class="content">
    <!-- Error Area -->
    <div id="error-messages-area">
        @{
            SuccessErrorMsgEntity? successErrorMsgEntity = new SuccessErrorMsgEntity();
            successErrorMsgEntity = Model.SuccessErrorMsgEntityObj == null ? new SuccessErrorMsgEntity() : Model.SuccessErrorMsgEntityObj;
        }

        @await Html.PartialAsync("~/Views/Common/_SuccessErrorMsg.cshtml", successErrorMsgEntity)
    </div>
    <!-- /Error Area -->

    <form class="form-validate-jquery" id="password-change-form" action="@Url.Action("UpdatePassword", "UserManagement", new { langCode = Model?.PageBasicInfoObj?.langCode })" method="post">
        <div class="card border-left-3 border-left-slate">
            <div class="card-header header-elements-inline">
                <h6 class="card-title">Reset User Password</h6>
                <div class="header-elements">
                    <div class="list-icons">
                        <a class="list-icons-item" data-action="collapse"></a>
                    </div>
                </div>
            </div>

            <div class="card-body">
                <fieldset class="mb-3">
                    <input type="hidden" id="UserId" name="UserId" value="@(Model.UserEntityObj.UserId)" />

                    <div class="alert alert-info">
                        <h6><i class="icon-info22"></i> Admin Password Reset</h6>
                        <p>As an administrator, you can reset this user's password without requiring their current password. The user will be able to log in with the new password immediately.</p>
                    </div>

                    <div class="form-group row">
                        <label class="col-form-label col-lg-3">
                            <span>User Information</span>
                        </label>
                        <div class="col-lg-9">
                            <div class="card bg-light">
                                <div class="card-body p-3">
                                    <strong>Username:</strong> @Model.UserEntityObj.UserName<br>
                                    <strong>Email:</strong> @Model.UserEntityObj.EmailAddress<br>
                                    <strong>User ID:</strong> @Model.UserEntityObj.UserId
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label class="col-form-label col-lg-3">
                            <span>New Password</span>
                            <span class="text-danger">*</span>
                        </label>
                        <div class="col-lg-9">
                            <input type="password" name="NewPassword" id="NewPassword" class="form-control" required placeholder="Enter new password">
                            <small class="form-text text-muted">Password must be at least 6 characters long</small>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label class="col-form-label col-lg-3">
                            <span>Confirm New Password</span>
                            <span class="text-danger">*</span>
                        </label>
                        <div class="col-lg-9">
                            <input type="password" name="ConfirmNewPassword" id="ConfirmNewPassword" class="form-control" required placeholder="Confirm new password">
                        </div>
                    </div>

                    <div class="text-right">
                        <button type="submit" class="btn btn-primary">Reset Password<i class="icon-paperplane ml-2"></i></button>
                    </div>
                </fieldset>
            </div>
        </div>
    </form>
</div>

<script src="~/js/jquery-validation-unobtrusive/jquery.validate.min.js"></script>
<script src="~/js/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
    $(document).ready(function() {
        // Form validation
        $('#password-change-form').validate({
            rules: {
                NewPassword: {
                    required: true,
                    minlength: 6
                },
                ConfirmNewPassword: {
                    required: true,
                    equalTo: '#NewPassword'
                }
            },
            messages: {
                NewPassword: {
                    required: 'Please enter a new password',
                    minlength: 'Password must be at least 6 characters long'
                },
                ConfirmNewPassword: {
                    required: 'Please confirm your new password',
                    equalTo: 'Passwords do not match'
                }
            },
            errorElement: 'span',
            errorClass: 'validation-invalid-label',
            highlight: function(element, errorClass) {
                $(element).addClass('is-invalid');
            },
            unhighlight: function(element, errorClass) {
                $(element).removeClass('is-invalid');
            },
            errorPlacement: function(error, element) {
                error.insertAfter(element);
            }
        });

        // Form submission
        $("#password-change-form").submit(function(e) {
            e.preventDefault();

            if (!$(this).valid()) {
                return false;
            }

            var userId = $("#UserId").val();
            var newPassword = $("#NewPassword").val();
            var confirmNewPassword = $("#ConfirmNewPassword").val();

            // Form data
            var formData = {
                UserId: userId,
                NewPassword: newPassword,
                ConfirmPassword: confirmNewPassword
            };

            // AJAX submission
            $.ajax({
                type: "POST",
                url: "@Url.Action("UpdatePassword", "UserManagement", new { langCode = Model?.PageBasicInfoObj?.langCode })",
                data: formData,
                cache: false,
                success: function(response) {
                    if (response.success) {
                        // Show success message
                        Swal.fire({
                            title: "Password Reset Successful",
                            text: "The user's password has been reset successfully. They can now log in with the new password.",
                            icon: "success",
                            confirmButtonText: "OK"
                        }).then((result) => {
                            // Redirect to users list page
                            window.location.href = "@Url.Action("UsersList", "UserManagement", new { langCode = Model?.PageBasicInfoObj?.langCode })";
                        });
                    } else {
                        // Show error message
                        Swal.fire({
                            title: "Error",
                            text: response.message || "Failed to update password",
                            icon: "error",
                            confirmButtonText: "OK"
                        });
                    }
                },
                error: function(xhr, status, error) {
                    // Show error message
                    Swal.fire({
                        title: "Error",
                        text: "An error occurred while updating the password",
                        icon: "error",
                        confirmButtonText: "OK"
                    });
                }
            });
        });
    });
</script>