"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/wishlist/page",{

/***/ "(app-pages-browser)/./contexts/cart-context.tsx":
/*!***********************************!*\
  !*** ./contexts/cart-context.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CartProvider: () => (/* binding */ CartProvider),\n/* harmony export */   useCart: () => (/* binding */ useCart)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ CartProvider,useCart auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst CartContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction CartProvider(param) {\n    let { children } = param;\n    _s();\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isHydrated, setIsHydrated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Load cart from localStorage on initial render\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CartProvider.useEffect\": ()=>{\n            const savedCart = localStorage.getItem('cart');\n            if (savedCart) {\n                try {\n                    setItems(JSON.parse(savedCart));\n                } catch (error) {\n                    console.error('Failed to parse cart from localStorage:', error);\n                }\n            }\n            setIsHydrated(true);\n        }\n    }[\"CartProvider.useEffect\"], []);\n    // Save cart to localStorage whenever it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CartProvider.useEffect\": ()=>{\n            localStorage.setItem('cart', JSON.stringify(items));\n        }\n    }[\"CartProvider.useEffect\"], [\n        items\n    ]);\n    const addToCart = function(item, quantity) {\n        let attributes = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : [], iqdPrice = arguments.length > 3 ? arguments[3] : void 0;\n        setItems((prevItems)=>{\n            // Start with the base price (which could be a discounted price if applicable)\n            let adjustedPrice = item.price;\n            // Apply attribute-based price adjustments\n            attributes.forEach((attr)=>{\n                if (attr.PriceAdjustment && attr.PriceAdjustmentType) {\n                    const basePriceForAdjustment = item.originalPrice || item.price;\n                    switch(attr.PriceAdjustmentType){\n                        case 1:\n                            adjustedPrice += attr.PriceAdjustment;\n                            break;\n                        case 2:\n                            adjustedPrice += basePriceForAdjustment * attr.PriceAdjustment / 100;\n                            break;\n                    }\n                }\n            });\n            // Find if item with same ID and attributes already exists\n            const existingItemIndex = prevItems.findIndex((i)=>{\n                var _i_attributes;\n                return i.id === item.id && JSON.stringify((_i_attributes = i.attributes) === null || _i_attributes === void 0 ? void 0 : _i_attributes.sort((a, b)=>a.ProductAttributeID - b.ProductAttributeID)) === JSON.stringify(attributes === null || attributes === void 0 ? void 0 : attributes.sort((a, b)=>a.ProductAttributeID - b.ProductAttributeID));\n            });\n            if (existingItemIndex >= 0) {\n                // Item with same attributes exists, update quantity\n                const updatedItems = [\n                    ...prevItems\n                ];\n                updatedItems[existingItemIndex].quantity += quantity;\n                return updatedItems;\n            } else {\n                // Add new item with all price information\n                return [\n                    ...prevItems,\n                    {\n                        ...item,\n                        iqdPrice: iqdPrice,\n                        quantity,\n                        attributes,\n                        adjustedPrice: Math.max(0, adjustedPrice),\n                        originalPrice: item.originalPrice\n                    }\n                ];\n            }\n        });\n    };\n    const removeFromCart = (id)=>{\n        setItems((prevItems)=>prevItems.filter((item)=>item.id !== id));\n    };\n    const updateQuantity = (id, quantity)=>{\n        if (quantity <= 0) {\n            removeFromCart(id);\n            return;\n        }\n        setItems((prevItems)=>prevItems.map((item)=>item.id === id ? {\n                    ...item,\n                    quantity\n                } : item));\n    };\n    const clearCart = ()=>{\n        setItems([]);\n    };\n    const totalItems = items.reduce((total, item)=>total + item.quantity, 0);\n    // Update localStorage and trigger re-render when cart changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CartProvider.useEffect\": ()=>{\n            localStorage.setItem('cart', JSON.stringify(items));\n        }\n    }[\"CartProvider.useEffect\"], [\n        items\n    ]);\n    const subtotal = items.reduce((total, item)=>{\n        const price = item.discountPrice ? Math.min(item.discountPrice, item.adjustedPrice) : item.adjustedPrice;\n        return total + price * item.quantity;\n    }, 0);\n    // For now, total is same as subtotal, but could include shipping, tax, etc.\n    const total = subtotal;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CartContext.Provider, {\n        value: {\n            items,\n            addToCart,\n            removeFromCart,\n            updateQuantity,\n            clearCart,\n            totalItems,\n            subtotal,\n            total,\n            isHydrated\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\contexts\\\\cart-context.tsx\",\n        lineNumber: 161,\n        columnNumber: 5\n    }, this);\n}\n_s(CartProvider, \"orx7hoWf+wJ/pl3ceK141eCKGB8=\");\n_c = CartProvider;\nfunction useCart() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(CartContext);\n    if (context === undefined) {\n        throw new Error('useCart must be used within a CartProvider');\n    }\n    return context;\n}\n_s1(useCart, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"CartProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./contexts/cart-context.tsx\n"));

/***/ })

});