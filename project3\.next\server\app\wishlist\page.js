(()=>{var e={};e.id=720,e.ids=[720],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},14494:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(41680).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},21956:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(41680).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37778:(e,r,s)=>{"use strict";s.d(r,{AB:()=>d,J5:()=>n,Qp:()=>c,tH:()=>u,tJ:()=>m,w1:()=>p});var t=s(45512),a=s(58009),i=s(12705),l=s(99905),o=(s(14494),s(59462));let c=a.forwardRef(({...e},r)=>(0,t.jsx)("nav",{ref:r,"aria-label":"breadcrumb",...e}));c.displayName="Breadcrumb";let d=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("ol",{ref:s,className:(0,o.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",e),...r}));d.displayName="BreadcrumbList";let n=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("li",{ref:s,className:(0,o.cn)("inline-flex items-center gap-1.5",e),...r}));n.displayName="BreadcrumbItem";let p=a.forwardRef(({asChild:e,className:r,...s},a)=>{let l=e?i.DX:"a";return(0,t.jsx)(l,{ref:a,className:(0,o.cn)("transition-colors hover:text-foreground",r),...s})});p.displayName="BreadcrumbLink";let m=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("span",{ref:s,role:"link","aria-disabled":"true","aria-current":"page",className:(0,o.cn)("font-normal text-foreground",e),...r}));m.displayName="BreadcrumbPage";let u=({children:e,className:r,...s})=>(0,t.jsx)("li",{role:"presentation","aria-hidden":"true",className:(0,o.cn)("[&>svg]:size-3.5",r),...s,children:e??(0,t.jsx)(l.A,{})});u.displayName="BreadcrumbSeparator"},44048:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>v});var t=s(45512),a=s(58009),i=s(37778),l=s(97643),o=s(87021),c=s(28531),d=s.n(c),n=s(71901),p=s(84194),m=s(31961);let u=(0,s(41680).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);var x=s(49656),h=s(21956),f=s(91124),g=s(10453),j=s(99905);s(85668);var y=s(91542);function v(){let{t:e}=(0,n.t)(),r=(0,p._)(),{wishlistItems:s,removeFromWishlist:c}=(0,m.n)(),[v,N]=(0,a.useState)([]),[b,w]=(0,a.useState)(!1),k=e=>{c(e),y.oR.success("Product removed from wishlist")};return b?(0,t.jsxs)("div",{className:"container mx-auto py-12 flex flex-col items-center justify-center min-h-[50vh]",children:[(0,t.jsx)(u,{className:"h-12 w-12 animate-spin text-primary mb-4"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Loading your wishlist..."})]}):(0,t.jsxs)("div",{className:"container mx-auto py-6 px-4",children:[(0,t.jsx)(i.Qp,{className:"mb-6",children:(0,t.jsxs)(i.AB,{children:[(0,t.jsx)(i.J5,{children:(0,t.jsx)(i.w1,{asChild:!0,children:(0,t.jsx)(d(),{href:"/",children:"Home"})})}),(0,t.jsx)(i.tH,{}),(0,t.jsx)(i.tJ,{children:"Wishlist"})]})}),(0,t.jsx)("h1",{className:"text-3xl font-bold mb-8",children:"Your Wishlist"}),v.length>0?(0,t.jsx)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",children:v.map(e=>(0,t.jsxs)(l.Zp,{className:"overflow-hidden",children:[(0,t.jsxs)("div",{className:"relative aspect-square",children:[(0,t.jsx)("img",{src:e.image,alt:e.name,className:"w-full h-full object-cover",onError:e=>{let r=e.target;r.onerror=null,r.src="/placeholder-image.jpg"}}),(0,t.jsx)(o.$,{variant:"ghost",size:"icon",className:"absolute top-2 right-2 rounded-full bg-background/80 backdrop-blur-sm hover:bg-background/60",onClick:()=>k(e.id),children:(0,t.jsx)(x.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{className:"p-4",children:[(0,t.jsx)("h3",{className:"font-medium text-lg mb-2 line-clamp-2",children:e.name}),(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,t.jsxs)("span",{className:"text-lg font-bold",children:["$",e.price.toFixed(2)]}),e.originalPrice&&e.originalPrice>e.price&&(0,t.jsxs)("span",{className:"text-sm text-muted-foreground line-through",children:["$",e.originalPrice.toFixed(2)]})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(o.$,{variant:"outline",size:"sm",className:"flex-1",asChild:!0,children:(0,t.jsxs)(d(),{href:`/product/${e.id}`,children:[(0,t.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"View"]})}),(0,t.jsxs)(o.$,{size:"sm",className:"flex-1",disabled:!e.inStock,onClick:()=>{r.addToCart({id:e.id,name:e.name,price:e.price,discountPrice:e.originalPrice&&e.originalPrice>e.price?e.price:void 0,originalPrice:e.originalPrice||e.price,image:e.image},1,[],void 0),y.oR.success(`Added ${e.name} to cart`)},children:[(0,t.jsx)(f.A,{className:"h-4 w-4 mr-2"}),e.inStock?"Add to Cart":"Out of Stock"]})]})]})]},e.id))}):(0,t.jsxs)(l.Zp,{className:"p-8 text-center",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)(g.A,{className:"h-8 w-8 text-muted-foreground"})}),(0,t.jsx)("h3",{className:"text-lg font-medium mb-2",children:"Your wishlist is empty"}),(0,t.jsx)("p",{className:"text-muted-foreground mb-6",children:"You haven't added any products to your wishlist yet."}),(0,t.jsx)(o.$,{asChild:!0,children:(0,t.jsxs)(d(),{href:"/products",children:["Continue Shopping",(0,t.jsx)(j.A,{className:"ml-2 h-4 w-4"})]})})]})]})}},49656:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(41680).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},55591:e=>{"use strict";e.exports=require("https")},60294:(e,r,s)=>{Promise.resolve().then(s.bind(s,84276))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78446:(e,r,s)=>{Promise.resolve().then(s.bind(s,44048))},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84276:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\wishlist\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\wishlist\\page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")},97643:(e,r,s)=>{"use strict";s.d(r,{Wu:()=>o,Zp:()=>l,wL:()=>c});var t=s(45512),a=s(58009),i=s(59462);let l=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));l.displayName="Card",a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...r})).displayName="CardHeader",a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("h3",{ref:s,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r})).displayName="CardTitle",a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("p",{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",e),...r})).displayName="CardDescription";let o=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,i.cn)("p-6 pt-0",e),...r}));o.displayName="CardContent";let c=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,i.cn)("flex items-center p-6 pt-0",e),...r}));c.displayName="CardFooter"},98766:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>l.a,__next_app__:()=>p,pages:()=>n,routeModule:()=>m,tree:()=>d});var t=s(70260),a=s(28203),i=s(25155),l=s.n(i),o=s(67292),c={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>o[e]);s.d(r,c);let d={children:["",{children:["wishlist",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,84276)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\wishlist\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,19611)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"]}]}.children,n=["C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\wishlist\\page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/wishlist/page",pathname:"/wishlist",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},99905:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(41680).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[638,320,875],()=>s(98766));module.exports=t})();