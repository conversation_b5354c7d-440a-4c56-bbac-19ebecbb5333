exports.id=875,exports.ids=[875],exports.modules={14495:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,13219,23)),Promise.resolve().then(s.t.bind(s,34863,23)),Promise.resolve().then(s.t.bind(s,25155,23)),Promise.resolve().then(s.t.bind(s,40802,23)),Promise.resolve().then(s.t.bind(s,9350,23)),Promise.resolve().then(s.t.bind(s,48530,23)),Promise.resolve().then(s.t.bind(s,81601,23)),Promise.resolve().then(s.t.bind(s,88921,23))},15348:(e,t,s)=>{"use strict";s.d(t,{$g:()=>u,MakeApiCallAsync:()=>l,TS:()=>o,XX:()=>d,k6:()=>c});var r=s(85668),a=s(39913);r.A.defaults.timeout=3e4;let o={ADMIN_BASE_URL:a.T.ADMIN_BASE_URL,API_VERSION:"v1",DYNAMIC_METHOD_SUB_URL:"api/v1/dynamic/dataoperation/",END_POINT_NAMES:{...a.T.END_POINT_NAMES,GET_CATEGORIES_LIST:"get-categories-list",SIGNUP_USER:"signup-user",GET_USER_LOGIN:"get-user-login",GET_USER_BY_PHONE:"get-user-by-phone",RESET_PASSWORD_BY_PHONE:"reset-password-by-phone",GET_HOME_SCREEN_BANNER:"get-home-screen-banner",GET_RECENT_PRODUCTS:"get-recents-products-list",GET_POPULAR_PRODUCTS:"get-popular-products-list",GET_HOT_DEAL_PRODUCTS:"get-hot-deal-products",GET_CAMPAIGNS_LIST:"get-web-campaign-list",GET_PRODUCTS_LIST:"get-products-list",GET_ALL_PRODUCTS:"api/v1/products/get-all-products",GET_MANUFACTURERS_LIST:"get-manufacturers-list",GET_TAGS_LIST:"get-tags-list",GET_CURRENCY_RATE:"get-currency-rate",GET_COUPON_CODE_DISCOUNT:"get-coupon-code-discount-value/calculate-coupon-discount"},COMMON_CONTROLLER_SUB_URL:"api/v1/common/"},n=async()=>localStorage.getItem("token")||null,i=async()=>localStorage.getItem("userId")||null,l=async(e,t,s,a,l,c=!0)=>{try{let c={...a};if(!c.hasOwnProperty("Authorization")){let e=await n();e&&(c.Authorization="Bearer "+e)}if(!c.hasOwnProperty("Token")){let e=await n();c.Token=e??""}if(!c.hasOwnProperty("UserID")){let e=await i();c.UserID=e??""}c.hasOwnProperty("Accept")||(c.Accept="application/json"),c.hasOwnProperty("Content-Type")||(c["Content-Type"]="application/json");let d=o.ADMIN_BASE_URL+(null===t||void 0==t?o.DYNAMIC_METHOD_SUB_URL:t)+e;l=l??"POST";let u={headers:c,responseType:"json",timeout:3e4,withCredentials:!1};if("POST"===l)return await r.A.post(d,s,u);if("GET"==l)return u.params=s,await r.A.get(d,u);return{data:{errorMessage:`Unsupported method type: ${l}`,status:"method_not_supported"}}}catch(t){console.error("API call failed:",t);let e={data:{errorMessage:"An unexpected error occurred",status:"unknown_error"}};if(t&&"object"==typeof t&&"response"in t&&t.response){let s=t.response?.data;e.data={errorMessage:s?.errorMessage||"An error occurred while processing your request.",status:t.response?.status}}else if(t&&"object"==typeof t&&"request"in t){let s="Network error: No response received from server.";t.message&&t.message.includes("Network Error")&&(s="Network Error: This may be due to CORS policy restrictions, server unavailability, or an invalid SSL certificate. Please check that:\n1. The server is running and accessible\n2. The URL is correct: "+o.ADMIN_BASE_URL+"\n3. CORS is properly configured on the server\n4. If using HTTPS, the SSL certificate is valid"),e.data={errorMessage:s,status:"network_error"}}else{let s=t&&"object"==typeof t&&"message"in t?t.message:"An unexpected error occurred";e.data={errorMessage:s,status:"request_error"}}return e}},c=async()=>{try{let e=await l("getrate","api/v1/common/",{},{},"GET");if(e&&e.data&&!e.data.errorMessage)return parseInt(e.data)||1500;return 1500}catch(e){return console.error("Error fetching currency rate:",e),1500}},d=(e,t)=>Math.round(e*t),u=(e,t="USD")=>"IQD"===t?`${e.toLocaleString()} IQD`:`$${e.toFixed(2)}`},19611:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\layout.tsx","default")},27725:(e,t,s)=>{"use strict";s.d(t,{B:()=>i,H:()=>l});var r=s(45512),a=s(58009),o=s(15348);let n=(0,a.createContext)(void 0);function i({children:e}){let[t,s]=(0,a.useState)(1500),[i,l]=(0,a.useState)(!0),c=async()=>{l(!0);try{let e=await (0,o.k6)();s(e)}catch(e){console.error("Failed to load currency rate:",e)}finally{l(!1)}},d=async()=>{await c()};return(0,r.jsx)(n.Provider,{value:{rate:t,isLoading:i,convertToIQD:e=>(0,o.XX)(e,t),formatUSD:e=>(0,o.$g)(e,"USD"),formatIQD:e=>(0,o.$g)(e,"IQD"),refreshRate:d},children:e})}function l(){let e=(0,a.useContext)(n);if(void 0===e)throw Error("useCurrency must be used within a CurrencyProvider");return e}},31961:(e,t,s)=>{"use strict";s.d(t,{Z:()=>n,n:()=>i});var r=s(45512),a=s(58009);let o=(0,a.createContext)(void 0);function n({children:e}){let[t,s]=(0,a.useState)([]),[n,i]=(0,a.useState)(!1);return(0,r.jsx)(o.Provider,{value:{wishlistItems:t,addToWishlist:e=>{t.includes(e)||s([...t,e])},removeFromWishlist:e=>{s(t.filter(t=>t!==e))},isInWishlist:e=>t.includes(e),totalItems:t.length,isHydrated:n},children:e})}function i(){let e=(0,a.useContext)(o);if(void 0===e)throw Error("useWishlist must be used within a WishlistProvider");return e}},33853:(e,t,s)=>{"use strict";s.d(t,{J:()=>l,v:()=>i});var r=s(45512),a=s(58009),o=s(15348);let n=(0,a.createContext)(void 0);function i({children:e}){let[t,s]=(0,a.useState)(null),[i,l]=(0,a.useState)(null),[c,d]=(0,a.useState)(!0),u=async(e,t)=>{try{d(!0);let r=await (0,o.MakeApiCallAsync)(o.TS.END_POINT_NAMES.GET_USER_LOGIN,null,{requestParameters:{Email:e,Password:t}},{Accept:"application/json","Content-Type":"application/json"},"POST",!0);if(!r||!r.data||r.data.errorMessage)return{success:!1,message:r.data?.errorMessage||"Login failed. Please try again."};{let e;if(e="string"==typeof r.data.data?JSON.parse(r.data.data):r.data.data,!Array.isArray(e)||!(e.length>0)||"Login Successfully"!==e[0].ResponseMsg)return{success:!1,message:"Incorrect email or password!"};{let t=e[0];s(t),localStorage.setItem("user",JSON.stringify(t));let a=r.data.token||"";return l(a),localStorage.setItem("token",a),{success:!0,message:"Login successful!"}}}}catch(e){return console.error("Login error:",e),{success:!1,message:"An error occurred. Please try again!"}}finally{d(!1)}},m=null!==t&&t.UserId>0;return(0,r.jsx)(n.Provider,{value:{user:t,isLoggedIn:m,isLoading:c,login:u,logout:()=>{s(null),l(null),localStorage.removeItem("user"),localStorage.removeItem("token"),localStorage.removeItem("userId")},token:i},children:e})}function l(){let e=(0,a.useContext)(n);if(void 0===e)throw Error("useUser must be used within a UserProvider");return e}},37534:(e,t,s)=>{Promise.resolve().then(s.bind(s,19611))},39913:(e,t,s)=>{"use strict";s.d(t,{T:()=>r});let r={ADMIN_BASE_URL:"https://admin.codemedicalapps.com/",DYNAMIC_METHOD_SUB_URL:"api/",COMMON_CONTROLLER_SUB_URL:"api/common/",END_POINT_NAMES:{DOWNLOAD_DIGITAL_FILE:"downloadDigitalFile",GET_COUPON_CODE_DISCOUNT:"get-coupon-code-discount-value/calculate-coupon-discount"}}},59462:(e,t,s)=>{"use strict";s.d(t,{cn:()=>o});var r=s(82281),a=s(94805);function o(...e){return(0,a.QP)((0,r.$)(e))}},65518:(e,t,s)=>{"use strict";s.d(t,{dj:()=>m});var r=s(58009);let a=0,o=new Map,n=e=>{if(o.has(e))return;let t=setTimeout(()=>{o.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);o.set(e,t)},i=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:s}=t;return s?n(s):e.toasts.forEach(e=>{n(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],c={toasts:[]};function d(e){c=i(c,e),l.forEach(e=>{e(c)})}function u({...e}){let t=(a=(a+1)%100).toString(),s=()=>d({type:"DISMISS_TOAST",toastId:t});return d({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||s()}}}),{id:t,dismiss:s,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function m(){let[e,t]=r.useState(c);return r.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>d({type:"DISMISS_TOAST",toastId:e})}}},71580:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>Q});var r=s(45512);s(76080);var a=s(58009),o=s(8685),n=s.n(o),i=s(90550),l=s(72517),c=s(15607),d=s(31575),u=s(1372),m=s(88651),h=s(28531),p=s.n(h),x=s(71901);function g(){let{primaryColor:e,t}=(0,x.t)(),[s,o]=(0,a.useState)(""),[n,i]=(0,a.useState)(!1),[h,g]=(0,a.useState)({type:"",text:""}),{executeRecaptcha:f}=(0,m._Y)(),y=async e=>{if(e.preventDefault(),!f){console.error("Execute recaptcha not yet available");return}try{i(!0),g({type:"",text:""}),await f("subscribe");let e=await fetch("https://admin.codemedicalapps.com/api/v1/dynamic/dataoperation/insert-subscriber",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({requestParameters:{SubscriberEmail:s}})}),t=await e.json();e.ok?(g({type:"success",text:"Successfully subscribed!"}),o("")):g({type:"error",text:t.message||"Failed to subscribe"})}catch(e){g({type:"error",text:"An error occurred. Please try again."})}finally{i(!1)}};return(0,r.jsx)("footer",{className:"w-full",children:(0,r.jsxs)("div",{className:"text-white py-8 sm:py-12",style:{backgroundColor:e},children:[(0,r.jsxs)("div",{className:"container mx-auto px-4 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6 sm:gap-8",children:[(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{className:"flex items-center gap-2",children:(0,r.jsx)("img",{src:"https://admin.codemedicalapps.com/content/commonImages/otherImages/18b_logo2x.png".replace(/\//g,"/"),alt:"Logo",className:"h-12 sm:h-16 w-auto bg-white p-2 rounded-md"})}),(0,r.jsx)("p",{className:"text-gray-300 text-sm sm:text-base",children:"We are professional team specialized in providing well known valuable medical courses, Ebooks, Printed books and popular medical accounts for all medical field staff all around the world with low cost and in short time."}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)(p(),{href:"https://www.facebook.com/codemedicalapps/",target:"_blank",rel:"noopener noreferrer",className:"hover:text-gray-300 transition-colors",children:(0,r.jsx)(l.A,{className:"h-5 w-5"})}),(0,r.jsx)(p(),{href:"https://t.me/codemedicalapps",target:"_blank",rel:"noopener noreferrer",className:"hover:text-gray-300 transition-colors",children:(0,r.jsx)(c.A,{className:"h-5 w-5"})}),(0,r.jsx)(p(),{href:"https://wa.me/*************",target:"_blank",rel:"noopener noreferrer",className:"hover:text-gray-300 transition-colors",children:(0,r.jsx)(d.A,{className:"h-5 w-5"})}),(0,r.jsx)(p(),{href:"https://m.me/***************",target:"_blank",rel:"noopener noreferrer",className:"hover:text-gray-300 transition-colors",children:(0,r.jsx)(u.A,{className:"h-5 w-5"})})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-6 sm:gap-8 col-span-2 sm:col-span-1 md:col-span-2",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h3",{className:"text-base sm:text-lg font-semibold",children:t("quickLinks")}),(0,r.jsxs)("ul",{className:"space-y-2 sm:space-y-3 text-sm sm:text-base",children:[(0,r.jsx)("li",{children:(0,r.jsx)(p(),{href:"/about",className:"hover:text-gray-300 transition-colors",children:t("about")})}),(0,r.jsx)("li",{children:(0,r.jsx)(p(),{href:"/contact",className:"hover:text-gray-300 transition-colors",children:t("contact")})}),(0,r.jsx)("li",{children:(0,r.jsx)(p(),{href:"/hot-deals",className:"hover:text-gray-300 transition-colors",children:t("hotDeals")})}),(0,r.jsx)("li",{children:(0,r.jsx)(p(),{href:"/login",className:"hover:text-gray-300 transition-colors",children:t("login")})}),(0,r.jsx)("li",{children:(0,r.jsx)(p(),{href:"/signup",className:"hover:text-gray-300 transition-colors",children:t("signup")})})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h3",{className:"text-base sm:text-lg font-semibold",children:t("customerArea")}),(0,r.jsxs)("ul",{className:"space-y-2 sm:space-y-3 text-sm sm:text-base",children:[(0,r.jsx)("li",{children:(0,r.jsx)(p(),{href:"/account",className:"hover:text-gray-300 transition-colors",children:t("myAccount")})}),(0,r.jsx)("li",{children:(0,r.jsx)(p(),{href:"/orders",className:"hover:text-gray-300 transition-colors",children:t("orders")})}),(0,r.jsx)("li",{children:(0,r.jsx)(p(),{href:"/cart",className:"hover:text-gray-300 transition-colors",children:t("cart")})}),(0,r.jsx)("li",{children:(0,r.jsx)(p(),{href:"/wishlist",className:"hover:text-gray-300 transition-colors",children:t("wishlist")})}),(0,r.jsx)("li",{children:(0,r.jsx)(p(),{href:"/payment-methods",className:"hover:text-gray-300 transition-colors",children:t("paymentMethods")})})]})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h3",{className:"text-base sm:text-lg font-semibold",children:t("contact")}),(0,r.jsxs)("ul",{className:"space-y-2 sm:space-y-3 text-sm sm:text-base",children:[(0,r.jsxs)("li",{className:"flex items-center gap-2",children:[(0,r.jsxs)("span",{className:"text-gray-300",children:[t("location"),":"]}),(0,r.jsx)("span",{children:"Iraq"})]}),(0,r.jsxs)("li",{className:"flex items-center gap-2",children:[(0,r.jsxs)("span",{className:"text-gray-300",children:[t("callUs"),":"]}),(0,r.jsx)("a",{href:"tel:+*************",className:"hover:text-gray-300 transition-colors",children:"+964 ************"})]}),(0,r.jsxs)("li",{className:"flex items-center gap-2",children:[(0,r.jsxs)("span",{className:"text-gray-300",children:[t("emailUs"),":"]}),(0,r.jsx)("a",{href:"mailto:<EMAIL>",className:"hover:text-gray-300 transition-colors",children:"<EMAIL>"})]})]}),(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsx)("h3",{className:"text-base sm:text-lg font-semibold mb-4",children:t("newsletter")}),(0,r.jsxs)("form",{onSubmit:y,className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,r.jsx)("input",{type:"email",value:s,onChange:e=>o(e.target.value),placeholder:t("enterEmail"),required:!0,className:"w-full px-4 py-2 text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-opacity-50",style:{outlineColor:e}}),(0,r.jsx)("button",{type:"submit",disabled:n,className:" px-2 py-2 border border-white rounded-md",style:{backgroundColor:e,color:"white",borderColor:"white"},children:n?(0,r.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white"}):(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("span",{children:t("subscribe")})})})]}),h.text&&(0,r.jsx)("p",{className:`text-sm text-center ${"success"===h.type?"text-green-400":"text-red-400"}`,children:h.text}),(0,r.jsx)("p",{className:"text-xs text-gray-300 text-center",children:t("newsletterDisclaimer")})]})]})]})]}),(0,r.jsx)("div",{className:"mt-6 pt-6 border-t border-white/20 text-center text-gray-300 text-sm sm:text-base px-4",children:(0,r.jsx)("p",{children:"\xa9 2024 Code Medical. All rights reserved."})})]})})}function f({className:e=""}){return(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",className:e,fill:"currentColor",children:(0,r.jsx)("path",{d:"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413Z"})})}function y(){return(0,r.jsx)("button",{className:"fixed bottom-20 right-6 z-50 flex h-12 w-12 items-center justify-center rounded-full bg-[#25D366] text-white shadow-md transition-all duration-300 hover:bg-[#128C7E] hover:scale-110",onClick:()=>{window.open("https://wa.me/00*************","_blank")},"aria-label":"Chat on WhatsApp",children:(0,r.jsx)(f,{className:"h-6 w-6"})})}var b=s(87137),v=s(35256),j=s(91124),N=s(10453),w=s(87798),S=s(79334),C=s(84194),A=s(31961),T=s(87021),P=s(15348);function _(){let e=(0,S.usePathname)(),{totalItems:t}=(0,C._)(),{totalItems:s}=(0,A.n)(),{primaryColor:o,t:n}=(0,x.t)(),[i,l]=(0,a.useState)(!1),[c,d]=(0,a.useState)(!1),[u,m]=(0,a.useState)([]),[h,g]=(0,a.useState)([]),[f,y]=(0,a.useState)(null),[_,E]=(0,a.useState)(!1),[I,D]=(0,a.useState)("parent"),O=async()=>{if(h.length>0){d(!0);return}E(!0);try{let e={"Content-Type":"application/json",Accept:"application/json",Authorization:"Bearer "+localStorage.getItem("token")},t=await (0,P.MakeApiCallAsync)(P.TS.END_POINT_NAMES.GET_CATEGORIES_LIST,null,{PageNumber:1,PageSize:100,SortColumn:"Name",SortOrder:"ASC"},e,"POST",!0);if(t?.data?.data)try{let e=JSON.parse(t.data.data);if(Array.isArray(e)){m(e);let t=e.filter(e=>!e.ParentCategoryID);g(t),d(!0)}}catch(e){console.error("Error parsing categories data:",e)}}catch(e){console.error("Error fetching categories:",e)}finally{E(!1)}};if(!i)return null;let U=[{href:"/",icon:b.A,label:n("home")||"الرئيسية",isActive:"/"===e,onClick:null},{href:"#",icon:v.A,label:n("categories")||"التصنيفات",isActive:!1,onClick:O},{href:"/cart",icon:j.A,label:n("cart")||"سلة التسوق",isActive:"/cart"===e,badge:t||0,onClick:null},{href:"/wishlist",icon:N.A,label:n("wishlist")||"المفضلة",isActive:"/wishlist"===e,badge:s||0,onClick:null},{href:"/login",icon:w.A,label:n("login")||"حسابي",isActive:"/login"===e||"/signup"===e,onClick:null}];return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50 safe-area-pb",children:(0,r.jsx)("div",{className:"flex items-center justify-around py-2",children:U.map(e=>{let t=e.icon,s=!!e.onClick,a="flex flex-col items-center justify-center min-w-0 flex-1 py-2 px-1"+(s?" bg-transparent border-none":""),n=(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(t,{className:"h-6 w-6 mb-1",style:{color:e.isActive?o:"#6B7280"}}),void 0!==e.badge&&e.badge>0&&(0,r.jsx)("span",{className:"absolute -top-1 -right-1 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center font-medium shadow-md",style:{backgroundColor:o},children:e.badge>99?"99+":e.badge})]}),(0,r.jsx)("span",{className:"text-xs font-medium text-center leading-tight mt-1",style:{color:e.isActive?o:"#6B7280"},children:e.label})]});return s?(0,r.jsx)("button",{onClick:e.onClick||void 0,className:a,type:"button",children:n},e.href):(0,r.jsx)(p(),{href:e.href,className:a,children:n},e.href)})})}),c&&(0,r.jsx)("div",{className:"md:hidden fixed inset-0 bg-black/50 z-50 flex items-end",children:(0,r.jsxs)("div",{className:"bg-white w-full max-h-[80vh] rounded-t-xl overflow-hidden",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border-b",children:[(0,r.jsxs)("div",{className:"flex items-center",children:["child"===I&&(0,r.jsx)("button",{onClick:()=>{D("parent"),y(null)},className:"mr-2 p-1",children:"←"}),(0,r.jsx)("h2",{className:"text-lg font-semibold",style:{color:o},children:"parent"===I?n("categories")||"التصنيفات":f?.Name})]}),(0,r.jsx)(T.$,{variant:"ghost",size:"sm",onClick:()=>{d(!1),D("parent"),y(null)},className:"text-gray-500 hover:text-gray-700",children:"✕"})]}),(0,r.jsx)("div",{className:"overflow-y-auto max-h-[calc(80vh-80px)]",children:_?(0,r.jsxs)("div",{className:"p-8 text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 mx-auto mb-4",style:{borderColor:o}}),(0,r.jsx)("p",{className:"text-gray-500",children:"Loading..."})]}):"parent"===I?(0,r.jsx)("div",{className:"divide-y",children:h.map(e=>{let t=u.some(t=>t.ParentCategoryID===e.CategoryID);return(0,r.jsx)("div",{className:"border-b border-gray-100",children:(0,r.jsxs)("button",{onClick:()=>{t?(y(e),D("child")):window.location.href=`/products?category=${e.CategoryID}`},className:"w-full text-left p-4 flex justify-between items-center hover:bg-gray-50 transition-colors",children:[(0,r.jsx)("span",{children:e.Name}),t&&(0,r.jsx)("span",{children:"›"})]})},e.CategoryID)})}):(0,r.jsxs)("div",{className:"divide-y",children:[(0,r.jsxs)(p(),{href:`/products?category=${f.CategoryID}`,className:"block p-4 font-medium text-center hover:bg-gray-50",style:{color:o},onClick:()=>d(!1),children:["View All in"," ",f.Name]}),u.filter(e=>e.ParentCategoryID===f.CategoryID).map(e=>(0,r.jsx)(p(),{href:`/products?category=${e.CategoryID}`,className:"block p-4 pl-8 border-b border-gray-100 hover:bg-gray-50 transition-colors",onClick:()=>d(!1),children:e.Name},e.CategoryID))]})})]})})]})}var E=s(65518),I=s(44269),D=s(59462);let O=a.forwardRef(({className:e,title:t,description:s,action:a,...o},n)=>(0,r.jsxs)("div",{ref:n,className:(0,D.cn)("group relative flex items-center justify-between p-4 pr-8 space-x-4 overflow-hidden rounded-md border bg-background shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none",e),...o,children:[(0,r.jsxs)("div",{className:"grid gap-1",children:[t&&(0,r.jsx)("div",{className:"text-sm font-semibold",children:t}),s&&(0,r.jsx)("div",{className:"text-sm opacity-90",children:s})]}),a,(0,r.jsx)("button",{className:"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100",children:(0,r.jsx)(I.A,{className:"h-4 w-4"})})]}));O.displayName="Toast";let U=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,D.cn)("text-sm font-semibold",e),...t}));U.displayName="ToastTitle";let M=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,D.cn)("text-sm opacity-90",e),...t}));M.displayName="ToastDescription";let k=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("button",{ref:s,className:(0,D.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100",e),...t,children:(0,r.jsx)(I.A,{className:"h-4 w-4"})}));k.displayName="ToastClose";let L=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,D.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));function R({children:e,...t}){return(0,r.jsx)(r.Fragment,{children:e})}function G(){let{toasts:e}=(0,E.dj)();return(0,r.jsxs)(R,{children:[e.map(function({id:e,title:t,description:s,action:a,...o}){return(0,r.jsxs)(O,{...o,children:[(0,r.jsxs)("div",{className:"grid gap-1",children:[t&&(0,r.jsx)(U,{children:t}),s&&(0,r.jsx)(M,{children:s})]}),a,(0,r.jsx)(k,{})]},e)}),(0,r.jsx)(L,{})]})}L.displayName="ToastViewport",R.displayName="ToastProvider";let B={whatsappNumber:"+**********",phoneNumber:"+**********",whatsappLink:"https://wa.me/**********"},q=(0,a.createContext)(B);function F({children:e}){return(0,r.jsx)(q.Provider,{value:B,children:e})}var V=s(81758),W=s(27725),$=s(33853);function z({children:e}){return(0,r.jsx)(x.Z,{children:(0,r.jsx)($.v,{children:(0,r.jsx)(W.B,{children:(0,r.jsx)(C.e,{children:(0,r.jsx)(F,{children:(0,r.jsx)(V.U,{children:(0,r.jsx)(A.Z,{children:e})})})})})})})}var H=s(91542);function Q({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:n().className,suppressHydrationWarning:!0,children:(0,r.jsx)(m.G3,{reCaptchaKey:"6LfS7D0rAAAAAAybL-FSr2N1pWStAiKxN_EapgHJ",scriptProps:{async:!0,defer:!0,appendTo:"body",nonce:void 0},container:{parameters:{badge:"inline",theme:"light"}},children:(0,r.jsxs)(z,{children:[(0,r.jsx)(i.Y,{}),(0,r.jsx)("main",{className:"min-h-screen pb-16 md:pb-0",children:e}),(0,r.jsx)(g,{}),(0,r.jsx)(y,{}),(0,r.jsx)(_,{}),(0,r.jsx)(G,{}),(0,r.jsx)(H.l$,{position:"top-right"})]})})})})}},71901:(e,t,s)=>{"use strict";s.d(t,{Z:()=>i,t:()=>l});var r=s(45512),a=s(58009);let o={en:{menu:"Menu",newsletter:"Newsletter",enterEmail:"Enter your email",newsletterDisclaimer:"Subscribe to our newsletter to receive updates and exclusive offers",popularCategories:"Popular Categories!",settings:"Settings",language:"Language",theme:"Theme",color:"Color",save:"Save",cancel:"Cancel",close:"Close",phone:"00*************",phonenumber:"Phone Number",email:"<EMAIL>",liveChat:"Live Chat",welcome:"Welcome",logout:"Logout",login:"Login",signup:"Sign Up",signUp:"Sign Up",category:"Category",categories:"Categories",filters:"Filters",clearAll:"Clear All",products:"Products",loadingCategories:"Loading categories...",home:"Home",todayDeals:"Today's Deals",followUs:"Follow Us",aboutUs:"About Us",contactUs:"Contact Us",tryAgain:"Try Again",noProductsFound:"No products found",allProducts:"All Products",quickLinks:"Quick Links",about:"About Us",contact:"Contact Us",hotDeals:"Hot Deals",customerArea:"Customer Area",myAccount:"My Account",orders:"Orders",cart:"Cart",wishlist:"Wishlist",paymentMethods:"Payment Methods",location:"Location",callUs:"Call Us",emailUs:"Email Us",subscribe:"Subscribe",name:"Name",subject:"Subject",message:"Message",sendMessage:"Send Message",sending:"Sending...",messageSent:"Message sent successfully!",messageError:"Failed to send message",contactInfo:"Contact Information",address:"Address",findUs:"Find Us"},ar:{menu:"القائمة",popularCategories:"الفئات الشائعة!",settings:"الإعدادات",language:"اللغة",theme:"المظهر",color:"اللون",save:"حفظ",cancel:"إلغاء",close:"إغلاق",phone:"00*************",phonenumber:"رقم الهاتف",email:"<EMAIL>",liveChat:"محادثة مباشرة",welcome:"مرحباً",logout:"تسجيل الخروج",login:"تسجيل الدخول",signup:"تسجيل جديد",signUp:"تسجيل جديد",category:"الفئة",categories:"الفئات",products:"المنتجات",loadingCategories:"جاري تحميل الفئات...",filters:"المرشحات",clearAll:"مسح الكل",home:"الرئيسية",todayDeals:"عروض اليوم",followUs:"تابعنا",aboutUs:"من نحن",contactUs:"اتصل بنا",tryAgain:"حاول مرة أخرى",noProductsFound:"لم يتم العثور على منتجات",allProducts:"جميع المنتجات",quickLinks:"روابط سريعة",about:"من نحن",contact:"اتصل بنا",hotDeals:"عروض ساخنة",customerArea:"منطقة العملاء",myAccount:"حسابي",orders:"الطلبات",cart:"السلة",wishlist:"المفضلة",paymentMethods:"طرق الدفع",location:"الموقع",callUs:"اتصل بنا",emailUs:"راسلنا",subscribe:"اشترك",name:"الاسم",subject:"الموضوع",message:"الرسالة",sendMessage:"إرسال الرسالة",sending:"جاري الإرسال...",messageSent:"تم إرسال الرسالة بنجاح!",messageError:"فشل في إرسال الرسالة",contactInfo:"معلومات الاتصال",address:"العنوان",findUs:"موقعنا"}},n=(0,a.createContext)(void 0);function i({children:e}){let[t,s]=(0,a.useState)("light"),[i,l]=(0,a.useState)("en"),[c,d]=(0,a.useState)("#0074b2");return(0,r.jsx)(n.Provider,{value:{theme:t,language:i,primaryColor:c,toggleTheme:()=>{s("light"===t?"dark":"light")},setLanguage:e=>{l(e),document.documentElement.dir="ar"===e?"rtl":"ltr"},setPrimaryColor:e=>{d(e)},t:e=>(function(e,t){let s=o[t];return e in s?s[e]:"en"!==t&&e in o.en?o.en[e]:e})(e,i)},children:e})}function l(){let e=(0,a.useContext)(n);if(void 0===e)throw Error("useSettings must be used within a SettingsProvider");return e}},74743:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,66959,23)),Promise.resolve().then(s.t.bind(s,33875,23)),Promise.resolve().then(s.t.bind(s,88903,23)),Promise.resolve().then(s.t.bind(s,57174,23)),Promise.resolve().then(s.t.bind(s,84178,23)),Promise.resolve().then(s.t.bind(s,87190,23)),Promise.resolve().then(s.t.bind(s,48429,23)),Promise.resolve().then(s.t.bind(s,61365,23))},76080:()=>{},77702:(e,t,s)=>{Promise.resolve().then(s.bind(s,71580))},81758:(e,t,s)=>{"use strict";s.d(t,{U:()=>i,Y:()=>l});var r=s(45512),a=s(58009),o=s(15348);let n=(0,a.createContext)(void 0);function i({children:e}){let[t,s]=(0,a.useState)(null),[i,l]=(0,a.useState)(!1),c=async(e,t,r)=>{if(!e.trim())return{valid:!1,message:"Please enter a coupon code",discount:0};l(!0);try{let a=r?.map(e=>({ProductId:e.id,ProductName:e.name,Price:e.adjustedPrice||e.price,Quantity:e.quantity,IsDiscountAllowed:!0}))||[],n=JSON.stringify(a),i={requestParameters:{CouponCode:e.toUpperCase(),cartJsonData:n}},l=await (0,o.MakeApiCallAsync)(o.TS.END_POINT_NAMES.GET_COUPON_CODE_DISCOUNT,o.TS.COMMON_CONTROLLER_SUB_URL,i,{"Content-Type":"application/json",Accept:"application/json"},"POST");if(!l||!l.data||l.data.errorMessage)return{valid:!1,message:l.data?.errorMessage||"Failed to validate coupon",discount:0};{let r;if((r="string"==typeof l.data.data?JSON.parse(l.data.data):l.data.data)&&r.DiscountValueAfterCouponAppliedWithQuantity>0){let t=r.DiscountValueAfterCouponAppliedWithQuantity,a=2===r.DiscountValueType?"percentage":"fixed",o={code:e.toUpperCase(),discount:t,type:a};return s(o),{valid:!0,message:"Coupon applied successfully!",discount:t}}if(r&&Array.isArray(r)&&r.length>0){let a=r[0];if(a&&a.DiscountValue>0){let r=0;if(1===a.DiscountValueType?r=a.DiscountValue:2===a.DiscountValueType&&(r=a.DiscountValue*t/100),r>0){let t={code:e.toUpperCase(),discount:r,type:2===a.DiscountValueType?"percentage":"fixed"};return s(t),{valid:!0,message:"Coupon applied successfully!",discount:r}}}}return{valid:!1,message:"Invalid coupon code or coupon not applicable to your cart",discount:0}}}catch(e){return console.error("Coupon validation error:",e),{valid:!1,message:"Error validating coupon. Please try again.",discount:0}}finally{l(!1)}};return(0,r.jsx)(n.Provider,{value:{appliedCoupon:t,validateCoupon:c,clearCoupon:()=>{s(null)},isLoading:i},children:e})}function l(){let e=(0,a.useContext)(n);if(void 0===e)throw Error("useCoupon must be used within a CouponProvider");return e}},84194:(e,t,s)=>{"use strict";s.d(t,{_:()=>i,e:()=>n});var r=s(45512),a=s(58009);let o=(0,a.createContext)(void 0);function n({children:e}){let[t,s]=(0,a.useState)([]),[n,i]=(0,a.useState)(!1),l=e=>{s(t=>t.filter(t=>t.id!==e))},c=t.reduce((e,t)=>e+t.quantity,0),d=t.reduce((e,t)=>e+(t.discountPrice?Math.min(t.discountPrice,t.adjustedPrice):t.adjustedPrice)*t.quantity,0),u=t.reduce((e,t)=>e+(t.adjustedIqdPrice||t.iqdPrice||0)*t.quantity,0);return(0,r.jsx)(o.Provider,{value:{items:t,addToCart:(e,t,r=[],a,o=1500)=>{s(s=>{let n=e.price,i=a||Math.round(e.price*o),l=i;r.forEach(t=>{if(t.PriceAdjustment&&t.PriceAdjustmentType){let s=e.originalPrice||e.price;switch(t.PriceAdjustmentType){case 1:n+=t.PriceAdjustment,l+=Math.round(t.PriceAdjustment*o);break;case 2:let r=s*t.PriceAdjustment/100;n+=r,l+=Math.round(r*o)}}});let c=s.findIndex(t=>t.id===e.id&&JSON.stringify(t.attributes?.sort((e,t)=>e.ProductAttributeID-t.ProductAttributeID))===JSON.stringify(r?.sort((e,t)=>e.ProductAttributeID-t.ProductAttributeID)));if(!(c>=0))return[...s,{...e,iqdPrice:i,adjustedIqdPrice:Math.max(0,l),quantity:t,attributes:r,adjustedPrice:Math.max(0,n),originalPrice:e.originalPrice}];{let e=[...s];return e[c].quantity+=t,e}})},removeFromCart:l,updateQuantity:(e,t)=>{if(t<=0){l(e);return}s(s=>s.map(s=>s.id===e?{...s,quantity:t}:s))},clearCart:()=>{s([])},totalItems:c,subtotal:d,subtotalIQD:u,total:d,totalIQD:u,isHydrated:n},children:e})}function i(){let e=(0,a.useContext)(o);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e}},87021:(e,t,s)=>{"use strict";s.d(t,{$:()=>c,r:()=>l});var r=s(45512),a=s(58009),o=s(12705),n=s(21643),i=s(59462);let l=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-red-500 text-destructive-foreground hover:bg-red-600",outline:"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef(({className:e,variant:t,size:s,asChild:a=!1,...n},c)=>{let d=a?o.DX:"button";return(0,r.jsx)(d,{className:(0,i.cn)(l({variant:t,size:s,className:e})),ref:c,...n})});c.displayName="Button"},90550:()=>{throw Error('Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31mx\x1b[0m Unexpected token `header`. Expected jsx identifier\n     ,-[\x1b[36;1;4mC:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\components\\ui\\header.tsx\x1b[0m:160:1]\n \x1b[2m157\x1b[0m |   }, [wishlist, wishlist?.wishlistItems]);\n \x1b[2m158\x1b[0m | \n \x1b[2m159\x1b[0m |   return (\n \x1b[2m160\x1b[0m |     <header className="w-full">\n     : \x1b[35;1m     ^^^^^^\x1b[0m\n \x1b[2m161\x1b[0m |       <Button\n \x1b[2m162\x1b[0m |         variant="ghost"\n \x1b[2m162\x1b[0m |         size="sm"\r\n     `----\n\n\nCaused by:\n    Syntax Error')}};