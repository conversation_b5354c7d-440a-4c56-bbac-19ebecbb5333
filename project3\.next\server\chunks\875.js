exports.id=875,exports.ids=[875],exports.modules={14495:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,13219,23)),Promise.resolve().then(s.t.bind(s,34863,23)),Promise.resolve().then(s.t.bind(s,25155,23)),Promise.resolve().then(s.t.bind(s,40802,23)),Promise.resolve().then(s.t.bind(s,9350,23)),Promise.resolve().then(s.t.bind(s,48530,23)),Promise.resolve().then(s.t.bind(s,81601,23)),Promise.resolve().then(s.t.bind(s,88921,23))},15348:(e,t,s)=>{"use strict";s.d(t,{$g:()=>u,MakeApiCallAsync:()=>l,TS:()=>n,XX:()=>d,k6:()=>c});var a=s(85668),r=s(39913);a.A.defaults.timeout=3e4;let n={ADMIN_BASE_URL:r.T.ADMIN_BASE_URL,API_VERSION:"v1",DYNAMIC_METHOD_SUB_URL:"api/v1/dynamic/dataoperation/",END_POINT_NAMES:{...r.T.END_POINT_NAMES,GET_CATEGORIES_LIST:"get-categories-list",SIGNUP_USER:"signup-user",GET_USER_LOGIN:"get-user-login",GET_USER_BY_PHONE:"get-user-by-phone",RESET_PASSWORD_BY_PHONE:"reset-password-by-phone",GET_HOME_SCREEN_BANNER:"get-home-screen-banner",GET_RECENT_PRODUCTS:"get-recents-products-list",GET_POPULAR_PRODUCTS:"get-popular-products-list",GET_HOT_DEAL_PRODUCTS:"get-hot-deal-products",GET_CAMPAIGNS_LIST:"get-web-campaign-list",GET_PRODUCTS_LIST:"get-products-list",GET_ALL_PRODUCTS:"api/v1/products/get-all-products",GET_MANUFACTURERS_LIST:"get-manufacturers-list",GET_TAGS_LIST:"get-tags-list",GET_CURRENCY_RATE:"get-currency-rate",GET_COUPON_CODE_DISCOUNT:"get-coupon-code-discount-value/calculate-coupon-discount"},COMMON_CONTROLLER_SUB_URL:"api/v1/common/"},o=async()=>localStorage.getItem("token")||null,i=async()=>localStorage.getItem("userId")||null,l=async(e,t,s,r,l,c=!0)=>{try{let c={...r};if(!c.hasOwnProperty("Authorization")){let e=await o();e&&(c.Authorization="Bearer "+e)}if(!c.hasOwnProperty("Token")){let e=await o();c.Token=e??""}if(!c.hasOwnProperty("UserID")){let e=await i();c.UserID=e??""}c.hasOwnProperty("Accept")||(c.Accept="application/json"),c.hasOwnProperty("Content-Type")||(c["Content-Type"]="application/json");let d=n.ADMIN_BASE_URL+(null===t||void 0==t?n.DYNAMIC_METHOD_SUB_URL:t)+e;l=l??"POST";let u={headers:c,responseType:"json",timeout:3e4,withCredentials:!1};if("POST"===l)return await a.A.post(d,s,u);if("GET"==l)return u.params=s,await a.A.get(d,u);return{data:{errorMessage:`Unsupported method type: ${l}`,status:"method_not_supported"}}}catch(t){console.error("API call failed:",t);let e={data:{errorMessage:"An unexpected error occurred",status:"unknown_error"}};if(t&&"object"==typeof t&&"response"in t&&t.response){let s=t.response?.data;e.data={errorMessage:s?.errorMessage||"An error occurred while processing your request.",status:t.response?.status}}else if(t&&"object"==typeof t&&"request"in t){let s="Network error: No response received from server.";t.message&&t.message.includes("Network Error")&&(s="Network Error: This may be due to CORS policy restrictions, server unavailability, or an invalid SSL certificate. Please check that:\n1. The server is running and accessible\n2. The URL is correct: "+n.ADMIN_BASE_URL+"\n3. CORS is properly configured on the server\n4. If using HTTPS, the SSL certificate is valid"),e.data={errorMessage:s,status:"network_error"}}else{let s=t&&"object"==typeof t&&"message"in t?t.message:"An unexpected error occurred";e.data={errorMessage:s,status:"request_error"}}return e}},c=async()=>{try{let e=await l("getrate","api/v1/common/",{},{},"GET");if(e&&e.data&&!e.data.errorMessage)return parseInt(e.data)||1500;return 1500}catch(e){return console.error("Error fetching currency rate:",e),1500}},d=(e,t)=>Math.round(e*t),u=(e,t="USD")=>"IQD"===t?`${e.toLocaleString()} IQD`:`$${e.toFixed(2)}`},19611:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\layout.tsx","default")},27725:(e,t,s)=>{"use strict";s.d(t,{B:()=>i,H:()=>l});var a=s(45512),r=s(58009),n=s(15348);let o=(0,r.createContext)(void 0);function i({children:e}){let[t,s]=(0,r.useState)(1500),[i,l]=(0,r.useState)(!0),c=async()=>{l(!0);try{let e=await (0,n.k6)();s(e)}catch(e){console.error("Failed to load currency rate:",e)}finally{l(!1)}},d=async()=>{await c()};return(0,a.jsx)(o.Provider,{value:{rate:t,isLoading:i,convertToIQD:e=>(0,n.XX)(e,t),formatUSD:e=>(0,n.$g)(e,"USD"),formatIQD:e=>(0,n.$g)(e,"IQD"),refreshRate:d},children:e})}function l(){let e=(0,r.useContext)(o);if(void 0===e)throw Error("useCurrency must be used within a CurrencyProvider");return e}},31961:(e,t,s)=>{"use strict";s.d(t,{Z:()=>o,n:()=>i});var a=s(45512),r=s(58009);let n=(0,r.createContext)(void 0);function o({children:e}){let[t,s]=(0,r.useState)([]),[o,i]=(0,r.useState)(!1);return(0,a.jsx)(n.Provider,{value:{wishlistItems:t,addToWishlist:e=>{t.includes(e)||s([...t,e])},removeFromWishlist:e=>{s(t.filter(t=>t!==e))},isInWishlist:e=>t.includes(e),totalItems:t.length,isHydrated:o},children:e})}function i(){let e=(0,r.useContext)(n);if(void 0===e)throw Error("useWishlist must be used within a WishlistProvider");return e}},33053:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>ex});var a=s(45512);s(76080);var r=s(58009),n=s(8685),o=s.n(n),i=s(94209),l=s(8866),c=s(74464),d=s(31575),u=s(87798),m=s(69855),h=s(16873),p=s(98755),x=s(10453),g=s(91124),f=s(79334),b=s(28531),v=s.n(b),y=s(84194),j=s(31961),N=s(33853),w=s(15348),C=s(19125),S=s(21643),A=s(59462);let T=r.forwardRef(({className:e,children:t,...s},r)=>(0,a.jsxs)(C.bL,{ref:r,className:(0,A.cn)("relative z-10 flex max-w-max flex-1 items-center justify-center",e),...s,children:[t,(0,a.jsx)(k,{})]}));T.displayName=C.bL.displayName;let P=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(C.B8,{ref:s,className:(0,A.cn)("group flex flex-1 list-none items-center justify-center space-x-1 bg-white",e),...t}));P.displayName=C.B8.displayName;let _=C.q7,E=(0,S.F)("group inline-flex h-10 w-max items-center justify-center rounded-md bg-white px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50");r.forwardRef(({className:e,children:t,...s},r)=>(0,a.jsxs)(C.l9,{ref:r,className:(0,A.cn)(E(),"group",e),...s,children:[t," ",(0,a.jsx)(p.A,{className:"relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180","aria-hidden":"true"})]})).displayName=C.l9.displayName,r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(C.UC,{ref:s,className:(0,A.cn)("left-0 top-0 w-full data-[motion^=from-]:animate-in data-[motion^=to-]:animate-out data-[motion^=from-]:fade-in data-[motion^=to-]:fade-out data-[motion=from-end]:slide-in-from-right-52 data-[motion=from-start]:slide-in-from-left-52 data-[motion=to-end]:slide-out-to-right-52 data-[motion=to-start]:slide-out-to-left-52 md:absolute md:w-auto ",e),...t})).displayName=C.UC.displayName;let I=C.N_,k=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{className:(0,A.cn)("absolute left-0 top-full flex justify-center z-50"),children:(0,a.jsx)(C.LM,{className:(0,A.cn)("origin-top-center relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full overflow-hidden rounded-md border bg-white text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 md:w-[var(--radix-navigation-menu-viewport-width)] !bg-white",e),ref:s,...t})}));k.displayName=C.LM.displayName,r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(C.C1,{ref:s,className:(0,A.cn)("top-full z-[1] flex h-1.5 items-end justify-center overflow-hidden data-[state=visible]:animate-in data-[state=hidden]:animate-out data-[state=hidden]:fade-out data-[state=visible]:fade-in",e),...t,children:(0,a.jsx)("div",{className:"relative top-[60%] h-2 w-2 rotate-45 rounded-tl-sm bg-border shadow-md"})})).displayName=C.C1.displayName;var U=s(13490);let D=U.bL,O=U.l9,M=r.forwardRef(({className:e,align:t="center",sideOffset:s=4,...r},n)=>(0,a.jsx)(U.ZL,{children:(0,a.jsx)(U.UC,{ref:n,align:t,sideOffset:s,className:(0,A.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...r})}));M.displayName=U.UC.displayName;var L=s(87021),R=s(71901),B=s(44269);let z=["#0074b2","#194234","#2a9d8f","#81d4fa","#f295ce","#fce4ec","#b39ddb","#bcaaa4","#ffccbc","#b2dfdb","#6c9bcf","#ffd552","#39b1df","#7986cb","#003554"],G=e=>{let t=e.replace("#",""),s=parseInt(t.slice(0,2),16),a=parseInt(t.slice(2,4),16);return(.299*s+.587*a+.114*parseInt(t.slice(4,6),16))/255>.5?"#000000":"#ffffff"};function $({onColorSelect:e,onClose:t}){let[s,n]=(0,r.useState)("#0074b2"),o=t=>{n(t),document.documentElement.style.setProperty("--primary",t),document.documentElement.style.setProperty("--primary-foreground",G(t)),e(t)};return(0,a.jsx)("div",{className:"fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"bg-card border rounded-lg shadow-lg p-6 w-[320px] space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Choose a Color"}),(0,a.jsx)(L.$,{variant:"ghost",size:"icon",onClick:t,children:(0,a.jsx)(B.A,{className:"h-4 w-4"})})]}),(0,a.jsx)("div",{className:"grid grid-cols-3 gap-2",children:z.map(e=>(0,a.jsx)("button",{className:`w-full aspect-square rounded-lg transition-all duration-200 hover:scale-105 hover:shadow-lg ${s===e?"ring-2 ring-primary":""}`,style:{backgroundColor:e,color:G(e)},onClick:()=>o(e),children:s===e&&"✓"},e))})]})})}function F(){let e=(0,f.useRouter)(),[t,s]=(0,r.useState)([]),[n,o]=(0,r.useState)(!0),[b,w]=(0,r.useState)(!1),[C,S]=(0,r.useState)(null),[E,k]=(0,r.useState)(null),[U,B]=(0,r.useState)(null),[z,G]=(0,r.useState)(!1),[F,q]=(0,r.useState)(!1),[H,V]=(0,r.useState)(""),[W,J]=(0,r.useState)(0),[Q,Y]=(0,r.useState)(0);(0,y._)(),(0,j.n)();let{user:Z,isLoggedIn:X,logout:K}=(0,N.J)(),{theme:ee,language:et,primaryColor:es,toggleTheme:ea,setLanguage:er,setPrimaryColor:en,t:eo}=(0,R.t)(),ei=()=>{let t=new URLSearchParams;H&&t.append("search",H),E&&t.append("category",E.toString()),e.push(`/products?${t.toString()}`)};return(0,a.jsxs)("header",{className:"w-full",children:[(0,a.jsx)(L.$,{variant:"ghost",size:"sm",className:"fixed right-0 top-1/2 -translate-y-1/2 z-50 bg-gradient-to-r from-background/90 to-background/60 backdrop-blur-sm shadow-lg rounded-l-lg border-l border-y border-border/40 md:flex hidden hover:translate-x-1 transition-all duration-200 hover:shadow-xl group items-center justify-center w-16 h-12",onClick:()=>w(!0),children:(0,a.jsx)("div",{className:"h-8 w-8 rounded-full ring-2 ring-border/50 group-hover:ring-primary/50 transition-all duration-200 shadow-inner",style:{backgroundColor:es}})}),(0,a.jsx)("div",{className:"hidden md:block text-white py-2.5",style:{backgroundColor:es},children:(0,a.jsxs)("div",{className:"container mx-auto flex flex-col md:flex-row justify-between items-center text-sm px-4",children:[(0,a.jsxs)("div",{className:"flex md:flex-row items-start justify-start gap-4 md:gap-8",children:[(0,a.jsxs)(v(),{href:"tel:00*************",className:"flex items-center gap-2 hover:text-white/80",children:[(0,a.jsx)(i.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"text-xs md:text-sm",children:eo("phone")})]}),(0,a.jsxs)(v(),{href:"mailto:<EMAIL>",className:"flex items-center gap-2 hover:text-white/80",children:[(0,a.jsx)(l.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"text-xs md:text-sm",children:eo("email")})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 md:gap-4",children:[(0,a.jsxs)(L.$,{variant:"ghost",size:"sm",className:"text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2",onClick:()=>er("en"===et?"ar":"en"),children:[(0,a.jsx)(c.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"text-sm",children:"en"===et?"العربية":"English"})]}),(0,a.jsxs)(L.$,{variant:"ghost",size:"sm",className:"text-white hover:text-white/80 flex items-center gap-2",onClick:()=>window.open(`https://wa.me/*************?text=${encodeURIComponent("Hello! I would like to chat with you regarding your services.")}`,"_blank"),children:[(0,a.jsx)(d.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"text-sm",children:eo("liveChat")})]}),X?(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)("span",{className:"text-white text-sm",children:[eo("welcome")," ",Z?.FirstName||Z?.UserName||Z?.Email]}),(0,a.jsxs)(L.$,{variant:"ghost",size:"sm",className:"text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2",onClick:K,children:[(0,a.jsx)(u.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"text-sm",children:eo("logout")||"Logout"})]})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(v(),{href:"/login",children:(0,a.jsxs)(L.$,{variant:"ghost",size:"sm",className:"text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2",children:[(0,a.jsx)(u.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"text-sm",children:eo("login")})]})}),(0,a.jsx)(v(),{href:"/signup",children:(0,a.jsxs)(L.$,{variant:"ghost",size:"sm",className:"text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"text-sm",children:eo("signUp")})]})})]})]})]})}),(0,a.jsxs)("div",{className:"container mx-auto py-4 px-4",children:[(0,a.jsxs)("div",{className:"md:hidden",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("div",{className:"w-20"}),(0,a.jsx)(v(),{href:"/",className:"flex items-center gap-2",children:(0,a.jsx)("div",{className:"text-[#1B3764] flex items-center gap-2",children:(0,a.jsx)("img",{src:"https://admin.codemedicalapps.com/content/commonImages/otherImages/18b_logo2x.png",alt:"Logo",className:"h-12 w-auto"})})}),(0,a.jsxs)(L.$,{variant:"ghost",size:"sm",className:"flex items-center gap-2 px-3 py-2 rounded-full border border-gray-200 hover:bg-gray-50",onClick:()=>er("en"===et?"ar":"en"),children:[(0,a.jsx)("span",{className:"text-lg",children:"en"===et?"\uD83C\uDDFA\uD83C\uDDF8":"\uD83C\uDDEE\uD83C\uDDF6"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"en"===et?"EN":"AR"})]})]}),(0,a.jsx)("div",{className:"w-full",children:(0,a.jsxs)("div",{className:"flex items-center gap-2 border rounded-full px-4 py-3 bg-background shadow-sm",children:[(0,a.jsx)("input",{type:"text",placeholder:eo("products")||"البحث عن المنتجات...",className:"bg-transparent border-none focus:outline-none text-sm flex-1 placeholder:text-gray-400",value:H,onChange:e=>V(e.target.value),onKeyDown:e=>"Enter"===e.key&&ei()}),(0,a.jsx)(L.$,{variant:"ghost",className:"h-8 w-8 p-0 hover:bg-accent/80 transition-colors",style:{color:es},onClick:ei,children:(0,a.jsx)(h.A,{className:"h-4 w-4"})})]})})]}),(0,a.jsxs)("div",{className:"hidden md:flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4 flex-1",children:[(0,a.jsx)(v(),{href:"/",className:"flex items-center gap-2",children:(0,a.jsx)("div",{className:"text-[#1B3764] flex items-center gap-2",children:(0,a.jsx)("img",{src:"https://admin.codemedicalapps.com/content/commonImages/otherImages/18b_logo2x.png",alt:"Logo",className:"h-16 w-auto"})})}),(0,a.jsxs)("div",{className:"flex items-center gap-2 border rounded-full px-3 py-1.5 flex-1 max-w-md ml-4",children:[(0,a.jsxs)(D,{children:[(0,a.jsx)(O,{asChild:!0,children:(0,a.jsxs)(L.$,{variant:"ghost",className:"h-8 flex items-center gap-1 px-2",children:[(0,a.jsx)("span",{className:"text-muted-foreground text-sm",children:C||U||eo("category")}),(0,a.jsx)(p.A,{className:"h-4 w-4 text-muted-foreground"})]})}),(0,a.jsx)(M,{className:"w-64 p-0",align:"start",children:(0,a.jsx)("div",{className:"max-h-[300px] overflow-auto",children:n?(0,a.jsx)("div",{className:"p-4 text-center text-muted-foreground",children:eo("loadingCategories")}):(0,a.jsx)("div",{className:"grid",children:t.map(e=>(0,a.jsxs)("div",{className:"group",children:[(0,a.jsx)("button",{className:"w-full px-4 py-2 text-left hover:bg-accent",onClick:()=>{S(e.name),k(e.id),B(null)},children:e.name}),(0,a.jsx)("div",{className:"hidden group-hover:block absolute left-full top-0 w-48 bg-white shadow-lg rounded-md border",children:e.subcategories.map((t,s)=>(0,a.jsx)("button",{className:"w-full px-4 py-2 text-left hover:bg-accent",onClick:()=>{B(t),S(null),k(e.id)},children:t},s))})]},e.id))})})})]}),(0,a.jsx)("div",{className:"h-5 w-px bg-border mx-2"}),(0,a.jsx)("input",{type:"text",placeholder:eo("products"),className:"bg-transparent border-none focus:outline-none text-sm flex-1",value:H,onChange:e=>V(e.target.value),onKeyDown:e=>"Enter"===e.key&&ei()}),(0,a.jsx)(L.$,{variant:"ghost",className:"h-8 w-8 p-0",onClick:ei,children:(0,a.jsx)(h.A,{className:"h-4 w-4"})})]})]}),(0,a.jsx)("div",{className:"hidden md:block",children:(0,a.jsx)(T,{children:(0,a.jsxs)(P,{children:[(0,a.jsx)(_,{children:(0,a.jsx)(v(),{href:"/",legacyBehavior:!0,passHref:!0,children:(0,a.jsx)(I,{className:(0,A.cn)("group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"),children:eo("home")})})}),(0,a.jsx)(_,{children:(0,a.jsx)(v(),{href:"/hot-deals",legacyBehavior:!0,passHref:!0,children:(0,a.jsx)(I,{className:(0,A.cn)("group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"),children:eo("hotDeals")})})}),(0,a.jsx)(_,{children:(0,a.jsx)(v(),{href:"/products",legacyBehavior:!0,passHref:!0,children:(0,a.jsx)(I,{className:(0,A.cn)("group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"),children:eo("products")||"Products"})})}),(0,a.jsx)(_,{children:(0,a.jsx)(v(),{href:"/payment-methods",legacyBehavior:!0,passHref:!0,children:(0,a.jsx)(I,{className:(0,A.cn)("group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"),children:eo("paymentMethods")})})}),(0,a.jsx)(_,{children:(0,a.jsx)(v(),{href:"/follow-us",legacyBehavior:!0,passHref:!0,children:(0,a.jsx)(I,{className:(0,A.cn)("group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"),children:eo("followUs")})})}),(0,a.jsx)(_,{children:(0,a.jsx)(v(),{href:"/about",legacyBehavior:!0,passHref:!0,children:(0,a.jsx)(I,{className:(0,A.cn)("group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"),children:eo("aboutUs")})})}),(0,a.jsx)(_,{children:(0,a.jsx)(v(),{href:"/contact",legacyBehavior:!0,passHref:!0,children:(0,a.jsx)(I,{className:(0,A.cn)("group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"),children:eo("contactUs")})})})]})})}),(0,a.jsxs)("div",{className:"hidden md:flex items-center gap-4",children:[(0,a.jsx)(v(),{href:"/wishlist",children:(0,a.jsxs)(L.$,{variant:"ghost",size:"icon",className:"relative",children:[(0,a.jsx)(x.A,{className:"h-5 w-5",style:{color:es}}),(0,a.jsx)("span",{className:"absolute -top-1 -right-1 bg-primary text-white text-xs rounded-full h-4 w-4 flex items-center justify-center",children:Q})]})}),(0,a.jsx)(v(),{href:"/cart",children:(0,a.jsxs)(L.$,{variant:"ghost",size:"icon",className:"relative",children:[(0,a.jsx)(g.A,{className:"h-5 w-5",style:{color:es}}),(0,a.jsx)("span",{className:"absolute -top-1 -right-1 bg-primary text-white text-xs rounded-full h-4 w-4 flex items-center justify-center",children:W})]})})]})]})]}),b&&(0,a.jsx)($,{onColorSelect:e=>{en(e),w(!1)},onClose:()=>w(!1)})]})}var q=s(72517),H=s(15607),V=s(1372),W=s(88651);function J(){let{primaryColor:e,t}=(0,R.t)(),[s,n]=(0,r.useState)(""),[o,i]=(0,r.useState)(!1),[l,c]=(0,r.useState)({type:"",text:""}),{executeRecaptcha:u}=(0,W._Y)(),m=async e=>{if(e.preventDefault(),!u){console.error("Execute recaptcha not yet available");return}try{i(!0),c({type:"",text:""}),await u("subscribe");let e=await fetch("https://admin.codemedicalapps.com/api/v1/dynamic/dataoperation/insert-subscriber",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({requestParameters:{SubscriberEmail:s}})}),t=await e.json();e.ok?(c({type:"success",text:"Successfully subscribed!"}),n("")):c({type:"error",text:t.message||"Failed to subscribe"})}catch(e){c({type:"error",text:"An error occurred. Please try again."})}finally{i(!1)}};return(0,a.jsx)("footer",{className:"w-full",children:(0,a.jsxs)("div",{className:"text-white py-8 sm:py-12",style:{backgroundColor:e},children:[(0,a.jsxs)("div",{className:"container mx-auto px-4 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6 sm:gap-8",children:[(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"flex items-center gap-2",children:(0,a.jsx)("img",{src:"https://admin.codemedicalapps.com/content/commonImages/otherImages/18b_logo2x.png".replace(/\//g,"/"),alt:"Logo",className:"h-12 sm:h-16 w-auto bg-white p-2 rounded-md"})}),(0,a.jsx)("p",{className:"text-gray-300 text-sm sm:text-base",children:"We are professional team specialized in providing well known valuable medical courses, Ebooks, Printed books and popular medical accounts for all medical field staff all around the world with low cost and in short time."}),(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)(v(),{href:"https://www.facebook.com/codemedicalapps/",target:"_blank",rel:"noopener noreferrer",className:"hover:text-gray-300 transition-colors",children:(0,a.jsx)(q.A,{className:"h-5 w-5"})}),(0,a.jsx)(v(),{href:"https://t.me/codemedicalapps",target:"_blank",rel:"noopener noreferrer",className:"hover:text-gray-300 transition-colors",children:(0,a.jsx)(H.A,{className:"h-5 w-5"})}),(0,a.jsx)(v(),{href:"https://wa.me/*************",target:"_blank",rel:"noopener noreferrer",className:"hover:text-gray-300 transition-colors",children:(0,a.jsx)(d.A,{className:"h-5 w-5"})}),(0,a.jsx)(v(),{href:"https://m.me/***************",target:"_blank",rel:"noopener noreferrer",className:"hover:text-gray-300 transition-colors",children:(0,a.jsx)(V.A,{className:"h-5 w-5"})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-6 sm:gap-8 col-span-2 sm:col-span-1 md:col-span-2",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-base sm:text-lg font-semibold",children:t("quickLinks")}),(0,a.jsxs)("ul",{className:"space-y-2 sm:space-y-3 text-sm sm:text-base",children:[(0,a.jsx)("li",{children:(0,a.jsx)(v(),{href:"/about",className:"hover:text-gray-300 transition-colors",children:t("about")})}),(0,a.jsx)("li",{children:(0,a.jsx)(v(),{href:"/contact",className:"hover:text-gray-300 transition-colors",children:t("contact")})}),(0,a.jsx)("li",{children:(0,a.jsx)(v(),{href:"/hot-deals",className:"hover:text-gray-300 transition-colors",children:t("hotDeals")})}),(0,a.jsx)("li",{children:(0,a.jsx)(v(),{href:"/login",className:"hover:text-gray-300 transition-colors",children:t("login")})}),(0,a.jsx)("li",{children:(0,a.jsx)(v(),{href:"/signup",className:"hover:text-gray-300 transition-colors",children:t("signup")})})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-base sm:text-lg font-semibold",children:t("customerArea")}),(0,a.jsxs)("ul",{className:"space-y-2 sm:space-y-3 text-sm sm:text-base",children:[(0,a.jsx)("li",{children:(0,a.jsx)(v(),{href:"/account",className:"hover:text-gray-300 transition-colors",children:t("myAccount")})}),(0,a.jsx)("li",{children:(0,a.jsx)(v(),{href:"/orders",className:"hover:text-gray-300 transition-colors",children:t("orders")})}),(0,a.jsx)("li",{children:(0,a.jsx)(v(),{href:"/cart",className:"hover:text-gray-300 transition-colors",children:t("cart")})}),(0,a.jsx)("li",{children:(0,a.jsx)(v(),{href:"/wishlist",className:"hover:text-gray-300 transition-colors",children:t("wishlist")})}),(0,a.jsx)("li",{children:(0,a.jsx)(v(),{href:"/payment-methods",className:"hover:text-gray-300 transition-colors",children:t("paymentMethods")})})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-base sm:text-lg font-semibold",children:t("contact")}),(0,a.jsxs)("ul",{className:"space-y-2 sm:space-y-3 text-sm sm:text-base",children:[(0,a.jsxs)("li",{className:"flex items-center gap-2",children:[(0,a.jsxs)("span",{className:"text-gray-300",children:[t("location"),":"]}),(0,a.jsx)("span",{children:"Iraq"})]}),(0,a.jsxs)("li",{className:"flex items-center gap-2",children:[(0,a.jsxs)("span",{className:"text-gray-300",children:[t("callUs"),":"]}),(0,a.jsx)("a",{href:"tel:+*************",className:"hover:text-gray-300 transition-colors",children:"+964 ************"})]}),(0,a.jsxs)("li",{className:"flex items-center gap-2",children:[(0,a.jsxs)("span",{className:"text-gray-300",children:[t("emailUs"),":"]}),(0,a.jsx)("a",{href:"mailto:<EMAIL>",className:"hover:text-gray-300 transition-colors",children:"<EMAIL>"})]})]}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsx)("h3",{className:"text-base sm:text-lg font-semibold mb-4",children:t("newsletter")}),(0,a.jsxs)("form",{onSubmit:m,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,a.jsx)("input",{type:"email",value:s,onChange:e=>n(e.target.value),placeholder:t("enterEmail"),required:!0,className:"w-full px-4 py-2 text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-opacity-50",style:{outlineColor:e}}),(0,a.jsx)("button",{type:"submit",disabled:o,className:" px-2 py-2 border border-white rounded-md",style:{backgroundColor:e,color:"white",borderColor:"white"},children:o?(0,a.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white"}):(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("span",{children:t("subscribe")})})})]}),l.text&&(0,a.jsx)("p",{className:`text-sm text-center ${"success"===l.type?"text-green-400":"text-red-400"}`,children:l.text}),(0,a.jsx)("p",{className:"text-xs text-gray-300 text-center",children:t("newsletterDisclaimer")})]})]})]})]}),(0,a.jsx)("div",{className:"mt-6 pt-6 border-t border-white/20 text-center text-gray-300 text-sm sm:text-base px-4",children:(0,a.jsx)("p",{children:"\xa9 2024 Code Medical. All rights reserved."})})]})})}function Q({className:e=""}){return(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",className:e,fill:"currentColor",children:(0,a.jsx)("path",{d:"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413Z"})})}function Y(){return(0,a.jsx)("button",{className:"fixed bottom-20 right-6 z-50 flex h-12 w-12 items-center justify-center rounded-full bg-[#25D366] text-white shadow-md transition-all duration-300 hover:bg-[#128C7E] hover:scale-110",onClick:()=>{window.open("https://wa.me/00*************","_blank")},"aria-label":"Chat on WhatsApp",children:(0,a.jsx)(Q,{className:"h-6 w-6"})})}var Z=s(87137),X=s(35256);function K(){let e=(0,f.usePathname)(),{totalItems:t}=(0,y._)(),{totalItems:s}=(0,j.n)(),{primaryColor:n,t:o}=(0,R.t)(),[i,l]=(0,r.useState)(!1),[c,d]=(0,r.useState)(!1),[m,h]=(0,r.useState)([]),[p,b]=(0,r.useState)(!1),N=async()=>{if(m.length>0){d(!0);return}b(!0);try{let e={"Content-Type":"application/json",Accept:"application/json",Authorization:"Bearer "+localStorage.getItem("token")},t=await (0,w.MakeApiCallAsync)(w.TS.END_POINT_NAMES.GET_CATEGORIES_LIST,null,{PageNumber:1,PageSize:100,SortColumn:"Name",SortOrder:"ASC"},e,"POST",!0);if(t?.data?.data)try{let e=JSON.parse(t.data.data);if(Array.isArray(e)){let t=e.filter(e=>!e.ParentCategoryID);h(t),d(!0)}}catch(e){console.error("Error parsing categories data:",e)}}catch(e){console.error("Error fetching categories:",e)}finally{b(!1)}};if(!i)return null;let C=[{href:"/",icon:Z.A,label:o("home")||"الرئيسية",isActive:"/"===e,onClick:null},{href:"#",icon:X.A,label:o("categories")||"التصنيفات",isActive:!1,onClick:N},{href:"/cart",icon:g.A,label:o("cart")||"سلة التسوق",isActive:"/cart"===e,badge:t||0,onClick:null},{href:"/wishlist",icon:x.A,label:o("wishlist")||"المفضلة",isActive:"/wishlist"===e,badge:s||0,onClick:null},{href:"/login",icon:u.A,label:o("login")||"حسابي",isActive:"/login"===e||"/signup"===e,onClick:null}];return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50 safe-area-pb",children:(0,a.jsx)("div",{className:"flex items-center justify-around py-2",children:C.map(e=>{let t=e.icon,s=!!e.onClick,r="flex flex-col items-center justify-center min-w-0 flex-1 py-2 px-1"+(s?" bg-transparent border-none":""),o=(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(t,{className:"h-6 w-6 mb-1",style:{color:e.isActive?n:"#6B7280"}}),void 0!==e.badge&&e.badge>0&&(0,a.jsx)("span",{className:"absolute -top-1 -right-1 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center font-medium shadow-md",style:{backgroundColor:n},children:e.badge>99?"99+":e.badge})]}),(0,a.jsx)("span",{className:"text-xs font-medium text-center leading-tight mt-1",style:{color:e.isActive?n:"#6B7280"},children:e.label})]});return s?(0,a.jsx)("button",{onClick:e.onClick||void 0,className:r,type:"button",children:o},e.href):(0,a.jsx)(v(),{href:e.href,className:r,children:o},e.href)})})}),c&&(0,a.jsx)("div",{className:"md:hidden fixed inset-0 bg-black/50 z-50 flex items-end",children:(0,a.jsxs)("div",{className:"bg-white w-full max-h-[70vh] rounded-t-xl overflow-hidden",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold",style:{color:n},children:o("categories")||"التصنيفات"}),(0,a.jsx)(L.$,{variant:"ghost",size:"sm",onClick:()=>d(!1),className:"text-gray-500 hover:text-gray-700",children:"✕"})]}),(0,a.jsx)("div",{className:"overflow-y-auto max-h-[calc(70vh-80px)]",children:p?(0,a.jsxs)("div",{className:"p-8 text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 mx-auto mb-4",style:{borderColor:n}}),(0,a.jsx)("p",{className:"text-gray-500",children:o("loadingCategories")})]}):(0,a.jsx)("div",{className:"p-4 space-y-2",children:m.map(e=>(0,a.jsx)(v(),{href:`/products?category=${e.CategoryID}`,className:"block p-3 rounded-lg hover:bg-gray-50 transition-colors",onClick:()=>d(!1),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"font-medium text-gray-900",children:e.Name}),(0,a.jsx)("span",{className:"text-gray-400",children:"›"})]})},e.CategoryID))})})]})})]})}var ee=s(65518);let et=r.forwardRef(({className:e,title:t,description:s,action:r,...n},o)=>(0,a.jsxs)("div",{ref:o,className:(0,A.cn)("group relative flex items-center justify-between p-4 pr-8 space-x-4 overflow-hidden rounded-md border bg-background shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none",e),...n,children:[(0,a.jsxs)("div",{className:"grid gap-1",children:[t&&(0,a.jsx)("div",{className:"text-sm font-semibold",children:t}),s&&(0,a.jsx)("div",{className:"text-sm opacity-90",children:s})]}),r,(0,a.jsx)("button",{className:"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100",children:(0,a.jsx)(B.A,{className:"h-4 w-4"})})]}));et.displayName="Toast";let es=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,className:(0,A.cn)("text-sm font-semibold",e),...t}));es.displayName="ToastTitle";let ea=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,className:(0,A.cn)("text-sm opacity-90",e),...t}));ea.displayName="ToastDescription";let er=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("button",{ref:s,className:(0,A.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100",e),...t,children:(0,a.jsx)(B.A,{className:"h-4 w-4"})}));er.displayName="ToastClose";let en=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,className:(0,A.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));function eo({children:e,...t}){return(0,a.jsx)(a.Fragment,{children:e})}function ei(){let{toasts:e}=(0,ee.dj)();return(0,a.jsxs)(eo,{children:[e.map(function({id:e,title:t,description:s,action:r,...n}){return(0,a.jsxs)(et,{...n,children:[(0,a.jsxs)("div",{className:"grid gap-1",children:[t&&(0,a.jsx)(es,{children:t}),s&&(0,a.jsx)(ea,{children:s})]}),r,(0,a.jsx)(er,{})]},e)}),(0,a.jsx)(en,{})]})}en.displayName="ToastViewport",eo.displayName="ToastProvider";let el={whatsappNumber:"+**********",phoneNumber:"+**********",whatsappLink:"https://wa.me/**********"},ec=(0,r.createContext)(el);function ed({children:e}){return(0,a.jsx)(ec.Provider,{value:el,children:e})}var eu=s(81758),em=s(27725);function eh({children:e}){return(0,a.jsx)(R.Z,{children:(0,a.jsx)(N.v,{children:(0,a.jsx)(em.B,{children:(0,a.jsx)(y.e,{children:(0,a.jsx)(ed,{children:(0,a.jsx)(eu.U,{children:(0,a.jsx)(j.Z,{children:e})})})})})})})}var ep=s(91542);function ex({children:e}){return(0,a.jsx)("html",{lang:"en",children:(0,a.jsx)("body",{className:o().className,suppressHydrationWarning:!0,children:(0,a.jsx)(W.G3,{reCaptchaKey:"6LfS7D0rAAAAAAybL-FSr2N1pWStAiKxN_EapgHJ",scriptProps:{async:!0,defer:!0,appendTo:"body",nonce:void 0},container:{parameters:{badge:"inline",theme:"light"}},children:(0,a.jsxs)(eh,{children:[(0,a.jsx)(F,{}),(0,a.jsx)("main",{className:"min-h-screen pb-16 md:pb-0",children:e}),(0,a.jsx)(J,{}),(0,a.jsx)(Y,{}),(0,a.jsx)(K,{}),(0,a.jsx)(ei,{}),(0,a.jsx)(ep.l$,{position:"top-right"})]})})})})}},33853:(e,t,s)=>{"use strict";s.d(t,{J:()=>l,v:()=>i});var a=s(45512),r=s(58009),n=s(15348);let o=(0,r.createContext)(void 0);function i({children:e}){let[t,s]=(0,r.useState)(null),[i,l]=(0,r.useState)(null),[c,d]=(0,r.useState)(!0),u=async(e,t)=>{try{d(!0);let a=await (0,n.MakeApiCallAsync)(n.TS.END_POINT_NAMES.GET_USER_LOGIN,null,{requestParameters:{Email:e,Password:t}},{Accept:"application/json","Content-Type":"application/json"},"POST",!0);if(!a||!a.data||a.data.errorMessage)return{success:!1,message:a.data?.errorMessage||"Login failed. Please try again."};{let e;if(e="string"==typeof a.data.data?JSON.parse(a.data.data):a.data.data,!Array.isArray(e)||!(e.length>0)||"Login Successfully"!==e[0].ResponseMsg)return{success:!1,message:"Incorrect email or password!"};{let t=e[0];s(t),localStorage.setItem("user",JSON.stringify(t));let r=a.data.token||"";return l(r),localStorage.setItem("token",r),{success:!0,message:"Login successful!"}}}}catch(e){return console.error("Login error:",e),{success:!1,message:"An error occurred. Please try again!"}}finally{d(!1)}},m=null!==t&&t.UserId>0;return(0,a.jsx)(o.Provider,{value:{user:t,isLoggedIn:m,isLoading:c,login:u,logout:()=>{s(null),l(null),localStorage.removeItem("user"),localStorage.removeItem("token"),localStorage.removeItem("userId")},token:i},children:e})}function l(){let e=(0,r.useContext)(o);if(void 0===e)throw Error("useUser must be used within a UserProvider");return e}},37534:(e,t,s)=>{Promise.resolve().then(s.bind(s,19611))},39913:(e,t,s)=>{"use strict";s.d(t,{T:()=>a});let a={ADMIN_BASE_URL:"https://admin.codemedicalapps.com/",DYNAMIC_METHOD_SUB_URL:"api/",COMMON_CONTROLLER_SUB_URL:"api/common/",END_POINT_NAMES:{DOWNLOAD_DIGITAL_FILE:"downloadDigitalFile",GET_COUPON_CODE_DISCOUNT:"get-coupon-code-discount-value/calculate-coupon-discount"}}},59462:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n});var a=s(82281),r=s(94805);function n(...e){return(0,r.QP)((0,a.$)(e))}},65518:(e,t,s)=>{"use strict";s.d(t,{dj:()=>m});var a=s(58009);let r=0,n=new Map,o=e=>{if(n.has(e))return;let t=setTimeout(()=>{n.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);n.set(e,t)},i=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:s}=t;return s?o(s):e.toasts.forEach(e=>{o(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],c={toasts:[]};function d(e){c=i(c,e),l.forEach(e=>{e(c)})}function u({...e}){let t=(r=(r+1)%100).toString(),s=()=>d({type:"DISMISS_TOAST",toastId:t});return d({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||s()}}}),{id:t,dismiss:s,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function m(){let[e,t]=a.useState(c);return a.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>d({type:"DISMISS_TOAST",toastId:e})}}},71901:(e,t,s)=>{"use strict";s.d(t,{Z:()=>i,t:()=>l});var a=s(45512),r=s(58009);let n={en:{menu:"Menu",newsletter:"Newsletter",enterEmail:"Enter your email",newsletterDisclaimer:"Subscribe to our newsletter to receive updates and exclusive offers",popularCategories:"Popular Categories!",settings:"Settings",language:"Language",theme:"Theme",color:"Color",save:"Save",cancel:"Cancel",close:"Close",phone:"00*************",phonenumber:"Phone Number",email:"<EMAIL>",liveChat:"Live Chat",welcome:"Welcome",logout:"Logout",login:"Login",signup:"Sign Up",signUp:"Sign Up",category:"Category",categories:"Categories",filters:"Filters",clearAll:"Clear All",products:"Products",loadingCategories:"Loading categories...",home:"Home",todayDeals:"Today's Deals",followUs:"Follow Us",aboutUs:"About Us",contactUs:"Contact Us",tryAgain:"Try Again",noProductsFound:"No products found",allProducts:"All Products",quickLinks:"Quick Links",about:"About Us",contact:"Contact Us",hotDeals:"Hot Deals",customerArea:"Customer Area",myAccount:"My Account",orders:"Orders",cart:"Cart",wishlist:"Wishlist",paymentMethods:"Payment Methods",location:"Location",callUs:"Call Us",emailUs:"Email Us",subscribe:"Subscribe",name:"Name",subject:"Subject",message:"Message",sendMessage:"Send Message",sending:"Sending...",messageSent:"Message sent successfully!",messageError:"Failed to send message",contactInfo:"Contact Information",address:"Address",findUs:"Find Us"},ar:{menu:"القائمة",popularCategories:"الفئات الشائعة!",settings:"الإعدادات",language:"اللغة",theme:"المظهر",color:"اللون",save:"حفظ",cancel:"إلغاء",close:"إغلاق",phone:"00*************",phonenumber:"رقم الهاتف",email:"<EMAIL>",liveChat:"محادثة مباشرة",welcome:"مرحباً",logout:"تسجيل الخروج",login:"تسجيل الدخول",signup:"تسجيل جديد",signUp:"تسجيل جديد",category:"الفئة",categories:"الفئات",products:"المنتجات",loadingCategories:"جاري تحميل الفئات...",filters:"المرشحات",clearAll:"مسح الكل",home:"الرئيسية",todayDeals:"عروض اليوم",followUs:"تابعنا",aboutUs:"من نحن",contactUs:"اتصل بنا",tryAgain:"حاول مرة أخرى",noProductsFound:"لم يتم العثور على منتجات",allProducts:"جميع المنتجات",quickLinks:"روابط سريعة",about:"من نحن",contact:"اتصل بنا",hotDeals:"عروض ساخنة",customerArea:"منطقة العملاء",myAccount:"حسابي",orders:"الطلبات",cart:"السلة",wishlist:"المفضلة",paymentMethods:"طرق الدفع",location:"الموقع",callUs:"اتصل بنا",emailUs:"راسلنا",subscribe:"اشترك",name:"الاسم",subject:"الموضوع",message:"الرسالة",sendMessage:"إرسال الرسالة",sending:"جاري الإرسال...",messageSent:"تم إرسال الرسالة بنجاح!",messageError:"فشل في إرسال الرسالة",contactInfo:"معلومات الاتصال",address:"العنوان",findUs:"موقعنا"}},o=(0,r.createContext)(void 0);function i({children:e}){let[t,s]=(0,r.useState)("light"),[i,l]=(0,r.useState)("en"),[c,d]=(0,r.useState)("#0074b2");return(0,a.jsx)(o.Provider,{value:{theme:t,language:i,primaryColor:c,toggleTheme:()=>{s("light"===t?"dark":"light")},setLanguage:e=>{l(e),document.documentElement.dir="ar"===e?"rtl":"ltr"},setPrimaryColor:e=>{d(e)},t:e=>(function(e,t){let s=n[t];return e in s?s[e]:"en"!==t&&e in n.en?n.en[e]:e})(e,i)},children:e})}function l(){let e=(0,r.useContext)(o);if(void 0===e)throw Error("useSettings must be used within a SettingsProvider");return e}},74743:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,66959,23)),Promise.resolve().then(s.t.bind(s,33875,23)),Promise.resolve().then(s.t.bind(s,88903,23)),Promise.resolve().then(s.t.bind(s,57174,23)),Promise.resolve().then(s.t.bind(s,84178,23)),Promise.resolve().then(s.t.bind(s,87190,23)),Promise.resolve().then(s.t.bind(s,48429,23)),Promise.resolve().then(s.t.bind(s,61365,23))},76080:()=>{},77702:(e,t,s)=>{Promise.resolve().then(s.bind(s,33053))},81758:(e,t,s)=>{"use strict";s.d(t,{U:()=>i,Y:()=>l});var a=s(45512),r=s(58009),n=s(15348);let o=(0,r.createContext)(void 0);function i({children:e}){let[t,s]=(0,r.useState)(null),[i,l]=(0,r.useState)(!1),c=async(e,t,a)=>{if(!e.trim())return{valid:!1,message:"Please enter a coupon code",discount:0};l(!0);try{let r=a?.map(e=>({ProductId:e.id,ProductName:e.name,Price:e.adjustedPrice||e.price,Quantity:e.quantity,IsDiscountAllowed:!0}))||[],o=JSON.stringify(r),i={requestParameters:{CouponCode:e.toUpperCase(),cartJsonData:o}},l=await (0,n.MakeApiCallAsync)(n.TS.END_POINT_NAMES.GET_COUPON_CODE_DISCOUNT,n.TS.COMMON_CONTROLLER_SUB_URL,i,{"Content-Type":"application/json",Accept:"application/json"},"POST");if(!l||!l.data||l.data.errorMessage)return{valid:!1,message:l.data?.errorMessage||"Failed to validate coupon",discount:0};{let a;if((a="string"==typeof l.data.data?JSON.parse(l.data.data):l.data.data)&&a.DiscountValueAfterCouponAppliedWithQuantity>0){let t=a.DiscountValueAfterCouponAppliedWithQuantity,r=2===a.DiscountValueType?"percentage":"fixed",n={code:e.toUpperCase(),discount:t,type:r};return s(n),{valid:!0,message:"Coupon applied successfully!",discount:t}}if(a&&Array.isArray(a)&&a.length>0){let r=a[0];if(r&&r.DiscountValue>0){let a=0;if(1===r.DiscountValueType?a=r.DiscountValue:2===r.DiscountValueType&&(a=r.DiscountValue*t/100),a>0){let t={code:e.toUpperCase(),discount:a,type:2===r.DiscountValueType?"percentage":"fixed"};return s(t),{valid:!0,message:"Coupon applied successfully!",discount:a}}}}return{valid:!1,message:"Invalid coupon code or coupon not applicable to your cart",discount:0}}}catch(e){return console.error("Coupon validation error:",e),{valid:!1,message:"Error validating coupon. Please try again.",discount:0}}finally{l(!1)}};return(0,a.jsx)(o.Provider,{value:{appliedCoupon:t,validateCoupon:c,clearCoupon:()=>{s(null)},isLoading:i},children:e})}function l(){let e=(0,r.useContext)(o);if(void 0===e)throw Error("useCoupon must be used within a CouponProvider");return e}},84194:(e,t,s)=>{"use strict";s.d(t,{_:()=>i,e:()=>o});var a=s(45512),r=s(58009);let n=(0,r.createContext)(void 0);function o({children:e}){let[t,s]=(0,r.useState)([]),[o,i]=(0,r.useState)(!1),l=e=>{s(t=>t.filter(t=>t.id!==e))},c=t.reduce((e,t)=>e+t.quantity,0),d=t.reduce((e,t)=>e+(t.discountPrice?Math.min(t.discountPrice,t.adjustedPrice):t.adjustedPrice)*t.quantity,0),u=t.reduce((e,t)=>e+(t.adjustedIqdPrice||t.iqdPrice||0)*t.quantity,0);return(0,a.jsx)(n.Provider,{value:{items:t,addToCart:(e,t,a=[],r,n=1500)=>{s(s=>{let o=e.price,i=r||Math.round(e.price*n),l=i;a.forEach(t=>{if(t.PriceAdjustment&&t.PriceAdjustmentType){let s=e.originalPrice||e.price;switch(t.PriceAdjustmentType){case 1:o+=t.PriceAdjustment,l+=Math.round(t.PriceAdjustment*n);break;case 2:let a=s*t.PriceAdjustment/100;o+=a,l+=Math.round(a*n)}}});let c=s.findIndex(t=>t.id===e.id&&JSON.stringify(t.attributes?.sort((e,t)=>e.ProductAttributeID-t.ProductAttributeID))===JSON.stringify(a?.sort((e,t)=>e.ProductAttributeID-t.ProductAttributeID)));if(!(c>=0))return[...s,{...e,iqdPrice:i,adjustedIqdPrice:Math.max(0,l),quantity:t,attributes:a,adjustedPrice:Math.max(0,o),originalPrice:e.originalPrice}];{let e=[...s];return e[c].quantity+=t,e}})},removeFromCart:l,updateQuantity:(e,t)=>{if(t<=0){l(e);return}s(s=>s.map(s=>s.id===e?{...s,quantity:t}:s))},clearCart:()=>{s([])},totalItems:c,subtotal:d,subtotalIQD:u,total:d,totalIQD:u,isHydrated:o},children:e})}function i(){let e=(0,r.useContext)(n);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e}},87021:(e,t,s)=>{"use strict";s.d(t,{$:()=>c,r:()=>l});var a=s(45512),r=s(58009),n=s(12705),o=s(21643),i=s(59462);let l=(0,o.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-red-500 text-destructive-foreground hover:bg-red-600",outline:"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=r.forwardRef(({className:e,variant:t,size:s,asChild:r=!1,...o},c)=>{let d=r?n.DX:"button";return(0,a.jsx)(d,{className:(0,i.cn)(l({variant:t,size:s,className:e})),ref:c,...o})});c.displayName="Button"}};