(()=>{var e={};e.id=272,e.ids=[272],e.modules={80:(e,t,s)=>{Promise.resolve().then(s.bind(s,67618))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},14494:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},24790:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(45512),a=s(37778),l=s(97643),d=s(28531),m=s.n(d),i=s(71901);function n(){let{t:e,primaryColor:t}=(0,i.t)();return(0,r.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,r.jsx)(a.Qp,{className:"mb-6",children:(0,r.jsxs)(a.AB,{children:[(0,r.jsx)(a.J5,{children:(0,r.jsx)(a.w1,{asChild:!0,children:(0,r.jsx)(m(),{href:"/",children:e("home")})})}),(0,r.jsx)(a.tH,{}),(0,r.jsx)(a.J5,{children:(0,r.jsx)(a.tJ,{children:e("paymentMethods")})})]})}),(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,r.jsx)("h1",{className:"text-2xl md:text-3xl font-bold mb-6 md:mb-8 text-center",children:"Payment Methods"}),(0,r.jsxs)("section",{className:"mb-12",children:[(0,r.jsx)("h2",{className:"text-xl md:text-2xl font-semibold mb-4 md:mb-6",style:{color:t},children:"Inside Iraq"}),(0,r.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,r.jsx)(l.Zp,{className:"p-4 md:p-6",children:(0,r.jsxs)("div",{className:"flex flex-col items-center text-center md:flex-row md:items-start md:text-left md:gap-4",children:[(0,r.jsx)("img",{src:"/Zaincash iraq.png",alt:"Zain Cash",className:"h-16 w-16 flex-shrink-0 mb-2 md:mb-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-base md:text-xl font-semibold mb-1 md:mb-2 break-words",children:"Zain cash (Iraq)"}),(0,r.jsx)("p",{className:"text-base md:text-lg font-medium break-all",children:"***********"})]})]})}),(0,r.jsx)(l.Zp,{className:"p-4 md:p-6",children:(0,r.jsxs)("div",{className:"flex flex-col items-center text-center md:flex-row md:items-start md:text-left md:gap-4",children:[(0,r.jsx)("img",{src:"/Qicard iraq.png",alt:"Rafidein Account",className:"h-16 w-16 flex-shrink-0 mb-2 md:mb-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-base md:text-xl font-semibold mb-1 md:mb-2 break-words",children:" Rafidain Bank"}),(0,r.jsx)("p",{className:"text-base md:text-lg font-medium break-all",children:"**********"})]})]})}),(0,r.jsx)(l.Zp,{className:"p-4 md:p-6",children:(0,r.jsxs)("div",{className:"flex flex-col items-center text-center md:flex-row md:items-start md:text-left md:gap-4",children:[(0,r.jsx)("img",{src:"/Asia pay.png",alt:"Asia Pay",className:"h-16 w-16 flex-shrink-0 mb-2 md:mb-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-base md:text-xl font-semibold mb-1 md:mb-2",children:"Asia Pay"}),(0,r.jsx)("p",{className:"text-base md:text-lg font-medium break-all",children:"***********"})]})]})})]})]}),(0,r.jsxs)("section",{children:[(0,r.jsx)("h2",{className:"text-xl md:text-2xl font-semibold mb-4 md:mb-6 mt-8 md:mt-12",style:{color:t},children:"Outside Iraq"}),(0,r.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,r.jsx)(l.Zp,{className:"p-4 md:p-6",children:(0,r.jsxs)("div",{className:"flex flex-col items-center text-center md:flex-row md:items-start md:text-left md:gap-4",children:[(0,r.jsx)("img",{src:"/Paypal.png",alt:"PayPal",className:"h-16 w-16 flex-shrink-0 mb-2 md:mb-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-base md:text-xl font-semibold mb-1 md:mb-2",children:"PayPal"}),(0,r.jsx)("p",{className:"mb-1 md:mb-2 text-sm md:text-base",children:"You can pay through this link\uD83D\uDC47"}),(0,r.jsx)("a",{href:"https://paypal.me/FatimahNaser?country.x=JO&locale.x=en_US",target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:text-blue-800 underline mb-2 block break-all",children:"https://paypal.me/FatimahNaser?country.x=JO&locale.x=en_US"})]})]})}),(0,r.jsx)(l.Zp,{className:"p-4 md:p-6",children:(0,r.jsxs)("div",{className:"flex flex-col items-center text-center md:flex-row md:items-start md:text-left md:gap-4",children:[(0,r.jsx)("img",{src:"/Amazon gift card.png",alt:"Amazon Gift",className:"h-16 w-16 flex-shrink-0 mb-2 md:mb-0"}),(0,r.jsxs)("div",{className:"space-y-2 w-full",children:[(0,r.jsx)("h3",{className:"text-base md:text-xl font-semibold mb-1 md:mb-2",children:"Amazon Gift"}),(0,r.jsx)("a",{href:"https://www.amazon.com/Amazon-eGift-Card-Logo-Animated/dp/B07PCMWTSG/ref=mp_s_a_1_1?adgrpid=160438626878&dib=eyJ2IjoiMSJ9.y343JC2nqCCCtAt_MaFdYdSEoDk1IL8C8OVn3MADfESEozTH6jWzFIJ4WqlXn7_W-n2IrnPR-rfE3Spk4QYVuOOL7cvbuK9Esy0CXQivH6v0c4KW6RfZeH8pYn15Gdj-s62p0V-fiHzAE12D4YOgeY2zQf3sUuAQF30eHiR7nSfSyvGj9P0M79Suz3VRAqqxS64beG-r2SJhB_Y_apq-6Q.gbfTjpxr2hWpO9dWg-U8dthgvZM21cwxR6PrsZBpG38&dib_tag=se&hvadid=692707382867&hvdev=m&hvlocphy=9211521&hvnetw=g&hvqmt=e&hvrand=15003258399388157606&hvtargid=kwd-2389411675177&hydadcr=22339_13333066&keywords=amazon%27+gift+card&mcid=90ecf431d9b83733b420d0f87065fc78&qid=1748814128&sr=8-1",target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:text-blue-800 underline block break-all",children:"Amazon eGift Card Link"}),(0,r.jsx)("p",{className:"text-gray-700 text-sm md:text-base",children:"Please choose the amount and then send it to this email\uD83D\uDC47"}),(0,r.jsx)("p",{className:"text-base md:text-lg font-medium break-all",children:"<EMAIL>"})]})]})})]})]}),(0,r.jsxs)("section",{className:"mt-8",children:[(0,r.jsx)("h2",{className:"text-xl md:text-2xl font-semibold mb-4 md:mb-6 mt-8 md:mt-12",style:{color:t},children:"Cash on Delivery"}),(0,r.jsx)(l.Zp,{className:"p-2 md:p-4",children:(0,r.jsxs)("div",{className:"flex flex-col items-center text-center md:flex-row md:items-start md:text-left",children:[(0,r.jsx)("div",{className:"w-32 h-32 md:w-40 md:h-40 rounded-full flex items-center justify-center mb-4 md:mb-0 md:mr-4",style:{backgroundColor:`${t}20`},children:(0,r.jsx)("img",{src:"/Cash on delivery.png",alt:"Cash on Delivery",className:"w-full h-full rounded-full bg-white p-2 object-contain"})}),(0,r.jsxs)("div",{className:"md:flex-1",children:[(0,r.jsx)("h3",{className:"text-base md:text-xl font-semibold mb-1 md:mb-2",children:"Cash on Delivery"}),(0,r.jsx)("p",{className:"text-muted-foreground text-sm md:text-base",children:"Pay in cash upon delivery - we offer delivery to all provinces within Iraq. Additional fees may apply depending on your location."})]})]})})]}),(0,r.jsxs)("section",{className:"mt-12",children:[(0,r.jsx)("h2",{className:"text-xl md:text-2xl font-bold mb-4 md:mb-6 mt-8 md:mt-12",children:"Payment Process"}),(0,r.jsx)(l.Zp,{className:"p-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 rounded-full bg-muted flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)("span",{className:"text-xl font-bold",children:"1"})}),(0,r.jsx)("h3",{className:"font-medium text-sm md:text-base mb-1 md:mb-2",children:"Select Products"}),(0,r.jsx)("p",{className:"text-xs md:text-sm text-muted-foreground",children:"Add items to your cart"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 rounded-full bg-muted flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)("span",{className:"text-xl font-bold",children:"2"})}),(0,r.jsx)("h3",{className:"font-medium text-sm md:text-base mb-1 md:mb-2",children:"Shipping Details"}),(0,r.jsx)("p",{className:"text-xs md:text-sm text-muted-foreground",children:"Enter your shipping information"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 rounded-full bg-muted flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)("span",{className:"text-xl font-bold",children:"3"})}),(0,r.jsx)("h3",{className:"font-medium text-sm md:text-base mb-1 md:mb-2",children:"Payment Method"}),(0,r.jsx)("p",{className:"text-xs md:text-sm text-muted-foreground",children:"Choose your payment method"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 rounded-full bg-muted flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)("span",{className:"text-xl font-bold",children:"4"})}),(0,r.jsx)("h3",{className:"font-medium text-sm md:text-base mb-1 md:mb-2",children:"Confirmation"}),(0,r.jsx)("p",{className:"text-xs md:text-sm text-muted-foreground",children:"Review and confirm your order"})]})]})})]}),(0,r.jsx)("div",{className:"mt-12 text-center",children:(0,r.jsx)("p",{className:"text-gray-700 text-sm md:text-base text-center px-4",children:"For payment support, please contact our customer service team."})})]})]})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34928:(e,t,s)=>{Promise.resolve().then(s.bind(s,24790))},37778:(e,t,s)=>{"use strict";s.d(t,{AB:()=>n,J5:()=>c,Qp:()=>i,tH:()=>p,tJ:()=>x,w1:()=>o});var r=s(45512),a=s(58009),l=s(12705),d=s(99905),m=(s(14494),s(59462));let i=a.forwardRef(({...e},t)=>(0,r.jsx)("nav",{ref:t,"aria-label":"breadcrumb",...e}));i.displayName="Breadcrumb";let n=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("ol",{ref:s,className:(0,m.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",e),...t}));n.displayName="BreadcrumbList";let c=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("li",{ref:s,className:(0,m.cn)("inline-flex items-center gap-1.5",e),...t}));c.displayName="BreadcrumbItem";let o=a.forwardRef(({asChild:e,className:t,...s},a)=>{let d=e?l.DX:"a";return(0,r.jsx)(d,{ref:a,className:(0,m.cn)("transition-colors hover:text-foreground",t),...s})});o.displayName="BreadcrumbLink";let x=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("span",{ref:s,role:"link","aria-disabled":"true","aria-current":"page",className:(0,m.cn)("font-normal text-foreground",e),...t}));x.displayName="BreadcrumbPage";let p=({children:e,className:t,...s})=>(0,r.jsx)("li",{role:"presentation","aria-hidden":"true",className:(0,m.cn)("[&>svg]:size-3.5",t),...s,children:e??(0,r.jsx)(d.A,{})});p.displayName="BreadcrumbSeparator"},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67618:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\payment-methods\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\payment-methods\\page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},90824:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>d.a,__next_app__:()=>o,pages:()=>c,routeModule:()=>x,tree:()=>n});var r=s(70260),a=s(28203),l=s(25155),d=s.n(l),m=s(67292),i={};for(let e in m)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>m[e]);s.d(t,i);let n={children:["",{children:["payment-methods",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,67618)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\payment-methods\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,19611)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\payment-methods\\page.tsx"],o={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/payment-methods/page",pathname:"/payment-methods",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:n}})},94735:e=>{"use strict";e.exports=require("events")},97643:(e,t,s)=>{"use strict";s.d(t,{Wu:()=>m,Zp:()=>d,wL:()=>i});var r=s(45512),a=s(58009),l=s(59462);let d=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));d.displayName="Card",a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",e),...t})).displayName="CardHeader",a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("h3",{ref:s,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t})).displayName="CardTitle",a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("p",{ref:s,className:(0,l.cn)("text-sm text-muted-foreground",e),...t})).displayName="CardDescription";let m=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,l.cn)("p-6 pt-0",e),...t}));m.displayName="CardContent";let i=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,l.cn)("flex items-center p-6 pt-0",e),...t}));i.displayName="CardFooter"},99905:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,551,875],()=>s(90824));module.exports=r})();