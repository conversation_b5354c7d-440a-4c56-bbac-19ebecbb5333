(()=>{var e={};e.id=965,e.ids=[965],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},14494:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(41680).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},23339:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>u});var t=s(45512);s(58009);var a=s(37778),o=s(97643),l=s(28531),i=s.n(l),n=s(71901),c=s(31575),d=s(1372),p=s(72517),m=s(15607);function u(){let{t:e,primaryColor:r}=(0,n.t)(),s=[{name:"WhatsApp",Icon:c.A,url:"https://wa.me/9647836071686",description:"Chat with us on WhatsApp"},{name:"Messenger",Icon:d.A,url:"https://m.me/259317334778090",description:"Message us on Facebook Messenger"},{name:"Facebook",Icon:p.A,url:"https://www.facebook.com/codemedicalapps/",description:"Follow us on Facebook"},{name:"Telegram",Icon:m.A,url:"https://t.me/codemedicalapps",description:"Join our Telegram channel"}];return(0,t.jsxs)("div",{className:"container mx-auto py-4 sm:py-8 px-2 sm:px-4",children:[(0,t.jsx)(a.Qp,{className:"mb-4 sm:mb-6 text-sm sm:text-base",children:(0,t.jsxs)(a.AB,{children:[(0,t.jsx)(a.J5,{children:(0,t.jsx)(a.w1,{asChild:!0,children:(0,t.jsx)(i(),{href:"/",children:"Home"})})}),(0,t.jsx)(a.tH,{}),(0,t.jsx)(a.J5,{children:(0,t.jsx)(a.tJ,{children:"Follow Us"})})]})}),(0,t.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,t.jsx)("h1",{className:"text-2xl sm:text-3xl font-bold mb-6 sm:mb-8 text-center",children:"Follow Us"}),(0,t.jsx)(o.Zp,{className:"p-4 sm:p-6 mb-6 sm:mb-8 text-center",children:(0,t.jsx)("p",{className:"text-base sm:text-lg text-muted-foreground",children:"Your message is always our highest priority and we highly appreciate your valuable time - our support team will reply to your message as soon as possible."})}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6",children:s.map(e=>(0,t.jsx)(o.Zp,{className:"p-4 sm:p-6 hover:shadow-lg transition-shadow",children:(0,t.jsxs)("a",{href:e.url,target:"_blank",rel:"noopener noreferrer",className:"flex flex-col sm:flex-row items-center sm:items-start text-center sm:text-left gap-3 sm:gap-4",children:[(0,t.jsx)("div",{className:"w-12 h-12 sm:w-14 sm:h-14 rounded-full flex items-center justify-center mb-2 sm:mb-0",style:{backgroundColor:`${r}20`},children:(0,t.jsx)(e.Icon,{className:"h-6 w-6 sm:h-7 sm:w-7",style:{color:r}})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h3",{className:"text-lg sm:text-xl font-semibold mb-1 sm:mb-2",children:e.name}),(0,t.jsx)("p",{className:"text-sm sm:text-base text-muted-foreground",children:e.description})]})]})},e.name))})]})]})}},26580:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>l.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>m,tree:()=>c});var t=s(70260),a=s(28203),o=s(25155),l=s.n(o),i=s(67292),n={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>i[e]);s.d(r,n);let c={children:["",{children:["follow-us",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,67543)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\follow-us\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,19611)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\follow-us\\page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/follow-us/page",pathname:"/follow-us",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37778:(e,r,s)=>{"use strict";s.d(r,{AB:()=>c,J5:()=>d,Qp:()=>n,tH:()=>u,tJ:()=>m,w1:()=>p});var t=s(45512),a=s(58009),o=s(12705),l=s(99905),i=(s(14494),s(59462));let n=a.forwardRef(({...e},r)=>(0,t.jsx)("nav",{ref:r,"aria-label":"breadcrumb",...e}));n.displayName="Breadcrumb";let c=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("ol",{ref:s,className:(0,i.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",e),...r}));c.displayName="BreadcrumbList";let d=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("li",{ref:s,className:(0,i.cn)("inline-flex items-center gap-1.5",e),...r}));d.displayName="BreadcrumbItem";let p=a.forwardRef(({asChild:e,className:r,...s},a)=>{let l=e?o.DX:"a";return(0,t.jsx)(l,{ref:a,className:(0,i.cn)("transition-colors hover:text-foreground",r),...s})});p.displayName="BreadcrumbLink";let m=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("span",{ref:s,role:"link","aria-disabled":"true","aria-current":"page",className:(0,i.cn)("font-normal text-foreground",e),...r}));m.displayName="BreadcrumbPage";let u=({children:e,className:r,...s})=>(0,t.jsx)("li",{role:"presentation","aria-hidden":"true",className:(0,i.cn)("[&>svg]:size-3.5",r),...s,children:e??(0,t.jsx)(l.A,{})});u.displayName="BreadcrumbSeparator"},55591:e=>{"use strict";e.exports=require("https")},59515:(e,r,s)=>{Promise.resolve().then(s.bind(s,67543))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67543:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\follow-us\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\follow-us\\page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")},96307:(e,r,s)=>{Promise.resolve().then(s.bind(s,23339))},97643:(e,r,s)=>{"use strict";s.d(r,{Wu:()=>i,Zp:()=>l,wL:()=>n});var t=s(45512),a=s(58009),o=s(59462);let l=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,o.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));l.displayName="Card",a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",e),...r})).displayName="CardHeader",a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("h3",{ref:s,className:(0,o.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r})).displayName="CardTitle",a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("p",{ref:s,className:(0,o.cn)("text-sm text-muted-foreground",e),...r})).displayName="CardDescription";let i=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,o.cn)("p-6 pt-0",e),...r}));i.displayName="CardContent";let n=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,o.cn)("flex items-center p-6 pt-0",e),...r}));n.displayName="CardFooter"},99905:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(41680).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[638,320,875],()=>s(26580));module.exports=t})();