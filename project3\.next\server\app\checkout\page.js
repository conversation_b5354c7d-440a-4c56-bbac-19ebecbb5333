(()=>{var e={};e.id=279,e.ids=[279],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},18195:(e,r,s)=>{Promise.resolve().then(s.bind(s,93243))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},25409:(e,r,s)=>{"use strict";s.d(r,{p:()=>d});var t=s(45512),a=s(58009),i=s(59462);let d=a.forwardRef(({className:e,type:r,...s},a)=>(0,t.jsx)("input",{type:r,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...s}));d.displayName="Input"},27183:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\checkout\\page.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30830:(e,r,s)=>{"use strict";s.d(r,{hO:()=>o,sG:()=>l});var t=s(58009),a=s(55740),i=s(12705),d=s(45512),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,r)=>{let s=t.forwardRef((e,s)=>{let{asChild:t,...a}=e,l=t?i.DX:r;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,d.jsx)(l,{...a,ref:s})});return s.displayName=`Primitive.${r}`,{...e,[r]:s}},{});function o(e,r){e&&a.flushSync(()=>e.dispatchEvent(r))}},33873:e=>{"use strict";e.exports=require("path")},37778:(e,r,s)=>{"use strict";s.d(r,{AB:()=>n,J5:()=>c,Qp:()=>o,tH:()=>x,tJ:()=>u,w1:()=>m});var t=s(45512),a=s(58009),i=s(12705),d=s(99905),l=(s(14494),s(59462));let o=a.forwardRef(({...e},r)=>(0,t.jsx)("nav",{ref:r,"aria-label":"breadcrumb",...e}));o.displayName="Breadcrumb";let n=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("ol",{ref:s,className:(0,l.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",e),...r}));n.displayName="BreadcrumbList";let c=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("li",{ref:s,className:(0,l.cn)("inline-flex items-center gap-1.5",e),...r}));c.displayName="BreadcrumbItem";let m=a.forwardRef(({asChild:e,className:r,...s},a)=>{let d=e?i.DX:"a";return(0,t.jsx)(d,{ref:a,className:(0,l.cn)("transition-colors hover:text-foreground",r),...s})});m.displayName="BreadcrumbLink";let u=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("span",{ref:s,role:"link","aria-disabled":"true","aria-current":"page",className:(0,l.cn)("font-normal text-foreground",e),...r}));u.displayName="BreadcrumbPage";let x=({children:e,className:r,...s})=>(0,t.jsx)("li",{role:"presentation","aria-hidden":"true",className:(0,l.cn)("[&>svg]:size-3.5",r),...s,children:e??(0,t.jsx)(d.A,{})});x.displayName="BreadcrumbSeparator"},45037:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(41680).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},47699:(e,r,s)=>{"use strict";s.d(r,{J:()=>n});var t=s(45512),a=s(58009),i=s(92405),d=s(21643),l=s(59462);let o=(0,d.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),n=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)(i.b,{ref:s,className:(0,l.cn)(o(),e),...r}));n.displayName=i.b.displayName},55591:e=>{"use strict";e.exports=require("https")},60814:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>d.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>n});var t=s(70260),a=s(28203),i=s(25155),d=s.n(i),l=s(67292),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(r,o);let n={children:["",{children:["checkout",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,27183)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\checkout\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,19611)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\checkout\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/checkout/page",pathname:"/checkout",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:n}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65051:(e,r,s)=>{Promise.resolve().then(s.bind(s,27183))},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},92405:(e,r,s)=>{"use strict";s.d(r,{b:()=>l});var t=s(58009),a=s(30830),i=s(45512),d=t.forwardRef((e,r)=>(0,i.jsx)(a.sG.label,{...e,ref:r,onMouseDown:r=>{r.target.closest("button, input, select, textarea")||(e.onMouseDown?.(r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));d.displayName="Label";var l=d},93243:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>g});var t=s(45512),a=s(58009),i=s(84194),d=s(71901),l=s(87021),o=s(97643),n=s(25409),c=s(47699),m=s(37778),u=s(28531),x=s.n(u),p=s(45037),h=s(96795),f=s(31393),j=s(79334),y=s(42720);function g(){let{t:e,primaryColor:r}=(0,d.t)(),{items:s,total:u,clearCart:g}=(0,i._)(),N=(0,j.useRouter)(),[v,b]=(0,a.useState)(!1),[C,w]=(0,a.useState)(""),[k,P]=(0,a.useState)({firstName:"",lastName:"",email:"",phone:"",address:"",city:"",country:"",zipCode:""}),A=e=>{let{name:r,value:s}=e.target;P(e=>({...e,[r]:s}))},q=async e=>{if(e.preventDefault(),!v){y.A.fire({title:"Login Required",text:"Please login to complete your purchase",icon:"info",showCancelButton:!0,confirmButtonText:"Login",cancelButtonText:"Cancel"}).then(e=>{e.isConfirmed&&N.push("/login?redirect=checkout")});return}if(!C){y.A.fire({title:"Error",text:"Please select a payment method",icon:"error"});return}if(["firstName","lastName","email","phone","address"].filter(e=>!k[e]).length>0){y.A.fire({title:"Error",text:"Please fill in all required fields",icon:"error"});return}try{y.A.fire({title:"Processing",text:"Please wait while we process your order...",allowOutsideClick:!1,didOpen:()=>{y.A.showLoading()}});let e=s.map(e=>({ProductId:e.id,ProductName:e.name,Quantity:e.quantity,UnitPrice:e.discountPrice||e.price})),r={requestParameters:{UserID:localStorage.getItem("userId")||"0",OrderNote:"Order from Next.js app",cartJsonData:JSON.stringify(e),CouponCode:"",PaymentMethod:C,paymentToken:"",payPalOrderConfirmJson:"",recordValueJson:JSON.stringify({ShippingAddress:{FirstName:k.firstName,LastName:k.lastName,Email:k.email,Phone:k.phone,Address:k.address,City:k.city,Country:k.country,ZipCode:k.zipCode}})}},t=await fetch("https://admin.codemedicalapps.com/api/v1/dynamic/dataoperation/post-customer-order",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify(r)}),a=await t.json();if(t.ok&&a.data){let e;try{e=JSON.parse(a.data)}catch(r){e=a.data}if(Array.isArray(e)&&e.length>0&&"Order Placed Successfully"===e[0].ResponseMsg)y.A.fire({title:"Success!",text:"Your order has been placed successfully",icon:"success"}),g(),window.location.href="/orders";else throw Error("Order placement failed")}else throw Error("Order placement failed")}catch(e){console.error("Error placing order:",e),y.A.fire({title:"Error",text:"There was an error processing your order. Please try again.",icon:"error"})}};return 0===s.length?(0,t.jsx)("div",{className:"container mx-auto py-8 px-4",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"Your cart is empty"}),(0,t.jsx)("p",{className:"text-muted-foreground mb-4",children:"Add items to your cart to proceed to checkout"}),(0,t.jsx)(l.$,{asChild:!0,children:(0,t.jsx)(x(),{href:"/",children:"Continue Shopping"})})]})}):v?(0,t.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,t.jsx)(m.Qp,{className:"mb-6",children:(0,t.jsxs)(m.AB,{children:[(0,t.jsx)(m.J5,{children:(0,t.jsx)(m.w1,{asChild:!0,children:(0,t.jsx)(x(),{href:"/",children:"Home"})})}),(0,t.jsx)(m.tH,{}),(0,t.jsx)(m.J5,{children:(0,t.jsx)(m.w1,{asChild:!0,children:(0,t.jsx)(x(),{href:"/cart",children:"Cart"})})}),(0,t.jsx)(m.tH,{}),(0,t.jsx)(m.J5,{children:(0,t.jsx)(m.tJ,{children:"Checkout"})})]})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,t.jsx)("div",{className:"lg:col-span-2",children:(0,t.jsx)(o.Zp,{children:(0,t.jsxs)("form",{onSubmit:q,className:"p-6 space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h2",{className:"text-xl font-bold",children:"Contact Information"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(c.J,{htmlFor:"firstName",children:"First Name *"}),(0,t.jsx)(n.p,{id:"firstName",name:"firstName",value:k.firstName,onChange:A,required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(c.J,{htmlFor:"lastName",children:"Last Name *"}),(0,t.jsx)(n.p,{id:"lastName",name:"lastName",value:k.lastName,onChange:A,required:!0})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(c.J,{htmlFor:"email",children:"Email *"}),(0,t.jsx)(n.p,{id:"email",name:"email",type:"email",value:k.email,onChange:A,required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(c.J,{htmlFor:"phone",children:"Phone *"}),(0,t.jsx)(n.p,{id:"phone",name:"phone",type:"tel",value:k.phone,onChange:A,required:!0})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h2",{className:"text-xl font-bold",children:"Shipping Address"}),(0,t.jsxs)("div",{children:[(0,t.jsx)(c.J,{htmlFor:"address",children:"Address *"}),(0,t.jsx)(n.p,{id:"address",name:"address",value:k.address,onChange:A,required:!0})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(c.J,{htmlFor:"city",children:"City"}),(0,t.jsx)(n.p,{id:"city",name:"city",value:k.city,onChange:A})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(c.J,{htmlFor:"country",children:"Country"}),(0,t.jsx)(n.p,{id:"country",name:"country",value:k.country,onChange:A})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(c.J,{htmlFor:"zipCode",children:"ZIP Code"}),(0,t.jsx)(n.p,{id:"zipCode",name:"zipCode",value:k.zipCode,onChange:A})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h2",{className:"text-xl font-bold",children:"Payment Method"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-md cursor-pointer hover:bg-muted/50 transition-colors",onClick:()=>w("CreditCard"),style:{borderColor:"CreditCard"===C?r:""},children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"w-10 h-10 rounded-full flex items-center justify-center",style:{backgroundColor:`${r}20`},children:(0,t.jsx)(h.A,{className:"h-5 w-5",style:{color:r}})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:"Credit Card"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Pay with your credit card"})]})]}),(0,t.jsx)("div",{className:"w-5 h-5 rounded-full border flex items-center justify-center",style:{borderColor:r},children:"CreditCard"===C&&(0,t.jsx)("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:r}})})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-md cursor-pointer hover:bg-muted/50 transition-colors",onClick:()=>w("CashOnDelivery"),style:{borderColor:"CashOnDelivery"===C?r:""},children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"w-10 h-10 rounded-full flex items-center justify-center",style:{backgroundColor:`${r}20`},children:(0,t.jsx)(f.A,{className:"h-5 w-5",style:{color:r}})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:"Cash on Delivery"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Pay when you receive your order"})]})]}),(0,t.jsx)("div",{className:"w-5 h-5 rounded-full border flex items-center justify-center",style:{borderColor:r},children:"CashOnDelivery"===C&&(0,t.jsx)("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:r}})})]})]}),(0,t.jsx)("div",{className:"mt-4",children:(0,t.jsx)(x(),{href:"/payment-methods",className:"text-sm text-primary hover:underline",children:"View all payment methods"})})]}),(0,t.jsx)(l.$,{type:"submit",className:"w-full",style:{backgroundColor:r},children:"Place Order"})]})})}),(0,t.jsx)("div",{className:"lg:col-span-1",children:(0,t.jsx)(o.Zp,{children:(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsx)("h2",{className:"text-xl font-bold mb-4",children:"Order Summary"}),(0,t.jsxs)("div",{className:"space-y-4",children:[s.map(e=>(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-muted rounded-md overflow-hidden",children:(0,t.jsx)("img",{src:e.image||`/products/book${e.id}.jpg`,alt:e.name,className:"w-full h-full object-cover"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:e.name}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Qty: ",e.quantity]})]})]}),(0,t.jsxs)("p",{className:"font-medium",children:["$",(e.discountPrice||e.price).toFixed(2)]})]},e.id)),(0,t.jsx)("div",{className:"border-t pt-4 mt-4",children:(0,t.jsxs)("div",{className:"flex justify-between items-center font-bold",children:[(0,t.jsx)("span",{children:"Total"}),(0,t.jsxs)("span",{style:{color:r},children:["$",u.toFixed(2)]})]})})]})]})})})]})]}):(0,t.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,t.jsx)(m.Qp,{className:"mb-6",children:(0,t.jsxs)(m.AB,{children:[(0,t.jsx)(m.J5,{children:(0,t.jsx)(m.w1,{asChild:!0,children:(0,t.jsx)(x(),{href:"/",children:"Home"})})}),(0,t.jsx)(m.tH,{}),(0,t.jsx)(m.J5,{children:(0,t.jsx)(m.w1,{asChild:!0,children:(0,t.jsx)(x(),{href:"/cart",children:"Cart"})})}),(0,t.jsx)(m.tH,{}),(0,t.jsx)(m.J5,{children:(0,t.jsx)(m.tJ,{children:"Checkout"})})]})}),(0,t.jsx)(o.Zp,{className:"max-w-md mx-auto",children:(0,t.jsxs)("div",{className:"p-6 text-center",children:[(0,t.jsx)(p.A,{className:"w-12 h-12 mx-auto mb-4 text-amber-500"}),(0,t.jsx)("h2",{className:"text-2xl font-bold mb-2",children:"Login Required"}),(0,t.jsx)("p",{className:"text-muted-foreground mb-6",children:"Please login to your account to continue with checkout"}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,t.jsx)(l.$,{asChild:!0,children:(0,t.jsx)(x(),{href:"/login?redirect=checkout",children:"Login"})}),(0,t.jsx)(l.$,{variant:"outline",asChild:!0,children:(0,t.jsx)(x(),{href:"/signup",children:"Create Account"})})]})]})})]})}},94735:e=>{"use strict";e.exports=require("events")},96795:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(41680).A)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},97643:(e,r,s)=>{"use strict";s.d(r,{Wu:()=>l,Zp:()=>d,wL:()=>o});var t=s(45512),a=s(58009),i=s(59462);let d=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));d.displayName="Card",a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...r})).displayName="CardHeader",a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("h3",{ref:s,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r})).displayName="CardTitle",a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("p",{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",e),...r})).displayName="CardDescription";let l=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,i.cn)("p-6 pt-0",e),...r}));l.displayName="CardContent";let o=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,i.cn)("flex items-center p-6 pt-0",e),...r}));o.displayName="CardFooter"}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[638,320,307,875],()=>s(60814));module.exports=t})();