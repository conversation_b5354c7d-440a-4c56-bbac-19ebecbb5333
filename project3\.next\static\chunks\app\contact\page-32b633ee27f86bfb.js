(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[977],{2336:(e,s,r)=>{"use strict";r.d(s,{p:()=>l});var a=r(5155),t=r(2115),n=r(9602);let l=t.forwardRef((e,s)=>{let{className:r,type:t,...l}=e;return(0,a.jsx)("input",{type:t,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:s,...l})});l.displayName="Input"},2446:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>v});var a=r(5155),t=r(2115),n=r(9426),l=r(5007),i=r(4085),o=r(2336),d=r(5785),c=r(9602);let m=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("textarea",{className:(0,c.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:s,...t})});m.displayName="Textarea";var u=r(8173),h=r.n(u),x=r(7110),f=r(3239),g=r(8283),p=r(7223),b=r(3831),j=r(6462),y=r(495);function v(){let{t:e,primaryColor:s}=(0,x.t)(),[r,c]=(0,t.useState)(!1),[u,v]=(0,t.useState)(!1),[N,w]=(0,t.useState)(""),{executeRecaptcha:C}=(0,y._Y)(),[k,A]=(0,t.useState)({name:"",email:"",phoneNumber:"",subject:"",message:""}),S=e=>{let{name:s,value:r}=e.target;A(e=>({...e,[s]:r}))},P=async e=>{if(e.preventDefault(),c(!0),w(""),!C){w("reCAPTCHA not ready"),c(!1);return}try{let e=await C("contact_us"),s=await fetch("".concat("https://admin.codemedicalapps.com/","api/v1/dynamic/dataoperation/contact-us"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({requestParameters:{FullName:k.name,Email:k.email,PhoneNumber:k.phoneNumber,Subject:k.subject,Message:k.message,RecaptchaToken:e}})}),r=await s.json();s.ok?(v(!0),A({name:"",email:"",phoneNumber:"",subject:"",message:""}),setTimeout(()=>v(!1),5e3)):w(r.message||"Failed to send message.")}catch(e){w("An error occurred. Please try again.")}finally{c(!1)}};return(0,a.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,a.jsx)(n.Qp,{className:"mb-6",children:(0,a.jsxs)(n.AB,{children:[(0,a.jsx)(n.J5,{children:(0,a.jsx)(n.w1,{asChild:!0,children:(0,a.jsx)(h(),{href:"/",children:e("home")})})}),(0,a.jsx)(n.tH,{}),(0,a.jsx)(n.J5,{children:(0,a.jsx)(n.tJ,{children:e("contact")})})]})}),(0,a.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold mb-6",children:e("contact")}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-[1fr_400px] gap-8 mb-12",children:[(0,a.jsx)(l.Zp,{children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold mb-6",children:"Get in Touch"}),u?(0,a.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-6 text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(f.A,{className:"h-8 w-8 text-green-600"})}),(0,a.jsx)("h3",{className:"text-xl font-medium text-green-800 mb-2",children:"Message Sent!"}),(0,a.jsx)("p",{className:"text-green-700 mb-4",children:"Thank you for contacting us. We'll get back to you as soon as possible."}),(0,a.jsx)(i.$,{onClick:()=>v(!1),children:"Send Another Message"})]}):(0,a.jsxs)("form",{onSubmit:P,children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"name",children:"Your Name"}),(0,a.jsx)(o.p,{id:"name",name:"name",value:k.name,onChange:S,required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"email",children:"Email Address"}),(0,a.jsx)(o.p,{id:"email",name:"email",type:"email",value:k.email,onChange:S,required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"phoneNumber",children:e("phonenumber")}),(0,a.jsx)(o.p,{id:"phoneNumber",name:"phoneNumber",type:"tel",value:k.phoneNumber,onChange:S,required:!0})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)(d.J,{htmlFor:"subject",children:"Subject"}),(0,a.jsx)(o.p,{id:"subject",name:"subject",value:k.subject,onChange:S,required:!0})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)(d.J,{htmlFor:"message",children:"Message"}),(0,a.jsx)(m,{id:"message",name:"message",value:k.message,onChange:S,rows:6,required:!0})]}),N&&(0,a.jsx)("p",{className:"text-red-500 text-sm mb-4",children:N}),(0,a.jsx)(i.$,{type:"submit",disabled:r,className:"w-full",children:r?(0,a.jsxs)("span",{className:"flex items-center gap-2",children:[(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Sending..."]}):(0,a.jsxs)("span",{className:"flex items-center gap-2",children:[(0,a.jsx)(g.A,{className:"h-4 w-4"}),"Send Message"]})})]})]})}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(l.Zp,{children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Contact Information"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"w-10 h-10 rounded-full flex items-center justify-center mr-4",style:{backgroundColor:"".concat(s,"20")},children:(0,a.jsx)(p.A,{className:"h-5 w-5",style:{color:s}})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium",children:"Address"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Iraq"})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"w-10 h-10 rounded-full flex items-center justify-center mr-4",style:{backgroundColor:"".concat(s,"20")},children:(0,a.jsx)(b.A,{className:"h-5 w-5",style:{color:s}})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium",children:"Phone"}),(0,a.jsx)(h(),{href:"tel:".concat(e("phone")),className:"text-muted-foreground hover:text-primary transition-colors",children:e("phone")})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"w-10 h-10 rounded-full flex items-center justify-center mr-4",style:{backgroundColor:"".concat(s,"20")},children:(0,a.jsx)(j.A,{className:"h-5 w-5",style:{color:s}})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium",children:"Email"}),(0,a.jsx)(h(),{href:"mailto:".concat(e("email")),className:"text-muted-foreground hover:text-primary transition-colors",children:e("email")})]})]})]})]})}),(0,a.jsx)(l.Zp,{children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Business Hours"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Monday - Friday"}),(0,a.jsx)("span",{children:"9:00 AM - 11:00 PM"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Saturday"}),(0,a.jsx)("span",{children:"10:00 AM - 10:00 PM"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Sunday"}),(0,a.jsx)("span",{children:"Closed"})," "]}),(0,a.jsx)("div",{className:"flex justify-between",children:(0,a.jsx)("p",{children:"We’re available 24/7 for orders and inquiries. Our team will get back to you during our regular working hours."})})]})]})})]})]}),(0,a.jsxs)("div",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold mb-6",children:"Frequently Asked Questions"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsx)(l.Zp,{children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"How can I track my order?"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"You can track your order by logging into your account and visiting the Orders section. Alternatively, you can use the tracking number provided in your order confirmation email."})]})}),(0,a.jsx)(l.Zp,{children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"What payment methods do you accept?"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"We accept various payment methods including credit/debit cards, PayPal, and bank transfers. For more information, please visit our Payment Methods page."})]})}),(0,a.jsx)(l.Zp,{children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"How long does shipping take?"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Shipping times vary depending on your location. Domestic orders typically take 3-5 business days, while international orders may take 7-14 business days."})]})}),(0,a.jsx)(l.Zp,{children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"What is your return policy?"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"We offer a 30-day return policy for most products. Please visit our Returns page for detailed information on our return process and eligibility criteria."})]})})]})]})]})]})}},4085:(e,s,r)=>{"use strict";r.d(s,{$:()=>d,r:()=>o});var a=r(5155),t=r(2115),n=r(2317),l=r(1027),i=r(9602);let o=(0,l.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-red-500 text-destructive-foreground hover:bg-red-600",outline:"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=t.forwardRef((e,s)=>{let{className:r,variant:t,size:l,asChild:d=!1,...c}=e,m=d?n.DX:"button";return(0,a.jsx)(m,{className:(0,i.cn)(o({variant:t,size:l,className:r})),ref:s,...c})});d.displayName="Button"},5007:(e,s,r)=>{"use strict";r.d(s,{Wu:()=>i,Zp:()=>l,wL:()=>o});var a=r(5155),t=r(2115),n=r(9602);let l=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...t})});l.displayName="Card",t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",r),...t})}).displayName="CardHeader",t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("h3",{ref:s,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",r),...t})}).displayName="CardTitle",t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("p",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",r),...t})}).displayName="CardDescription";let i=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("p-6 pt-0",r),...t})});i.displayName="CardContent";let o=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",r),...t})});o.displayName="CardFooter"},5785:(e,s,r)=>{"use strict";r.d(s,{J:()=>d});var a=r(5155),t=r(2115),n=r(6195),l=r(1027),i=r(9602);let o=(0,l.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)(n.b,{ref:s,className:(0,i.cn)(o(),r),...t})});d.displayName=n.b.displayName},7110:(e,s,r)=>{"use strict";r.d(s,{Z:()=>i,t:()=>o});var a=r(5155),t=r(2115);let n={en:{menu:"Menu",newsletter:"Newsletter",enterEmail:"Enter your email",newsletterDisclaimer:"Subscribe to our newsletter to receive updates and exclusive offers",popularCategories:"Popular Categories!",settings:"Settings",language:"Language",theme:"Theme",color:"Color",save:"Save",cancel:"Cancel",close:"Close",phone:"***************",phonenumber:"Phone Number",email:"<EMAIL>",liveChat:"Live Chat",welcome:"Welcome",logout:"Logout",login:"Login",signup:"Sign Up",signUp:"Sign Up",category:"Category",categories:"Categories",filters:"Filters",clearAll:"Clear All",products:"Products",loadingCategories:"Loading categories...",home:"Home",todayDeals:"Today's Deals",followUs:"Follow Us",aboutUs:"About Us",contactUs:"Contact Us",tryAgain:"Try Again",noProductsFound:"No products found",allProducts:"All Products",quickLinks:"Quick Links",about:"About Us",contact:"Contact Us",hotDeals:"Hot Deals",customerArea:"Customer Area",myAccount:"My Account",orders:"Orders",cart:"Cart",wishlist:"Wishlist",paymentMethods:"Payment Methods",location:"Location",callUs:"Call Us",emailUs:"Email Us",subscribe:"Subscribe",name:"Name",subject:"Subject",message:"Message",sendMessage:"Send Message",sending:"Sending...",messageSent:"Message sent successfully!",messageError:"Failed to send message",contactInfo:"Contact Information",address:"Address",findUs:"Find Us"},ar:{menu:"القائمة",popularCategories:"الفئات الشائعة!",settings:"الإعدادات",language:"اللغة",theme:"المظهر",color:"اللون",save:"حفظ",cancel:"إلغاء",close:"إغلاق",phone:"***************",phonenumber:"رقم الهاتف",email:"<EMAIL>",liveChat:"محادثة مباشرة",welcome:"مرحباً",logout:"تسجيل الخروج",login:"تسجيل الدخول",signup:"تسجيل جديد",signUp:"تسجيل جديد",category:"الفئة",categories:"الفئات",products:"المنتجات",loadingCategories:"جاري تحميل الفئات...",filters:"المرشحات",clearAll:"مسح الكل",home:"الرئيسية",todayDeals:"عروض اليوم",followUs:"تابعنا",aboutUs:"من نحن",contactUs:"اتصل بنا",tryAgain:"حاول مرة أخرى",noProductsFound:"لم يتم العثور على منتجات",allProducts:"جميع المنتجات",quickLinks:"روابط سريعة",about:"من نحن",contact:"اتصل بنا",hotDeals:"عروض ساخنة",customerArea:"منطقة العملاء",myAccount:"حسابي",orders:"الطلبات",cart:"السلة",wishlist:"المفضلة",paymentMethods:"طرق الدفع",location:"الموقع",callUs:"اتصل بنا",emailUs:"راسلنا",subscribe:"اشترك",name:"الاسم",subject:"الموضوع",message:"الرسالة",sendMessage:"إرسال الرسالة",sending:"جاري الإرسال...",messageSent:"تم إرسال الرسالة بنجاح!",messageError:"فشل في إرسال الرسالة",contactInfo:"معلومات الاتصال",address:"العنوان",findUs:"موقعنا"}},l=(0,t.createContext)(void 0);function i(e){let{children:s}=e,[r,i]=(0,t.useState)("light"),[o,d]=(0,t.useState)("en"),[c,m]=(0,t.useState)("#0074b2");return(0,t.useEffect)(()=>{document.documentElement.style.setProperty("--primary",c),document.documentElement.style.setProperty("--primary-foreground","#ffffff")},[]),(0,a.jsx)(l.Provider,{value:{theme:r,language:o,primaryColor:c,toggleTheme:()=>{i("light"===r?"dark":"light")},setLanguage:e=>{d(e),document.documentElement.dir="ar"===e?"rtl":"ltr"},setPrimaryColor:e=>{m(e)},t:e=>(function(e,s){let r=n[s];return e in r?r[e]:"en"!==s&&e in n.en?n.en[e]:e})(e,o)},children:s})}function o(){let e=(0,t.useContext)(l);if(void 0===e)throw Error("useSettings must be used within a SettingsProvider");return e}},8949:(e,s,r)=>{Promise.resolve().then(r.bind(r,2446))},9426:(e,s,r)=>{"use strict";r.d(s,{AB:()=>d,J5:()=>c,Qp:()=>o,tH:()=>h,tJ:()=>u,w1:()=>m});var a=r(5155),t=r(2115),n=r(2317),l=r(6967),i=(r(4858),r(9602));let o=t.forwardRef((e,s)=>{let{...r}=e;return(0,a.jsx)("nav",{ref:s,"aria-label":"breadcrumb",...r})});o.displayName="Breadcrumb";let d=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("ol",{ref:s,className:(0,i.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",r),...t})});d.displayName="BreadcrumbList";let c=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("li",{ref:s,className:(0,i.cn)("inline-flex items-center gap-1.5",r),...t})});c.displayName="BreadcrumbItem";let m=t.forwardRef((e,s)=>{let{asChild:r,className:t,...l}=e,o=r?n.DX:"a";return(0,a.jsx)(o,{ref:s,className:(0,i.cn)("transition-colors hover:text-foreground",t),...l})});m.displayName="BreadcrumbLink";let u=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("span",{ref:s,role:"link","aria-disabled":"true","aria-current":"page",className:(0,i.cn)("font-normal text-foreground",r),...t})});u.displayName="BreadcrumbPage";let h=e=>{let{children:s,className:r,...t}=e;return(0,a.jsx)("li",{role:"presentation","aria-hidden":"true",className:(0,i.cn)("[&>svg]:size-3.5",r),...t,children:null!=s?s:(0,a.jsx)(l.A,{})})};h.displayName="BreadcrumbSeparator"},9602:(e,s,r)=>{"use strict";r.d(s,{cn:()=>n});var a=r(3463),t=r(9795);function n(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return(0,t.QP)((0,a.$)(s))}}},e=>{var s=s=>e(e.s=s);e.O(0,[1345,2825,8441,6587,7358],()=>s(8949)),_N_E=e.O()}]);