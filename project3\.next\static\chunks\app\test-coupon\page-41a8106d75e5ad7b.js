(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4584],{340:(e,t,r)=>{Promise.resolve().then(r.bind(r,7410))},1027:(e,t,r)=>{"use strict";r.d(t,{F:()=>n});var s=r(3463);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,o=s.$,n=(e,t)=>r=>{var s;if((null==t?void 0:t.variants)==null)return o(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:n,defaultVariants:l}=t,i=Object.keys(n).map(e=>{let t=null==r?void 0:r[e],s=null==l?void 0:l[e];if(null===t)return null;let o=a(t)||a(s);return n[e][o]}),c=r&&Object.entries(r).reduce((e,t)=>{let[r,s]=t;return void 0===s||(e[r]=s),e},{});return o(e,i,null==t?void 0:null===(s=t.compoundVariants)||void 0===s?void 0:s.reduce((e,t)=>{let{class:r,className:s,...a}=t;return Object.entries(a).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...l,...c}[t]):({...l,...c})[t]===r})?[...e,r,s]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},2523:(e,t,r)=>{"use strict";r.d(t,{T:()=>s});let s={ADMIN_BASE_URL:"https://admin.codemedicalapps.com/",DYNAMIC_METHOD_SUB_URL:"api/",COMMON_CONTROLLER_SUB_URL:"api/common/",END_POINT_NAMES:{DOWNLOAD_DIGITAL_FILE:"downloadDigitalFile",GET_COUPON_CODE_DISCOUNT:"get-coupon-code-discount-value/calculate-coupon-discount"}}},2862:(e,t,r)=>{"use strict";r.d(t,{$g:()=>u,MakeApiCallAsync:()=>i,TS:()=>o,XX:()=>d,k6:()=>c});var s=r(2651),a=r(2523);s.A.defaults.timeout=3e4,"https:"===window.location.protocol&&a.T.ADMIN_BASE_URL.includes("localhost")&&(s.A.defaults.httpsAgent={rejectUnauthorized:!1});let o={ADMIN_BASE_URL:a.T.ADMIN_BASE_URL,API_VERSION:"v1",DYNAMIC_METHOD_SUB_URL:"api/v1/dynamic/dataoperation/",END_POINT_NAMES:{...a.T.END_POINT_NAMES,GET_CATEGORIES_LIST:"get-categories-list",SIGNUP_USER:"signup-user",GET_USER_LOGIN:"get-user-login",GET_USER_BY_PHONE:"get-user-by-phone",RESET_PASSWORD_BY_PHONE:"reset-password-by-phone",GET_HOME_SCREEN_BANNER:"get-home-screen-banner",GET_RECENT_PRODUCTS:"get-recents-products-list",GET_POPULAR_PRODUCTS:"get-popular-products-list",GET_HOT_DEAL_PRODUCTS:"get-hot-deal-products",GET_CAMPAIGNS_LIST:"get-web-campaign-list",GET_PRODUCTS_LIST:"get-products-list",GET_ALL_PRODUCTS:"api/v1/products/get-all-products",GET_MANUFACTURERS_LIST:"get-manufacturers-list",GET_TAGS_LIST:"get-tags-list",GET_CURRENCY_RATE:"get-currency-rate",GET_COUPON_CODE_DISCOUNT:"get-coupon-code-discount-value/calculate-coupon-discount"},COMMON_CONTROLLER_SUB_URL:"api/v1/common/"},n=async()=>localStorage.getItem("token")||null,l=async()=>localStorage.getItem("userId")||null,i=async function(e,t,r,a,i){arguments.length>5&&void 0!==arguments[5]&&arguments[5];try{let c={...a};if(!c.hasOwnProperty("Authorization")){let e=await n();e&&(c.Authorization="Bearer "+e)}if(!c.hasOwnProperty("Token")){let e=await n();c.Token=null!=e?e:""}if(!c.hasOwnProperty("UserID")){let e=await l();c.UserID=null!=e?e:""}c.hasOwnProperty("Accept")||(c.Accept="application/json"),c.hasOwnProperty("Content-Type")||(c["Content-Type"]="application/json");let d=o.ADMIN_BASE_URL+(null===t||void 0==t?o.DYNAMIC_METHOD_SUB_URL:t)+e;i=null!=i?i:"POST";let u={headers:c,responseType:"json",timeout:3e4,withCredentials:!1};if("POST"===i)return await s.A.post(d,r,u);if("GET"==i)return u.params=r,await s.A.get(d,u);return{data:{errorMessage:"Unsupported method type: ".concat(i),status:"method_not_supported"}}}catch(t){console.error("API call failed:",t);let e={data:{errorMessage:"An unexpected error occurred",status:"unknown_error"}};if(t&&"object"==typeof t&&"response"in t&&t.response){var c,d;let r=null===(c=t.response)||void 0===c?void 0:c.data;e.data={errorMessage:(null==r?void 0:r.errorMessage)||"An error occurred while processing your request.",status:null===(d=t.response)||void 0===d?void 0:d.status}}else if(t&&"object"==typeof t&&"request"in t){let r="Network error: No response received from server.";t.message&&t.message.includes("Network Error")&&(r="Network Error: This may be due to CORS policy restrictions, server unavailability, or an invalid SSL certificate. Please check that:\n1. The server is running and accessible\n2. The URL is correct: "+o.ADMIN_BASE_URL+"\n3. CORS is properly configured on the server\n4. If using HTTPS, the SSL certificate is valid"),e.data={errorMessage:r,status:"network_error"}}else{let r=t&&"object"==typeof t&&"message"in t?t.message:"An unexpected error occurred";e.data={errorMessage:r,status:"request_error"}}return e}},c=async()=>{try{let e=await i("getrate","api/v1/common/",{},{},"GET");if(e&&e.data&&!e.data.errorMessage)return parseInt(e.data)||1500;return 1500}catch(e){return console.error("Error fetching currency rate:",e),1500}},d=(e,t)=>Math.round(e*t),u=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD";return"IQD"===t?"".concat(e.toLocaleString()," IQD"):"$".concat(e.toFixed(2))}},4085:(e,t,r)=>{"use strict";r.d(t,{$:()=>c,r:()=>i});var s=r(5155),a=r(2115),o=r(2317),n=r(1027),l=r(9602);let i=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-red-500 text-destructive-foreground hover:bg-red-600",outline:"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef((e,t)=>{let{className:r,variant:a,size:n,asChild:c=!1,...d}=e,u=c?o.DX:"button";return(0,s.jsx)(u,{className:(0,l.cn)(i({variant:a,size:n,className:r})),ref:t,...d})});c.displayName="Button"},5007:(e,t,r)=>{"use strict";r.d(t,{Wu:()=>l,Zp:()=>n,wL:()=>i});var s=r(5155),a=r(2115),o=r(9602);let n=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,o.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});n.displayName="Card",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",r),...a})}).displayName="CardHeader",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("h3",{ref:t,className:(0,o.cn)("text-2xl font-semibold leading-none tracking-tight",r),...a})}).displayName="CardTitle",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("p",{ref:t,className:(0,o.cn)("text-sm text-muted-foreground",r),...a})}).displayName="CardDescription";let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,o.cn)("p-6 pt-0",r),...a})});l.displayName="CardContent";let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,o.cn)("flex items-center p-6 pt-0",r),...a})});i.displayName="CardFooter"},7410:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(5155),a=r(2115),o=r(2862),n=r(4085),l=r(5007);function i(){let[e,t]=(0,a.useState)("test123"),[r,i]=(0,a.useState)(null),[c,d]=(0,a.useState)(!1),u=async()=>{d(!0),i(null);try{let t={requestParameters:{CouponCode:e.toUpperCase(),cartJsonData:JSON.stringify([{ProductId:1,ProductName:"Test Product",Price:100,Quantity:1,IsDiscountAllowed:!0}])}};console.log("Testing coupon with params:",t);let r=await (0,o.MakeApiCallAsync)(o.TS.END_POINT_NAMES.GET_COUPON_CODE_DISCOUNT,o.TS.COMMON_CONTROLLER_SUB_URL,t,{"Content-Type":"application/json",Accept:"application/json"},"POST");console.log("API Response:",r),i(r)}catch(e){console.error("Error testing coupon:",e),i({error:e instanceof Error?e.message:"Unknown error occurred"})}finally{d(!1)}};return(0,s.jsx)("div",{className:"container mx-auto p-6",children:(0,s.jsxs)(l.Zp,{className:"max-w-2xl mx-auto p-6",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Test Coupon API"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Coupon Code"}),(0,s.jsx)("input",{type:"text",value:e,onChange:e=>t(e.target.value),className:"w-full p-2 border rounded-md",placeholder:"Enter coupon code"})]}),(0,s.jsx)(n.$,{onClick:u,disabled:c,className:"w-full",children:c?"Testing...":"Test Coupon"}),r&&(0,s.jsxs)("div",{className:"mt-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"API Response:"}),(0,s.jsx)("pre",{className:"bg-gray-100 p-4 rounded-md overflow-auto text-sm",children:JSON.stringify(r,null,2)})]})]}),(0,s.jsxs)("div",{className:"mt-8 p-4 bg-blue-50 rounded-md",children:[(0,s.jsx)("h4",{className:"font-semibold text-blue-800 mb-2",children:"Test Instructions:"}),(0,s.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1",children:[(0,s.jsx)("li",{children:'• Default test coupon code is "test123"'}),(0,s.jsxs)("li",{children:["• The API endpoint is: ",o.TS.COMMON_CONTROLLER_SUB_URL+o.TS.END_POINT_NAMES.GET_COUPON_CODE_DISCOUNT]}),(0,s.jsx)("li",{children:"• Check the browser console for detailed logs"}),(0,s.jsx)("li",{children:"• Make sure you have valid coupons in the database"})]})]})]})})}},9602:(e,t,r)=>{"use strict";r.d(t,{cn:()=>o});var s=r(3463),a=r(9795);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[1345,2651,8441,6587,7358],()=>t(340)),_N_E=e.O()}]);