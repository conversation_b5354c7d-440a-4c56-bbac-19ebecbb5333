(()=>{var e={};e.id=763,e.ids=[763],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},14494:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(41680).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},22790:(e,r,t)=>{Promise.resolve().then(t.bind(t,31057))},25404:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>n,routeModule:()=>u,tree:()=>l});var s=t(70260),a=t(28203),o=t(25155),i=t.n(o),d=t(67292),c={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>d[e]);t.d(r,c);let l={children:["",{children:["category",{children:["[categoryId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,31057)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\category\\[categoryId]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,19611)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"]}]}.children,n=["C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\category\\[categoryId]\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/category/[categoryId]/page",pathname:"/category/[categoryId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31057:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\category\\\\[categoryId]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\category\\[categoryId]\\page.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},37778:(e,r,t)=>{"use strict";t.d(r,{AB:()=>l,J5:()=>n,Qp:()=>c,tH:()=>m,tJ:()=>u,w1:()=>p});var s=t(45512),a=t(58009),o=t(12705),i=t(99905),d=(t(14494),t(59462));let c=a.forwardRef(({...e},r)=>(0,s.jsx)("nav",{ref:r,"aria-label":"breadcrumb",...e}));c.displayName="Breadcrumb";let l=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("ol",{ref:t,className:(0,d.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",e),...r}));l.displayName="BreadcrumbList";let n=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("li",{ref:t,className:(0,d.cn)("inline-flex items-center gap-1.5",e),...r}));n.displayName="BreadcrumbItem";let p=a.forwardRef(({asChild:e,className:r,...t},a)=>{let i=e?o.DX:"a";return(0,s.jsx)(i,{ref:a,className:(0,d.cn)("transition-colors hover:text-foreground",r),...t})});p.displayName="BreadcrumbLink";let u=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("span",{ref:t,role:"link","aria-disabled":"true","aria-current":"page",className:(0,d.cn)("font-normal text-foreground",e),...r}));u.displayName="BreadcrumbPage";let m=({children:e,className:r,...t})=>(0,s.jsx)("li",{role:"presentation","aria-hidden":"true",className:(0,d.cn)("[&>svg]:size-3.5",r),...t,children:e??(0,s.jsx)(i.A,{})});m.displayName="BreadcrumbSeparator"},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},92526:(e,r,t)=>{Promise.resolve().then(t.bind(t,99061))},94735:e=>{"use strict";e.exports=require("events")},97643:(e,r,t)=>{"use strict";t.d(r,{Wu:()=>d,Zp:()=>i,wL:()=>c});var s=t(45512),a=t(58009),o=t(59462);let i=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,o.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));i.displayName="Card",a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",e),...r})).displayName="CardHeader",a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("h3",{ref:t,className:(0,o.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r})).displayName="CardTitle",a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("p",{ref:t,className:(0,o.cn)("text-sm text-muted-foreground",e),...r})).displayName="CardDescription";let d=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,o.cn)("p-6 pt-0",e),...r}));d.displayName="CardContent";let c=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,o.cn)("flex items-center p-6 pt-0",e),...r}));c.displayName="CardFooter"},99061:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var s=t(45512),a=t(58009),o=t(79334),i=t(37778),d=t(97643),c=t(28531),l=t.n(c),n=t(71901),p=t(15348);function u(){let{categoryId:e}=(0,o.useParams)(),{t:r,primaryColor:t}=(0,n.t)(),[c,u]=(0,a.useState)([]),[m,x]=(0,a.useState)(!0),[f,h]=(0,a.useState)("");return m?(0,s.jsx)("div",{className:"container mx-auto py-8 px-4",children:"Loading..."}):(0,s.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,s.jsx)(i.Qp,{className:"mb-6",children:(0,s.jsxs)(i.AB,{children:[(0,s.jsx)(i.J5,{children:(0,s.jsx)(i.w1,{asChild:!0,children:(0,s.jsx)(l(),{href:"/",children:r("home")})})}),(0,s.jsx)(i.tH,{}),(0,s.jsx)(i.J5,{children:(0,s.jsx)(i.tJ,{children:f})})]})}),(0,s.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold mb-8",children:f}),0===c.length?(0,s.jsx)(d.Zp,{className:"p-6 text-center",children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"No products found in this category."})}):(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:c.map(e=>(0,s.jsx)(d.Zp,{className:"overflow-hidden hover:shadow-lg transition-shadow",children:(0,s.jsxs)(l(),{href:`/product/${e.id}`,children:[(0,s.jsx)("div",{className:"aspect-[3/4] relative bg-muted",children:(0,s.jsx)("img",{src:e.image?e.image.startsWith("http")?e.image:e.image.startsWith("/")?`${p.TS.ADMIN_BASE_URL.replace(/\/$/,"")}${e.image}`:`${p.TS.ADMIN_BASE_URL.replace(/\/$/,"")}/${e.image}`:`/products/book${e.id}.jpg`,alt:e.name,className:"object-cover w-full h-full",onError:r=>{let t=r.target;t.onerror=null,t.src=`/products/book${e.id}.jpg`}})}),(0,s.jsxs)("div",{className:"p-4",children:[(0,s.jsx)("h3",{className:"font-medium mb-2 line-clamp-2",children:e.name}),(0,s.jsx)("div",{className:"flex items-baseline gap-2",children:e.discountPrice?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("span",{className:"text-lg font-bold",style:{color:t},children:["$",e.discountPrice.toFixed(2)]}),(0,s.jsxs)("span",{className:"text-sm text-muted-foreground line-through",children:["$",e.price.toFixed(2)]})]}):(0,s.jsxs)("span",{className:"text-lg font-bold",style:{color:t},children:["$",e.price.toFixed(2)]})})]})]})},e.id))})]})]})}},99905:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(41680).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[638,320,875],()=>t(25404));module.exports=s})();