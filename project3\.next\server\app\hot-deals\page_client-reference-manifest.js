globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/hot-deals/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"1284":{"*":{"id":"74096","name":"*","chunks":[],"async":false}},"1427":{"*":{"id":"23339","name":"*","chunks":[],"async":false}},"2328":{"*":{"id":"57174","name":"*","chunks":[],"async":false}},"2446":{"*":{"id":"43198","name":"*","chunks":[],"async":false}},"3265":{"*":{"id":"99061","name":"*","chunks":[],"async":false}},"3866":{"*":{"id":"87190","name":"*","chunks":[],"async":false}},"3924":{"*":{"id":"32092","name":"*","chunks":[],"async":false}},"4547":{"*":{"id":"33875","name":"*","chunks":[],"async":false}},"4835":{"*":{"id":"88903","name":"*","chunks":[],"async":false}},"5244":{"*":{"id":"84178","name":"*","chunks":[],"async":false}},"5328":{"*":{"id":"20730","name":"*","chunks":[],"async":false}},"5650":{"*":{"id":"63566","name":"*","chunks":[],"async":false}},"5987":{"*":{"id":"94172","name":"*","chunks":[],"async":false}},"6061":{"*":{"id":"59815","name":"*","chunks":[],"async":false}},"6213":{"*":{"id":"61365","name":"*","chunks":[],"async":false}},"6482":{"*":{"id":"90308","name":"*","chunks":[],"async":false}},"6539":{"*":{"id":"33053","name":"*","chunks":[],"async":false}},"6664":{"*":{"id":"43916","name":"*","chunks":[],"async":false}},"7033":{"*":{"id":"66959","name":"*","chunks":[],"async":false}},"7242":{"*":{"id":"24790","name":"*","chunks":[],"async":false}},"7410":{"*":{"id":"93978","name":"*","chunks":[],"async":false}},"7539":{"*":{"id":"48429","name":"*","chunks":[],"async":false}},"7549":{"*":{"id":"90087","name":"*","chunks":[],"async":false}},"8013":{"*":{"id":"93243","name":"*","chunks":[],"async":false}},"8569":{"*":{"id":"19978","name":"*","chunks":[],"async":false}},"8896":{"*":{"id":"44048","name":"*","chunks":[],"async":false}},"8964":{"*":{"id":"38597","name":"*","chunks":[],"async":false}},"9745":{"*":{"id":"93036","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":7033,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":7033,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":4547,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":4547,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":4835,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":4835,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":2328,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":2328,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":5244,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":5244,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":3866,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":3866,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\node_modules\\next\\dist\\lib\\metadata\\async-metadata.js":{"id":7539,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\node_modules\\next\\dist\\esm\\lib\\metadata\\async-metadata.js":{"id":7539,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\node_modules\\next\\dist\\lib\\metadata\\metadata-boundary.js":{"id":6213,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\node_modules\\next\\dist\\esm\\lib\\metadata\\metadata-boundary.js":{"id":6213,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\layout.tsx":{"id":6539,"name":"*","chunks":["1345","static/chunks/1345-ea9467ae30e8998c.js","2651","static/chunks/2651-3cdf513092fbc3bb.js","6764","static/chunks/6764-b8ac848b356d7af4.js","7663","static/chunks/7663-3970ec0af4445a0d.js","7542","static/chunks/7542-****************.js","2081","static/chunks/2081-d01a592a3fda7fa8.js","3414","static/chunks/3414-f4f3cc7412dd3030.js","7177","static/chunks/app/layout-0a5d048ab893f58f.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\account\\page.tsx":{"id":1284,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\contact\\page.tsx":{"id":2446,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\cart\\page.tsx":{"id":6061,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\hot-deals\\page.tsx":{"id":5650,"name":"*","chunks":["1416","static/chunks/app/hot-deals/page-60211cb799f23225.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\category\\[categoryId]\\page.tsx":{"id":3265,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\page.tsx":{"id":6664,"name":"*","chunks":["1345","static/chunks/1345-ea9467ae30e8998c.js","2651","static/chunks/2651-3cdf513092fbc3bb.js","2446","static/chunks/2446-9a7b7e0a21029ca6.js","6764","static/chunks/6764-b8ac848b356d7af4.js","6392","static/chunks/6392-96fc9b63e2aa0e98.js","8974","static/chunks/app/page-e4a51d282a423524.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\forgot-password\\page.tsx":{"id":9745,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\about\\page.tsx":{"id":5987,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\follow-us\\page.tsx":{"id":1427,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\login\\page.tsx":{"id":6482,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\terms\\page.tsx":{"id":3924,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\checkout\\page.tsx":{"id":8013,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\orders\\page.tsx":{"id":8964,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\payment-methods\\page.tsx":{"id":7242,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\test-coupon\\page.tsx":{"id":7410,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\product\\[id]\\product-details-client.tsx":{"id":5328,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\wishlist\\page.tsx":{"id":8896,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\signup\\page.tsx":{"id":7549,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\products\\page.tsx":{"id":8569,"name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\":[],"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\layout":[{"inlined":false,"path":"static/css/9aae0b081fe5b0d9.css"}],"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\page":[],"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\hot-deals\\page":[]},"rscModuleMapping":{"1284":{"*":{"id":"1284","name":"*","chunks":[],"async":false}},"1427":{"*":{"id":"67543","name":"*","chunks":[],"async":false}},"2328":{"*":{"id":"40802","name":"*","chunks":[],"async":false}},"2446":{"*":{"id":"6531","name":"*","chunks":[],"async":false}},"3265":{"*":{"id":"31057","name":"*","chunks":[],"async":false}},"3866":{"*":{"id":"48530","name":"*","chunks":[],"async":false}},"3924":{"*":{"id":"74440","name":"*","chunks":[],"async":false}},"4547":{"*":{"id":"34863","name":"*","chunks":[],"async":false}},"4835":{"*":{"id":"25155","name":"*","chunks":[],"async":false}},"5244":{"*":{"id":"9350","name":"*","chunks":[],"async":false}},"5328":{"*":{"id":"78381","name":"*","chunks":[],"async":false}},"5650":{"*":{"id":"19034","name":"*","chunks":[],"async":false}},"5987":{"*":{"id":"7678","name":"*","chunks":[],"async":false}},"6061":{"*":{"id":"64397","name":"*","chunks":[],"async":false}},"6213":{"*":{"id":"88921","name":"*","chunks":[],"async":false}},"6482":{"*":{"id":"43642","name":"*","chunks":[],"async":false}},"6539":{"*":{"id":"19611","name":"*","chunks":[],"async":false}},"6664":{"*":{"id":"35104","name":"*","chunks":[],"async":false}},"7033":{"*":{"id":"13219","name":"*","chunks":[],"async":false}},"7242":{"*":{"id":"67618","name":"*","chunks":[],"async":false}},"7410":{"*":{"id":"4934","name":"*","chunks":[],"async":false}},"7539":{"*":{"id":"81601","name":"*","chunks":[],"async":false}},"7549":{"*":{"id":"75395","name":"*","chunks":[],"async":false}},"8013":{"*":{"id":"27183","name":"*","chunks":[],"async":false}},"8569":{"*":{"id":"61551","name":"*","chunks":[],"async":false}},"8896":{"*":{"id":"84276","name":"*","chunks":[],"async":false}},"8964":{"*":{"id":"34462","name":"*","chunks":[],"async":false}},"9745":{"*":{"id":"93220","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}