'use client';

import React, { useState } from 'react';
import { MakeApiCallAsync, Config } from '@/lib/api-helper';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';

export default function TestCouponPage() {
  const [couponCode, setCouponCode] = useState('test123');
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const testCoupon = async () => {
    setLoading(true);
    setResult(null);

    try {
      // Sample cart data for testing
      const cartData = [
        {
          ProductId: 1,
          ProductName: 'Test Product',
          Price: 100,
          Quantity: 1,
          IsDiscountAllowed: true
        }
      ];

      const param = {
        requestParameters: {
          CouponCode: couponCode.toUpperCase(),
          cartJsonData: JSON.stringify(cartData)
        }
      };

      const headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      };

      console.log('Testing coupon with params:', param);

      const response = await MakeApiCallAsync(
        Config.END_POINT_NAMES.GET_COUPON_CODE_DISCOUNT,
        Config.COMMON_CONTROLLER_SUB_URL,
        param,
        headers,
        'POST'
      );

      console.log('API Response:', response);
      setResult(response);

    } catch (error) {
      console.error('Error testing coupon:', error);
      setResult({ error: error instanceof Error ? error.message : 'Unknown error occurred' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6">
      <Card className="max-w-2xl mx-auto p-6">
        <h1 className="text-2xl font-bold mb-6">Test Coupon API</h1>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">
              Coupon Code
            </label>
            <input
              type="text"
              value={couponCode}
              onChange={(e) => setCouponCode(e.target.value)}
              className="w-full p-2 border rounded-md"
              placeholder="Enter coupon code"
            />
          </div>

          <Button 
            onClick={testCoupon}
            disabled={loading}
            className="w-full"
          >
            {loading ? 'Testing...' : 'Test Coupon'}
          </Button>

          {result && (
            <div className="mt-6">
              <h3 className="text-lg font-semibold mb-2">API Response:</h3>
              <pre className="bg-gray-100 p-4 rounded-md overflow-auto text-sm">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}
        </div>

        <div className="mt-8 p-4 bg-blue-50 rounded-md">
          <h4 className="font-semibold text-blue-800 mb-2">Test Instructions:</h4>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• Default test coupon code is "test123"</li>
            <li>• The API endpoint is: {Config.COMMON_CONTROLLER_SUB_URL + Config.END_POINT_NAMES.GET_COUPON_CODE_DISCOUNT}</li>
            <li>• Check the browser console for detailed logs</li>
            <li>• Make sure you have valid coupons in the database</li>
          </ul>
        </div>
      </Card>
    </div>
  );
}
