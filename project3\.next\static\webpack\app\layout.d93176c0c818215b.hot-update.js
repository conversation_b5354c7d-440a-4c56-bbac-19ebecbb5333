"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8075f49bf62b\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xceWh5YXNvZnRcXERvd25sb2Fkc1xcZWNcXC5ORVQgOCBWZXJzaW9uIC0gTGF0ZXN0XFxwcm9qZWN0XFxjb2RlbWVkaWNhbFxccHJvamVjdDNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI4MDc1ZjQ5YmY2MmJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/ui/mobile-bottom-nav.tsx":
/*!*********************************************!*\
  !*** ./components/ui/mobile-bottom-nav.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MobileBottomNav: () => (/* binding */ MobileBottomNav)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Home_Package_ShoppingCart_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Home,Package,ShoppingCart,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Home_Package_ShoppingCart_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Home,Package,ShoppingCart,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Home_Package_ShoppingCart_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Home,Package,ShoppingCart,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Home_Package_ShoppingCart_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Home,Package,ShoppingCart,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Home_Package_ShoppingCart_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Home,Package,ShoppingCart,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_cart_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/cart-context */ \"(app-pages-browser)/./contexts/cart-context.tsx\");\n/* harmony import */ var _contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/wishlist-context */ \"(app-pages-browser)/./contexts/wishlist-context.tsx\");\n/* harmony import */ var _contexts_settings_context__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/settings-context */ \"(app-pages-browser)/./contexts/settings-context.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* __next_internal_client_entry_do_not_use__ MobileBottomNav auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction MobileBottomNav() {\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { totalItems: cartCount } = (0,_contexts_cart_context__WEBPACK_IMPORTED_MODULE_3__.useCart)();\n    const { totalItems: wishlistCount } = (0,_contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_4__.useWishlist)();\n    const { primaryColor, t } = (0,_contexts_settings_context__WEBPACK_IMPORTED_MODULE_5__.useSettings)();\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)({\n        \"MobileBottomNav.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"MobileBottomNav.useEffect\"], []);\n    if (!mounted) return null;\n    const navItems = [\n        {\n            href: '/',\n            icon: _barrel_optimize_names_Heart_Home_Package_ShoppingCart_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            label: t('home') || 'الرئيسية',\n            isActive: pathname === '/'\n        },\n        {\n            href: '/products',\n            icon: _barrel_optimize_names_Heart_Home_Package_ShoppingCart_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            label: t('products') || 'التصنيفات',\n            isActive: pathname === '/products'\n        },\n        {\n            href: '/cart',\n            icon: _barrel_optimize_names_Heart_Home_Package_ShoppingCart_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            label: t('cart') || 'سلة التسوق',\n            isActive: pathname === '/cart',\n            badge: cartCount\n        },\n        {\n            href: '/wishlist',\n            icon: _barrel_optimize_names_Heart_Home_Package_ShoppingCart_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            label: t('wishlist') || 'المفضلة',\n            isActive: pathname === '/wishlist',\n            badge: wishlistCount\n        },\n        {\n            href: '/login',\n            icon: _barrel_optimize_names_Heart_Home_Package_ShoppingCart_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            label: t('login') || 'حسابي',\n            isActive: pathname === '/login' || pathname === '/signup'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50 safe-area-pb\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-around py-2\",\n            children: navItems.map((item)=>{\n                const Icon = item.icon;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                    href: item.href,\n                    className: \"flex flex-col items-center justify-center min-w-0 flex-1 py-2 px-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative flex flex-col items-center\",\n                            children: [\n                                item.badge && item.badge > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium mb-1 shadow-md\",\n                                    style: {\n                                        backgroundColor: primaryColor\n                                    },\n                                    children: item.badge > 99 ? '99+' : item.badge\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\mobile-bottom-nav.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: \"h-6 w-6\",\n                                    style: {\n                                        color: item.isActive ? primaryColor : '#6B7280'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\mobile-bottom-nav.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\mobile-bottom-nav.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs font-medium text-center leading-tight mt-1\",\n                            style: {\n                                color: item.isActive ? primaryColor : '#6B7280'\n                            },\n                            children: item.label\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\mobile-bottom-nav.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, item.href, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\mobile-bottom-nav.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 13\n                }, this);\n            })\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\mobile-bottom-nav.tsx\",\n            lineNumber: 61,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\mobile-bottom-nav.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, this);\n}\n_s(MobileBottomNav, \"b4NakXd/uLYhEvb4eWnHcDtNNP8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _contexts_cart_context__WEBPACK_IMPORTED_MODULE_3__.useCart,\n        _contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_4__.useWishlist,\n        _contexts_settings_context__WEBPACK_IMPORTED_MODULE_5__.useSettings\n    ];\n});\n_c = MobileBottomNav;\nvar _c;\n$RefreshReg$(_c, \"MobileBottomNav\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ui/mobile-bottom-nav.tsx\n"));

/***/ })

});