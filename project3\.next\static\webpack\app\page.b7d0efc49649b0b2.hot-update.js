"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./contexts/cart-context.tsx":
/*!***********************************!*\
  !*** ./contexts/cart-context.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CartProvider: () => (/* binding */ CartProvider),\n/* harmony export */   useCart: () => (/* binding */ useCart)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ CartProvider,useCart auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst CartContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction CartProvider(param) {\n    let { children } = param;\n    _s();\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isHydrated, setIsHydrated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Load cart from localStorage on initial render\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CartProvider.useEffect\": ()=>{\n            const savedCart = localStorage.getItem('cart');\n            if (savedCart) {\n                try {\n                    setItems(JSON.parse(savedCart));\n                } catch (error) {\n                    console.error('Failed to parse cart from localStorage:', error);\n                }\n            }\n            setIsHydrated(true);\n        }\n    }[\"CartProvider.useEffect\"], []);\n    // Save cart to localStorage whenever it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CartProvider.useEffect\": ()=>{\n            localStorage.setItem('cart', JSON.stringify(items));\n        }\n    }[\"CartProvider.useEffect\"], [\n        items\n    ]);\n    const addToCart = function(item, quantity) {\n        let attributes = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : [], iqdPrice = arguments.length > 3 ? arguments[3] : void 0, currencyRate = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : 1500;\n        setItems((prevItems)=>{\n            // Start with the base price (which could be a discounted price if applicable)\n            let adjustedPrice = item.price;\n            // Calculate base IQD price\n            let baseIqdPrice = iqdPrice || Math.round(item.price * currencyRate);\n            let adjustedIqdPrice = baseIqdPrice;\n            // Apply attribute-based price adjustments\n            attributes.forEach((attr)=>{\n                if (attr.PriceAdjustment && attr.PriceAdjustmentType) {\n                    const basePriceForAdjustment = item.originalPrice || item.price;\n                    switch(attr.PriceAdjustmentType){\n                        case 1:\n                            adjustedPrice += attr.PriceAdjustment;\n                            adjustedIqdPrice += Math.round(attr.PriceAdjustment * currencyRate);\n                            break;\n                        case 2:\n                            const percentageAdjustment = basePriceForAdjustment * attr.PriceAdjustment / 100;\n                            adjustedPrice += percentageAdjustment;\n                            adjustedIqdPrice += Math.round(percentageAdjustment * currencyRate);\n                            break;\n                    }\n                }\n            });\n            // Find if item with same ID and attributes already exists\n            const existingItemIndex = prevItems.findIndex((i)=>{\n                var _i_attributes;\n                return i.id === item.id && JSON.stringify((_i_attributes = i.attributes) === null || _i_attributes === void 0 ? void 0 : _i_attributes.sort((a, b)=>a.ProductAttributeID - b.ProductAttributeID)) === JSON.stringify(attributes === null || attributes === void 0 ? void 0 : attributes.sort((a, b)=>a.ProductAttributeID - b.ProductAttributeID));\n            });\n            if (existingItemIndex >= 0) {\n                // Item with same attributes exists, update quantity\n                const updatedItems = [\n                    ...prevItems\n                ];\n                updatedItems[existingItemIndex].quantity += quantity;\n                return updatedItems;\n            } else {\n                // Add new item with all price information\n                return [\n                    ...prevItems,\n                    {\n                        ...item,\n                        iqdPrice: iqdPrice,\n                        quantity,\n                        attributes,\n                        adjustedPrice: Math.max(0, adjustedPrice),\n                        originalPrice: item.originalPrice\n                    }\n                ];\n            }\n        });\n    };\n    const removeFromCart = (id)=>{\n        setItems((prevItems)=>prevItems.filter((item)=>item.id !== id));\n    };\n    const updateQuantity = (id, quantity)=>{\n        if (quantity <= 0) {\n            removeFromCart(id);\n            return;\n        }\n        setItems((prevItems)=>prevItems.map((item)=>item.id === id ? {\n                    ...item,\n                    quantity\n                } : item));\n    };\n    const clearCart = ()=>{\n        setItems([]);\n    };\n    const totalItems = items.reduce((total, item)=>total + item.quantity, 0);\n    // Update localStorage and trigger re-render when cart changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CartProvider.useEffect\": ()=>{\n            localStorage.setItem('cart', JSON.stringify(items));\n        }\n    }[\"CartProvider.useEffect\"], [\n        items\n    ]);\n    const subtotal = items.reduce((total, item)=>{\n        const price = item.discountPrice ? Math.min(item.discountPrice, item.adjustedPrice) : item.adjustedPrice;\n        return total + price * item.quantity;\n    }, 0);\n    // For now, total is same as subtotal, but could include shipping, tax, etc.\n    const total = subtotal;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CartContext.Provider, {\n        value: {\n            items,\n            addToCart,\n            removeFromCart,\n            updateQuantity,\n            clearCart,\n            totalItems,\n            subtotal,\n            total,\n            isHydrated\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\contexts\\\\cart-context.tsx\",\n        lineNumber: 169,\n        columnNumber: 5\n    }, this);\n}\n_s(CartProvider, \"orx7hoWf+wJ/pl3ceK141eCKGB8=\");\n_c = CartProvider;\nfunction useCart() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(CartContext);\n    if (context === undefined) {\n        throw new Error('useCart must be used within a CartProvider');\n    }\n    return context;\n}\n_s1(useCart, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"CartProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./contexts/cart-context.tsx\n"));

/***/ })

});