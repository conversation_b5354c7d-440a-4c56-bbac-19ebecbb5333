using System.ComponentModel.DataAnnotations;

namespace Entities.CommonModels
{
    public class UpdateShippingDetailsModel
    {
        [Required]
        public int OrderId { get; set; }

        [Required(ErrorMessage = "Address Line 1 is required")]
        public string AddressLine1 { get; set; }

        public string? AddressLine2 { get; set; }

        public string? City { get; set; }

        public string? Country { get; set; }

        public string? State { get; set; }

        public string? ZipCode { get; set; }

        [Required(ErrorMessage = "Phone number is required")]
        public string PhoneNumber { get; set; }

        [Required(ErrorMessage = "Email address is required")]
        [EmailAddress(ErrorMessage = "Invalid email address")]
        public string EmailAddress { get; set; }
    }
}
