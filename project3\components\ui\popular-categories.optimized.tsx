"use client"

import { useEffect, useState, useRef, useMemo, useCallback, memo } from "react"
import Image from 'next/image'

interface Category {
  id: number
  title: string
  image: string
  parentId?: number
}

// Memoized category item component to prevent unnecessary re-renders
const CategoryItem = memo(({ 
  category, 
  onInteraction 
}: { 
  category: Category, 
  onInteraction: () => void 
}) => {
  const [imgSrc, setImgSrc] = useState(category.image || '/placeholder.svg')
  
  const handleError = useCallback(() => {
    if (imgSrc !== '/placeholder.svg') {
      setImgSrc('https://admin.codemedicalapps.com/images/no-image.jpg')
    }
  }, [imgSrc])

  return (
    <div className="flex flex-col items-center px-2">
      <a
        href={`/products?category=${category.id}`}
        className="group flex flex-col items-center cursor-pointer transition-all duration-300 hover:scale-105"
        onClick={onInteraction}
      >
        <div className="w-20 h-20 sm:w-24 sm:h-24 md:w-28 md:h-28 lg:w-32 lg:h-32 xl:w-36 xl:h-36 mb-3 relative">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-blue-100 rounded-full p-1 group-hover:shadow-lg transition-all duration-300">
            <div className="w-full h-full rounded-full overflow-hidden border-2 border-white bg-white">
              <Image
                src={imgSrc}
                alt={category.title}
                width={144}
                height={144}
                className="w-full h-full object-cover"
                onError={handleError}
                loading="lazy"
                unoptimized={!imgSrc.startsWith('/')} // Only optimize local images
              />
            </div>
          </div>
        </div>
        <h3 className="text-sm sm:text-base font-medium text-gray-800 text-center group-hover:text-blue-600 transition-colors duration-300 line-clamp-2">
          {category.title}
        </h3>
      </a>
    </div>
  )
})
CategoryItem.displayName = 'CategoryItem'

export default function PopularCategories() {
  const t = useCallback((key: string) => {
    const translations: Record<string, string> = {
      popularCategories: "Popular Categories",
    }
    return translations[key] || key
  }, [])

  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [currentPage, setCurrentPage] = useState(0)
  const carouselRef = useRef<HTMLDivElement>(null)
  const resizeTimeoutRef = useRef<NodeJS.Timeout>()
  const [autoplay, setAutoplay] = useState(true)
  const [autoplaySpeed] = useState(5000)
  const autoplayTimerRef = useRef<NodeJS.Timeout | null>(null)
  const [windowSize, setWindowSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 1200,
    height: typeof window !== 'undefined' ? window.innerHeight : 800,
  })

  // Memoize items per page calculation
  const itemsPerPage = useMemo(() => ({
    sm: 6,  // 2 per row (3 rows)
    md: 6,  // 3 per row (2 rows)
    lg: 8,  // 4 per row (2 rows)
    xl: 10, // 5 per row (2 rows)
  }), [])

  // Calculate items per page based on window size
  const getItemsPerPage = useCallback(() => {
    const { width } = windowSize
    if (width >= 1280) return itemsPerPage.xl
    if (width >= 1024) return itemsPerPage.lg
    if (width >= 768) return itemsPerPage.md
    return itemsPerPage.sm
  }, [windowSize, itemsPerPage])

  const currentItemsPerPage = useMemo(
    () => getItemsPerPage(),
    [getItemsPerPage]
  )

  const totalPages = useMemo(
    () => Math.ceil(categories.length / currentItemsPerPage) || 1,
    [categories.length, currentItemsPerPage]
  )

  // Navigation functions
  const nextPage = useCallback(() => {
    setCurrentPage(prev => (prev < totalPages - 1 ? prev + 1 : 0))
  }, [totalPages])

  // Autoplay controls
  const startAutoplay = useCallback(() => {
    stopAutoplay()
    autoplayTimerRef.current = setInterval(nextPage, autoplaySpeed)
  }, [autoplaySpeed, nextPage])

  const stopAutoplay = useCallback(() => {
    if (autoplayTimerRef.current) {
      clearInterval(autoplayTimerRef.current)
      autoplayTimerRef.current = null
    }
  }, [])

  const handleInteraction = useCallback(() => {
    stopAutoplay()
    if (autoplay) {
      setTimeout(startAutoplay, 10000)
    }
  }, [autoplay, startAutoplay, stopAutoplay])

  // Handle window resize with debounce
  useEffect(() => {
    const handleResize = () => {
      if (resizeTimeoutRef.current) {
        clearTimeout(resizeTimeoutRef.current)
      }
      resizeTimeoutRef.current = setTimeout(() => {
        setWindowSize({
          width: window.innerWidth,
          height: window.innerHeight,
        })
      }, 100)
    }

    window.addEventListener('resize', handleResize)
    return () => {
      window.removeEventListener('resize', handleResize)
      if (resizeTimeoutRef.current) {
        clearTimeout(resizeTimeoutRef.current)
      }
    }
  }, [])

  // Fetch categories
  useEffect(() => {
    let isMounted = true
    const controller = new AbortController()

    const fetchCategories = async () => {
      try {
        setLoading(true)
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_ADMIN_BASE_URL}api/v1/dynamic/dataoperation/get-popular-categories`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
            body: JSON.stringify({
              requestParameters: { recordValueJson: '[]' },
            }),
            signal: controller.signal,
          }
        )

        if (!isMounted) return

        const data = await response.json()
        
        if (data?.data) {
          try {
            const parsedData = JSON.parse(data.data)
            if (Array.isArray(parsedData)) {
              const popularCategories = parsedData.map((item: any) => ({
                id: item.CategoryID,
                title: item.Name,
                image: item.AttachmentURL || 'https://admin.codemedicalapps.com/images/no-image.jpg',
              }))
              if (isMounted) {
                setCategories(popularCategories)
              }
            }
          } catch (error) {
            console.error('Error parsing categories:', error)
          }
        }
      } catch (error) {
        if (error.name !== 'AbortError') {
          console.error('Error fetching categories:', error)
        }
      } finally {
        if (isMounted) {
          setLoading(false)
        }
      }
    }

    fetchCategories()
    return () => {
      isMounted = false
      controller.abort()
    }
  }, [])

  // Handle autoplay
  useEffect(() => {
    if (autoplay && categories.length > 0) {
      startAutoplay()
    }
    return () => stopAutoplay()
  }, [autoplay, categories.length, startAutoplay, stopAutoplay])

  // Generate paginated categories
  const paginatedCategories = useMemo(() => {
    return Array.from({ length: totalPages }).map((_, pageIndex) => (
      categories.slice(
        pageIndex * currentItemsPerPage,
        (pageIndex + 1) * currentItemsPerPage
      )
    ))
  }, [categories, currentItemsPerPage, totalPages])

  if (loading) {
    return (
      <section className="py-8">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl font-bold mb-6">{t("popularCategories")}</h2>
          <div className="w-full flex justify-center items-center py-12">
            <div className="animate-pulse text-lg">Loading popular categories...</div>
          </div>
        </div>
      </section>
    )
  }

  if (!categories.length) {
    return (
      <section className="py-8">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl font-bold mb-6">{t("popularCategories")}</h2>
          <div className="w-full flex justify-center items-center py-12">
            <div className="text-lg text-gray-500">No categories available at the moment.</div>
          </div>
        </div>
      </section>
    )
  }

  return (
    <section className="py-8">
      <div className="container mx-auto px-4">
        <h2 className="text-2xl font-bold mb-6">{t("popularCategories")}</h2>
        
        <div
          className="w-full relative overflow-hidden"
          onMouseEnter={stopAutoplay}
          onMouseLeave={() => autoplay && startAutoplay()}
          onTouchStart={stopAutoplay}
          onTouchEnd={() => autoplay && startAutoplay()}
        >
          <div
            ref={carouselRef}
            className="w-full transition-transform duration-500 ease-in-out"
            style={{
              transform: `translateX(-${(currentPage * 100) / totalPages}%)`,
              width: `${totalPages * 100}%`,
            }}
          >
            <div className="flex flex-nowrap">
              {paginatedCategories.map((pageCategories, pageIndex) => (
                <div
                  key={`page-${pageIndex}`}
                  className="w-full flex-shrink-0 grid grid-cols-3 sm:grid-cols-3 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 sm:gap-6 md:gap-8"
                  style={{ width: `${100 / totalPages}%` }}
                >
                  {pageCategories.map((category) => (
                    <CategoryItem 
                      key={category.id} 
                      category={category} 
                      onInteraction={handleInteraction} 
                    />
                  ))}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Pagination indicators */}
        <div className="flex justify-center mt-4 space-x-2">
          {Array.from({ length: totalPages }).map((_, index) => (
            <button
              key={`indicator-${index}`}
              className={`h-2 rounded-full transition-all ${
                currentPage === index ? "w-6 bg-blue-600" : "w-2 bg-gray-300"
              }`}
              onClick={() => {
                setCurrentPage(index)
                handleInteraction()
              }}
              aria-label={`Go to page ${index + 1}`}
            />
          ))}
        </div>
      </div>
    </section>
  )
}
