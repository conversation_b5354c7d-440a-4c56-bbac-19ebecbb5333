(()=>{var e={};e.id=66,e.ids=[66],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},14494:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(41680).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20770:(e,t,r)=>{Promise.resolve().then(r.bind(r,74440))},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32092:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var s=r(45512),a=r(37778),o=r(97643),i=r(28531),n=r.n(i),l=r(71901);function d(){let{t:e,primaryColor:t}=(0,l.t)();return(0,s.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,s.jsx)(a.Qp,{className:"mb-6",children:(0,s.jsxs)(a.AB,{children:[(0,s.jsx)(a.J5,{children:(0,s.jsx)(a.w1,{asChild:!0,children:(0,s.jsx)(n(),{href:"/",children:"Home"})})}),(0,s.jsx)(a.tH,{}),(0,s.jsx)(a.J5,{children:(0,s.jsx)(a.tJ,{children:"Terms & Conditions"})})]})}),(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold mb-8 text-center",children:"Terms & Conditions"}),(0,s.jsx)("div",{className:"space-y-6",children:[{title:"Purchase Guidelines",content:"Before buying any product, please check the content carefully to make sure its exactly what has been requested weather it is courses, ebooks, printed books or medical application account). We disclaim our responsibility from any mistake made by the customer regarding wrong request. In the same time we will do our best to try to be in the side of our highly valuable customer."},{title:"International Customers",content:"Concerning those who live outside Iraq, we provide link guarantee for limited time and you have to download the course through 1 month from the time you receive it, otherwise we are not responsible for the link expiring or deleting."},{title:"Refund Policy",content:"The only situation in which refund it is possible, is that when the mistake made by our side like in providing different content from that has been requested."},{title:"Account Usage",content:"Whenever we detect using of the medical account on more than the allowed number of devices, the account will be blocked and the refund is not applicable."},{title:"Intellectual Property",content:"Our materials are highly valuable and they take a long time and hard work to provide them, so any sharing of these materials will give us the right to prevent dealing in the future with the customer who uses them for impersonal purpose."}].map((e,r)=>(0,s.jsxs)(o.Zp,{className:"p-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-4",style:{color:t},children:e.title}),(0,s.jsx)("p",{className:"text-muted-foreground",children:e.content})]},r))}),(0,s.jsx)("div",{className:"mt-12 text-center text-sm text-muted-foreground",children:(0,s.jsxs)("p",{children:["Last updated: ",new Date().toLocaleDateString()]})})]})]})}},33873:e=>{"use strict";e.exports=require("path")},37778:(e,t,r)=>{"use strict";r.d(t,{AB:()=>d,J5:()=>c,Qp:()=>l,tH:()=>m,tJ:()=>p,w1:()=>u});var s=r(45512),a=r(58009),o=r(12705),i=r(99905),n=(r(14494),r(59462));let l=a.forwardRef(({...e},t)=>(0,s.jsx)("nav",{ref:t,"aria-label":"breadcrumb",...e}));l.displayName="Breadcrumb";let d=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("ol",{ref:r,className:(0,n.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",e),...t}));d.displayName="BreadcrumbList";let c=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("li",{ref:r,className:(0,n.cn)("inline-flex items-center gap-1.5",e),...t}));c.displayName="BreadcrumbItem";let u=a.forwardRef(({asChild:e,className:t,...r},a)=>{let i=e?o.DX:"a";return(0,s.jsx)(i,{ref:a,className:(0,n.cn)("transition-colors hover:text-foreground",t),...r})});u.displayName="BreadcrumbLink";let p=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("span",{ref:r,role:"link","aria-disabled":"true","aria-current":"page",className:(0,n.cn)("font-normal text-foreground",e),...t}));p.displayName="BreadcrumbPage";let m=({children:e,className:t,...r})=>(0,s.jsx)("li",{role:"presentation","aria-hidden":"true",className:(0,n.cn)("[&>svg]:size-3.5",t),...r,children:e??(0,s.jsx)(i.A,{})});m.displayName="BreadcrumbSeparator"},38922:(e,t,r)=>{Promise.resolve().then(r.bind(r,32092))},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},74440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\terms\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\terms\\page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")},96300:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(70260),a=r(28203),o=r(25155),i=r.n(o),n=r(67292),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let d={children:["",{children:["terms",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,74440)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\terms\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,19611)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\terms\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/terms/page",pathname:"/terms",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},97643:(e,t,r)=>{"use strict";r.d(t,{Wu:()=>n,Zp:()=>i,wL:()=>l});var s=r(45512),a=r(58009),o=r(59462);let i=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,o.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));i.displayName="Card",a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",e),...t})).displayName="CardHeader",a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("h3",{ref:r,className:(0,o.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t})).displayName="CardTitle",a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("p",{ref:r,className:(0,o.cn)("text-sm text-muted-foreground",e),...t})).displayName="CardDescription";let n=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,o.cn)("p-6 pt-0",e),...t}));n.displayName="CardContent";let l=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,o.cn)("flex items-center p-6 pt-0",e),...t}));l.displayName="CardFooter"},99905:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(41680).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[638,551,875],()=>r(96300));module.exports=s})();