"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./lib/api-helper.ts":
/*!***************************!*\
  !*** ./lib/api-helper.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Config: () => (/* binding */ Config),\n/* harmony export */   MakeApiCallAsync: () => (/* binding */ MakeApiCallAsync),\n/* harmony export */   convertUSDToIQD: () => (/* binding */ convertUSDToIQD),\n/* harmony export */   fetchCurrencyRate: () => (/* binding */ fetchCurrencyRate),\n/* harmony export */   formatPrice: () => (/* binding */ formatPrice)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./config */ \"(app-pages-browser)/./lib/config.ts\");\n\n\n// Configure axios defaults for HTTPS connections\naxios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].defaults.timeout = 30000; // 30 seconds timeout\n// Handle self-signed certificates for local development\nif ( true && window.location.protocol === 'https:' && _config__WEBPACK_IMPORTED_MODULE_0__.Config.ADMIN_BASE_URL.includes('localhost')) {\n    axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].defaults.httpsAgent = {\n        rejectUnauthorized: false\n    };\n}\nconst Config = {\n    ADMIN_BASE_URL: _config__WEBPACK_IMPORTED_MODULE_0__.Config.ADMIN_BASE_URL,\n    API_VERSION: 'v1',\n    DYNAMIC_METHOD_SUB_URL: 'api/v1/dynamic/dataoperation/',\n    END_POINT_NAMES: {\n        GET_CATEGORIES_LIST: 'get-categories-list',\n        SIGNUP_USER: 'signup-user',\n        GET_HOME_SCREEN_BANNER: 'get-home-screen-banner',\n        GET_RECENT_PRODUCTS: 'get-recents-products-list',\n        GET_POPULAR_PRODUCTS: 'get-popular-products-list',\n        GET_HOT_DEAL_PRODUCTS: 'get-hot-deal-products',\n        GET_CAMPAIGNS_LIST: 'get-web-campaign-list',\n        GET_PRODUCTS_LIST: 'get-products-list',\n        GET_ALL_PRODUCTS: 'api/v1/products/get-all-products',\n        GET_MANUFACTURERS_LIST: 'get-manufacturers-list',\n        GET_TAGS_LIST: 'get-tags-list',\n        GET_CURRENCY_RATE: 'get-currency-rate',\n        GET_COUPON_CODE_DISCOUNT: 'get-coupon-code-discount-value/calculate-coupon-discount',\n        ..._config__WEBPACK_IMPORTED_MODULE_0__.Config.END_POINT_NAMES\n    },\n    COMMON_CONTROLLER_SUB_URL: 'api/v1/common/'\n};\nconst GetTokenForHeader = async ()=>{\n    // Implement token retrieval logic here\n    // For example, from localStorage or a secure storage\n    return localStorage.getItem('token') || null;\n};\nconst GetUserIdForHeader = async ()=>{\n    // Implement user ID retrieval logic here\n    return localStorage.getItem('userId') || null;\n};\nconst MakeApiCallAsync = async function(endPointName, methodSubURL, param, headers, methodType) {\n    let loading = arguments.length > 5 && arguments[5] !== void 0 ? arguments[5] : true;\n    try {\n        // Create a copy of headers to avoid modifying the original object\n        const updatedHeaders = {\n            ...headers\n        };\n        // Check if Authorization header already exists\n        if (!updatedHeaders.hasOwnProperty('Authorization')) {\n            // If not, try to add it using the token from GetTokenForHeader\n            const token = await GetTokenForHeader();\n            if (token) {\n                updatedHeaders['Authorization'] = 'Bearer ' + token;\n            }\n        }\n        // For backward compatibility, also add Token header if it doesn't exist\n        if (!updatedHeaders.hasOwnProperty('Token')) {\n            const token = await GetTokenForHeader();\n            updatedHeaders['Token'] = token !== null && token !== void 0 ? token : \"\";\n        }\n        // Add user id in header\n        if (!updatedHeaders.hasOwnProperty('UserID')) {\n            const UserID = await GetUserIdForHeader();\n            updatedHeaders['UserID'] = UserID !== null && UserID !== void 0 ? UserID : \"\";\n        }\n        // Always ensure proper content type headers are set\n        if (!updatedHeaders.hasOwnProperty('Accept')) {\n            updatedHeaders['Accept'] = 'application/json';\n        }\n        if (!updatedHeaders.hasOwnProperty('Content-Type')) {\n            updatedHeaders['Content-Type'] = 'application/json';\n        }\n        const URL = Config['ADMIN_BASE_URL'] + (methodSubURL === null || methodSubURL == undefined ? Config['DYNAMIC_METHOD_SUB_URL'] : methodSubURL) + endPointName;\n        methodType = methodType !== null && methodType !== void 0 ? methodType : \"POST\";\n        const axiosConfig = {\n            headers: updatedHeaders,\n            responseType: 'json',\n            timeout: 30000,\n            withCredentials: false // Disable sending cookies with cross-origin requests\n        };\n        if (methodType === 'POST') {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(URL, param, axiosConfig);\n            return response;\n        } else if (methodType == 'GET') {\n            axiosConfig.params = param; // For GET requests, params should be used\n            const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(URL, axiosConfig);\n            return response;\n        } else {\n            // Return a default error response for unsupported method types\n            return {\n                data: {\n                    errorMessage: \"Unsupported method type: \".concat(methodType),\n                    status: 'method_not_supported'\n                }\n            };\n        }\n    } catch (error) {\n        console.error('API call failed:', error);\n        // Return a structured error response instead of throwing\n        // This allows components to handle errors more gracefully\n        // Create a response object with the ApiResponse interface\n        const errorResponse = {\n            data: {\n                errorMessage: 'An unexpected error occurred',\n                status: 'unknown_error'\n            }\n        };\n        // Type guard for axios error with response\n        if (error && typeof error === 'object' && 'response' in error && error.response) {\n            var _axiosError_response, _axiosError_response1;\n            // The request was made and the server responded with a status code\n            // that falls out of the range of 2xx\n            const axiosError = error;\n            const responseData = (_axiosError_response = axiosError.response) === null || _axiosError_response === void 0 ? void 0 : _axiosError_response.data;\n            errorResponse.data = {\n                errorMessage: (responseData === null || responseData === void 0 ? void 0 : responseData.errorMessage) || 'An error occurred while processing your request.',\n                status: (_axiosError_response1 = axiosError.response) === null || _axiosError_response1 === void 0 ? void 0 : _axiosError_response1.status\n            };\n        // Type guard for axios error with request but no response\n        } else if (error && typeof error === 'object' && 'request' in error) {\n            // The request was made but no response was received\n            // This is likely a network error, CORS issue, or server not running\n            const axiosError = error;\n            let networkErrorMessage = 'Network error: No response received from server.';\n            // Check if it's a CORS issue\n            if (axiosError.message && axiosError.message.includes('Network Error')) {\n                networkErrorMessage = 'Network Error: This may be due to CORS policy restrictions, server unavailability, or an invalid SSL certificate. Please check that:';\n                networkErrorMessage += '\\n1. The server is running and accessible';\n                networkErrorMessage += '\\n2. The URL is correct: ' + Config.ADMIN_BASE_URL;\n                networkErrorMessage += '\\n3. CORS is properly configured on the server';\n                networkErrorMessage += '\\n4. If using HTTPS, the SSL certificate is valid';\n            }\n            errorResponse.data = {\n                errorMessage: networkErrorMessage,\n                status: 'network_error'\n            };\n        } else {\n            // Something happened in setting up the request that triggered an Error\n            // Type guard for standard Error object\n            const errorMessage = error && typeof error === 'object' && 'message' in error ? error.message : 'An unexpected error occurred';\n            errorResponse.data = {\n                errorMessage,\n                status: 'request_error'\n            };\n        }\n        return errorResponse;\n    }\n};\n// Currency rate service\nconst fetchCurrencyRate = async ()=>{\n    try {\n        const response = await MakeApiCallAsync('getrate', 'api/v1/common/', {}, {}, 'GET');\n        if (response && response.data && !response.data.errorMessage) {\n            return parseInt(response.data) || 1500; // Default rate if parsing fails\n        }\n        return 1500; // Default fallback rate\n    } catch (error) {\n        console.error('Error fetching currency rate:', error);\n        return 1500; // Default fallback rate\n    }\n};\nconst convertUSDToIQD = (usdPrice, rate)=>{\n    return Math.round(usdPrice * rate);\n};\nconst formatPrice = function(price) {\n    let currency = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'USD';\n    if (currency === 'IQD') {\n        return \"\".concat(price.toLocaleString(), \" IQD\");\n    }\n    return \"$\".concat(price.toFixed(2));\n};\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api-helper.ts\n"));

/***/ })

});