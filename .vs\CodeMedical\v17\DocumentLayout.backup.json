{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\adminpanel\\helpers\\serviceextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|solutionrelative:adminpanel\\helpers\\serviceextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\adminpanel\\areas\\v1\\controllers\\apidynamiccontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|solutionrelative:adminpanel\\areas\\v1\\controllers\\apidynamiccontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{92047E6A-828E-47EE-9D53-71F584E9E5EA}|DAL\\DAL.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\dal\\repository\\iservices\\iapioperationservicesdal.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{92047E6A-828E-47EE-9D53-71F584E9E5EA}|DAL\\DAL.csproj|solutionrelative:dal\\repository\\iservices\\iapioperationservicesdal.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{92047E6A-828E-47EE-9D53-71F584E9E5EA}|DAL\\DAL.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\dal\\repository\\services\\apioperationservicesdal.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{92047E6A-828E-47EE-9D53-71F584E9E5EA}|DAL\\DAL.csproj|solutionrelative:dal\\repository\\services\\apioperationservicesdal.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{92047E6A-828E-47EE-9D53-71F584E9E5EA}|DAL\\DAL.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\dal\\repository\\services\\productservicesdal.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{92047E6A-828E-47EE-9D53-71F584E9E5EA}|DAL\\DAL.csproj|solutionrelative:dal\\repository\\services\\productservicesdal.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{92047E6A-828E-47EE-9D53-71F584E9E5EA}|DAL\\DAL.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\dal\\repository\\iservices\\iproductservicesdal.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{92047E6A-828E-47EE-9D53-71F584E9E5EA}|DAL\\DAL.csproj|solutionrelative:dal\\repository\\iservices\\iproductservicesdal.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\adminpanel\\areas\\v1\\controllers\\apiproductscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|solutionrelative:adminpanel\\areas\\v1\\controllers\\apiproductscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\adminpanel\\areas\\v1\\controllers\\apicommoncontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|solutionrelative:adminpanel\\areas\\v1\\controllers\\apicommoncontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\adminpanel\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|solutionrelative:adminpanel\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\adminpanel\\controllers\\productscatalogcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|solutionrelative:adminpanel\\controllers\\productscatalogcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{92047E6A-828E-47EE-9D53-71F584E9E5EA}|DAL\\DAL.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\dal\\repository\\services\\basicdataservicesdal.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{92047E6A-828E-47EE-9D53-71F584E9E5EA}|DAL\\DAL.csproj|solutionrelative:dal\\repository\\services\\basicdataservicesdal.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\adminpanel\\views\\productscatalog\\updateproduct.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|solutionrelative:adminpanel\\views\\productscatalog\\updateproduct.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\adminpanel\\program.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|solutionrelative:adminpanel\\program.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\adminpanel\\views\\productscatalog\\createnewproduct.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|solutionrelative:adminpanel\\views\\productscatalog\\createnewproduct.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{A2C6AA03-5F94-4108-A8E0-92DD51A7F60A}|Entities\\Entities.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\entities\\dbmodels\\product.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{A2C6AA03-5F94-4108-A8E0-92DD51A7F60A}|Entities\\Entities.csproj|solutionrelative:entities\\dbmodels\\product.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{A2C6AA03-5F94-4108-A8E0-92DD51A7F60A}|Entities\\Entities.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\entities\\dbmodels\\producttype.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{A2C6AA03-5F94-4108-A8E0-92DD51A7F60A}|Entities\\Entities.csproj|solutionrelative:entities\\dbmodels\\producttype.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{A2C6AA03-5F94-4108-A8E0-92DD51A7F60A}|Entities\\Entities.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\entities\\dbmodels\\manufacturer.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{A2C6AA03-5F94-4108-A8E0-92DD51A7F60A}|Entities\\Entities.csproj|solutionrelative:entities\\dbmodels\\manufacturer.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{92047E6A-828E-47EE-9D53-71F584E9E5EA}|DAL\\DAL.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\dal\\repository\\iservices\\ibasicdataservicesdal.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{92047E6A-828E-47EE-9D53-71F584E9E5EA}|DAL\\DAL.csproj|solutionrelative:dal\\repository\\iservices\\ibasicdataservicesdal.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{A2C6AA03-5F94-4108-A8E0-92DD51A7F60A}|Entities\\Entities.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\entities\\dbinheritedmodels\\inheritedentitieslevelone.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{A2C6AA03-5F94-4108-A8E0-92DD51A7F60A}|Entities\\Entities.csproj|solutionrelative:entities\\dbinheritedmodels\\inheritedentitieslevelone.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{A2C6AA03-5F94-4108-A8E0-92DD51A7F60A}|Entities\\Entities.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\entities\\dbmodels\\bankstatus.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{A2C6AA03-5F94-4108-A8E0-92DD51A7F60A}|Entities\\Entities.csproj|solutionrelative:entities\\dbmodels\\bankstatus.cs||{A6C744A8-0E4A-4FC6-886A-************}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 17, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{34e76e81-ee4a-11d0-ae2e-00a0c90fffc3}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}, {"$type": "Bookmark", "Name": "ST:0:0:{********-fc2c-11d2-a433-00c04f72d18a}"}, {"$type": "Bookmark", "Name": "ST:0:0:{0504ff91-9d61-11d0-a794-00a0c9110051}"}, {"$type": "Bookmark", "Name": "ST:0:0:{be4d7042-ba3f-11d2-840e-00c04f9902c1}"}, {"$type": "Bookmark", "Name": "ST:0:0:{605322a2-17ae-43f4-b60f-766556e46c87}"}, {"$type": "Bookmark", "Name": "ST:0:0:{ecb7191a-597b-41f5-9843-03a4cf275dde}"}, {"$type": "Bookmark", "Name": "ST:0:0:{0ad07096-bba9-4900-a651-0598d26f6d24}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Bookmark", "Name": "ST:0:0:{6324226f-61b6-4f28-92ee-18d4b5fe1e48}"}, {"$type": "Bookmark", "Name": "ST:0:0:{57d563b6-44a5-47df-85be-f4199ad6b651}"}, {"$type": "Bookmark", "Name": "ST:0:0:{004be353-6879-467c-9d1e-9ac23cdf6d49}"}, {"$type": "Document", "DocumentIndex": 7, "Title": "ApiCommonController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Areas\\V1\\Controllers\\ApiCommonController.cs", "RelativeDocumentMoniker": "AdminPanel\\Areas\\V1\\Controllers\\ApiCommonController.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Areas\\V1\\Controllers\\ApiCommonController.cs", "RelativeToolTip": "AdminPanel\\Areas\\V1\\Controllers\\ApiCommonController.cs", "ViewState": "AgIAAIMAAAAAAAAAAAAYwKcEAABtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-04T07:07:17.677Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "ApiProductsController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Areas\\V1\\Controllers\\ApiProductsController.cs", "RelativeDocumentMoniker": "AdminPanel\\Areas\\V1\\Controllers\\ApiProductsController.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Areas\\V1\\Controllers\\ApiProductsController.cs", "RelativeToolTip": "AdminPanel\\Areas\\V1\\Controllers\\ApiProductsController.cs", "ViewState": "AgIAAE4AAAAAAAAAAAAYwCsAAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T17:11:23.025Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "IProductServicesDAL.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\DAL\\Repository\\IServices\\IProductServicesDAL.cs", "RelativeDocumentMoniker": "DAL\\Repository\\IServices\\IProductServicesDAL.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\DAL\\Repository\\IServices\\IProductServicesDAL.cs", "RelativeToolTip": "DAL\\Repository\\IServices\\IProductServicesDAL.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAuwBgAAAA9AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T10:01:27.136Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "ProductServicesDAL.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\DAL\\Repository\\Services\\ProductServicesDAL.cs", "RelativeDocumentMoniker": "DAL\\Repository\\Services\\ProductServicesDAL.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\DAL\\Repository\\Services\\ProductServicesDAL.cs", "RelativeToolTip": "DAL\\Repository\\Services\\ProductServicesDAL.cs", "ViewState": "AgIAAFwAAAAAAAAAAAAiwBMAAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T08:16:28.188Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "ApiDynamicController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Areas\\V1\\Controllers\\ApiDynamicController.cs", "RelativeDocumentMoniker": "AdminPanel\\Areas\\V1\\Controllers\\ApiDynamicController.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Areas\\V1\\Controllers\\ApiDynamicController.cs", "RelativeToolTip": "AdminPanel\\Areas\\V1\\Controllers\\ApiDynamicController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAGcAAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T08:06:21.831Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "ServiceExtensions.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Helpers\\ServiceExtensions.cs", "RelativeDocumentMoniker": "AdminPanel\\Helpers\\ServiceExtensions.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Helpers\\ServiceExtensions.cs", "RelativeToolTip": "AdminPanel\\Helpers\\ServiceExtensions.cs", "ViewState": "AgIAABIAAAAAAAAAAAAUwCQAAAC8AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T08:04:36.24Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "IApiOperationServicesDAL.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\DAL\\Repository\\IServices\\IApiOperationServicesDAL.cs", "RelativeDocumentMoniker": "DAL\\Repository\\IServices\\IApiOperationServicesDAL.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\DAL\\Repository\\IServices\\IApiOperationServicesDAL.cs", "RelativeToolTip": "DAL\\Repository\\IServices\\IApiOperationServicesDAL.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAmwA0AAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-15T13:14:01.854Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "ApiOperationServicesDAL.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\DAL\\Repository\\Services\\ApiOperationServicesDAL.cs", "RelativeDocumentMoniker": "DAL\\Repository\\Services\\ApiOperationServicesDAL.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\DAL\\Repository\\Services\\ApiOperationServicesDAL.cs", "RelativeToolTip": "DAL\\Repository\\Services\\ApiOperationServicesDAL.cs", "ViewState": "AgIAAD0AAAAAAAAAAAAYwHMAAAAyAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T08:06:21.848Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "UpdateProduct.cshtml", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Views\\ProductsCatalog\\UpdateProduct.cshtml", "RelativeDocumentMoniker": "AdminPanel\\Views\\ProductsCatalog\\UpdateProduct.cshtml", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Views\\ProductsCatalog\\UpdateProduct.cshtml", "RelativeToolTip": "AdminPanel\\Views\\ProductsCatalog\\UpdateProduct.cshtml", "ViewState": "AgIAABgHAAAAAAAAAAAQwDAHAAA/AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-05-31T07:57:42.375Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "CreateNewProduct.cshtml", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Views\\ProductsCatalog\\CreateNewProduct.cshtml", "RelativeDocumentMoniker": "AdminPanel\\Views\\ProductsCatalog\\CreateNewProduct.cshtml", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Views\\ProductsCatalog\\CreateNewProduct.cshtml", "RelativeToolTip": "AdminPanel\\Views\\ProductsCatalog\\CreateNewProduct.cshtml", "ViewState": "AgIAAHgAAAAAAAAAAAAAAIgAAABPAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-05-31T07:46:43.483Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "appsettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\appsettings.json", "RelativeDocumentMoniker": "AdminPanel\\appsettings.json", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\appsettings.json", "RelativeToolTip": "AdminPanel\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAB4AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-05-31T09:11:35.873Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "BasicDataServicesDAL.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\DAL\\Repository\\Services\\BasicDataServicesDAL.cs", "RelativeDocumentMoniker": "DAL\\Repository\\Services\\BasicDataServicesDAL.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\DAL\\Repository\\Services\\BasicDataServicesDAL.cs", "RelativeToolTip": "DAL\\Repository\\Services\\BasicDataServicesDAL.cs", "ViewState": "AgIAAMECAAAAAAAAAAAcwKECAAA8AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T07:39:33.529Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "ProductsCatalogController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Controllers\\ProductsCatalogController.cs", "RelativeDocumentMoniker": "AdminPanel\\Controllers\\ProductsCatalogController.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Controllers\\ProductsCatalogController.cs", "RelativeToolTip": "AdminPanel\\Controllers\\ProductsCatalogController.cs", "ViewState": "AgIAAHEDAAAAAAAAAAAIwIIDAABlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T07:30:40.463Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Program.cs", "RelativeDocumentMoniker": "AdminPanel\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Program.cs", "RelativeToolTip": "AdminPanel\\Program.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T09:11:44.229Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "IBasicDataServicesDAL.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\DAL\\Repository\\IServices\\IBasicDataServicesDAL.cs", "RelativeDocumentMoniker": "DAL\\Repository\\IServices\\IBasicDataServicesDAL.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\DAL\\Repository\\IServices\\IBasicDataServicesDAL.cs", "RelativeToolTip": "DAL\\Repository\\IServices\\IBasicDataServicesDAL.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAlwBUAAAA7AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T07:33:43.47Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "producttype.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\Entities\\DBModels\\producttype.cs", "RelativeDocumentMoniker": "Entities\\DBModels\\producttype.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\Entities\\DBModels\\producttype.cs", "RelativeToolTip": "Entities\\DBModels\\producttype.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T07:28:37.743Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "Product.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\Entities\\DBModels\\Product.cs", "RelativeDocumentMoniker": "Entities\\DBModels\\Product.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\Entities\\DBModels\\Product.cs", "RelativeToolTip": "Entities\\DBModels\\Product.cs", "ViewState": "AgIAAA0AAAAAAAAAAAAMwBsAAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T07:25:09.494Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "Manufacturer.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\Entities\\DBModels\\Manufacturer.cs", "RelativeDocumentMoniker": "Entities\\DBModels\\Manufacturer.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\Entities\\DBModels\\Manufacturer.cs", "RelativeToolTip": "Entities\\DBModels\\Manufacturer.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T07:24:15.865Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "InheritedEntitiesLevelOne.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\Entities\\DBInheritedModels\\InheritedEntitiesLevelOne.cs", "RelativeDocumentMoniker": "Entities\\DBInheritedModels\\InheritedEntitiesLevelOne.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\Entities\\DBInheritedModels\\InheritedEntitiesLevelOne.cs", "RelativeToolTip": "Entities\\DBInheritedModels\\InheritedEntitiesLevelOne.cs", "ViewState": "AgIAAFgAAAAAAAAAAAAmwGkAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T07:34:38.358Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "BankStatus.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\Entities\\DBModels\\BankStatus.cs", "RelativeDocumentMoniker": "Entities\\DBModels\\BankStatus.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\Entities\\DBModels\\BankStatus.cs", "RelativeToolTip": "Entities\\DBModels\\BankStatus.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T07:24:54.019Z"}]}]}]}