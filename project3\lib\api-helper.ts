import axios, { AxiosError, AxiosResponse } from 'axios';
import { Config as AppConfig } from './config';

// Configure axios defaults for HTTPS connections
axios.defaults.timeout = 30000; // 30 seconds timeout

// <PERSON>le self-signed certificates for local development
if (typeof window !== 'undefined' && window.location.protocol === 'https:' &&
    AppConfig.ADMIN_BASE_URL.includes('localhost')) {
  axios.defaults.httpsAgent = { rejectUnauthorized: false };
}

// Define interfaces for API responses
interface ApiErrorResponse {
    errorMessage?: string;
    status?: number | string;
}

interface ApiResponse {
    data?: any;
    errorMessage?: string;
    status?: number | string;
}

const Config = {
    ADMIN_BASE_URL: AppConfig.ADMIN_BASE_URL,
    API_VERSION: 'v1',
    DYNAMIC_METHOD_SUB_URL: 'api/v1/dynamic/dataoperation/',
    END_POINT_NAMES: {
        GET_CATEGORIES_LIST: 'get-categories-list',
        SIGNUP_USER: 'signup-user',
        GET_HOME_SCREEN_BANNER: 'get-home-screen-banner',
        GET_RECENT_PRODUCTS: 'get-recents-products-list',
        GET_POPULAR_PRODUCTS: 'get-popular-products-list',
        GET_HOT_DEAL_PRODUCTS: 'get-hot-deal-products',
        GET_CAMPAIGNS_LIST: 'get-web-campaign-list',
        GET_PRODUCTS_LIST: 'get-products-list',
        GET_ALL_PRODUCTS: 'api/v1/products/get-all-products',
        GET_MANUFACTURERS_LIST: 'get-manufacturers-list',
        GET_TAGS_LIST: 'get-tags-list',
        GET_CURRENCY_RATE: 'get-currency-rate',
        GET_COUPON_CODE_DISCOUNT: 'get-coupon-code-discount-value/calculate-coupon-discount',
        ...AppConfig.END_POINT_NAMES
    },
    COMMON_CONTROLLER_SUB_URL: 'api/v1/common/'
};

const GetTokenForHeader = async () => {
    // Implement token retrieval logic here
    // For example, from localStorage or a secure storage
    return localStorage.getItem('token') || null;
};

const GetUserIdForHeader = async () => {
    // Implement user ID retrieval logic here
    return localStorage.getItem('userId') || null;
};

export const MakeApiCallAsync = async (endPointName: string, methodSubURL: string | null, param: any, headers: any, methodType: string, loading = true): Promise<AxiosResponse | { data: ApiErrorResponse }> => {
    try {
        // Create a copy of headers to avoid modifying the original object
        const updatedHeaders = { ...headers };

        // Check if Authorization header already exists
        if (!updatedHeaders.hasOwnProperty('Authorization')) {
            // If not, try to add it using the token from GetTokenForHeader
            const token = await GetTokenForHeader();
            if (token) {
                updatedHeaders['Authorization'] = 'Bearer ' + token;
            }
        }

        // For backward compatibility, also add Token header if it doesn't exist
        if (!updatedHeaders.hasOwnProperty('Token')) {
            const token = await GetTokenForHeader();
            updatedHeaders['Token'] = token ?? "";
        }

        // Add user id in header
        if (!updatedHeaders.hasOwnProperty('UserID')) {
            const UserID = await GetUserIdForHeader();
            updatedHeaders['UserID'] = UserID ?? "";
        }

        // Always ensure proper content type headers are set
        if (!updatedHeaders.hasOwnProperty('Accept')) {
            updatedHeaders['Accept'] = 'application/json';
        }

        if (!updatedHeaders.hasOwnProperty('Content-Type')) {
            updatedHeaders['Content-Type'] = 'application/json';
        }

        const URL = Config['ADMIN_BASE_URL'] + (methodSubURL === null || methodSubURL == undefined ? Config['DYNAMIC_METHOD_SUB_URL'] : methodSubURL) + endPointName;
        methodType = methodType ?? "POST";

        const axiosConfig: import('axios').AxiosRequestConfig = {
            headers: updatedHeaders,
            responseType: 'json' as const, // Explicitly set response type to JSON with proper type assertion
            timeout: 30000, // 30 seconds timeout
            withCredentials: false // Disable sending cookies with cross-origin requests
        };

        if (methodType === 'POST') {
            const response = await axios.post(URL, param, axiosConfig);
            return response;
        } else if (methodType == 'GET') {
            axiosConfig.params = param; // For GET requests, params should be used
            const response = await axios.get(URL, axiosConfig);
            return response;
        } else {
            // Return a default error response for unsupported method types
            return {
                data: {
                    errorMessage: `Unsupported method type: ${methodType}`,
                    status: 'method_not_supported'
                }
            };
        }
    } catch (error: unknown) {
        console.error('API call failed:', error);
        // Return a structured error response instead of throwing
        // This allows components to handle errors more gracefully

        // Create a response object with the ApiResponse interface
        const errorResponse: { data: ApiErrorResponse } = {
            data: {
                errorMessage: 'An unexpected error occurred',
                status: 'unknown_error'
            }
        };

        // Type guard for axios error with response
        if (error && typeof error === 'object' && 'response' in error && error.response) {
            // The request was made and the server responded with a status code
            // that falls out of the range of 2xx
            const axiosError = error as AxiosError;
            const responseData = axiosError.response?.data as ApiErrorResponse | undefined;

            errorResponse.data = {
                errorMessage: responseData?.errorMessage || 'An error occurred while processing your request.',
                status: axiosError.response?.status
            };
        // Type guard for axios error with request but no response
        } else if (error && typeof error === 'object' && 'request' in error) {
            // The request was made but no response was received
            // This is likely a network error, CORS issue, or server not running
            const axiosError = error as AxiosError;
            let networkErrorMessage = 'Network error: No response received from server.';

            // Check if it's a CORS issue
            if (axiosError.message && axiosError.message.includes('Network Error')) {
                networkErrorMessage = 'Network Error: This may be due to CORS policy restrictions, server unavailability, or an invalid SSL certificate. Please check that:';
                networkErrorMessage += '\n1. The server is running and accessible';
                networkErrorMessage += '\n2. The URL is correct: ' + Config.ADMIN_BASE_URL;
                networkErrorMessage += '\n3. CORS is properly configured on the server';
                networkErrorMessage += '\n4. If using HTTPS, the SSL certificate is valid';
            }

            errorResponse.data = {
                errorMessage: networkErrorMessage,
                status: 'network_error'
            };
        } else {
            // Something happened in setting up the request that triggered an Error
            // Type guard for standard Error object
            const errorMessage = error && typeof error === 'object' && 'message' in error
                ? (error as Error).message
                : 'An unexpected error occurred';

            errorResponse.data = {
                errorMessage,
                status: 'request_error'
            };
        }

        return errorResponse;
    }
};

// Currency rate service
export const fetchCurrencyRate = async (): Promise<number> => {
    try {
        const response = await MakeApiCallAsync('getrate', 'api/v1/common/', {}, {}, 'GET');

        if (response && response.data && !response.data.errorMessage) {
            return parseInt(response.data) || 1500; // Default rate if parsing fails
        }

        return 1500; // Default fallback rate
    } catch (error) {
        console.error('Error fetching currency rate:', error);
        return 1500; // Default fallback rate
    }
};

export const convertUSDToIQD = (usdPrice: number, rate: number): number => {
    return Math.round(usdPrice * rate);
};

export const formatPrice = (price: number, currency: 'USD' | 'IQD' = 'USD'): string => {
    if (currency === 'IQD') {
        return `${price.toLocaleString()} IQD`;
    }
    return `$${price.toFixed(2)}`;
};

export { Config };