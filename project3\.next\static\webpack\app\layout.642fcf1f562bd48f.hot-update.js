"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./contexts/coupon-context.tsx":
/*!*************************************!*\
  !*** ./contexts/coupon-context.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CouponProvider: () => (/* binding */ CouponProvider),\n/* harmony export */   useCoupon: () => (/* binding */ useCoupon)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api_helper__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api-helper */ \"(app-pages-browser)/./lib/api-helper.ts\");\n/* __next_internal_client_entry_do_not_use__ CouponProvider,useCoupon auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst CouponContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction CouponProvider(param) {\n    let { children } = param;\n    _s();\n    const [appliedCoupon, setAppliedCoupon] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const validateCoupon = async (code, amount, cartItems)=>{\n        if (!code.trim()) {\n            return {\n                valid: false,\n                message: 'Please enter a coupon code',\n                discount: 0\n            };\n        }\n        setIsLoading(true);\n        try {\n            // Prepare cart data from cart items\n            const cartData = (cartItems === null || cartItems === void 0 ? void 0 : cartItems.map((item)=>({\n                    ProductId: item.id,\n                    ProductName: item.name,\n                    Price: item.adjustedPrice || item.price,\n                    Quantity: item.quantity,\n                    IsDiscountAllowed: true\n                }))) || [];\n            const cartJsonData = JSON.stringify(cartData);\n            const param = {\n                requestParameters: {\n                    CouponCode: code.toUpperCase(),\n                    cartJsonData: cartJsonData\n                }\n            };\n            const headers = {\n                'Content-Type': 'application/json',\n                'Accept': 'application/json'\n            };\n            const response = await (0,_lib_api_helper__WEBPACK_IMPORTED_MODULE_2__.MakeApiCallAsync)(_lib_api_helper__WEBPACK_IMPORTED_MODULE_2__.Config.END_POINT_NAMES.GET_COUPON_CODE_DISCOUNT, _lib_api_helper__WEBPACK_IMPORTED_MODULE_2__.Config.COMMON_CONTROLLER_SUB_URL, param, headers, 'POST');\n            if (response && response.data && !response.data.errorMessage) {\n                let couponData;\n                // Parse the response data\n                if (typeof response.data.data === 'string') {\n                    couponData = JSON.parse(response.data.data);\n                } else {\n                    couponData = response.data.data;\n                }\n                if (couponData && couponData.DiscountValueAfterCouponAppliedWithQuantity > 0) {\n                    const discountAmount = couponData.DiscountValueAfterCouponAppliedWithQuantity;\n                    // Create coupon object for state\n                    const coupon = {\n                        code: code.toUpperCase(),\n                        discount: discountAmount,\n                        type: 'fixed' // Assuming fixed amount from API\n                    };\n                    setAppliedCoupon(coupon);\n                    return {\n                        valid: true,\n                        message: 'Coupon applied successfully!',\n                        discount: discountAmount\n                    };\n                } else {\n                    return {\n                        valid: false,\n                        message: 'Invalid coupon code or coupon not applicable to your cart',\n                        discount: 0\n                    };\n                }\n            } else {\n                var _response_data;\n                return {\n                    valid: false,\n                    message: ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.errorMessage) || 'Failed to validate coupon',\n                    discount: 0\n                };\n            }\n        } catch (error) {\n            console.error('Coupon validation error:', error);\n            return {\n                valid: false,\n                message: 'Error validating coupon. Please try again.',\n                discount: 0\n            };\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const clearCoupon = ()=>{\n        setAppliedCoupon(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CouponContext.Provider, {\n        value: {\n            appliedCoupon,\n            validateCoupon,\n            clearCoupon,\n            isLoading\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\contexts\\\\coupon-context.tsx\",\n        lineNumber: 125,\n        columnNumber: 5\n    }, this);\n}\n_s(CouponProvider, \"kjHJqm53e+vsbGz9dHK1EJBrEqk=\");\n_c = CouponProvider;\nfunction useCoupon() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(CouponContext);\n    if (context === undefined) {\n        throw new Error('useCoupon must be used within a CouponProvider');\n    }\n    return context;\n}\n_s1(useCoupon, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"CouponProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./contexts/coupon-context.tsx\n"));

/***/ })

});