"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"4fa3d51619c0\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xceWh5YXNvZnRcXERvd25sb2Fkc1xcZWNcXC5ORVQgOCBWZXJzaW9uIC0gTGF0ZXN0XFxwcm9qZWN0XFxjb2RlbWVkaWNhbFxccHJvamVjdDNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI0ZmEzZDUxNjE5YzBcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/ui/header.tsx":
/*!**********************************!*\
  !*** ./components/ui/header.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_Mail_MessageCircle_Phone_Search_ShoppingCart_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,Mail,MessageCircle,Phone,Search,ShoppingCart,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_Mail_MessageCircle_Phone_Search_ShoppingCart_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,Mail,MessageCircle,Phone,Search,ShoppingCart,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_Mail_MessageCircle_Phone_Search_ShoppingCart_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,Mail,MessageCircle,Phone,Search,ShoppingCart,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_Mail_MessageCircle_Phone_Search_ShoppingCart_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,Mail,MessageCircle,Phone,Search,ShoppingCart,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_Mail_MessageCircle_Phone_Search_ShoppingCart_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,Mail,MessageCircle,Phone,Search,ShoppingCart,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_Mail_MessageCircle_Phone_Search_ShoppingCart_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,Mail,MessageCircle,Phone,Search,ShoppingCart,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_Mail_MessageCircle_Phone_Search_ShoppingCart_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,Mail,MessageCircle,Phone,Search,ShoppingCart,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_Mail_MessageCircle_Phone_Search_ShoppingCart_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,Mail,MessageCircle,Phone,Search,ShoppingCart,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_Mail_MessageCircle_Phone_Search_ShoppingCart_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,Mail,MessageCircle,Phone,Search,ShoppingCart,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_Mail_MessageCircle_Phone_Search_ShoppingCart_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,Mail,MessageCircle,Phone,Search,ShoppingCart,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _contexts_cart_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/cart-context */ \"(app-pages-browser)/./contexts/cart-context.tsx\");\n/* harmony import */ var _contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/wishlist-context */ \"(app-pages-browser)/./contexts/wishlist-context.tsx\");\n/* harmony import */ var _lib_api_helper__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api-helper */ \"(app-pages-browser)/./lib/api-helper.ts\");\n/* harmony import */ var _components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/navigation-menu */ \"(app-pages-browser)/./components/ui/navigation-menu.tsx\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./components/ui/popover.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var _button__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _contexts_settings_context__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/contexts/settings-context */ \"(app-pages-browser)/./contexts/settings-context.tsx\");\n/* harmony import */ var _color_picker__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./color-picker */ \"(app-pages-browser)/./components/ui/color-picker.tsx\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Header() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [showColorPicker, setShowColorPicker] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [selectedCategoryId, setSelectedCategoryId] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [selectedSubcategory, setSelectedSubcategory] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [showMobileMenu, setShowMobileMenu] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [showMobileCategories, setShowMobileCategories] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('');\n    const [cartCount, setCartCount] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(0);\n    const [wishlistCount, setWishlistCount] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(0);\n    const cart = (0,_contexts_cart_context__WEBPACK_IMPORTED_MODULE_4__.useCart)();\n    const wishlist = (0,_contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_5__.useWishlist)();\n    const { theme, language, primaryColor, toggleTheme, setLanguage, setPrimaryColor, t } = (0,_contexts_settings_context__WEBPACK_IMPORTED_MODULE_11__.useSettings)();\n    const handleSearch = ()=>{\n        // Navigate to products page with search parameters\n        const params = new URLSearchParams();\n        if (searchTerm) {\n            params.append('search', searchTerm);\n        }\n        if (selectedCategoryId) {\n            params.append('category', selectedCategoryId.toString());\n        }\n        router.push(\"/products?\".concat(params.toString()));\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"Header.useEffect\": ()=>{\n            const fetchCategories = {\n                \"Header.useEffect.fetchCategories\": async ()=>{\n                    try {\n                        var _categoriesResponse_data, _categoriesResponse_data1;\n                        const param = {\n                            \"PageNumber\": 1,\n                            \"PageSize\": 100,\n                            \"SortColumn\": \"Name\",\n                            \"SortOrder\": \"ASC\"\n                        };\n                        const headers = {\n                            'Content-Type': 'application/json',\n                            'Accept': 'application/json',\n                            'Authorization': 'Bearer ' + localStorage.getItem('token')\n                        };\n                        const categoriesResponse = await (0,_lib_api_helper__WEBPACK_IMPORTED_MODULE_6__.MakeApiCallAsync)(_lib_api_helper__WEBPACK_IMPORTED_MODULE_6__.Config.END_POINT_NAMES.GET_CATEGORIES_LIST, null, param, headers, \"POST\", true);\n                        if (categoriesResponse === null || categoriesResponse === void 0 ? void 0 : (_categoriesResponse_data = categoriesResponse.data) === null || _categoriesResponse_data === void 0 ? void 0 : _categoriesResponse_data.data) {\n                            try {\n                                const parsedData = JSON.parse(categoriesResponse.data.data);\n                                if (Array.isArray(parsedData)) {\n                                    // Create a map of parent categories\n                                    const parentCategories = parsedData.filter({\n                                        \"Header.useEffect.fetchCategories.parentCategories\": (cat)=>!cat.ParentCategoryID\n                                    }[\"Header.useEffect.fetchCategories.parentCategories\"]);\n                                    const childCategories = parsedData.filter({\n                                        \"Header.useEffect.fetchCategories.childCategories\": (cat)=>cat.ParentCategoryID\n                                    }[\"Header.useEffect.fetchCategories.childCategories\"]);\n                                    // Format parent categories with their children\n                                    const formattedCategories = parentCategories.map({\n                                        \"Header.useEffect.fetchCategories.formattedCategories\": (parent)=>({\n                                                id: parent.CategoryID,\n                                                name: parent.Name,\n                                                subcategories: childCategories.filter({\n                                                    \"Header.useEffect.fetchCategories.formattedCategories\": (child)=>child.ParentCategoryID === parent.CategoryID\n                                                }[\"Header.useEffect.fetchCategories.formattedCategories\"]).map({\n                                                    \"Header.useEffect.fetchCategories.formattedCategories\": (child)=>child.Name\n                                                }[\"Header.useEffect.fetchCategories.formattedCategories\"])\n                                            })\n                                    }[\"Header.useEffect.fetchCategories.formattedCategories\"]);\n                                    setCategories(formattedCategories);\n                                } else {\n                                    console.error('Categories data is not an array:', parsedData);\n                                    setCategories([]);\n                                }\n                            } catch (parseError) {\n                                console.error('Error parsing categories data:', parseError);\n                                setCategories([]);\n                            }\n                        } else if (categoriesResponse === null || categoriesResponse === void 0 ? void 0 : (_categoriesResponse_data1 = categoriesResponse.data) === null || _categoriesResponse_data1 === void 0 ? void 0 : _categoriesResponse_data1.errorMessage) {\n                            console.error('API Error:', categoriesResponse.data.errorMessage);\n                            setCategories([]);\n                        } else {\n                            console.error('Invalid or empty response from API');\n                            setCategories([]);\n                        }\n                    } catch (error) {\n                        console.error('Error fetching categories:', error);\n                        setCategories([]);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"Header.useEffect.fetchCategories\"];\n            fetchCategories();\n        }\n    }[\"Header.useEffect\"], []);\n    // Update cart count when cart items change\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"Header.useEffect\": ()=>{\n            if (cart && cart.items) {\n                setCartCount(cart.totalItems);\n            }\n        }\n    }[\"Header.useEffect\"], [\n        cart,\n        cart === null || cart === void 0 ? void 0 : cart.items\n    ]);\n    // Update wishlist count when wishlist items change\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"Header.useEffect\": ()=>{\n            if (wishlist) {\n                setWishlistCount(wishlist.totalItems);\n            }\n        }\n    }[\"Header.useEffect\"], [\n        wishlist,\n        wishlist === null || wishlist === void 0 ? void 0 : wishlist.wishlistItems\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                variant: \"ghost\",\n                size: \"sm\",\n                className: \"fixed right-0 top-1/2 -translate-y-1/2 z-50 bg-gradient-to-r from-background/90 to-background/60 backdrop-blur-sm shadow-lg rounded-l-lg border-l border-y border-border/40 md:flex hidden hover:translate-x-1 transition-all duration-200 hover:shadow-xl group items-center justify-center w-16 h-12\",\n                onClick: ()=>setShowColorPicker(true),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-8 w-8 rounded-full ring-2 ring-border/50 group-hover:ring-primary/50 transition-all duration-200 shadow-inner\",\n                    style: {\n                        backgroundColor: primaryColor\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden md:block text-white py-2.5\",\n                style: {\n                    backgroundColor: primaryColor\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto flex flex-col md:flex-row justify-between items-center text-sm px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex md:flex-row items-start justify-start gap-4 md:gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"tel:009647836071686\",\n                                    className: \"flex items-center gap-2 hover:text-white/80\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_Mail_MessageCircle_Phone_Search_ShoppingCart_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs md:text-sm\",\n                                            children: t('phone')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"mailto:<EMAIL>\",\n                                    className: \"flex items-center gap-2 hover:text-white/80\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_Mail_MessageCircle_Phone_Search_ShoppingCart_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs md:text-sm\",\n                                            children: t('email')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 md:gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    className: \"text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2\",\n                                    onClick: ()=>setLanguage(language === 'en' ? 'ar' : 'en'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_Mail_MessageCircle_Phone_Search_ShoppingCart_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm\",\n                                            children: language === 'en' ? 'العربية' : 'English'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    className: \"text-white hover:text-white/80 flex items-center gap-2\",\n                                    onClick: ()=>window.open(\"https://wa.me/9647836071686?text=\".concat(encodeURIComponent('Hello! I would like to chat with you regarding your services.')), '_blank'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_Mail_MessageCircle_Phone_Search_ShoppingCart_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm\",\n                                            children: t('liveChat')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/login\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_Mail_MessageCircle_Phone_Search_ShoppingCart_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm\",\n                                                children: t('login')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/signup\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_Mail_MessageCircle_Phone_Search_ShoppingCart_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm\",\n                                                children: t('signUp')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                lineNumber: 171,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto py-4 px-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"flex items-center gap-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-[#1B3764] flex items-center gap-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"\".concat(\"https://admin.codemedicalapps.com/\", \"content/commonImages/otherImages/18b_logo2x.png\"),\n                                            alt: \"Logo\",\n                                            className: \"h-12 w-auto\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 border rounded-full px-4 py-3 bg-background shadow-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: t('products') || 'البحث عن المنتجات...',\n                                            className: \"bg-transparent border-none focus:outline-none text-sm flex-1 placeholder:text-gray-400\",\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                            onKeyDown: (e)=>e.key === 'Enter' && handleSearch()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                            variant: \"ghost\",\n                                            className: \"h-8 w-8 p-0 hover:bg-accent/80 transition-colors\",\n                                            style: {\n                                                color: primaryColor\n                                            },\n                                            onClick: handleSearch,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_Mail_MessageCircle_Phone_Search_ShoppingCart_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4 flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/\",\n                                        className: \"flex items-center gap-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-[#1B3764] flex items-center gap-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"\".concat(\"https://admin.codemedicalapps.com/\", \"content/commonImages/otherImages/18b_logo2x.png\"),\n                                                alt: \"Logo\",\n                                                className: \"h-16 w-auto\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 border rounded-full px-3 py-1.5 flex-1 max-w-md ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_8__.Popover, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_8__.PopoverTrigger, {\n                                                        asChild: true,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                                            variant: \"ghost\",\n                                                            className: \"h-8 flex items-center gap-1 px-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-muted-foreground text-sm\",\n                                                                    children: selectedCategory || selectedSubcategory || t('category')\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                    lineNumber: 264,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_Mail_MessageCircle_Phone_Search_ShoppingCart_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                    lineNumber: 265,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                            lineNumber: 263,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_8__.PopoverContent, {\n                                                        className: \"w-64 p-0\",\n                                                        align: \"start\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"max-h-[300px] overflow-auto\",\n                                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 text-center text-muted-foreground\",\n                                                                children: t('loadingCategories')\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                lineNumber: 271,\n                                                                columnNumber: 23\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid\",\n                                                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"group\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"w-full px-4 py-2 text-left hover:bg-accent\",\n                                                                                onClick: ()=>{\n                                                                                    setSelectedCategory(category.name);\n                                                                                    setSelectedCategoryId(category.id);\n                                                                                    setSelectedSubcategory(null);\n                                                                                },\n                                                                                children: category.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                                lineNumber: 276,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"hidden group-hover:block absolute left-full top-0 w-48 bg-white shadow-lg rounded-md border\",\n                                                                                children: category.subcategories.map((sub, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        className: \"w-full px-4 py-2 text-left hover:bg-accent\",\n                                                                                        onClick: ()=>{\n                                                                                            setSelectedSubcategory(sub);\n                                                                                            setSelectedCategory(null);\n                                                                                            // Keep the parent category ID for search purposes\n                                                                                            setSelectedCategoryId(category.id);\n                                                                                        },\n                                                                                        children: sub\n                                                                                    }, index, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                                        lineNumber: 285,\n                                                                                        columnNumber: 33\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                                lineNumber: 283,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, category.id, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                        lineNumber: 275,\n                                                                        columnNumber: 27\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                lineNumber: 273,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-5 w-px bg-border mx-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: t('products'),\n                                                className: \"bg-transparent border-none focus:outline-none text-sm flex-1\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                onKeyDown: (e)=>e.key === 'Enter' && handleSearch()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                                variant: \"ghost\",\n                                                className: \"h-8 w-8 p-0\",\n                                                onClick: handleSearch,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_Mail_MessageCircle_Phone_Search_ShoppingCart_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:block\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__.NavigationMenu, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__.NavigationMenuList, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__.NavigationMenuItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/\",\n                                                    legacyBehavior: true,\n                                                    passHref: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__.NavigationMenuLink, {\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.cn)(\"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50\"),\n                                                        children: t('home')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__.NavigationMenuItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/hot-deals\",\n                                                    legacyBehavior: true,\n                                                    passHref: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__.NavigationMenuLink, {\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.cn)(\"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50\"),\n                                                        children: t('hotDeals')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__.NavigationMenuItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/products\",\n                                                    legacyBehavior: true,\n                                                    passHref: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__.NavigationMenuLink, {\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.cn)(\"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50\"),\n                                                        children: t('products') || 'Products'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__.NavigationMenuItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/payment-methods\",\n                                                    legacyBehavior: true,\n                                                    passHref: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__.NavigationMenuLink, {\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.cn)(\"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50\"),\n                                                        children: t('paymentMethods')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__.NavigationMenuItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/follow-us\",\n                                                    legacyBehavior: true,\n                                                    passHref: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__.NavigationMenuLink, {\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.cn)(\"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50\"),\n                                                        children: t('followUs')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__.NavigationMenuItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/about\",\n                                                    legacyBehavior: true,\n                                                    passHref: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__.NavigationMenuLink, {\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.cn)(\"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50\"),\n                                                        children: t('aboutUs')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__.NavigationMenuItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/contact\",\n                                                    legacyBehavior: true,\n                                                    passHref: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__.NavigationMenuLink, {\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.cn)(\"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50\"),\n                                                        children: t('contactUs')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/wishlist\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"icon\",\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_Mail_MessageCircle_Phone_Search_ShoppingCart_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-5 w-5\",\n                                                    style: {\n                                                        color: primaryColor\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 396,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute -top-1 -right-1 bg-primary text-white text-xs rounded-full h-4 w-4 flex items-center justify-center\",\n                                                    children: wishlistCount\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/cart\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"icon\",\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_Mail_MessageCircle_Phone_Search_ShoppingCart_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"h-5 w-5\",\n                                                    style: {\n                                                        color: primaryColor\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute -top-1 -right-1 bg-primary text-white text-xs rounded-full h-4 w-4 flex items-center justify-center\",\n                                                    children: cartCount\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 402,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                lineNumber: 393,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                lineNumber: 214,\n                columnNumber: 7\n            }, this),\n            showColorPicker && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_color_picker__WEBPACK_IMPORTED_MODULE_12__.ColorPicker, {\n                onColorSelect: (color)=>{\n                    setPrimaryColor(color);\n                    setShowColorPicker(false);\n                },\n                onClose: ()=>setShowColorPicker(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                lineNumber: 414,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n        lineNumber: 160,\n        columnNumber: 5\n    }, this);\n}\n_s(Header, \"uLv9Yi/mrNDTE+zWECTgE8zMIDY=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        _contexts_cart_context__WEBPACK_IMPORTED_MODULE_4__.useCart,\n        _contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_5__.useWishlist,\n        _contexts_settings_context__WEBPACK_IMPORTED_MODULE_11__.useSettings\n    ];\n});\n_c = Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ui/header.tsx\n"));

/***/ })

});