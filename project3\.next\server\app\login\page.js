(()=>{var e={};e.id=520,e.ids=[520],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},21956:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},25409:(e,t,s)=>{"use strict";s.d(t,{p:()=>o});var r=s(45512),a=s(58009),i=s(59462);let o=a.forwardRef(({className:e,type:t,...s},a)=>(0,r.jsx)("input",{type:t,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...s}));o.displayName="Input"},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34544:(e,t,s)=>{Promise.resolve().then(s.bind(s,90308))},43642:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\login\\page.tsx","default")},47699:(e,t,s)=>{"use strict";s.d(t,{J:()=>l});var r=s(45512),a=s(58009),i=s(92405),o=s(21643),n=s(59462);let d=(0,o.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),l=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(i.b,{ref:s,className:(0,n.cn)(d(),e),...t}));l.displayName=i.b.displayName},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69208:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},70801:(e,t,s)=>{"use strict";s.d(t,{dj:()=>u});var r=s(58009);let a=0,i=new Map,o=e=>{if(i.has(e))return;let t=setTimeout(()=>{i.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);i.set(e,t)},n=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:s}=t;return s?o(s):e.toasts.forEach(e=>{o(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},d=[],l={toasts:[]};function c(e){l=n(l,e),d.forEach(e=>{e(l)})}function p({...e}){let t=(a=(a+1)%Number.MAX_SAFE_INTEGER).toString(),s=()=>c({type:"DISMISS_TOAST",toastId:t});return c({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||s()}}}),{id:t,dismiss:s,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function u(){let[e,t]=r.useState(l);return r.useEffect(()=>(d.push(t),()=>{let e=d.indexOf(t);e>-1&&d.splice(e,1)}),[e]),{...e,toast:p,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},72734:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},90308:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>N});var r=s(45512),a=s(58009),i=s(97643),o=s(87021),n=s(25409),d=s(47699),l=s(24540),c=s(8866),p=s(72734),u=s(92557),m=s(28531),x=s.n(m),f=s(79334),h=s(71901),v=s(33853),g=s(59462),y=s(69208),b=s(21956);let w=a.forwardRef(({className:e,...t},s)=>{let[i,d]=a.useState(!1);return(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(n.p,{type:i?"text":"password",className:(0,g.cn)("pr-10",e),ref:s,...t}),(0,r.jsx)(o.$,{type:"button",variant:"ghost",size:"icon",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>d(!i),"aria-label":i?"Hide password":"Show password",children:i?(0,r.jsx)(y.A,{className:"h-4 w-4","aria-hidden":"true"}):(0,r.jsx)(b.A,{className:"h-4 w-4","aria-hidden":"true"})})]})});w.displayName="PasswordInput";var j=s(70801);function N(){let[e,t]=(0,a.useState)(""),[s,m]=(0,a.useState)({email:"",password:""}),{t:y}=(0,h.t)(),{login:b,isLoading:N,isLoggedIn:A}=(0,v.J)(),{toast:S}=(0,j.dj)(),T=(0,f.useRouter)(),[E,_]=(0,a.useState)({email:"",password:""}),k=()=>{let e={email:"",password:""},t=!0;return s.email.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(s.email)||(e.email="Please enter a valid email address",t=!1):(e.email="Email is required",t=!1),s.password.trim()||(e.password="Password is required",t=!1),_(e),t},q=async e=>{if(e.preventDefault(),k()){t("");try{let e=await b(s.email,s.password);e.success?(S({title:"Login Successful",description:e.message,variant:"default"}),T.push("/")):(t(e.message),S({title:"Login Failed",description:e.message,variant:"destructive"}))}catch(s){let e=s.message||"Failed to login";t(e),S({title:"Login Error",description:e,variant:"destructive"}),console.error("Error:",s)}}};return(0,r.jsx)("div",{className:"min-h-screen bg-background flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h2",{className:"mt-6 text-3xl font-extrabold",children:"Welcome Back"}),(0,r.jsx)("p",{className:"mt-2 text-sm text-muted-foreground",children:"Sign in to your account"})]}),(0,r.jsx)(i.Zp,{className:"mt-8 p-8 shadow-xl bg-card",children:(0,r.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:(0,r.jsxs)("form",{onSubmit:q,className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(d.J,{className:"block text-sm font-medium mb-2",children:"Email"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(n.p,{type:"email",value:s.email,onChange:e=>{m({...s,email:e.target.value}),E.email&&k()},className:(0,g.cn)("pl-10",E.email&&"border-red-500"),required:!0,disabled:N}),(0,r.jsx)(c.A,{className:"w-4 h-4 absolute left-3 top-3 text-muted-foreground"})]}),E.email&&(0,r.jsx)("p",{className:"mt-1 text-sm text-destructive",children:E.email})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(d.J,{className:"block text-sm font-medium mb-2",children:"Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(w,{value:s.password,onChange:e=>{m({...s,password:e.target.value}),E.password&&k()},className:(0,g.cn)("pl-10",E.password&&"border-red-500"),required:!0,disabled:N}),(0,r.jsx)(p.A,{className:"w-4 h-4 absolute left-3 top-3 text-muted-foreground"})]}),E.password&&(0,r.jsx)("p",{className:"mt-1 text-sm text-destructive",children:E.password})]}),e&&(0,r.jsx)("div",{className:"text-red-500 text-sm text-center",children:e}),(0,r.jsx)(o.$,{type:"submit",className:"w-full h-12 text-lg bg-primary text-primary-foreground hover:bg-primary/90 transition-colors",disabled:N,children:N?(0,r.jsx)(u.A,{className:"w-4 h-4 animate-spin"}):"Sign In"}),(0,r.jsxs)("div",{className:"text-center text-sm space-y-2",children:[(0,r.jsx)("div",{children:(0,r.jsx)(x(),{href:"/forgot-password",className:"text-primary hover:text-primary/80 hover:underline transition-colors",children:"Forgot your password?"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Don't have an account? "}),(0,r.jsx)(x(),{href:"/signup",className:"text-primary hover:text-primary/80 hover:underline transition-colors",children:"Sign up"})]})]})]})})})]})})}},92405:(e,t,s)=>{"use strict";s.d(t,{b:()=>n});var r=s(58009),a=s(30830),i=s(45512),o=r.forwardRef((e,t)=>(0,i.jsx)(a.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var n=o},94735:e=>{"use strict";e.exports=require("events")},97643:(e,t,s)=>{"use strict";s.d(t,{Wu:()=>n,Zp:()=>o,wL:()=>d});var r=s(45512),a=s(58009),i=s(59462);let o=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));o.displayName="Card",a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...t})).displayName="CardHeader",a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("h3",{ref:s,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t})).displayName="CardTitle",a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("p",{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",e),...t})).displayName="CardDescription";let n=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)("p-6 pt-0",e),...t}));n.displayName="CardContent";let d=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)("flex items-center p-6 pt-0",e),...t}));d.displayName="CardFooter"},99696:(e,t,s)=>{Promise.resolve().then(s.bind(s,43642))},99928:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>l});var r=s(70260),a=s(28203),i=s(25155),o=s.n(i),n=s(67292),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);s.d(t,d);let l={children:["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,43642)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\login\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,19611)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\login\\page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,551,400,875],()=>s(99928));module.exports=r})();