'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';

type User = {
  id: string | number;
  name: string;
  email: string;
  avatar?: string;
  // Add other user properties as needed
};

type AuthCredentials = {
  email: string;
  password: string;
};

type UserContextType = {
  user: User | null;
  isLoggedIn: boolean;
  isLoading: boolean;
  login: (credentials: AuthCredentials) => Promise<boolean>;
  register: (userData: any) => Promise<boolean>;
  logout: () => Promise<void>;
  updateUser: (userData: Partial<User>) => void;
};

const defaultUserContext: UserContextType = {
  user: null,
  isLoggedIn: false,
  isLoading: false,
  login: async () => false,
  register: async () => false,
  logout: async () => {},
  updateUser: () => {},
};

const UserContext = createContext<UserContextType>(defaultUserContext);

export const useUser = () => useContext(UserContext);

type UserProviderProps = {
  children: ReactNode;
};

export function UserProvider({ children }: UserProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  // Check for existing session on mount
  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Replace with your actual authentication check
        const token = localStorage.getItem('authToken');
        if (token) {
          // In a real app, you would validate the token with your backend
          // const userData = await fetchUserData(token);
          // setUser(userData);
        }
      } catch (error) {
        console.error('Auth check failed:', error);
        localStorage.removeItem('authToken');
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, []);

  const login = async (credentials: AuthCredentials): Promise<boolean> => {
    setIsLoading(true);
    try {
      // Replace with your actual login API call
      // const response = await fetch('/api/auth/login', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(credentials),
      // });
      // 
      // if (!response.ok) {
      //   throw new Error('Login failed');
      // }
      // 
      // const { user: userData, token } = await response.json();
      // localStorage.setItem('authToken', token);
      // setUser(userData);
      
      // Mock successful login for now
      const mockUser = {
        id: '1',
        name: 'Test User',
        email: credentials.email,
      };
      
      setUser(mockUser);
      toast.success('Login successful');
      return true;
    } catch (error) {
      console.error('Login error:', error);
      toast.error('Login failed. Please check your credentials.');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (userData: any): Promise<boolean> => {
    setIsLoading(true);
    try {
      // Replace with your actual registration API call
      // const response = await fetch('/api/auth/register', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(userData),
      // });
      // 
      // if (!response.ok) {
      //   throw new Error('Registration failed');
      // }
      // 
      // const { user: userData, token } = await response.json();
      // localStorage.setItem('authToken', token);
      // setUser(userData);
      
      // Mock successful registration for now
      const mockUser = {
        id: '1',
        name: userData.name,
        email: userData.email,
      };
      
      setUser(mockUser);
      toast.success('Registration successful');
      return true;
    } catch (error) {
      console.error('Registration error:', error);
      toast.error('Registration failed. Please try again.');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    try {
      // Replace with your actual logout API call if needed
      // await fetch('/api/auth/logout', { method: 'POST' });
      
      // Clear auth data
      localStorage.removeItem('authToken');
      setUser(null);
      
      // Redirect to home page
      router.push('/');
      toast.success('Logged out successfully');
    } catch (error) {
      console.error('Logout error:', error);
      toast.error('Failed to log out. Please try again.');
      throw error;
    }
  };

  const updateUser = (userData: Partial<User>) => {
    if (user) {
      setUser({ ...user, ...userData });
    }
  };

  return (
    <UserContext.Provider
      value={{
        user,
        isLoggedIn: !!user,
        isLoading,
        login,
        register,
        logout,
        updateUser,
      }}
    >
      {children}
    </UserContext.Provider>
  );
}

export default UserContext;
