"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7663],{1027:(e,t,n)=>{n.d(t,{F:()=>o});var r=n(3463);let l=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,u=r.$,o=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return u(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:o,defaultVariants:i}=t,a=Object.keys(o).map(e=>{let t=null==n?void 0:n[e],r=null==i?void 0:i[e];if(null===t)return null;let u=l(t)||l(r);return o[e][u]}),s=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return u(e,a,null==t?void 0:null===(r=t.compoundVariants)||void 0===r?void 0:r.reduce((e,t)=>{let{class:n,className:r,...l}=t;return Object.entries(l).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...i,...s}[t]):({...i,...s})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},1488:(e,t,n)=>{n.d(t,{i:()=>u});var r=n(2115),l=n(1524);function u({prop:e,defaultProp:t,onChange:n=()=>{}}){let[u,o]=function({defaultProp:e,onChange:t}){let n=r.useState(e),[u]=n,o=r.useRef(u),i=(0,l.c)(t);return r.useEffect(()=>{o.current!==u&&(i(u),o.current=u)},[u,o,i]),n}({defaultProp:t,onChange:n}),i=void 0!==e,a=i?e:u,s=(0,l.c)(n);return[a,r.useCallback(t=>{if(i){let n="function"==typeof t?t(e):t;n!==e&&s(n)}else o(t)},[i,e,o,s])]}},1524:(e,t,n)=>{n.d(t,{c:()=>l});var r=n(2115);function l(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},3360:(e,t,n)=>{n.d(t,{hO:()=>a,sG:()=>i});var r=n(2115),l=n(7650),u=n(2317),o=n(5155),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let n=r.forwardRef((e,n)=>{let{asChild:r,...l}=e,i=r?u.DX:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(i,{...l,ref:n})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function a(e,t){e&&l.flushSync(()=>e.dispatchEvent(t))}},3610:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},4256:(e,t,n)=>{n.d(t,{jH:()=>u});var r=n(2115);n(5155);var l=r.createContext(void 0);function u(e){let t=r.useContext(l);return e||t||"ltr"}},6611:(e,t,n)=>{n.d(t,{N:()=>l});var r=n(2115),l=globalThis?.document?r.useLayoutEffect:()=>{}},7028:(e,t,n)=>{n.d(t,{C:()=>o});var r=n(2115),l=n(8068),u=n(6611),o=e=>{let{present:t,children:n}=e,o=function(e){var t,n;let[l,o]=r.useState(),a=r.useRef({}),s=r.useRef(e),c=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=i(a.current);c.current="mounted"===d?e:"none"},[d]),(0,u.N)(()=>{let t=a.current,n=s.current;if(n!==e){let r=c.current,l=i(t);e?f("MOUNT"):"none"===l||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==l?f("ANIMATION_OUT"):f("UNMOUNT"),s.current=e}},[e,f]),(0,u.N)(()=>{if(l){var e;let t;let n=null!==(e=l.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=i(a.current).includes(e.animationName);if(e.target===l&&r&&(f("ANIMATION_END"),!s.current)){let e=l.style.animationFillMode;l.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===l.style.animationFillMode&&(l.style.animationFillMode=e)})}},u=e=>{e.target===l&&(c.current=i(a.current))};return l.addEventListener("animationstart",u),l.addEventListener("animationcancel",r),l.addEventListener("animationend",r),()=>{n.clearTimeout(t),l.removeEventListener("animationstart",u),l.removeEventListener("animationcancel",r),l.removeEventListener("animationend",r)}}f("ANIMATION_END")},[l,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{e&&(a.current=getComputedStyle(e)),o(e)},[])}}(t),a="function"==typeof n?n({present:o.isPresent}):r.Children.only(n),s=(0,l.s)(o.ref,function(e){var t,n;let r=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,l=r&&"isReactWarning"in r&&r.isReactWarning;return l?e.ref:(l=(r=null===(n=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(a));return"function"==typeof n||o.isPresent?r.cloneElement(a,{ref:s}):null};function i(e){return(null==e?void 0:e.animationName)||"none"}o.displayName="Presence"},7401:(e,t,n)=>{n.d(t,{A:()=>a});var r=n(2115);let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),u=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&n.indexOf(e)===t).join(" ")};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=(0,r.forwardRef)((e,t)=>{let{color:n="currentColor",size:l=24,strokeWidth:i=2,absoluteStrokeWidth:a,className:s="",children:c,iconNode:d,...f}=e;return(0,r.createElement)("svg",{ref:t,...o,width:l,height:l,stroke:n,strokeWidth:a?24*Number(i)/Number(l):i,className:u("lucide",s),...f},[...d.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(c)?c:[c]])}),a=(e,t)=>{let n=(0,r.forwardRef)((n,o)=>{let{className:a,...s}=n;return(0,r.createElement)(i,{ref:o,iconNode:t,className:u("lucide-".concat(l(e)),a),...s})});return n.displayName="".concat(e),n}},7668:(e,t,n)=>{n.d(t,{B:()=>a});var r,l=n(2115),u=n(6611),o=(r||(r=n.t(l,2)))["useId".toString()]||(()=>void 0),i=0;function a(e){let[t,n]=l.useState(o());return(0,u.N)(()=>{e||n(e=>e??String(i++))},[e]),e||(t?`radix-${t}`:"")}},8166:(e,t,n)=>{n.d(t,{A:()=>u});var r=n(2115),l=n(5155);function u(e,t=[]){let n=[],o=()=>{let t=n.map(e=>r.createContext(e));return function(n){let l=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:l}}),[n,l])}};return o.scopeName=e,[function(t,u){let o=r.createContext(u),i=n.length;n=[...n,u];let a=t=>{let{scope:n,children:u,...a}=t,s=n?.[e]?.[i]||o,c=r.useMemo(()=>a,Object.values(a));return(0,l.jsx)(s.Provider,{value:c,children:u})};return a.displayName=t+"Provider",[a,function(n,l){let a=l?.[e]?.[i]||o,s=r.useContext(a);if(s)return s;if(void 0!==u)return u;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let l=n.reduce((t,{useScope:n,scopeName:r})=>{let l=n(e)[`__scope${r}`];return{...t,...l}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:l}),[l])}};return n.scopeName=t.scopeName,n}(o,...t)]}},9741:(e,t,n)=>{n.d(t,{N:()=>a});var r=n(2115),l=n(8166),u=n(8068),o=n(2317),i=n(5155);function a(e){let t=e+"CollectionProvider",[n,a]=(0,l.A)(t),[s,c]=n(t,{collectionRef:{current:null},itemMap:new Map}),d=e=>{let{scope:t,children:n}=e,l=r.useRef(null),u=r.useRef(new Map).current;return(0,i.jsx)(s,{scope:t,itemMap:u,collectionRef:l,children:n})};d.displayName=t;let f=e+"CollectionSlot",m=r.forwardRef((e,t)=>{let{scope:n,children:r}=e,l=c(f,n),a=(0,u.s)(t,l.collectionRef);return(0,i.jsx)(o.DX,{ref:a,children:r})});m.displayName=f;let v=e+"CollectionItemSlot",p="data-radix-collection-item",N=r.forwardRef((e,t)=>{let{scope:n,children:l,...a}=e,s=r.useRef(null),d=(0,u.s)(t,s),f=c(v,n);return r.useEffect(()=>(f.itemMap.set(s,{ref:s,...a}),()=>void f.itemMap.delete(s))),(0,i.jsx)(o.DX,{[p]:"",ref:d,children:l})});return N.displayName=v,[{Provider:d,Slot:m,ItemSlot:N},function(t){let n=c(e+"CollectionConsumer",t);return r.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(p,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},a]}}}]);