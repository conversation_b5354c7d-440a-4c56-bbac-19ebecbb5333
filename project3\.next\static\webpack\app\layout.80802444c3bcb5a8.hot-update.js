"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./contexts/cart-context.tsx":
/*!***********************************!*\
  !*** ./contexts/cart-context.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CartProvider: () => (/* binding */ CartProvider),\n/* harmony export */   useCart: () => (/* binding */ useCart)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ CartProvider,useCart auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst CartContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction CartProvider(param) {\n    let { children } = param;\n    _s();\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isHydrated, setIsHydrated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Load cart from localStorage on initial render\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CartProvider.useEffect\": ()=>{\n            const savedCart = localStorage.getItem('cart');\n            if (savedCart) {\n                try {\n                    setItems(JSON.parse(savedCart));\n                } catch (error) {\n                    console.error('Failed to parse cart from localStorage:', error);\n                }\n            }\n            setIsHydrated(true);\n        }\n    }[\"CartProvider.useEffect\"], []);\n    // Save cart to localStorage whenever it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CartProvider.useEffect\": ()=>{\n            localStorage.setItem('cart', JSON.stringify(items));\n        }\n    }[\"CartProvider.useEffect\"], [\n        items\n    ]);\n    const addToCart = function(item, quantity) {\n        let attributes = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : [], iqdPrice = arguments.length > 3 ? arguments[3] : void 0, currencyRate = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : 1500;\n        setItems((prevItems)=>{\n            // Start with the base price (which could be a discounted price if applicable)\n            let adjustedPrice = item.price;\n            // Apply attribute-based price adjustments\n            attributes.forEach((attr)=>{\n                if (attr.PriceAdjustment && attr.PriceAdjustmentType) {\n                    const basePriceForAdjustment = item.originalPrice || item.price;\n                    switch(attr.PriceAdjustmentType){\n                        case 1:\n                            adjustedPrice += attr.PriceAdjustment;\n                            break;\n                        case 2:\n                            adjustedPrice += basePriceForAdjustment * attr.PriceAdjustment / 100;\n                            break;\n                    }\n                }\n            });\n            // Find if item with same ID and attributes already exists\n            const existingItemIndex = prevItems.findIndex((i)=>{\n                var _i_attributes;\n                return i.id === item.id && JSON.stringify((_i_attributes = i.attributes) === null || _i_attributes === void 0 ? void 0 : _i_attributes.sort((a, b)=>a.ProductAttributeID - b.ProductAttributeID)) === JSON.stringify(attributes === null || attributes === void 0 ? void 0 : attributes.sort((a, b)=>a.ProductAttributeID - b.ProductAttributeID));\n            });\n            if (existingItemIndex >= 0) {\n                // Item with same attributes exists, update quantity\n                const updatedItems = [\n                    ...prevItems\n                ];\n                updatedItems[existingItemIndex].quantity += quantity;\n                return updatedItems;\n            } else {\n                // Add new item with all price information\n                return [\n                    ...prevItems,\n                    {\n                        ...item,\n                        iqdPrice: iqdPrice,\n                        quantity,\n                        attributes,\n                        adjustedPrice: Math.max(0, adjustedPrice),\n                        originalPrice: item.originalPrice\n                    }\n                ];\n            }\n        });\n    };\n    const removeFromCart = (id)=>{\n        setItems((prevItems)=>prevItems.filter((item)=>item.id !== id));\n    };\n    const updateQuantity = (id, quantity)=>{\n        if (quantity <= 0) {\n            removeFromCart(id);\n            return;\n        }\n        setItems((prevItems)=>prevItems.map((item)=>item.id === id ? {\n                    ...item,\n                    quantity\n                } : item));\n    };\n    const clearCart = ()=>{\n        setItems([]);\n    };\n    const totalItems = items.reduce((total, item)=>total + item.quantity, 0);\n    // Update localStorage and trigger re-render when cart changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CartProvider.useEffect\": ()=>{\n            localStorage.setItem('cart', JSON.stringify(items));\n        }\n    }[\"CartProvider.useEffect\"], [\n        items\n    ]);\n    const subtotal = items.reduce((total, item)=>{\n        const price = item.discountPrice ? Math.min(item.discountPrice, item.adjustedPrice) : item.adjustedPrice;\n        return total + price * item.quantity;\n    }, 0);\n    // For now, total is same as subtotal, but could include shipping, tax, etc.\n    const total = subtotal;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CartContext.Provider, {\n        value: {\n            items,\n            addToCart,\n            removeFromCart,\n            updateQuantity,\n            clearCart,\n            totalItems,\n            subtotal,\n            total,\n            isHydrated\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\contexts\\\\cart-context.tsx\",\n        lineNumber: 162,\n        columnNumber: 5\n    }, this);\n}\n_s(CartProvider, \"orx7hoWf+wJ/pl3ceK141eCKGB8=\");\n_c = CartProvider;\nfunction useCart() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(CartContext);\n    if (context === undefined) {\n        throw new Error('useCart must be used within a CartProvider');\n    }\n    return context;\n}\n_s1(useCart, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"CartProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./contexts/cart-context.tsx\n"));

/***/ })

});