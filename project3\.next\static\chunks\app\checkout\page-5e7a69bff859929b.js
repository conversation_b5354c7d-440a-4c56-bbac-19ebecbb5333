(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8279],{1027:(e,r,t)=>{"use strict";t.d(r,{F:()=>l});var s=t(3463);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=s.$,l=(e,r)=>t=>{var s;if((null==r?void 0:r.variants)==null)return i(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:l,defaultVariants:n}=r,o=Object.keys(l).map(e=>{let r=null==t?void 0:t[e],s=null==n?void 0:n[e];if(null===r)return null;let i=a(r)||a(s);return l[e][i]}),d=t&&Object.entries(t).reduce((e,r)=>{let[t,s]=r;return void 0===s||(e[t]=s),e},{});return i(e,o,null==r?void 0:null===(s=r.compoundVariants)||void 0===s?void 0:s.reduce((e,r)=>{let{class:t,className:s,...a}=r;return Object.entries(a).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...n,...d}[r]):({...n,...d})[r]===t})?[...e,t,s]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}},1594:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(7401).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},2336:(e,r,t)=>{"use strict";t.d(r,{p:()=>l});var s=t(5155),a=t(2115),i=t(9602);let l=a.forwardRef((e,r)=>{let{className:t,type:a,...l}=e;return(0,s.jsx)("input",{type:a,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:r,...l})});l.displayName="Input"},3360:(e,r,t)=>{"use strict";t.d(r,{hO:()=>o,sG:()=>n});var s=t(2115),a=t(7650),i=t(2317),l=t(5155),n=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,r)=>{let t=s.forwardRef((e,t)=>{let{asChild:s,...a}=e,n=s?i.DX:r;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(n,{...a,ref:t})});return t.displayName=`Primitive.${r}`,{...e,[r]:t}},{});function o(e,r){e&&a.flushSync(()=>e.dispatchEvent(r))}},4085:(e,r,t)=>{"use strict";t.d(r,{$:()=>d,r:()=>o});var s=t(5155),a=t(2115),i=t(2317),l=t(1027),n=t(9602);let o=(0,l.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-red-500 text-destructive-foreground hover:bg-red-600",outline:"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,r)=>{let{className:t,variant:a,size:l,asChild:d=!1,...c}=e,u=d?i.DX:"button";return(0,s.jsx)(u,{className:(0,n.cn)(o({variant:a,size:l,className:t})),ref:r,...c})});d.displayName="Button"},4807:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(7401).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},4858:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(7401).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},5007:(e,r,t)=>{"use strict";t.d(r,{Wu:()=>n,Zp:()=>l,wL:()=>o});var s=t(5155),a=t(2115),i=t(9602);let l=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...a})});l.displayName="Card",a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",t),...a})}).displayName="CardHeader",a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("h3",{ref:r,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",t),...a})}).displayName="CardTitle",a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("p",{ref:r,className:(0,i.cn)("text-sm text-muted-foreground",t),...a})}).displayName="CardDescription";let n=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,i.cn)("p-6 pt-0",t),...a})});n.displayName="CardContent";let o=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,i.cn)("flex items-center p-6 pt-0",t),...a})});o.displayName="CardFooter"},5785:(e,r,t)=>{"use strict";t.d(r,{J:()=>d});var s=t(5155),a=t(2115),i=t(6195),l=t(1027),n=t(9602);let o=(0,l.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)(i.b,{ref:r,className:(0,n.cn)(o(),t),...a})});d.displayName=i.b.displayName},6046:(e,r,t)=>{"use strict";var s=t(6658);t.o(s,"useParams")&&t.d(r,{useParams:function(){return s.useParams}}),t.o(s,"usePathname")&&t.d(r,{usePathname:function(){return s.usePathname}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(r,{useSearchParams:function(){return s.useSearchParams}})},6195:(e,r,t)=>{"use strict";t.d(r,{b:()=>n});var s=t(2115),a=t(3360),i=t(5155),l=s.forwardRef((e,r)=>(0,i.jsx)(a.sG.label,{...e,ref:r,onMouseDown:r=>{var t;r.target.closest("button, input, select, textarea")||(null===(t=e.onMouseDown)||void 0===t||t.call(e,r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));l.displayName="Label";var n=l},6233:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(7401).A)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},6967:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(7401).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},7110:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n,t:()=>o});var s=t(5155),a=t(2115);let i={en:{menu:"Menu",newsletter:"Newsletter",enterEmail:"Enter your email",newsletterDisclaimer:"Subscribe to our newsletter to receive updates and exclusive offers",popularCategories:"Popular Categories!",settings:"Settings",language:"Language",theme:"Theme",color:"Color",save:"Save",cancel:"Cancel",close:"Close",phone:"***************",phonenumber:"Phone Number",email:"<EMAIL>",liveChat:"Live Chat",welcome:"Welcome",logout:"Logout",login:"Login",signup:"Sign Up",signUp:"Sign Up",category:"Category",categories:"Categories",filters:"Filters",clearAll:"Clear All",products:"Products",loadingCategories:"Loading categories...",home:"Home",todayDeals:"Today's Deals",followUs:"Follow Us",aboutUs:"About Us",contactUs:"Contact Us",tryAgain:"Try Again",noProductsFound:"No products found",allProducts:"All Products",quickLinks:"Quick Links",about:"About Us",contact:"Contact Us",hotDeals:"Hot Deals",customerArea:"Customer Area",myAccount:"My Account",orders:"Orders",cart:"Cart",wishlist:"Wishlist",paymentMethods:"Payment Methods",location:"Location",callUs:"Call Us",emailUs:"Email Us",subscribe:"Subscribe",name:"Name",subject:"Subject",message:"Message",sendMessage:"Send Message",sending:"Sending...",messageSent:"Message sent successfully!",messageError:"Failed to send message",contactInfo:"Contact Information",address:"Address",findUs:"Find Us"},ar:{menu:"القائمة",popularCategories:"الفئات الشائعة!",settings:"الإعدادات",language:"اللغة",theme:"المظهر",color:"اللون",save:"حفظ",cancel:"إلغاء",close:"إغلاق",phone:"***************",phonenumber:"رقم الهاتف",email:"<EMAIL>",liveChat:"محادثة مباشرة",welcome:"مرحباً",logout:"تسجيل الخروج",login:"تسجيل الدخول",signup:"تسجيل جديد",signUp:"تسجيل جديد",category:"الفئة",categories:"الفئات",products:"المنتجات",loadingCategories:"جاري تحميل الفئات...",filters:"المرشحات",clearAll:"مسح الكل",home:"الرئيسية",todayDeals:"عروض اليوم",followUs:"تابعنا",aboutUs:"من نحن",contactUs:"اتصل بنا",tryAgain:"حاول مرة أخرى",noProductsFound:"لم يتم العثور على منتجات",allProducts:"جميع المنتجات",quickLinks:"روابط سريعة",about:"من نحن",contact:"اتصل بنا",hotDeals:"عروض ساخنة",customerArea:"منطقة العملاء",myAccount:"حسابي",orders:"الطلبات",cart:"السلة",wishlist:"المفضلة",paymentMethods:"طرق الدفع",location:"الموقع",callUs:"اتصل بنا",emailUs:"راسلنا",subscribe:"اشترك",name:"الاسم",subject:"الموضوع",message:"الرسالة",sendMessage:"إرسال الرسالة",sending:"جاري الإرسال...",messageSent:"تم إرسال الرسالة بنجاح!",messageError:"فشل في إرسال الرسالة",contactInfo:"معلومات الاتصال",address:"العنوان",findUs:"موقعنا"}},l=(0,a.createContext)(void 0);function n(e){let{children:r}=e,[t,n]=(0,a.useState)("light"),[o,d]=(0,a.useState)("en"),[c,u]=(0,a.useState)("#0074b2");return(0,a.useEffect)(()=>{document.documentElement.style.setProperty("--primary",c),document.documentElement.style.setProperty("--primary-foreground","#ffffff")},[]),(0,s.jsx)(l.Provider,{value:{theme:t,language:o,primaryColor:c,toggleTheme:()=>{n("light"===t?"dark":"light")},setLanguage:e=>{d(e),document.documentElement.dir="ar"===e?"rtl":"ltr"},setPrimaryColor:e=>{u(e)},t:e=>(function(e,r){let t=i[r];return e in t?t[e]:"en"!==r&&e in i.en?i.en[e]:e})(e,o)},children:r})}function o(){let e=(0,a.useContext)(l);if(void 0===e)throw Error("useSettings must be used within a SettingsProvider");return e}},7401:(e,r,t)=>{"use strict";t.d(r,{A:()=>o});var s=t(2115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&t.indexOf(e)===r).join(" ")};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=(0,s.forwardRef)((e,r)=>{let{color:t="currentColor",size:a=24,strokeWidth:n=2,absoluteStrokeWidth:o,className:d="",children:c,iconNode:u,...m}=e;return(0,s.createElement)("svg",{ref:r,...l,width:a,height:a,stroke:t,strokeWidth:o?24*Number(n)/Number(a):n,className:i("lucide",d),...m},[...u.map(e=>{let[r,t]=e;return(0,s.createElement)(r,t)}),...Array.isArray(c)?c:[c]])}),o=(e,r)=>{let t=(0,s.forwardRef)((t,l)=>{let{className:o,...d}=t;return(0,s.createElement)(n,{ref:l,iconNode:r,className:i("lucide-".concat(a(e)),o),...d})});return t.displayName="".concat(e),t}},7597:(e,r,t)=>{Promise.resolve().then(t.bind(t,8013))},8013:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>v});var s=t(5155),a=t(2115),i=t(8936),l=t(7110),n=t(4085),o=t(5007),d=t(2336),c=t(5785),u=t(9426),m=t(8173),h=t.n(m),f=t(1594),x=t(6233),p=t(4807),g=t(6046),y=t(8897),j=t.n(y);function v(){let{t:e,primaryColor:r}=(0,l.t)(),{items:t,total:m,clearCart:y}=(0,i._)(),v=(0,g.useRouter)(),[b,N]=(0,a.useState)(!1),[C,w]=(0,a.useState)(""),[P,A]=(0,a.useState)({firstName:"",lastName:"",email:"",phone:"",address:"",city:"",country:"",zipCode:""});(0,a.useEffect)(()=>{{let e=localStorage.getItem("userId");N(!!(e&&"0"!==e))}},[]);let k=e=>{let{name:r,value:t}=e.target;A(e=>({...e,[r]:t}))},S=async e=>{if(e.preventDefault(),!b){j().fire({title:"Login Required",text:"Please login to complete your purchase",icon:"info",showCancelButton:!0,confirmButtonText:"Login",cancelButtonText:"Cancel"}).then(e=>{e.isConfirmed&&v.push("/login?redirect=checkout")});return}if(!C){j().fire({title:"Error",text:"Please select a payment method",icon:"error"});return}if(["firstName","lastName","email","phone","address"].filter(e=>!P[e]).length>0){j().fire({title:"Error",text:"Please fill in all required fields",icon:"error"});return}try{j().fire({title:"Processing",text:"Please wait while we process your order...",allowOutsideClick:!1,didOpen:()=>{j().showLoading()}});let e=t.map(e=>({ProductId:e.id,ProductName:e.name,Quantity:e.quantity,UnitPrice:e.discountPrice||e.price})),r={requestParameters:{UserID:localStorage.getItem("userId")||"0",OrderNote:"Order from Next.js app",cartJsonData:JSON.stringify(e),CouponCode:"",PaymentMethod:C,paymentToken:"",payPalOrderConfirmJson:"",recordValueJson:JSON.stringify({ShippingAddress:{FirstName:P.firstName,LastName:P.lastName,Email:P.email,Phone:P.phone,Address:P.address,City:P.city,Country:P.country,ZipCode:P.zipCode}})}},s=await fetch("".concat("https://admin.codemedicalapps.com/","api/v1/dynamic/dataoperation/post-customer-order"),{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify(r)}),a=await s.json();if(s.ok&&a.data){let e;try{e=JSON.parse(a.data)}catch(r){e=a.data}if(Array.isArray(e)&&e.length>0&&"Order Placed Successfully"===e[0].ResponseMsg)j().fire({title:"Success!",text:"Your order has been placed successfully",icon:"success"}),y(),window.location.href="/orders";else throw Error("Order placement failed")}else throw Error("Order placement failed")}catch(e){console.error("Error placing order:",e),j().fire({title:"Error",text:"There was an error processing your order. Please try again.",icon:"error"})}};return 0===t.length?(0,s.jsx)("div",{className:"container mx-auto py-8 px-4",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"Your cart is empty"}),(0,s.jsx)("p",{className:"text-muted-foreground mb-4",children:"Add items to your cart to proceed to checkout"}),(0,s.jsx)(n.$,{asChild:!0,children:(0,s.jsx)(h(),{href:"/",children:"Continue Shopping"})})]})}):b?(0,s.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,s.jsx)(u.Qp,{className:"mb-6",children:(0,s.jsxs)(u.AB,{children:[(0,s.jsx)(u.J5,{children:(0,s.jsx)(u.w1,{asChild:!0,children:(0,s.jsx)(h(),{href:"/",children:"Home"})})}),(0,s.jsx)(u.tH,{}),(0,s.jsx)(u.J5,{children:(0,s.jsx)(u.w1,{asChild:!0,children:(0,s.jsx)(h(),{href:"/cart",children:"Cart"})})}),(0,s.jsx)(u.tH,{}),(0,s.jsx)(u.J5,{children:(0,s.jsx)(u.tJ,{children:"Checkout"})})]})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,s.jsx)("div",{className:"lg:col-span-2",children:(0,s.jsx)(o.Zp,{children:(0,s.jsxs)("form",{onSubmit:S,className:"p-6 space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h2",{className:"text-xl font-bold",children:"Contact Information"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{htmlFor:"firstName",children:"First Name *"}),(0,s.jsx)(d.p,{id:"firstName",name:"firstName",value:P.firstName,onChange:k,required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{htmlFor:"lastName",children:"Last Name *"}),(0,s.jsx)(d.p,{id:"lastName",name:"lastName",value:P.lastName,onChange:k,required:!0})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{htmlFor:"email",children:"Email *"}),(0,s.jsx)(d.p,{id:"email",name:"email",type:"email",value:P.email,onChange:k,required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{htmlFor:"phone",children:"Phone *"}),(0,s.jsx)(d.p,{id:"phone",name:"phone",type:"tel",value:P.phone,onChange:k,required:!0})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h2",{className:"text-xl font-bold",children:"Shipping Address"}),(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{htmlFor:"address",children:"Address *"}),(0,s.jsx)(d.p,{id:"address",name:"address",value:P.address,onChange:k,required:!0})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{htmlFor:"city",children:"City"}),(0,s.jsx)(d.p,{id:"city",name:"city",value:P.city,onChange:k})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{htmlFor:"country",children:"Country"}),(0,s.jsx)(d.p,{id:"country",name:"country",value:P.country,onChange:k})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{htmlFor:"zipCode",children:"ZIP Code"}),(0,s.jsx)(d.p,{id:"zipCode",name:"zipCode",value:P.zipCode,onChange:k})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h2",{className:"text-xl font-bold",children:"Payment Method"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-md cursor-pointer hover:bg-muted/50 transition-colors",onClick:()=>w("CreditCard"),style:{borderColor:"CreditCard"===C?r:""},children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"w-10 h-10 rounded-full flex items-center justify-center",style:{backgroundColor:"".concat(r,"20")},children:(0,s.jsx)(x.A,{className:"h-5 w-5",style:{color:r}})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium",children:"Credit Card"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Pay with your credit card"})]})]}),(0,s.jsx)("div",{className:"w-5 h-5 rounded-full border flex items-center justify-center",style:{borderColor:r},children:"CreditCard"===C&&(0,s.jsx)("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:r}})})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-md cursor-pointer hover:bg-muted/50 transition-colors",onClick:()=>w("CashOnDelivery"),style:{borderColor:"CashOnDelivery"===C?r:""},children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"w-10 h-10 rounded-full flex items-center justify-center",style:{backgroundColor:"".concat(r,"20")},children:(0,s.jsx)(p.A,{className:"h-5 w-5",style:{color:r}})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium",children:"Cash on Delivery"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Pay when you receive your order"})]})]}),(0,s.jsx)("div",{className:"w-5 h-5 rounded-full border flex items-center justify-center",style:{borderColor:r},children:"CashOnDelivery"===C&&(0,s.jsx)("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:r}})})]})]}),(0,s.jsx)("div",{className:"mt-4",children:(0,s.jsx)(h(),{href:"/payment-methods",className:"text-sm text-primary hover:underline",children:"View all payment methods"})})]}),(0,s.jsx)(n.$,{type:"submit",className:"w-full",style:{backgroundColor:r},children:"Place Order"})]})})}),(0,s.jsx)("div",{className:"lg:col-span-1",children:(0,s.jsx)(o.Zp,{children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsx)("h2",{className:"text-xl font-bold mb-4",children:"Order Summary"}),(0,s.jsxs)("div",{className:"space-y-4",children:[t.map(e=>(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-muted rounded-md overflow-hidden",children:(0,s.jsx)("img",{src:e.image||"/products/book".concat(e.id,".jpg"),alt:e.name,className:"w-full h-full object-cover"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium",children:e.name}),(0,s.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Qty: ",e.quantity]})]})]}),(0,s.jsxs)("p",{className:"font-medium",children:["$",(e.discountPrice||e.price).toFixed(2)]})]},e.id)),(0,s.jsx)("div",{className:"border-t pt-4 mt-4",children:(0,s.jsxs)("div",{className:"flex justify-between items-center font-bold",children:[(0,s.jsx)("span",{children:"Total"}),(0,s.jsxs)("span",{style:{color:r},children:["$",m.toFixed(2)]})]})})]})]})})})]})]}):(0,s.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,s.jsx)(u.Qp,{className:"mb-6",children:(0,s.jsxs)(u.AB,{children:[(0,s.jsx)(u.J5,{children:(0,s.jsx)(u.w1,{asChild:!0,children:(0,s.jsx)(h(),{href:"/",children:"Home"})})}),(0,s.jsx)(u.tH,{}),(0,s.jsx)(u.J5,{children:(0,s.jsx)(u.w1,{asChild:!0,children:(0,s.jsx)(h(),{href:"/cart",children:"Cart"})})}),(0,s.jsx)(u.tH,{}),(0,s.jsx)(u.J5,{children:(0,s.jsx)(u.tJ,{children:"Checkout"})})]})}),(0,s.jsx)(o.Zp,{className:"max-w-md mx-auto",children:(0,s.jsxs)("div",{className:"p-6 text-center",children:[(0,s.jsx)(f.A,{className:"w-12 h-12 mx-auto mb-4 text-amber-500"}),(0,s.jsx)("h2",{className:"text-2xl font-bold mb-2",children:"Login Required"}),(0,s.jsx)("p",{className:"text-muted-foreground mb-6",children:"Please login to your account to continue with checkout"}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,s.jsx)(n.$,{asChild:!0,children:(0,s.jsx)(h(),{href:"/login?redirect=checkout",children:"Login"})}),(0,s.jsx)(n.$,{variant:"outline",asChild:!0,children:(0,s.jsx)(h(),{href:"/signup",children:"Create Account"})})]})]})})]})}},8936:(e,r,t)=>{"use strict";t.d(r,{_:()=>n,e:()=>l});var s=t(5155),a=t(2115);let i=(0,a.createContext)(void 0);function l(e){let{children:r}=e,[t,l]=(0,a.useState)([]),[n,o]=(0,a.useState)(!1);(0,a.useEffect)(()=>{let e=localStorage.getItem("cart");if(e)try{l(JSON.parse(e))}catch(e){console.error("Failed to parse cart from localStorage:",e)}o(!0)},[]),(0,a.useEffect)(()=>{localStorage.setItem("cart",JSON.stringify(t))},[t]);let d=e=>{l(r=>r.filter(r=>r.id!==e))},c=t.reduce((e,r)=>e+r.quantity,0);(0,a.useEffect)(()=>{localStorage.setItem("cart",JSON.stringify(t))},[t]);let u=t.reduce((e,r)=>e+(r.discountPrice?Math.min(r.discountPrice,r.adjustedPrice):r.adjustedPrice)*r.quantity,0),m=t.reduce((e,r)=>e+(r.adjustedIqdPrice||r.iqdPrice||0)*r.quantity,0);return(0,s.jsx)(i.Provider,{value:{items:t,addToCart:function(e,r){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],s=arguments.length>3?arguments[3]:void 0,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1500;l(i=>{let l=e.price,n=s||Math.round(e.price*a),o=n;t.forEach(r=>{if(r.PriceAdjustment&&r.PriceAdjustmentType){let t=e.originalPrice||e.price;switch(r.PriceAdjustmentType){case 1:l+=r.PriceAdjustment,o+=Math.round(r.PriceAdjustment*a);break;case 2:let s=t*r.PriceAdjustment/100;l+=s,o+=Math.round(s*a)}}});let d=i.findIndex(r=>{var s;return r.id===e.id&&JSON.stringify(null===(s=r.attributes)||void 0===s?void 0:s.sort((e,r)=>e.ProductAttributeID-r.ProductAttributeID))===JSON.stringify(null==t?void 0:t.sort((e,r)=>e.ProductAttributeID-r.ProductAttributeID))});if(!(d>=0))return[...i,{...e,iqdPrice:n,adjustedIqdPrice:Math.max(0,o),quantity:r,attributes:t,adjustedPrice:Math.max(0,l),originalPrice:e.originalPrice}];{let e=[...i];return e[d].quantity+=r,e}})},removeFromCart:d,updateQuantity:(e,r)=>{if(r<=0){d(e);return}l(t=>t.map(t=>t.id===e?{...t,quantity:r}:t))},clearCart:()=>{l([])},totalItems:c,subtotal:u,subtotalIQD:m,total:u,totalIQD:m,isHydrated:n},children:r})}function n(){let e=(0,a.useContext)(i);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e}},9426:(e,r,t)=>{"use strict";t.d(r,{AB:()=>d,J5:()=>c,Qp:()=>o,tH:()=>h,tJ:()=>m,w1:()=>u});var s=t(5155),a=t(2115),i=t(2317),l=t(6967),n=(t(4858),t(9602));let o=a.forwardRef((e,r)=>{let{...t}=e;return(0,s.jsx)("nav",{ref:r,"aria-label":"breadcrumb",...t})});o.displayName="Breadcrumb";let d=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("ol",{ref:r,className:(0,n.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",t),...a})});d.displayName="BreadcrumbList";let c=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("li",{ref:r,className:(0,n.cn)("inline-flex items-center gap-1.5",t),...a})});c.displayName="BreadcrumbItem";let u=a.forwardRef((e,r)=>{let{asChild:t,className:a,...l}=e,o=t?i.DX:"a";return(0,s.jsx)(o,{ref:r,className:(0,n.cn)("transition-colors hover:text-foreground",a),...l})});u.displayName="BreadcrumbLink";let m=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("span",{ref:r,role:"link","aria-disabled":"true","aria-current":"page",className:(0,n.cn)("font-normal text-foreground",t),...a})});m.displayName="BreadcrumbPage";let h=e=>{let{children:r,className:t,...a}=e;return(0,s.jsx)("li",{role:"presentation","aria-hidden":"true",className:(0,n.cn)("[&>svg]:size-3.5",t),...a,children:null!=r?r:(0,s.jsx)(l.A,{})})};h.displayName="BreadcrumbSeparator"},9602:(e,r,t)=>{"use strict";t.d(r,{cn:()=>i});var s=t(3463),a=t(9795);function i(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,s.$)(r))}}},e=>{var r=r=>e(e.s=r);e.O(0,[8320,1345,8441,6587,7358],()=>r(7597)),_N_E=e.O()}]);