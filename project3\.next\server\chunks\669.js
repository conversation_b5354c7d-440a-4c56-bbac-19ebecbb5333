"use strict";exports.id=669,exports.ids=[669],exports.modules={14494:(e,r,t)=>{t.d(r,{A:()=>a});let a=(0,t(41680).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},37778:(e,r,t)=>{t.d(r,{AB:()=>c,J5:()=>u,Qp:()=>l,tH:()=>m,tJ:()=>f,w1:()=>d});var a=t(45512),n=t(58009),o=t(12705),i=t(99905),s=(t(14494),t(59462));let l=n.forwardRef(({...e},r)=>(0,a.jsx)("nav",{ref:r,"aria-label":"breadcrumb",...e}));l.displayName="Breadcrumb";let c=n.forwardRef(({className:e,...r},t)=>(0,a.jsx)("ol",{ref:t,className:(0,s.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",e),...r}));c.displayName="BreadcrumbList";let u=n.forwardRef(({className:e,...r},t)=>(0,a.jsx)("li",{ref:t,className:(0,s.cn)("inline-flex items-center gap-1.5",e),...r}));u.displayName="BreadcrumbItem";let d=n.forwardRef(({asChild:e,className:r,...t},n)=>{let i=e?o.DX:"a";return(0,a.jsx)(i,{ref:n,className:(0,s.cn)("transition-colors hover:text-foreground",r),...t})});d.displayName="BreadcrumbLink";let f=n.forwardRef(({className:e,...r},t)=>(0,a.jsx)("span",{ref:t,role:"link","aria-disabled":"true","aria-current":"page",className:(0,s.cn)("font-normal text-foreground",e),...r}));f.displayName="BreadcrumbPage";let m=({children:e,className:r,...t})=>(0,a.jsx)("li",{role:"presentation","aria-hidden":"true",className:(0,s.cn)("[&>svg]:size-3.5",r),...t,children:e??(0,a.jsx)(i.A,{})});m.displayName="BreadcrumbSeparator"},60248:(e,r,t)=>{t.d(r,{tU:()=>J,av:()=>Q,j7:()=>X,Xi:()=>O});var a=t(45512),n=t(58009),o=t(31412),i=t(6004),s=t(39217),l=t(29952),c=t(30096),u=t(30830),d=t(92828),f=t(13024),m=t(59018),p="rovingFocusGroup.onEntryFocus",v={bubbles:!1,cancelable:!0},b="RovingFocusGroup",[g,x,w]=(0,s.N)(b),[y,h]=(0,i.A)(b,[w]),[j,R]=y(b),N=n.forwardRef((e,r)=>(0,a.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,a.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,a.jsx)(A,{...e,ref:r})})}));N.displayName=b;var A=n.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:t,orientation:i,loop:s=!1,dir:c,currentTabStopId:b,defaultCurrentTabStopId:g,onCurrentTabStopIdChange:w,onEntryFocus:y,preventScrollOnEntryFocus:h=!1,...R}=e,N=n.useRef(null),A=(0,l.s)(r,N),I=(0,m.jH)(c),[F=null,D]=(0,f.i)({prop:b,defaultProp:g,onChange:w}),[C,T]=n.useState(!1),E=(0,d.c)(y),B=x(t),G=n.useRef(!1),[K,L]=n.useState(0);return n.useEffect(()=>{let e=N.current;if(e)return e.addEventListener(p,E),()=>e.removeEventListener(p,E)},[E]),(0,a.jsx)(j,{scope:t,orientation:i,dir:I,loop:s,currentTabStopId:F,onItemFocus:n.useCallback(e=>D(e),[D]),onItemShiftTab:n.useCallback(()=>T(!0),[]),onFocusableItemAdd:n.useCallback(()=>L(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>L(e=>e-1),[]),children:(0,a.jsx)(u.sG.div,{tabIndex:C||0===K?-1:0,"data-orientation":i,...R,ref:A,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{G.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let r=!G.current;if(e.target===e.currentTarget&&r&&!C){let r=new CustomEvent(p,v);if(e.currentTarget.dispatchEvent(r),!r.defaultPrevented){let e=B().filter(e=>e.focusable);k([e.find(e=>e.active),e.find(e=>e.id===F),...e].filter(Boolean).map(e=>e.ref.current),h)}}G.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>T(!1))})})}),I="RovingFocusGroupItem",F=n.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:t,focusable:i=!0,active:s=!1,tabStopId:l,...d}=e,f=(0,c.B)(),m=l||f,p=R(I,t),v=p.currentTabStopId===m,b=x(t),{onFocusableItemAdd:w,onFocusableItemRemove:y}=p;return n.useEffect(()=>{if(i)return w(),()=>y()},[i,w,y]),(0,a.jsx)(g.ItemSlot,{scope:t,id:m,focusable:i,active:s,children:(0,a.jsx)(u.sG.span,{tabIndex:v?0:-1,"data-orientation":p.orientation,...d,ref:r,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i?p.onItemFocus(m):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>p.onItemFocus(m)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){p.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let r=function(e,r,t){var a;let n=(a=e.key,"rtl"!==t?a:"ArrowLeft"===a?"ArrowRight":"ArrowRight"===a?"ArrowLeft":a);if(!("vertical"===r&&["ArrowLeft","ArrowRight"].includes(n))&&!("horizontal"===r&&["ArrowUp","ArrowDown"].includes(n)))return D[n]}(e,p.orientation,p.dir);if(void 0!==r){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let t=b().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===r)t.reverse();else if("prev"===r||"next"===r){"prev"===r&&t.reverse();let a=t.indexOf(e.currentTarget);t=p.loop?function(e,r){return e.map((t,a)=>e[(r+a)%e.length])}(t,a+1):t.slice(a+1)}setTimeout(()=>k(t))}})})})});F.displayName=I;var D={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function k(e,r=!1){let t=document.activeElement;for(let a of e)if(a===t||(a.focus({preventScroll:r}),document.activeElement!==t))return}var C=t(98060),T="Tabs",[E,B]=(0,i.A)(T,[h]),G=h(),[K,L]=E(T),S=n.forwardRef((e,r)=>{let{__scopeTabs:t,value:n,onValueChange:o,defaultValue:i,orientation:s="horizontal",dir:l,activationMode:d="automatic",...p}=e,v=(0,m.jH)(l),[b,g]=(0,f.i)({prop:n,onChange:o,defaultProp:i});return(0,a.jsx)(K,{scope:t,baseId:(0,c.B)(),value:b,onValueChange:g,orientation:s,dir:v,activationMode:d,children:(0,a.jsx)(u.sG.div,{dir:v,"data-orientation":s,...p,ref:r})})});S.displayName=T;var M="TabsList",P=n.forwardRef((e,r)=>{let{__scopeTabs:t,loop:n=!0,...o}=e,i=L(M,t),s=G(t);return(0,a.jsx)(N,{asChild:!0,...s,orientation:i.orientation,dir:i.dir,loop:n,children:(0,a.jsx)(u.sG.div,{role:"tablist","aria-orientation":i.orientation,...o,ref:r})})});P.displayName=M;var z="TabsTrigger",H=n.forwardRef((e,r)=>{let{__scopeTabs:t,value:n,disabled:i=!1,...s}=e,l=L(z,t),c=G(t),d=$(l.baseId,n),f=_(l.baseId,n),m=n===l.value;return(0,a.jsx)(F,{asChild:!0,...c,focusable:!i,active:m,children:(0,a.jsx)(u.sG.button,{type:"button",role:"tab","aria-selected":m,"aria-controls":f,"data-state":m?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:d,...s,ref:r,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():l.onValueChange(n)}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&l.onValueChange(n)}),onFocus:(0,o.m)(e.onFocus,()=>{let e="manual"!==l.activationMode;m||i||!e||l.onValueChange(n)})})})});H.displayName=z;var U="TabsContent",V=n.forwardRef((e,r)=>{let{__scopeTabs:t,value:o,forceMount:i,children:s,...l}=e,c=L(U,t),d=$(c.baseId,o),f=_(c.baseId,o),m=o===c.value,p=n.useRef(m);return n.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,a.jsx)(C.C,{present:i||m,children:({present:t})=>(0,a.jsx)(u.sG.div,{"data-state":m?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":d,hidden:!t,id:f,tabIndex:0,...l,ref:r,style:{...e.style,animationDuration:p.current?"0s":void 0},children:t&&s})})});function $(e,r){return`${e}-trigger-${r}`}function _(e,r){return`${e}-content-${r}`}V.displayName=U;var q=t(59462);let J=S,X=n.forwardRef(({className:e,...r},t)=>(0,a.jsx)(P,{ref:t,className:(0,q.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...r}));X.displayName=P.displayName;let O=n.forwardRef(({className:e,...r},t)=>(0,a.jsx)(H,{ref:t,className:(0,q.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...r}));O.displayName=H.displayName;let Q=n.forwardRef(({className:e,...r},t)=>(0,a.jsx)(V,{ref:t,className:(0,q.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...r}));Q.displayName=V.displayName},99905:(e,r,t)=>{t.d(r,{A:()=>a});let a=(0,t(41680).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])}};