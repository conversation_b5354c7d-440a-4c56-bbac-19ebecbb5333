(()=>{var e={};e.id=298,e.ids=[298],e.modules={1284:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});let a=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\account\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\account\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10985:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(41680).A)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},21956:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(41680).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},24030:(e,s,r)=>{Promise.resolve().then(r.bind(r,74096))},25409:(e,s,r)=>{"use strict";r.d(s,{p:()=>l});var a=r(45512),t=r(58009),i=r(59462);let l=t.forwardRef(({className:e,type:s,...r},t)=>(0,a.jsx)("input",{type:s,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:t,...r}));l.displayName="Input"},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37778:(e,s,r)=>{"use strict";r.d(s,{AB:()=>d,J5:()=>c,Qp:()=>o,tH:()=>u,tJ:()=>p,w1:()=>m});var a=r(45512),t=r(58009),i=r(12705),l=r(99905),n=(r(14494),r(59462));let o=t.forwardRef(({...e},s)=>(0,a.jsx)("nav",{ref:s,"aria-label":"breadcrumb",...e}));o.displayName="Breadcrumb";let d=t.forwardRef(({className:e,...s},r)=>(0,a.jsx)("ol",{ref:r,className:(0,n.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",e),...s}));d.displayName="BreadcrumbList";let c=t.forwardRef(({className:e,...s},r)=>(0,a.jsx)("li",{ref:r,className:(0,n.cn)("inline-flex items-center gap-1.5",e),...s}));c.displayName="BreadcrumbItem";let m=t.forwardRef(({asChild:e,className:s,...r},t)=>{let l=e?i.DX:"a";return(0,a.jsx)(l,{ref:t,className:(0,n.cn)("transition-colors hover:text-foreground",s),...r})});m.displayName="BreadcrumbLink";let p=t.forwardRef(({className:e,...s},r)=>(0,a.jsx)("span",{ref:r,role:"link","aria-disabled":"true","aria-current":"page",className:(0,n.cn)("font-normal text-foreground",e),...s}));p.displayName="BreadcrumbPage";let u=({children:e,className:s,...r})=>(0,a.jsx)("li",{role:"presentation","aria-hidden":"true",className:(0,n.cn)("[&>svg]:size-3.5",s),...r,children:e??(0,a.jsx)(l.A,{})});u.displayName="BreadcrumbSeparator"},47699:(e,s,r)=>{"use strict";r.d(s,{J:()=>d});var a=r(45512),t=r(58009),i=r(92405),l=r(21643),n=r(59462);let o=(0,l.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=t.forwardRef(({className:e,...s},r)=>(0,a.jsx)(i.b,{ref:r,className:(0,n.cn)(o(),e),...s}));d.displayName=i.b.displayName},55591:e=>{"use strict";e.exports=require("https")},59048:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d});var a=r(70260),t=r(28203),i=r(25155),l=r.n(i),n=r(67292),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);r.d(s,o);let d={children:["",{children:["account",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1284)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\account\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,19611)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\account\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/account/page",pathname:"/account",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},60822:(e,s,r)=>{Promise.resolve().then(r.bind(r,1284))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69193:(e,s,r)=>{"use strict";r.d(s,{Xi:()=>d,av:()=>c,j7:()=>o,tU:()=>n});var a=r(45512),t=r(58009),i=r(66351),l=r(59462);let n=i.bL,o=t.forwardRef(({className:e,...s},r)=>(0,a.jsx)(i.B8,{ref:r,className:(0,l.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...s}));o.displayName=i.B8.displayName;let d=t.forwardRef(({className:e,...s},r)=>(0,a.jsx)(i.l9,{ref:r,className:(0,l.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...s}));d.displayName=i.l9.displayName;let c=t.forwardRef(({className:e,...s},r)=>(0,a.jsx)(i.UC,{ref:r,className:(0,l.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...s}));c.displayName=i.UC.displayName},69208:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(41680).A)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},74075:e=>{"use strict";e.exports=require("zlib")},74096:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>b});var a=r(45512),t=r(58009),i=r(37778),l=r(97643),n=r(87021),o=r(25409),d=r(47699),c=r(69193),m=r(28531),p=r.n(m),u=r(71901),x=r(87798),h=r(10985),f=r(96795),j=r(10453),w=r(41680);let g=(0,w.A)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]),v=(0,w.A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);var y=r(69208),N=r(21956);function b(){let{t:e}=(0,u.t)(),[s,r]=(0,t.useState)(!1),[m,w]=(0,t.useState)(!1),[b,P]=(0,t.useState)(!1),[C,k]=(0,t.useState)({firstName:"John",lastName:"Doe",email:"<EMAIL>",phone:"009647836071686",currentPassword:"",newPassword:"",confirmPassword:""}),A=e=>{let{name:s,value:r}=e.target;k(e=>({...e,[s]:r}))},[q,R]=(0,t.useState)(""),M=e=>{if(e.preventDefault(),w(!0),R(""),C.newPassword!==C.confirmPassword){R("New password and confirm password do not match"),w(!1);return}if(C.newPassword&&C.newPassword.length<8){R("Password must be at least 8 characters long"),w(!1);return}setTimeout(()=>{w(!1),P(!0),k(e=>({...e,currentPassword:"",newPassword:"",confirmPassword:""})),setTimeout(()=>P(!1),3e3)},1500)};return(0,a.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,a.jsx)(i.Qp,{className:"mb-6",children:(0,a.jsxs)(i.AB,{children:[(0,a.jsx)(i.J5,{children:(0,a.jsx)(i.w1,{asChild:!0,children:(0,a.jsx)(p(),{href:"/",children:e("home")})})}),(0,a.jsx)(i.tH,{}),(0,a.jsx)(i.J5,{children:(0,a.jsx)(i.tJ,{children:e("myAccount")})})]})}),(0,a.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold mb-6",children:e("myAccount")}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-[250px_1fr] gap-6",children:[(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsx)(l.Zp,{children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex flex-col items-center text-center mb-6",children:[(0,a.jsx)("div",{className:"w-20 h-20 rounded-full bg-primary/10 flex items-center justify-center mb-4",children:(0,a.jsx)(x.A,{className:"h-10 w-10 text-primary"})}),(0,a.jsx)("h3",{className:"font-medium",children:"John Doe"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"<EMAIL>"})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(n.$,{variant:"ghost",className:"w-full justify-start",asChild:!0,children:(0,a.jsxs)(p(),{href:"/account",children:[(0,a.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Profile"]})}),(0,a.jsx)(n.$,{variant:"ghost",className:"w-full justify-start",asChild:!0,children:(0,a.jsxs)(p(),{href:"/orders",children:[(0,a.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"Orders"]})}),(0,a.jsx)(n.$,{variant:"ghost",className:"w-full justify-start",asChild:!0,children:(0,a.jsxs)(p(),{href:"/payment-methods",children:[(0,a.jsx)(f.A,{className:"mr-2 h-4 w-4"}),"Payment Methods"]})}),(0,a.jsx)(n.$,{variant:"ghost",className:"w-full justify-start",asChild:!0,children:(0,a.jsxs)(p(),{href:"/wishlist",children:[(0,a.jsx)(j.A,{className:"mr-2 h-4 w-4"}),"Wishlist"]})}),(0,a.jsxs)(n.$,{variant:"ghost",className:"w-full justify-start text-red-500 hover:text-red-600 hover:bg-red-50",children:[(0,a.jsx)(g,{className:"mr-2 h-4 w-4"}),"Logout"]})]})]})})}),(0,a.jsx)("div",{children:(0,a.jsxs)(c.tU,{defaultValue:"profile",children:[(0,a.jsxs)(c.j7,{className:"mb-6",children:[(0,a.jsx)(c.Xi,{value:"profile",children:"Profile Information"}),(0,a.jsx)(c.Xi,{value:"password",children:"Change Password"})]}),(0,a.jsx)(c.av,{value:"profile",children:(0,a.jsx)(l.Zp,{children:(0,a.jsxs)("form",{onSubmit:M,className:"p-6",children:[b&&(0,a.jsx)("div",{className:"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6",children:"Profile updated successfully!"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"firstName",children:"First Name"}),(0,a.jsx)(o.p,{id:"firstName",name:"firstName",value:C.firstName,onChange:A})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"lastName",children:"Last Name"}),(0,a.jsx)(o.p,{id:"lastName",name:"lastName",value:C.lastName,onChange:A})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"email",children:"Email Address"}),(0,a.jsx)(o.p,{id:"email",name:"email",type:"email",value:C.email,onChange:A})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"phone",children:"Phone Number"}),(0,a.jsx)(o.p,{id:"phone",name:"phone",value:C.phone,onChange:A})]})]}),(0,a.jsx)(n.$,{type:"submit",disabled:m,children:m?(0,a.jsxs)("span",{className:"flex items-center gap-2",children:[(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Updating..."]}):(0,a.jsxs)("span",{className:"flex items-center gap-2",children:[(0,a.jsx)(v,{className:"h-4 w-4"}),"Save Changes"]})})]})})}),(0,a.jsx)(c.av,{value:"password",children:(0,a.jsx)(l.Zp,{children:(0,a.jsxs)("form",{onSubmit:M,className:"p-6",children:[b&&(0,a.jsx)("div",{className:"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6",children:"Password updated successfully!"}),(0,a.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"currentPassword",children:"Current Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(o.p,{id:"currentPassword",name:"currentPassword",type:s?"text":"password",value:C.currentPassword,onChange:A}),(0,a.jsx)(n.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3",onClick:()=>r(!s),children:s?(0,a.jsx)(y.A,{className:"h-4 w-4"}):(0,a.jsx)(N.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"newPassword",children:"New Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(o.p,{id:"newPassword",name:"newPassword",type:s?"text":"password",value:C.newPassword,onChange:A}),(0,a.jsx)(n.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3",onClick:()=>r(!s),children:s?(0,a.jsx)(y.A,{className:"h-4 w-4"}):(0,a.jsx)(N.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"confirmPassword",children:"Confirm New Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(o.p,{id:"confirmPassword",name:"confirmPassword",type:s?"text":"password",value:C.confirmPassword,onChange:A}),(0,a.jsx)(n.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3",onClick:()=>r(!s),children:s?(0,a.jsx)(y.A,{className:"h-4 w-4"}):(0,a.jsx)(N.A,{className:"h-4 w-4"})})]})]})]}),(0,a.jsx)(n.$,{type:"submit",disabled:m||!C.currentPassword||!C.newPassword||!C.confirmPassword||C.newPassword!==C.confirmPassword,children:m?(0,a.jsxs)("span",{className:"flex items-center gap-2",children:[(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Updating..."]}):(0,a.jsxs)("span",{className:"flex items-center gap-2",children:[(0,a.jsx)(v,{className:"h-4 w-4"}),"Update Password"]})}),q&&(0,a.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6",children:q})]})})})]})})]})]})]})}},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},92405:(e,s,r)=>{"use strict";r.d(s,{b:()=>n});var a=r(58009),t=r(30830),i=r(45512),l=a.forwardRef((e,s)=>(0,i.jsx)(t.sG.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));l.displayName="Label";var n=l},94735:e=>{"use strict";e.exports=require("events")},96795:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(41680).A)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},97643:(e,s,r)=>{"use strict";r.d(s,{Wu:()=>n,Zp:()=>l,wL:()=>o});var a=r(45512),t=r(58009),i=r(59462);let l=t.forwardRef(({className:e,...s},r)=>(0,a.jsx)("div",{ref:r,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));l.displayName="Card",t.forwardRef(({className:e,...s},r)=>(0,a.jsx)("div",{ref:r,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...s})).displayName="CardHeader",t.forwardRef(({className:e,...s},r)=>(0,a.jsx)("h3",{ref:r,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s})).displayName="CardTitle",t.forwardRef(({className:e,...s},r)=>(0,a.jsx)("p",{ref:r,className:(0,i.cn)("text-sm text-muted-foreground",e),...s})).displayName="CardDescription";let n=t.forwardRef(({className:e,...s},r)=>(0,a.jsx)("div",{ref:r,className:(0,i.cn)("p-6 pt-0",e),...s}));n.displayName="CardContent";let o=t.forwardRef(({className:e,...s},r)=>(0,a.jsx)("div",{ref:r,className:(0,i.cn)("flex items-center p-6 pt-0",e),...s}));o.displayName="CardFooter"}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),a=s.X(0,[638,320,684,875],()=>r(59048));module.exports=a})();