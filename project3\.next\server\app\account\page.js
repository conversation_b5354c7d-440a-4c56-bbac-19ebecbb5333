(()=>{var e={};e.id=298,e.ids=[298],e.modules={1284:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\account\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\account\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10985:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(41680).A)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},21956:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(41680).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},24030:(e,s,r)=>{Promise.resolve().then(r.bind(r,74096))},25409:(e,s,r)=>{"use strict";r.d(s,{p:()=>l});var t=r(45512),a=r(58009),i=r(59462);let l=a.forwardRef(({className:e,type:s,...r},a)=>(0,t.jsx)("input",{type:s,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...r}));l.displayName="Input"},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},47699:(e,s,r)=>{"use strict";r.d(s,{J:()=>n});var t=r(45512),a=r(58009),i=r(92405),l=r(21643),o=r(59462);let d=(0,l.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),n=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)(i.b,{ref:r,className:(0,o.cn)(d(),e),...s}));n.displayName=i.b.displayName},55591:e=>{"use strict";e.exports=require("https")},59048:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>n});var t=r(70260),a=r(28203),i=r(25155),l=r.n(i),o=r(67292),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(s,d);let n={children:["",{children:["account",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1284)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\account\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,19611)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\account\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/account/page",pathname:"/account",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:n}})},60822:(e,s,r)=>{Promise.resolve().then(r.bind(r,1284))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69208:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(41680).A)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},74075:e=>{"use strict";e.exports=require("zlib")},74096:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>b});var t=r(45512),a=r(58009),i=r(37778),l=r(97643),o=r(87021),d=r(25409),n=r(47699),c=r(60248),m=r(28531),p=r.n(m),h=r(71901),x=r(87798),u=r(10985),f=r(96795),j=r(10453),w=r(41680);let v=(0,w.A)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]),y=(0,w.A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);var g=r(69208),N=r(21956);function b(){let{t:e}=(0,h.t)(),[s,r]=(0,a.useState)(!1),[m,w]=(0,a.useState)(!1),[b,P]=(0,a.useState)(!1),[C,k]=(0,a.useState)({firstName:"John",lastName:"Doe",email:"<EMAIL>",phone:"009647836071686",currentPassword:"",newPassword:"",confirmPassword:""}),A=e=>{let{name:s,value:r}=e.target;k(e=>({...e,[s]:r}))},[q,M]=(0,a.useState)(""),_=e=>{if(e.preventDefault(),w(!0),M(""),C.newPassword!==C.confirmPassword){M("New password and confirm password do not match"),w(!1);return}if(C.newPassword&&C.newPassword.length<8){M("Password must be at least 8 characters long"),w(!1);return}setTimeout(()=>{w(!1),P(!0),k(e=>({...e,currentPassword:"",newPassword:"",confirmPassword:""})),setTimeout(()=>P(!1),3e3)},1500)};return(0,t.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,t.jsx)(i.Qp,{className:"mb-6",children:(0,t.jsxs)(i.AB,{children:[(0,t.jsx)(i.J5,{children:(0,t.jsx)(i.w1,{asChild:!0,children:(0,t.jsx)(p(),{href:"/",children:e("home")})})}),(0,t.jsx)(i.tH,{}),(0,t.jsx)(i.J5,{children:(0,t.jsx)(i.tJ,{children:e("myAccount")})})]})}),(0,t.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold mb-6",children:e("myAccount")}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-[250px_1fr] gap-6",children:[(0,t.jsx)("div",{className:"space-y-4",children:(0,t.jsx)(l.Zp,{children:(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex flex-col items-center text-center mb-6",children:[(0,t.jsx)("div",{className:"w-20 h-20 rounded-full bg-primary/10 flex items-center justify-center mb-4",children:(0,t.jsx)(x.A,{className:"h-10 w-10 text-primary"})}),(0,t.jsx)("h3",{className:"font-medium",children:"John Doe"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"<EMAIL>"})]}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)(o.$,{variant:"ghost",className:"w-full justify-start",asChild:!0,children:(0,t.jsxs)(p(),{href:"/account",children:[(0,t.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Profile"]})}),(0,t.jsx)(o.$,{variant:"ghost",className:"w-full justify-start",asChild:!0,children:(0,t.jsxs)(p(),{href:"/orders",children:[(0,t.jsx)(u.A,{className:"mr-2 h-4 w-4"}),"Orders"]})}),(0,t.jsx)(o.$,{variant:"ghost",className:"w-full justify-start",asChild:!0,children:(0,t.jsxs)(p(),{href:"/payment-methods",children:[(0,t.jsx)(f.A,{className:"mr-2 h-4 w-4"}),"Payment Methods"]})}),(0,t.jsx)(o.$,{variant:"ghost",className:"w-full justify-start",asChild:!0,children:(0,t.jsxs)(p(),{href:"/wishlist",children:[(0,t.jsx)(j.A,{className:"mr-2 h-4 w-4"}),"Wishlist"]})}),(0,t.jsxs)(o.$,{variant:"ghost",className:"w-full justify-start text-red-500 hover:text-red-600 hover:bg-red-50",children:[(0,t.jsx)(v,{className:"mr-2 h-4 w-4"}),"Logout"]})]})]})})}),(0,t.jsx)("div",{children:(0,t.jsxs)(c.tU,{defaultValue:"profile",children:[(0,t.jsxs)(c.j7,{className:"mb-6",children:[(0,t.jsx)(c.Xi,{value:"profile",children:"Profile Information"}),(0,t.jsx)(c.Xi,{value:"password",children:"Change Password"})]}),(0,t.jsx)(c.av,{value:"profile",children:(0,t.jsx)(l.Zp,{children:(0,t.jsxs)("form",{onSubmit:_,className:"p-6",children:[b&&(0,t.jsx)("div",{className:"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6",children:"Profile updated successfully!"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(n.J,{htmlFor:"firstName",children:"First Name"}),(0,t.jsx)(d.p,{id:"firstName",name:"firstName",value:C.firstName,onChange:A})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(n.J,{htmlFor:"lastName",children:"Last Name"}),(0,t.jsx)(d.p,{id:"lastName",name:"lastName",value:C.lastName,onChange:A})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(n.J,{htmlFor:"email",children:"Email Address"}),(0,t.jsx)(d.p,{id:"email",name:"email",type:"email",value:C.email,onChange:A})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(n.J,{htmlFor:"phone",children:"Phone Number"}),(0,t.jsx)(d.p,{id:"phone",name:"phone",value:C.phone,onChange:A})]})]}),(0,t.jsx)(o.$,{type:"submit",disabled:m,children:m?(0,t.jsxs)("span",{className:"flex items-center gap-2",children:[(0,t.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,t.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,t.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Updating..."]}):(0,t.jsxs)("span",{className:"flex items-center gap-2",children:[(0,t.jsx)(y,{className:"h-4 w-4"}),"Save Changes"]})})]})})}),(0,t.jsx)(c.av,{value:"password",children:(0,t.jsx)(l.Zp,{children:(0,t.jsxs)("form",{onSubmit:_,className:"p-6",children:[b&&(0,t.jsx)("div",{className:"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6",children:"Password updated successfully!"}),(0,t.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(n.J,{htmlFor:"currentPassword",children:"Current Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(d.p,{id:"currentPassword",name:"currentPassword",type:s?"text":"password",value:C.currentPassword,onChange:A}),(0,t.jsx)(o.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3",onClick:()=>r(!s),children:s?(0,t.jsx)(g.A,{className:"h-4 w-4"}):(0,t.jsx)(N.A,{className:"h-4 w-4"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(n.J,{htmlFor:"newPassword",children:"New Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(d.p,{id:"newPassword",name:"newPassword",type:s?"text":"password",value:C.newPassword,onChange:A}),(0,t.jsx)(o.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3",onClick:()=>r(!s),children:s?(0,t.jsx)(g.A,{className:"h-4 w-4"}):(0,t.jsx)(N.A,{className:"h-4 w-4"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(n.J,{htmlFor:"confirmPassword",children:"Confirm New Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(d.p,{id:"confirmPassword",name:"confirmPassword",type:s?"text":"password",value:C.confirmPassword,onChange:A}),(0,t.jsx)(o.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3",onClick:()=>r(!s),children:s?(0,t.jsx)(g.A,{className:"h-4 w-4"}):(0,t.jsx)(N.A,{className:"h-4 w-4"})})]})]})]}),(0,t.jsx)(o.$,{type:"submit",disabled:m||!C.currentPassword||!C.newPassword||!C.confirmPassword||C.newPassword!==C.confirmPassword,children:m?(0,t.jsxs)("span",{className:"flex items-center gap-2",children:[(0,t.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,t.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,t.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Updating..."]}):(0,t.jsxs)("span",{className:"flex items-center gap-2",children:[(0,t.jsx)(y,{className:"h-4 w-4"}),"Update Password"]})}),q&&(0,t.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6",children:q})]})})})]})})]})]})]})}},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},92405:(e,s,r)=>{"use strict";r.d(s,{b:()=>o});var t=r(58009),a=r(30830),i=r(45512),l=t.forwardRef((e,s)=>(0,i.jsx)(a.sG.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));l.displayName="Label";var o=l},94735:e=>{"use strict";e.exports=require("events")},96795:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(41680).A)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},97643:(e,s,r)=>{"use strict";r.d(s,{Wu:()=>o,Zp:()=>l,wL:()=>d});var t=r(45512),a=r(58009),i=r(59462);let l=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));l.displayName="Card",a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...s})).displayName="CardHeader",a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("h3",{ref:r,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s})).displayName="CardTitle",a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("p",{ref:r,className:(0,i.cn)("text-sm text-muted-foreground",e),...s})).displayName="CardDescription";let o=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,i.cn)("p-6 pt-0",e),...s}));o.displayName="CardContent";let d=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,i.cn)("flex items-center p-6 pt-0",e),...s}));d.displayName="CardFooter"}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[638,551,875,669],()=>r(59048));module.exports=t})();