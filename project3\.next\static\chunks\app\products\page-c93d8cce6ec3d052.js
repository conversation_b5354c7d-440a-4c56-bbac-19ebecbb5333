(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3571],{241:(e,t,s)=>{"use strict";s.d(t,{dj:()=>m});var a=s(2115);let r=0,l=new Map,i=e=>{if(l.has(e))return;let t=setTimeout(()=>{l.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);l.set(e,t)},n=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:s}=t;return s?i(s):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},o=[],c={toasts:[]};function d(e){c=n(c,e),o.forEach(e=>{e(c)})}function u(e){let{...t}=e,s=(r=(r+1)%Number.MAX_SAFE_INTEGER).toString(),a=()=>d({type:"DISMISS_TOAST",toastId:s});return d({type:"ADD_TOAST",toast:{...t,id:s,open:!0,onOpenChange:e=>{e||a()}}}),{id:s,dismiss:a,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:s}})}}function m(){let[e,t]=a.useState(c);return a.useEffect(()=>(o.push(t),()=>{let e=o.indexOf(t);e>-1&&o.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>d({type:"DISMISS_TOAST",toastId:e})}}},4085:(e,t,s)=>{"use strict";s.d(t,{$:()=>c,r:()=>o});var a=s(5155),r=s(2115),l=s(2317),i=s(1027),n=s(9602);let o=(0,i.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-red-500 text-destructive-foreground hover:bg-red-600",outline:"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=r.forwardRef((e,t)=>{let{className:s,variant:r,size:i,asChild:c=!1,...d}=e,u=c?l.DX:"button";return(0,a.jsx)(u,{className:(0,n.cn)(o({variant:r,size:i,className:s})),ref:t,...d})});c.displayName="Button"},5007:(e,t,s)=>{"use strict";s.d(t,{Wu:()=>n,Zp:()=>i,wL:()=>o});var a=s(5155),r=s(2115),l=s(9602);let i=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...r})});i.displayName="Card",r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",s),...r})}).displayName="CardHeader",r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("h3",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",s),...r})}).displayName="CardTitle",r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("p",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",s),...r})}).displayName="CardDescription";let n=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",s),...r})});n.displayName="CardContent";let o=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",s),...r})});o.displayName="CardFooter"},5488:(e,t,s)=>{"use strict";s.d(t,{E:()=>l});var a=s(5155),r=s(9602);function l(e){let{className:t,...s}=e;return(0,a.jsx)("div",{className:(0,r.cn)("animate-pulse rounded-md bg-muted",t),...s})}},7110:(e,t,s)=>{"use strict";s.d(t,{Z:()=>n,t:()=>o});var a=s(5155),r=s(2115);let l={en:{menu:"Menu",newsletter:"Newsletter",enterEmail:"Enter your email",newsletterDisclaimer:"Subscribe to our newsletter to receive updates and exclusive offers",popularCategories:"Popular Categories!",settings:"Settings",language:"Language",theme:"Theme",color:"Color",save:"Save",cancel:"Cancel",close:"Close",phone:"***************",phonenumber:"Phone Number",email:"<EMAIL>",liveChat:"Live Chat",welcome:"Welcome",logout:"Logout",login:"Login",signup:"Sign Up",signUp:"Sign Up",category:"Category",categories:"Categories",filters:"Filters",clearAll:"Clear All",products:"Products",loadingCategories:"Loading categories...",home:"Home",todayDeals:"Today's Deals",followUs:"Follow Us",aboutUs:"About Us",contactUs:"Contact Us",tryAgain:"Try Again",noProductsFound:"No products found",allProducts:"All Products",quickLinks:"Quick Links",about:"About Us",contact:"Contact Us",hotDeals:"Hot Deals",customerArea:"Customer Area",myAccount:"My Account",orders:"Orders",cart:"Cart",wishlist:"Wishlist",paymentMethods:"Payment Methods",location:"Location",callUs:"Call Us",emailUs:"Email Us",subscribe:"Subscribe",name:"Name",subject:"Subject",message:"Message",sendMessage:"Send Message",sending:"Sending...",messageSent:"Message sent successfully!",messageError:"Failed to send message",contactInfo:"Contact Information",address:"Address",findUs:"Find Us"},ar:{menu:"القائمة",popularCategories:"الفئات الشائعة!",settings:"الإعدادات",language:"اللغة",theme:"المظهر",color:"اللون",save:"حفظ",cancel:"إلغاء",close:"إغلاق",phone:"***************",phonenumber:"رقم الهاتف",email:"<EMAIL>",liveChat:"محادثة مباشرة",welcome:"مرحباً",logout:"تسجيل الخروج",login:"تسجيل الدخول",signup:"تسجيل جديد",signUp:"تسجيل جديد",category:"الفئة",categories:"الفئات",products:"المنتجات",loadingCategories:"جاري تحميل الفئات...",filters:"المرشحات",clearAll:"مسح الكل",home:"الرئيسية",todayDeals:"عروض اليوم",followUs:"تابعنا",aboutUs:"من نحن",contactUs:"اتصل بنا",tryAgain:"حاول مرة أخرى",noProductsFound:"لم يتم العثور على منتجات",allProducts:"جميع المنتجات",quickLinks:"روابط سريعة",about:"من نحن",contact:"اتصل بنا",hotDeals:"عروض ساخنة",customerArea:"منطقة العملاء",myAccount:"حسابي",orders:"الطلبات",cart:"السلة",wishlist:"المفضلة",paymentMethods:"طرق الدفع",location:"الموقع",callUs:"اتصل بنا",emailUs:"راسلنا",subscribe:"اشترك",name:"الاسم",subject:"الموضوع",message:"الرسالة",sendMessage:"إرسال الرسالة",sending:"جاري الإرسال...",messageSent:"تم إرسال الرسالة بنجاح!",messageError:"فشل في إرسال الرسالة",contactInfo:"معلومات الاتصال",address:"العنوان",findUs:"موقعنا"}},i=(0,r.createContext)(void 0);function n(e){let{children:t}=e,[s,n]=(0,r.useState)("light"),[o,c]=(0,r.useState)("en"),[d,u]=(0,r.useState)("#0074b2");return(0,r.useEffect)(()=>{document.documentElement.style.setProperty("--primary",d),document.documentElement.style.setProperty("--primary-foreground","#ffffff")},[]),(0,a.jsx)(i.Provider,{value:{theme:s,language:o,primaryColor:d,toggleTheme:()=>{n("light"===s?"dark":"light")},setLanguage:e=>{c(e),document.documentElement.dir="ar"===e?"rtl":"ltr"},setPrimaryColor:e=>{u(e)},t:e=>(function(e,t){let s=l[t];return e in s?s[e]:"en"!==t&&e in l.en?l.en[e]:e})(e,o)},children:t})}function o(){let e=(0,r.useContext)(i);if(void 0===e)throw Error("useSettings must be used within a SettingsProvider");return e}},8373:(e,t,s)=>{Promise.resolve().then(s.bind(s,8569))},8569:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>B});var a=s(5155),r=s(2115),l=s(6046),i=s(719),n=s(1594),o=s(4267),c=s(4085),d=s(5488),u=s(1014),m=s(1719),h=s(1902),p=s(8867),x=s(9602);let g=u.bL;u.YJ;let f=u.WT,y=r.forwardRef((e,t)=>{let{className:s,children:r,...l}=e;return(0,a.jsxs)(u.l9,{ref:t,className:(0,x.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",s),...l,children:[r,(0,a.jsx)(u.In,{asChild:!0,children:(0,a.jsx)(m.A,{className:"h-4 w-4 opacity-50"})})]})});y.displayName=u.l9.displayName;let N=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(u.PP,{ref:t,className:(0,x.cn)("flex cursor-default items-center justify-center py-1",s),...r,children:(0,a.jsx)(h.A,{className:"h-4 w-4"})})});N.displayName=u.PP.displayName;let j=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(u.wn,{ref:t,className:(0,x.cn)("flex cursor-default items-center justify-center py-1",s),...r,children:(0,a.jsx)(m.A,{className:"h-4 w-4"})})});j.displayName=u.wn.displayName;let v=r.forwardRef((e,t)=>{let{className:s,children:r,position:l="popper",...i}=e;return(0,a.jsx)(u.ZL,{children:(0,a.jsxs)(u.UC,{ref:t,className:(0,x.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-white text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===l&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:l,...i,children:[(0,a.jsx)(N,{}),(0,a.jsx)(u.LM,{className:(0,x.cn)("p-1","popper"===l&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:r}),(0,a.jsx)(j,{})]})})});v.displayName=u.UC.displayName,r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(u.JU,{ref:t,className:(0,x.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",s),...r})}).displayName=u.JU.displayName;let b=r.forwardRef((e,t)=>{let{className:s,children:r,...l}=e;return(0,a.jsxs)(u.q7,{ref:t,className:(0,x.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...l,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(u.VF,{children:(0,a.jsx)(p.A,{className:"h-4 w-4"})})}),(0,a.jsx)(u.p4,{children:r})]})});b.displayName=u.q7.displayName,r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(u.wv,{ref:t,className:(0,x.cn)("-mx-1 my-1 h-px bg-muted",s),...r})}).displayName=u.wv.displayName;var w=s(241),P=s(8173),S=s.n(P),A=s(5565),C=s(6889),I=s(865),D=s(2598),T=s(591),E=s(5007),U=s(9286),k=s(8936),M=s(9355),O=s(7110);function R(e){let{endDate:t}=e,[s,l]=(0,r.useState)(null);(0,r.useEffect)(()=>{let e=()=>{let e=new Date().getTime(),s=new Date(t).getTime()-e;return s>0?{days:Math.floor(s/864e5),hours:Math.floor(s%864e5/36e5),minutes:Math.floor(s%36e5/6e4),seconds:Math.floor(s%6e4/1e3)}:null},s=setInterval(()=>{l(e())},1e3);return l(e()),()=>clearInterval(s)},[t]);let i=e=>{let{value:t,label:s}=e;return(0,a.jsxs)("div",{className:"flex flex-col items-center mx-0.5",children:[(0,a.jsxs)("div",{className:"relative w-7 h-7 sm:w-8 sm:h-8 flex items-center justify-center bg-gradient-to-br from-pink-500 to-red-600 rounded-md shadow-md",children:[(0,a.jsx)("div",{className:"absolute inset-0.5 bg-black/20 rounded-sm"}),(0,a.jsx)("span",{className:"relative z-10 text-white font-bold text-xs sm:text-sm",children:String(t).padStart(2,"0")})]}),(0,a.jsx)("span",{className:"text-[9px] sm:text-[10px] text-white/80 mt-0.5 font-medium",children:s})]})};return s?(0,a.jsx)("div",{className:"bg-gradient-to-r from-pink-600/90 to-red-600/90 backdrop-blur-sm p-1.5 sm:p-2 rounded-lg shadow-lg",children:(0,a.jsxs)("div",{className:"flex justify-center items-center space-x-0.5",children:[s.days>0&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i,{value:s.days,label:"Days"}),(0,a.jsx)("span",{className:"text-white font-bold text-sm -mb-2",children:":"})]}),(0,a.jsx)(i,{value:s.hours,label:"Hrs"}),(0,a.jsx)("span",{className:"text-white font-bold text-sm -mb-2",children:":"}),(0,a.jsx)(i,{value:s.minutes,label:"Min"}),(0,a.jsx)("span",{className:"text-white font-bold text-sm -mb-2",children:":"}),(0,a.jsx)(i,{value:s.seconds,label:"Sec"})]})}):(0,a.jsxs)("div",{className:"px-3 py-1.5 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center",children:[(0,a.jsx)(C.A,{className:"w-3.5 h-3.5 mr-1.5 text-white animate-pulse"}),(0,a.jsx)("span",{className:"text-xs font-semibold text-white",children:"Sale Ended"})]})}function F(e){let{product:t}=e;(0,k._)();let s=(0,M.n)(),{toast:l}=(0,w.dj)(),{primaryColor:i}=(0,O.t)(),[n,o]=(0,r.useState)(!1),[d,u]=(0,r.useState)(!1),m=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD";return"IQD"===t?"IQD ".concat(e.toLocaleString()):"$".concat(e.toFixed(2))};t.SellStartDatetimeUTC&&t.SellEndDatetimeUTC;let h=new Date,p=t.SellStartDatetimeUTC?new Date(t.SellStartDatetimeUTC):null,x=t.SellEndDatetimeUTC?new Date(t.SellEndDatetimeUTC):null,g=p&&x&&h>=p&&h<=x;return(0,a.jsxs)(E.Zp,{className:"overflow-hidden flex flex-col h-full relative",children:[(0,a.jsxs)("div",{className:"absolute top-2 left-2 z-10 flex flex-col gap-1",children:[t.MarkAsNew&&(0,a.jsx)(U.E,{variant:"secondary",className:"bg-blue-500 text-white text-xs",children:"New"}),t.DiscountPrice&&t.DiscountPrice>0&&(0,a.jsx)(U.E,{variant:"destructive",className:"bg-red-500 text-white text-xs",children:"Sale"}),g&&!t.DiscountPrice&&(0,a.jsx)(U.E,{variant:"destructive",className:"bg-red-500 text-white text-xs",children:"Sale"})]}),(0,a.jsx)(S(),{href:"/product/".concat(t.ProductId),children:(0,a.jsxs)("div",{className:"aspect-square overflow-hidden relative",children:[(0,a.jsx)("div",{className:"h-full w-full relative",children:(0,a.jsx)(A.default,{src:t.ProductImageUrl||"/placeholder.svg?height=300&width=300",alt:t.ProductName||"Product",fill:!0,className:"object-cover transition-transform hover:scale-105",onError:e=>{e.target.src="/placeholder.svg?height=300&width=300"},priority:!1,loading:"lazy"})}),g&&t.SellEndDatetimeUTC&&(0,a.jsx)("div",{className:"absolute bottom-0 left-0 right-0 p-2 flex justify-center",children:(0,a.jsx)(R,{endDate:t.SellEndDatetimeUTC})})]})}),(0,a.jsxs)(E.Wu,{className:"pt-4 flex-grow",children:[(0,a.jsxs)("div",{className:"flex items-center mb-2",children:[(0,a.jsx)("div",{className:"flex",children:[void 0,void 0,void 0,void 0,void 0].map((e,s)=>(0,a.jsx)(I.A,{className:"w-4 h-4 ".concat(s<Math.floor(t.Rating||0)?"text-yellow-400 fill-yellow-400":"text-gray-300")},s))}),(0,a.jsxs)("span",{className:"text-xs text-gray-500 ml-1",children:["(",t.Rating||0,")"]})]}),(0,a.jsx)(S(),{href:"/product/".concat(t.ProductId),className:"hover:underline",children:(0,a.jsx)("h3",{className:"font-semibold text-lg line-clamp-2 mb-2",children:t.ProductName||"Unnamed Product"})}),t.ProductTypeName&&(0,a.jsxs)("p",{className:"text-sm text-gray-500 mb-2",children:["Type: ",t.ProductTypeName]}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("div",{className:"flex items-center gap-2",children:t.DiscountPrice?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{className:"text-lg font-bold text-red-500",children:m(t.DiscountPrice)}),(0,a.jsx)("span",{className:"text-xs text-gray-500 line-through",children:m(t.Price||0)})]}):t.OldPrice&&t.OldPrice>t.Price?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{className:"text-lg font-bold text-red-500",children:m(t.Price||0)}),(0,a.jsx)("span",{className:"text-xs text-gray-500 line-through",children:m(t.OldPrice)})]}):(0,a.jsx)("span",{className:"text-lg font-bold text-primary",children:m(t.Price||0)})}),t.IQDPrice&&(0,a.jsx)("span",{className:"text-sm font-medium text-green-600 mt-0.5",children:m(t.IQDPrice,"IQD")})]})]}),(0,a.jsx)(E.wL,{className:"p-3 pt-1 mt-auto",children:(0,a.jsx)("div",{className:"w-full",children:(0,a.jsxs)("div",{className:"flex items-center justify-between gap-2",children:[(0,a.jsx)(c.$,{variant:"ghost",size:"sm",className:"h-8 px-2 text-xs font-medium flex-1 gap-1.5 text-white hover:opacity-90",style:{backgroundColor:i},asChild:!0,children:(0,a.jsxs)(S(),{href:"/product/".concat(t.ProductId),children:[(0,a.jsx)(D.A,{className:"h-3.5 w-3.5"}),(0,a.jsx)("span",{children:"View"})]})}),(0,a.jsx)(c.$,{variant:"ghost",size:"icon",className:"h-8 w-8 rounded-full",onClick:()=>{if(s.isHydrated){u(!0);try{s.isInWishlist(t.ProductId)?(s.removeFromWishlist(t.ProductId),l({description:"".concat(t.ProductName," removed from wishlist"),type:"success"})):(s.addToWishlist(t.ProductId),l({description:"".concat(t.ProductName," added to wishlist"),type:"success"}))}catch(e){console.error("Error updating wishlist:",e),l({description:"Failed to update wishlist",type:"error"})}finally{setTimeout(()=>{u(!1)},500)}}},disabled:d,children:(0,a.jsx)(T.A,{className:"h-4 w-4 ".concat(s.isInWishlist(t.ProductId)?"fill-red-500 text-red-500":"")})})]})})})]})}var J=s(3518),z=s(6967),L=s(4858);let q=e=>{let{className:t,...s}=e;return(0,a.jsx)("nav",{role:"navigation","aria-label":"pagination",className:(0,x.cn)("mx-auto flex w-full justify-center",t),...s})};q.displayName="Pagination";let W=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("ul",{ref:t,className:(0,x.cn)("flex flex-row items-center gap-1",s),...r})});W.displayName="PaginationContent";let _=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("li",{ref:t,className:(0,x.cn)("",s),...r})});_.displayName="PaginationItem";let V=e=>{let{className:t,isActive:s,size:r="icon",...l}=e;return(0,a.jsx)("a",{"aria-current":s?"page":void 0,className:(0,x.cn)((0,c.r)({variant:s?"outline":"ghost",size:r}),t),...l})};V.displayName="PaginationLink";let $=e=>{let{className:t,...s}=e;return(0,a.jsxs)(V,{"aria-label":"Go to previous page",size:"default",className:(0,x.cn)("gap-1 pl-2.5",t),...s,children:[(0,a.jsx)(J.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Previous"})]})};$.displayName="PaginationPrevious";let Q=e=>{let{className:t,...s}=e;return(0,a.jsxs)(V,{"aria-label":"Go to next page",size:"default",className:(0,x.cn)("gap-1 pr-2.5",t),...s,children:[(0,a.jsx)("span",{children:"Next"}),(0,a.jsx)(z.A,{className:"h-4 w-4"})]})};Q.displayName="PaginationNext";let H=e=>{let{className:t,...s}=e;return(0,a.jsxs)("span",{"aria-hidden":!0,className:(0,x.cn)("flex h-9 w-9 items-center justify-center",t),...s,children:[(0,a.jsx)(L.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"More pages"})]})};function Z(e){let{children:t}=e,s=(0,l.useSearchParams)(),r=s.get("search")||"",i=s.get("category")||"all",n=s.get("productType")||"all";return(0,a.jsx)(a.Fragment,{children:t({searchTerm:r,categoryId:i,productTypeId:n})})}function B(){return(0,a.jsx)(r.Suspense,{fallback:(0,a.jsx)(G,{}),children:(0,a.jsx)(Z,{children:e=>{let{searchTerm:t,categoryId:s,productTypeId:r}=e;return(0,a.jsx)(X,{initialSearchTerm:t,initialCategoryId:s,initialProductTypeId:r})}})})}function G(){return(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsx)("div",{className:"h-8 w-64 bg-gray-200 rounded mb-4 animate-pulse"}),(0,a.jsx)("div",{className:"h-4 w-96 bg-gray-200 rounded mb-6 animate-pulse"}),(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[(0,a.jsx)("div",{className:"lg:w-1/4",children:(0,a.jsxs)("div",{className:"bg-white p-4 rounded-lg shadow",children:[(0,a.jsx)("div",{className:"h-6 w-32 bg-gray-200 rounded mb-4 animate-pulse"}),(0,a.jsx)("div",{className:"space-y-4",children:[,,,,].fill(0).map((e,t)=>(0,a.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"},t))})]})}),(0,a.jsx)("div",{className:"lg:w-3/4",children:(0,a.jsx)("div",{className:"grid grid-cols-2 gap-4 sm:gap-6 sm:grid-cols-2 lg:grid-cols-3",children:Array(12).fill(0).map((e,t)=>(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,a.jsx)("div",{className:"aspect-square bg-gray-200 animate-pulse"}),(0,a.jsxs)("div",{className:"p-4 space-y-2",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse"}),(0,a.jsx)("div",{className:"h-4 w-3/4 bg-gray-200 rounded animate-pulse"}),(0,a.jsx)("div",{className:"h-6 w-1/3 bg-gray-200 rounded animate-pulse"})]})]},t))})})]})]})}function X(e){var t,s,l,u,m,h;let p;let{initialSearchTerm:x,initialCategoryId:N,initialProductTypeId:j}=e,{toast:P}=(0,w.dj)(),[S,A]=(0,r.useState)([]),[C,I]=(0,r.useState)([]),[D,T]=(0,r.useState)(!0),[E,U]=(0,r.useState)(1),[k,M]=(0,r.useState)(1),[O,R]=(0,r.useState)("Price DESC"),[J,z]=(0,r.useState)("all"!==N?Number.parseInt(N):null),[L,Z]=(0,r.useState)("all"!==j?Number.parseInt(j):null),[B,G]=(0,r.useState)(x),[X,Y]=(0,r.useState)(x),[K,ee]=(0,r.useState)([]),[et,es]=(0,r.useState)([]),[ea,er]=(0,r.useState)({min:null,max:null}),[el,ei]=(0,r.useState)(!1),[en,eo]=(0,r.useState)(null),[ec,ed]=(0,r.useState)([]),[eu,em]=(0,r.useState)({}),[eh,ep]=(0,r.useState)({}),ex=(0,r.useCallback)((h=e=>{G(e),U(1)},function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];clearTimeout(p),p=setTimeout(()=>{h(...t)},500)}),[]);(0,r.useEffect)(()=>{ey(),eN(),ej()},[E,O,J,L,ea,B]),(0,r.useEffect)(()=>{C.length>0&&eg()},[eu,C]);let eg=()=>{let e=[...C];Object.entries(eu).forEach(t=>{let[s,a]=t;a.length>0&&(e=e.filter(e=>!!e.Attributes&&0!==e.Attributes.length&&e.Attributes.some(e=>e.AttributeName===s&&a.includes(e.AttributeValueID))))}),A(e),M(Math.ceil(e.length/12))},ef=e=>{let t=new Map;e.forEach(e=>{e.Attributes&&e.Attributes.length>0&&e.Attributes.forEach(e=>{let s="".concat(e.AttributeName,"|").concat(e.DisplayName);t.has(s)||t.set(s,new Map);let a=t.get(s),r=a.get(e.AttributeValueID);r?r.count++:a.set(e.AttributeValueID,{text:e.AttributeValueText,count:1})})});let s=[];t.forEach((e,t)=>{let[a,r]=t.split("|"),l=Array.from(e.entries()).map(e=>{let[t,s]=e;return{id:t,text:s.text,count:s.count}});s.push({attributeName:a,displayName:r,values:l.sort((e,t)=>e.text.localeCompare(t.text))})}),ed(s.sort((e,t)=>e.displayName.localeCompare(t.displayName)))},ey=async()=>{T(1===E),ei(E>1),eo(null);try{let e={requestParameters:{SearchTerm:B||"",SizeID:null,ColorID:null,CategoryID:J,TagID:null,ManufacturerID:null,producttypeId:L,MinPrice:ea.min,MaxPrice:ea.max,Rating:null,OrderByColumnName:(e=>{switch(e){case"Price ASC":return 1;case"Price DESC":default:return 0;case"ProductName ASC":return 2;case"ProductName DESC":return 3;case"Rating DESC":return 4}})(O),PageNo:1,PageSize:1e3,recordValueJson:"[]"}};console.log("Sending request to Next.js API route:",e);let t=await fetch("/api/products/get-products",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(console.log("API route response status:",t.status),!t.ok){let e=await t.json();throw Error(e.error||"HTTP error! status: ".concat(t.status))}let s=await t.json();if(console.log("API route response data:",s),s&&s.data){let e=[];try{if("string"==typeof s.data?(console.log("Parsing string data:",s.data),e=JSON.parse(s.data)):Array.isArray(s.data)?(console.log("Using array data directly"),e=s.data):(console.log("Converting object to array:",s.data),e=[s.data]),console.log("Processed products data:",e),!Array.isArray(e))throw Error("Parsed data is not an array");if(0===e.length){console.log("No products found in API response"),A([]),I([]),M(0);return}console.log("First product in response:",e[0]),console.log("Available fields:",Object.keys(e[0]));let t=e.map(e=>{let t=e.ProductImagesUrl||e.ProductImageUrl,s=null;try{if(t){let e=t;if("string"==typeof t&&(t.startsWith("[")||t.startsWith('"')))try{let s=JSON.parse(t);Array.isArray(s)&&s.length>0?e=s[0].AttachmentURL||s[0]:"string"==typeof s&&(e=s)}catch(s){e=t.replace(/^"|"/g,"")}if("string"==typeof e&&""!==e.trim()&&(e=e.replace(/^"|"$/g,"").trim())){let t=decodeURIComponent(e),a=t.startsWith("/")||t.startsWith("http")?t:"/".concat(t);s=a.startsWith("http")?a:"https://admin.codemedicalapps.com".concat(a)}}}catch(t){console.error("Error processing URL for product",e.ProductID||e.ProductId,":",t)}let a=e.ProductID||e.ProductId||e.Id||e.ID||e.id;return{...e,ProductId:a,ProductID:a,ProductName:e.ProductName||"Unnamed Product",Price:Number.parseFloat(e.Price)||0,OldPrice:e.OldPrice?Number.parseFloat(e.OldPrice):void 0,IQDPrice:Number.parseFloat(e.IQDPrice)||0,ProductTypeID:e.ProductTypeID,ProductTypeName:e.ProductTypeName,CategoryName:e.CategoryName||"Uncategorized",Rating:Number.parseFloat(e.Rating)||0,StockQuantity:Number.parseInt(e.StockQuantity,10)||0,ProductImageUrl:s,IsDiscountAllowed:!!e.IsDiscountAllowed,MarkAsNew:!!e.MarkAsNew,SellStartDatetimeUTC:e.SellStartDatetimeUTC||void 0,SellEndDatetimeUTC:e.SellEndDatetimeUTC||void 0,Attributes:e.Attributes||[],...e.DiscountPrice&&{DiscountPrice:Number.parseFloat(e.DiscountPrice)}}});if(console.log("Processed products:",t),I(t),A(t),ef(t),e.length>0&&(e[0].TotalRecords||e[0].totalRecords)){let t=e[0].TotalRecords||e[0].totalRecords;M(Math.ceil(Number.parseInt(t,10)/12))}}catch(e){throw console.error("Error parsing product data:",e),console.error("Raw response data:",s),eo("Error parsing data: ".concat(e.message||"Unknown")),e}}else console.warn("No data field in API response:",s),eo("API response missing data field"),A([]),I([])}catch(e){console.error("Error fetching products:",e),eo("API Error: ".concat(e.message||"Unknown")),P({description:"Failed to load products. Please try again.",type:"error"}),A([]),I([])}finally{ei(!1),T(!1)}},eN=async()=>{try{let e=await fetch("/api/categories",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({requestParameters:{recordValueJson:"[]"}})});if(!e.ok)throw Error("HTTP error! status: ".concat(e.status));let t=await e.json();if(t&&t.data){let e=JSON.parse(t.data);if(Array.isArray(e)){let t=e.map(e=>({id:e.CategoryID,name:e.Name}));ee(t)}}}catch(e){console.error("Error fetching categories:",e),ee([{id:1063,name:"Surgery and its subspecialties"},{id:1026,name:"Nursing"},{id:1025,name:"Dentistry"},{id:1043,name:"Internal medicine"},{id:1024,name:"Pharmacology"},{id:1058,name:"Radiology"}])}},ej=async()=>{try{let e=await fetch("/api/product-types",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({requestParameters:{recordValueJson:"[]"}})});if(!e.ok)throw Error("HTTP error! status: ".concat(e.status));let t=await e.json();if(t&&t.data){let e=JSON.parse(t.data);Array.isArray(e)&&es(e)}}catch(e){console.error("Error fetching product types:",e),es([{producttypeID:1,Name:"Courses"},{producttypeID:2,Name:"Books"},{producttypeID:3,Name:"Journals"},{producttypeID:4,Name:"Medical Apps"}])}},ev=()=>{let e=new URLSearchParams;B&&e.append("search",B),null!==J&&e.append("category",J.toString()),null!==L&&e.append("productType",L.toString()),window.history.pushState({},"","".concat(window.location.pathname,"?").concat(e.toString()))},eb=e=>{U(e),window.scrollTo(0,0)};return(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold mb-6",children:null!==J&&K.length>0?"".concat((null===(t=K.find(e=>e.id===J))||void 0===t?void 0:t.name)||"Category"," Products"):"All Products"}),(0,a.jsx)("div",{className:"bg-white p-6 rounded-lg shadow mb-6",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-4",children:[(0,a.jsx)(i.A,{className:"h-5 w-5 text-gray-600"}),(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"Filter by Product Type"})]}),(0,a.jsxs)("div",{className:"flex justify-center gap-3 flex-wrap",children:[(0,a.jsx)(c.$,{variant:null===L?"default":"outline",size:"lg",onClick:()=>{Z(null),U(1),ev()},className:"min-w-[120px]",children:"All Types"}),(0,a.jsx)(c.$,{variant:1===L?"default":"outline",size:"lg",onClick:()=>{Z(1),U(1),ev()},className:"min-w-[120px]",children:"Courses"}),(0,a.jsx)(c.$,{variant:2===L?"default":"outline",size:"lg",onClick:()=>{Z(2),U(1),ev()},className:"min-w-[120px]",children:"Books"}),(0,a.jsx)(c.$,{variant:3===L?"default":"outline",size:"lg",onClick:()=>{Z(3),U(1),ev()},className:"min-w-[120px]",children:"Journals"}),(0,a.jsx)(c.$,{variant:4===L?"default":"outline",size:"lg",onClick:()=>{Z(4),U(1),ev()},className:"min-w-[120px]",children:"Medical Apps"})]})]})}),en&&(0,a.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 text-yellow-700 p-4 mb-6 rounded-md",children:[(0,a.jsxs)("div",{className:"flex items-center mb-2",children:[(0,a.jsx)(n.A,{className:"h-5 w-5 mr-2"}),(0,a.jsx)("h3",{className:"font-semibold",children:"API Notice"})]}),(0,a.jsx)("p",{className:"text-sm",children:en}),(0,a.jsx)("div",{className:"mt-3 flex gap-2",children:(0,a.jsx)(c.$,{size:"sm",onClick:()=>ey(),children:"Retry API Call"})})]}),(0,a.jsxs)("div",{className:"mb-6",children:[B&&(0,a.jsxs)("p",{className:"text-lg",children:["Search results for: ",(0,a.jsxs)("span",{className:"font-semibold",children:['"',B,'"']}),null!==J&&K.length>0&&(0,a.jsxs)("span",{children:[" in ",(null===(s=K.find(e=>e.id===J))||void 0===s?void 0:s.name)||"selected category"]}),null!==L&&et.length>0&&(0,a.jsxs)("span",{children:[" - ",(null===(l=et.find(e=>e.producttypeID===L))||void 0===l?void 0:l.Name)||"selected type"]})]}),!B&&(null!==J||null!==L)&&(0,a.jsxs)("p",{className:"text-lg",children:["Browsing:",null!==J&&K.length>0&&(0,a.jsx)("span",{className:"font-semibold ml-1",children:(null===(u=K.find(e=>e.id===J))||void 0===u?void 0:u.name)||"selected category"}),null!==L&&et.length>0&&(0,a.jsxs)("span",{className:"font-semibold ml-1",children:[null!==J?" - ":" ",(null===(m=et.find(e=>e.producttypeID===L))||void 0===m?void 0:m.Name)||"selected type"]})]})]}),(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[(0,a.jsx)("div",{className:"lg:w-1/4 space-y-6",children:(0,a.jsxs)("div",{className:"bg-white p-4 rounded-lg shadow",children:[(0,a.jsxs)("h2",{className:"text-lg font-semibold mb-4 flex items-center",children:[(0,a.jsx)(o.A,{className:"mr-2 h-5 w-5"}),"Filters"]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium mb-2",children:"Category"}),(0,a.jsxs)(g,{value:(null==J?void 0:J.toString())||"all",onValueChange:e=>{z("all"===e?null:Number(e)),U(1),ev()},children:[(0,a.jsx)(y,{children:(0,a.jsx)(f,{placeholder:"All Categories"})}),(0,a.jsxs)(v,{children:[(0,a.jsx)(b,{value:"all",children:"All Categories"}),K.map(e=>(0,a.jsx)(b,{value:e.id.toString(),children:e.name},e.id))]})]})]}),(0,a.jsxs)("div",{className:"hidden",children:[(0,a.jsx)("h3",{className:"font-medium mb-2",children:"Price Range"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"number",placeholder:"Min",className:"w-full p-2 border rounded",onChange:e=>er({...ea,min:e.target.value?Number.parseFloat(e.target.value):null})}),(0,a.jsx)("span",{children:"-"}),(0,a.jsx)("input",{type:"number",placeholder:"Max",className:"w-full p-2 border rounded",onChange:e=>er({...ea,max:e.target.value?Number.parseFloat(e.target.value):null})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium mb-2",children:"Sort By"}),(0,a.jsxs)(g,{value:O,onValueChange:R,children:[(0,a.jsx)(y,{children:(0,a.jsx)(f,{})}),(0,a.jsxs)(v,{children:[(0,a.jsx)(b,{value:"ProductName ASC",children:"Name: A to Z"}),(0,a.jsx)(b,{value:"ProductName DESC",children:"Name: Z to A"}),(0,a.jsx)(b,{value:"Rating DESC",children:"Rating: High to Low"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium mb-2",children:"Search"}),(0,a.jsx)("input",{type:"text",placeholder:"Search products...",value:X,onChange:e=>{Y(e.target.value),ex(e.target.value)},className:"w-full p-2 border rounded"})]}),(0,a.jsx)(c.$,{className:"w-full",onClick:()=>{z(null),Z(null),er({min:null,max:null}),R("Price DESC"),U(1),G(""),Y(""),em({}),ep({}),window.history.pushState({},"",window.location.pathname)},disabled:el,children:"Reset Filters"})]})]})}),(0,a.jsxs)("div",{className:"lg:w-3/4",children:[(0,a.jsx)("div",{className:"grid grid-cols-2 gap-4 sm:gap-6 sm:grid-cols-2 lg:grid-cols-3",children:D?Array(12).fill(0).map((e,t)=>(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,a.jsx)("div",{className:"aspect-square",children:(0,a.jsx)(d.E,{className:"h-full w-full"})}),(0,a.jsxs)("div",{className:"p-4 space-y-2",children:[(0,a.jsx)(d.E,{className:"h-4 w-full"}),(0,a.jsx)(d.E,{className:"h-4 w-3/4"}),(0,a.jsx)(d.E,{className:"h-6 w-1/3"})]}),(0,a.jsx)("div",{className:"p-4 pt-0",children:(0,a.jsxs)("div",{className:"flex w-full gap-2",children:[(0,a.jsx)(d.E,{className:"h-10 flex-1"}),(0,a.jsx)(d.E,{className:"h-10 w-10"})]})})]},"skeleton-".concat(t))):(()=>{let e=(E-1)*12;return S.slice(e,e+12)})().map(e=>e.ProductId?(0,a.jsx)(F,{product:e},e.ProductId):null)}),!D&&0===S.length&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold mb-2",children:en?"Failed to load products":"No products found"}),(0,a.jsx)("p",{className:"text-gray-500",children:en?"Please check your connection and try again":"Try adjusting your filters or search criteria"}),en&&(0,a.jsx)(c.$,{className:"mt-4",onClick:()=>ey(),children:"Retry"})]}),!D&&S.length>0&&(()=>{let e=[],t=Math.max(1,E-Math.floor(2.5)),s=Math.min(k,t+5-1);s-t+1<5&&(t=Math.max(1,s-5+1));for(let r=t;r<=s;r++)e.push((0,a.jsx)(_,{children:(0,a.jsx)(V,{onClick:()=>eb(r),isActive:E===r,children:r})},r));return(0,a.jsx)(q,{className:"mt-8",children:(0,a.jsxs)(W,{children:[(0,a.jsx)(_,{children:(0,a.jsx)($,{onClick:()=>E>1&&eb(E-1),className:1===E?"pointer-events-none opacity-50":"cursor-pointer"})}),t>1&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(_,{children:(0,a.jsx)(V,{onClick:()=>eb(1),children:"1"})}),t>2&&(0,a.jsx)(H,{})]}),e,s<k&&(0,a.jsxs)(a.Fragment,{children:[s<k-1&&(0,a.jsx)(H,{}),(0,a.jsx)(_,{children:(0,a.jsx)(V,{onClick:()=>eb(k),children:k})})]}),(0,a.jsx)(_,{children:(0,a.jsx)(Q,{onClick:()=>E<k&&eb(E+1),className:E===k?"pointer-events-none opacity-50":"cursor-pointer"})})]})})})()]})]})]})}H.displayName="PaginationEllipsis"},8936:(e,t,s)=>{"use strict";s.d(t,{_:()=>n,e:()=>i});var a=s(5155),r=s(2115);let l=(0,r.createContext)(void 0);function i(e){let{children:t}=e,[s,i]=(0,r.useState)([]),[n,o]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let e=localStorage.getItem("cart");if(e)try{i(JSON.parse(e))}catch(e){console.error("Failed to parse cart from localStorage:",e)}o(!0)},[]),(0,r.useEffect)(()=>{localStorage.setItem("cart",JSON.stringify(s))},[s]);let c=e=>{i(t=>t.filter(t=>t.id!==e))},d=s.reduce((e,t)=>e+t.quantity,0);(0,r.useEffect)(()=>{localStorage.setItem("cart",JSON.stringify(s))},[s]);let u=s.reduce((e,t)=>e+(t.discountPrice?Math.min(t.discountPrice,t.adjustedPrice):t.adjustedPrice)*t.quantity,0),m=s.reduce((e,t)=>e+(t.adjustedIqdPrice||t.iqdPrice||0)*t.quantity,0);return(0,a.jsx)(l.Provider,{value:{items:s,addToCart:function(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],a=arguments.length>3?arguments[3]:void 0,r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1500;i(l=>{let i=e.price,n=a||Math.round(e.price*r),o=n;s.forEach(t=>{if(t.PriceAdjustment&&t.PriceAdjustmentType){let s=e.originalPrice||e.price;switch(t.PriceAdjustmentType){case 1:i+=t.PriceAdjustment,o+=Math.round(t.PriceAdjustment*r);break;case 2:let a=s*t.PriceAdjustment/100;i+=a,o+=Math.round(a*r)}}});let c=l.findIndex(t=>{var a;return t.id===e.id&&JSON.stringify(null===(a=t.attributes)||void 0===a?void 0:a.sort((e,t)=>e.ProductAttributeID-t.ProductAttributeID))===JSON.stringify(null==s?void 0:s.sort((e,t)=>e.ProductAttributeID-t.ProductAttributeID))});if(!(c>=0))return[...l,{...e,iqdPrice:n,adjustedIqdPrice:Math.max(0,o),quantity:t,attributes:s,adjustedPrice:Math.max(0,i),originalPrice:e.originalPrice}];{let e=[...l];return e[c].quantity+=t,e}})},removeFromCart:c,updateQuantity:(e,t)=>{if(t<=0){c(e);return}i(s=>s.map(s=>s.id===e?{...s,quantity:t}:s))},clearCart:()=>{i([])},totalItems:d,subtotal:u,subtotalIQD:m,total:u,totalIQD:m,isHydrated:n},children:t})}function n(){let e=(0,r.useContext)(l);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e}},9286:(e,t,s)=>{"use strict";s.d(t,{E:()=>n});var a=s(5155),r=s(1027),l=s(9602);let i=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function n(e){let{className:t,variant:s,...r}=e;return(0,a.jsx)("div",{className:(0,l.cn)(i({variant:s}),t),...r})}},9355:(e,t,s)=>{"use strict";s.d(t,{Z:()=>i,n:()=>n});var a=s(5155),r=s(2115);let l=(0,r.createContext)(void 0);function i(e){let{children:t}=e,[s,i]=(0,r.useState)([]),[n,o]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{let e=localStorage.getItem("wishlist");if(e)try{i(JSON.parse(e))}catch(e){console.error("Failed to parse wishlist from localStorage:",e)}o(!0)},[]),(0,r.useEffect)(()=>{localStorage.setItem("wishlist",JSON.stringify(s))},[s]),(0,a.jsx)(l.Provider,{value:{wishlistItems:s,addToWishlist:e=>{s.includes(e)||i([...s,e])},removeFromWishlist:e=>{i(s.filter(t=>t!==e))},isInWishlist:e=>s.includes(e),totalItems:s.length,isHydrated:n},children:t})}function n(){let e=(0,r.useContext)(l);if(void 0===e)throw Error("useWishlist must be used within a WishlistProvider");return e}},9602:(e,t,s)=>{"use strict";s.d(t,{cn:()=>l});var a=s(3463),r=s(9795);function l(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,a.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[1345,7542,6392,6506,8441,6587,7358],()=>t(8373)),_N_E=e.O()}]);