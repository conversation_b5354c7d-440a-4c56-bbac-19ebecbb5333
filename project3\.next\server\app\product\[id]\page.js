(()=>{var e={};e.id=0,e.ids=[0],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5884:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var s=r(45512),a=r(59462);function i({className:e,...t}){return(0,s.jsx)("div",{className:(0,a.cn)("animate-pulse rounded-md bg-muted",e),...t})}},10119:(e,t,r)=>{Promise.resolve().then(r.bind(r,78381))},10617:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(41680).A)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20730:(e,t,r)=>{"use strict";r.d(t,{default:()=>O});var s=r(45512),a=r(58009),i=r(28531),n=r.n(i),l=r(85668),o=r(41680);let c=(0,o.A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);var d=r(10617),u=r(91124),m=r(10453);let x=(0,o.A)("Share2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]]);var h=r(43464),p=r(71918);let y=(0,o.A)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]);var g=r(87021),f=r(37778),b=r(60248),j=r(77252),v=r(91542),N=r(84194),w=r(31961),P=r(27725);let A=(0,o.A)("Pause",[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]]),k=(0,o.A)("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]),I=(0,o.A)("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]),$=(0,o.A)("Video",[["path",{d:"m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5",key:"ftymec"}],["rect",{x:"2",y:"6",width:"14",height:"12",rx:"2",key:"158x01"}]]);var M=r(52706),D=r(99905),C=r(59462);function E({media:e,className:t}){let[r,i]=(0,a.useState)(0),[n,l]=(0,a.useState)(!1),[o,c]=(0,a.useState)(!1),d=(0,a.useRef)(null),[u,m]=(0,a.useState)("all"),x=e.filter(e=>"all"===u||e.type===u),h=x[r]||e[0],p=x.length>1,y=()=>{i(e=>e===x.length-1?0:e+1),l(!1)},g=e=>{i(e),l(!1)},f=e.reduce((e,t)=>(e[t.type]=(e[t.type]||0)+1,e),{});return(0,s.jsxs)("div",{className:(0,C.cn)("flex flex-col gap-4",t),children:[(0,s.jsxs)("div",{className:"relative aspect-square w-full bg-gray-100 rounded-lg overflow-hidden",children:["image"===h.type?(0,s.jsx)("img",{src:h.url,alt:h.alt||"Product image",className:"w-full h-full object-contain"}):(0,s.jsxs)("div",{className:"relative w-full h-full",children:[(0,s.jsx)("video",{ref:d,src:h.url,className:"w-full h-full object-contain",controls:!1,onEnded:()=>{l(!1),p&&y()},onPlay:()=>l(!0),onPause:()=>l(!1),onLoadedData:()=>c(!0),playsInline:!0}),!o&&(0,s.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-gray-200",children:(0,s.jsx)("div",{className:"animate-pulse",children:"Loading video..."})}),(0,s.jsx)("button",{onClick:()=>{"video"===h.type&&d.current&&(n?d.current.pause():d.current.play(),l(!n))},className:(0,C.cn)("absolute inset-0 flex items-center justify-center transition-opacity",n?"opacity-0 hover:opacity-100":"opacity-80",!o&&"hidden"),"aria-label":n?"Pause":"Play",children:(0,s.jsx)("div",{className:"bg-black/50 text-white rounded-full p-3",children:n?(0,s.jsx)(A,{size:24}):(0,s.jsx)(k,{size:24})})})]}),(0,s.jsxs)("div",{className:"absolute top-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded flex items-center gap-1",children:["image"===h.type?(0,s.jsx)(I,{size:12}):(0,s.jsx)($,{size:12}),(0,s.jsx)("span",{children:"image"===h.type?"Image":"Video"})]}),p&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("button",{onClick:()=>{i(e=>0===e?x.length-1:e-1),l(!1)},className:"absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white text-gray-900 rounded-full p-2 shadow-md transition-all","aria-label":"Previous media",children:(0,s.jsx)(M.A,{size:20})}),(0,s.jsx)("button",{onClick:y,className:"absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white text-gray-900 rounded-full p-2 shadow-md transition-all","aria-label":"Next media",children:(0,s.jsx)(D.A,{size:20})})]})]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)("button",{onClick:()=>m("all"),className:(0,C.cn)("px-3 py-1 text-sm rounded-full border","all"===u?"bg-blue-600 text-white border-blue-600":"bg-white text-gray-700 border-gray-300 hover:bg-gray-50"),children:["All (",e.length,")"]}),f.image>0&&(0,s.jsxs)("button",{onClick:()=>m("image"),className:(0,C.cn)("px-3 py-1 text-sm rounded-full border flex items-center gap-1","image"===u?"bg-blue-600 text-white border-blue-600":"bg-white text-gray-700 border-gray-300 hover:bg-gray-50"),children:[(0,s.jsx)(I,{size:14}),(0,s.jsx)("span",{children:f.image})]}),f.video>0&&(0,s.jsxs)("button",{onClick:()=>m("video"),className:(0,C.cn)("px-3 py-1 text-sm rounded-full border flex items-center gap-1","video"===u?"bg-blue-600 text-white border-blue-600":"bg-white text-gray-700 border-gray-300 hover:bg-gray-50"),children:[(0,s.jsx)($,{size:14}),(0,s.jsx)("span",{children:f.video})]})]}),p&&(0,s.jsx)("div",{className:"flex gap-2 overflow-x-auto pb-2 -mx-2 px-2",children:x.map((e,t)=>(0,s.jsx)("button",{onClick:()=>g(t),className:(0,C.cn)("relative flex-shrink-0 w-16 h-16 rounded-md overflow-hidden border-2 transition-all",t===r?"border-blue-600 ring-2 ring-blue-400":"border-gray-200 hover:border-gray-400"),"aria-label":`View ${e.type} ${t+1}`,children:"image"===e.type?(0,s.jsx)("img",{src:e.thumbnail||e.url,alt:e.alt||"",className:"w-full h-full object-cover"}):(0,s.jsx)("div",{className:"relative w-full h-full bg-gray-200 flex items-center justify-center",children:(0,s.jsx)($,{size:16,className:"text-gray-500"})})},t))})]})}var S=r(5884);function q(){return(0,s.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,s.jsxs)("div",{className:"flex flex-col md:flex-row gap-8",children:[(0,s.jsxs)("div",{className:"md:w-1/2",children:[(0,s.jsx)(S.E,{className:"h-[400px] w-full rounded-lg"}),(0,s.jsx)("div",{className:"flex gap-2 mt-4",children:[void 0,void 0,void 0,void 0].map((e,t)=>(0,s.jsx)(S.E,{className:"h-20 w-20 rounded-lg"},t))})]}),(0,s.jsxs)("div",{className:"md:w-1/2 space-y-4",children:[(0,s.jsx)(S.E,{className:"h-8 w-3/4"}),(0,s.jsx)(S.E,{className:"h-6 w-1/2"}),(0,s.jsx)(S.E,{className:"h-4 w-full"}),(0,s.jsx)(S.E,{className:"h-4 w-full"}),(0,s.jsx)(S.E,{className:"h-4 w-3/4"}),(0,s.jsx)(S.E,{className:"h-10 w-1/3"}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(S.E,{className:"h-12 flex-1"}),(0,s.jsx)(S.E,{className:"h-12 w-12"}),(0,s.jsx)(S.E,{className:"h-12 w-12"})]})]})]})})}var R=r(45037);function J({error:e,retry:t}){return(0,s.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center text-center p-8 border rounded-lg bg-red-50",children:[(0,s.jsx)(R.A,{className:"h-12 w-12 text-red-500 mb-4"}),(0,s.jsx)("h1",{className:"text-2xl font-bold mb-2",children:"Error Loading Product"}),(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:e}),(0,s.jsxs)("div",{className:"flex gap-4",children:[(0,s.jsx)(g.$,{onClick:t,children:"Try Again"}),(0,s.jsx)(n(),{href:"/products",children:(0,s.jsxs)(g.$,{variant:"outline",children:[(0,s.jsx)(c,{className:"mr-2 h-4 w-4"}),"Back to Products"]})})]})]})})}let T=e=>{if(!e)return"/placeholder.svg?height=400&width=400";if(e.startsWith("http"))return e;let t=e.startsWith("/")?e:`/${e}`;return`https://admin.codemedicalapps.com${t}`},O=function({productId:e}){let t=(0,N._)(),r=(0,w.n)(),{rate:i}=(0,P.H)(),[o,A]=(0,a.useState)(null),[k,I]=(0,a.useState)(!0),[$,M]=(0,a.useState)(1),[D,C]=(0,a.useState)(""),[S,R]=(0,a.useState)([]),[O,Q]=(0,a.useState)(0),[V,_]=(0,a.useState)(!1),[F,L]=(0,a.useState)(!1),[z,U]=(0,a.useState)(null),[H,W]=(0,a.useState)("description"),[B,G]=(0,a.useState)(!1),[K,X]=(0,a.useState)(null),[Y,Z]=(0,a.useState)(()=>{let e={};return o?.AttributesJson&&o.AttributesJson.forEach(t=>{e[`${t.ProductAttributeID}_${t.AttributeValueID}`]=!0}),e}),ee=async()=>{I(!0),U(null);try{let t;let r={requestParameters:{ProductId:Number.parseInt(e,10),recordValueJson:"[]"}};console.log("Fetching product with ID:",e,"Request body:",r);try{t=await l.A.post("https://admin.codemedicalapps.com/api/v1/dynamic/dataoperation/get-product_detail",r,{headers:{Accept:"application/json","Content-Type":"application/json"}}),console.log("Direct API response:",t.data)}catch(e){console.log("Direct API failed, trying proxy route:",e),t=await l.A.post("/api/product-detail",r,{headers:{Accept:"application/json","Content-Type":"application/json"}}),console.log("Proxy API response:",t.data)}if(t.data){let r=(t.data.data,t.data);if(r&&r.data)try{let t=JSON.parse(r.data);if(console.log("Parsed product data:",t),t){let r=Array.isArray(t)?t[0]:t;if(r){if(r.AttributesJson&&"string"==typeof r.AttributesJson)try{r.AttributesJson=JSON.parse(r.AttributesJson)}catch(e){console.error("Error parsing AttributesJson:",e),r.AttributesJson=[]}else r.AttributesJson||(r.AttributesJson=[]);if(console.log("Product data with attributes:",r),A(r),r.ProductImagesJson&&r.ProductImagesJson.length>0){let e=r.ProductImagesJson.find(e=>e.IsPrimary)||r.ProductImagesJson[0];C(T(e.AttachmentURL))}if(r.VideoLink){console.log("Video links found:",r.VideoLink);let e=r.VideoLink.split(",").map(e=>e.trim()).map(e=>et(e));R(e),Q(0)}r.OrderMinimumQuantity>0&&M(r.OrderMinimumQuantity)}else console.error("No product data found in parsed response"),U(`Product with ID ${e} not found. Please check if this product exists.`)}else console.error("Invalid product data format - parsedData is null/undefined"),U("Invalid product data format")}catch(e){console.error("Error parsing product data:",e,"Raw data:",r.data),U("Error parsing product data")}else console.error("No data property in API response:",t.data),U("No data in API response")}else console.error("Empty response from API"),U("Empty response from server")}catch(e){console.error("Error fetching product:",e),e.response?(console.error("Server error:",e.response.status,e.response.data),U(`Server error: ${e.response.status} - ${e.response.data?.message||"Unknown error"}`)):e.request?(console.error("Network error:",e.request),U("Network error - please check your connection")):(console.error("Request setup error:",e.message),U(`Error: ${e.message}`))}finally{I(!1)}},et=e=>{if(!e)return"";if(e.includes("youtube.com")||e.includes("youtu.be"))return e;if(e.startsWith("http"))return`/api/video-proxy?url=${encodeURIComponent(e)}`;let t=e.startsWith("/")?e:`/${e}`;return`/api/video-proxy?url=${encodeURIComponent(`https://admin.codemedicalapps.com${t}`)}`},er=(0,a.useMemo)(()=>o?.AttributesJson?o.AttributesJson.reduce((e,t)=>{let r=t.ProductAttributeID;return e[r]||(e[r]=[]),e[r].push(t),e},{}):{},[o?.AttributesJson]),es=(e,t,r)=>{Z(s=>{let a={...s},i=`${e.ProductAttributeID}_${e.AttributeValueID}`;return r&&t&&Object.keys(s).forEach(t=>{t.startsWith(`${e.ProductAttributeID}_`)&&t!==i&&(a[t]=!1)}),a[i]=!!r||!s[i],a})},ea=(0,a.useCallback)(()=>{if(!o)return 0;let e=o.Price;return o.AttributesJson&&o.AttributesJson.length>0&&o.AttributesJson.forEach(t=>{if(Y[`${t.ProductAttributeID}_${t.AttributeValueID}`]&&"number"==typeof t.PriceAdjustment&&"number"==typeof t.PriceAdjustmentType)switch(t.PriceAdjustmentType){case 1:e+=t.PriceAdjustment;break;case 2:e+=o.Price*t.PriceAdjustment/100}}),Math.max(0,e)},[o,Y]);(0,a.useMemo)(()=>ea(),[ea]);let ei=(0,a.useMemo)(()=>{let e=[];return o?.ProductImagesJson?.length&&o.ProductImagesJson.forEach(t=>{e.push({type:"image",url:T(t.AttachmentURL),alt:o?.ProductName||"Product image",thumbnail:T(t.AttachmentURL)})}),S.forEach((t,r)=>{e.push({type:"video",url:t,alt:`${o?.ProductName||"Product"} - Video ${r+1}`,thumbnail:D||""})}),e},[o,S,D]),en=e=>{X(e),G(!0),setTimeout(()=>G(!1),300)},el=e=>{let t="flex items-center justify-center w-10 h-10 rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2",r="bg-gray-100 text-gray-400 cursor-not-allowed";if("increment"===e){let e=o&&$>=(o.OrderMaximumQuantity>0?Math.min(o.OrderMaximumQuantity,o.StockQuantity):o.StockQuantity);return`${t} ${e?r:"bg-primary text-white hover:bg-primary/90 focus:ring-primary/50"}`}{let e=o&&$<=(o.OrderMinimumQuantity>0?o.OrderMinimumQuantity:1);return`${t} ${e?r:"bg-primary text-white hover:bg-primary/90 focus:ring-primary/50"}`}};return k?(0,s.jsx)(q,{}):z?(0,s.jsx)(J,{error:z,retry:ee}):o?(0,s.jsxs)("div",{className:"container mx-auto py-8 px-4 w-full max-w-[1200px] overflow-x-hidden",children:[(0,s.jsx)(f.Qp,{className:"mb-6",children:(0,s.jsxs)(f.AB,{children:[(0,s.jsx)(f.J5,{children:(0,s.jsx)(f.w1,{asChild:!0,children:(0,s.jsx)(n(),{href:"/",children:"Home"})})}),(0,s.jsx)(f.tH,{}),(0,s.jsx)(f.J5,{children:(0,s.jsx)(f.w1,{asChild:!0,children:(0,s.jsx)(n(),{href:"/products",children:"Products"})})}),(0,s.jsx)(f.tH,{}),o.CategoryName&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(f.J5,{children:(0,s.jsx)(f.w1,{asChild:!0,children:(0,s.jsx)(n(),{href:`/products/category/${o.CategoryID}`,children:o.CategoryName})})}),(0,s.jsx)(f.tH,{})]}),(0,s.jsx)(f.J5,{children:(0,s.jsx)(f.tJ,{children:o.ProductName})})]})}),(0,s.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[(0,s.jsx)("div",{className:"lg:w-1/2",children:(0,s.jsx)(E,{media:ei,className:"w-full rounded-lg overflow-hidden"})}),(0,s.jsxs)("div",{className:"md:w-1/2",children:[(0,s.jsx)("h1",{className:"text-xl font-bold mb-2",children:o.ProductName}),(0,s.jsxs)("div",{className:"flex items-center mb-4",children:[(0,s.jsx)("div",{className:"flex",children:[void 0,void 0,void 0,void 0,void 0].map((e,t)=>(0,s.jsx)(d.A,{className:`w-4 h-4 ${t<Math.floor(o.Rating||0)?"text-yellow-400 fill-yellow-400":"text-gray-300"}`},t))}),(0,s.jsxs)("span",{className:"text-sm text-gray-500 ml-2",children:["(",o.Rating||0,") ",o.TotalReviews||0," reviews"]})]}),(()=>{if(!o)return null;let e=o.DiscountPrice&&o.DiscountPrice<o.Price,t=ea(),r=t!==o.Price&&t!==(o.DiscountPrice||o.Price);return(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsxs)("div",{className:"flex items-baseline gap-2",children:[(0,s.jsxs)("span",{className:"text-3xl font-bold text-primary",children:["$",e?(o.DiscountPrice||0).toFixed(2):t.toFixed(2)]}),e&&(0,s.jsxs)("span",{className:"text-lg text-gray-400 line-through",children:["$",o.Price.toFixed(2)]}),r&&!e&&(0,s.jsxs)("span",{className:"text-lg text-gray-400 line-through",children:["$",o.Price.toFixed(2)]}),e&&(0,s.jsxs)("span",{className:"ml-2 px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded",children:[Math.round((o.Price-(o.DiscountPrice||0))/o.Price*100),"% OFF"]})]}),o.PriceIQD&&(0,s.jsxs)("div",{className:"mt-1 text-lg font-medium text-gray-600",children:[o.PriceIQD.toLocaleString()," IQD"]}),o.PointNo&&o.PointNo>0&&(0,s.jsx)("div",{className:"mt-2",children:(0,s.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800",children:["Buy & Earn ",o.PointNo," $ credit"]})}),o.OldPrice&&o.OldPrice>o.Price&&(0,s.jsxs)("div",{className:"mt-1 text-sm text-gray-500",children:[(0,s.jsxs)("span",{className:"line-through",children:["$",o.OldPrice.toFixed(2)]}),(0,s.jsxs)("span",{className:"ml-2 text-green-600",children:[Math.round((o.OldPrice-(o.DiscountPrice||o.Price))/o.OldPrice*100),"% OFF"]})]})]})})(),o.ShortDescription&&(0,s.jsx)("div",{className:"prose prose-sm max-w-none mb-6",children:(0,s.jsx)("p",{children:o.ShortDescription})}),(0,s.jsxs)("div",{className:"mb-6 border-t border-gray-200 pt-4",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Product Details"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Choose your preferences from the options below."}),Object.entries(er).length>0?(0,s.jsx)("div",{className:"space-y-6",children:Object.entries(er).map(([e,t])=>(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("h4",{className:"text-sm font-medium text-gray-700",children:[t[0]?.DisplayName||t[0]?.AttributeName,":"]}),(0,s.jsx)("div",{className:"space-y-2 pl-4",children:t.map(r=>{let a=`${r.ProductAttributeID}_${r.AttributeValueID}`,i=!!Y[a],n=t.length>1;return(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("div",{className:"flex items-center h-5",children:(0,s.jsx)("input",{type:n?"radio":"checkbox",id:`attr-${a}`,name:`attr-group-${e}`,className:`h-4 w-4 ${n?"rounded-full":"rounded"} border-gray-300 text-primary focus:ring-primary`,checked:i,onChange:e=>es(r,e.target.checked,n)})}),(0,s.jsx)("div",{className:"ml-3 text-sm",children:(0,s.jsxs)("label",{htmlFor:`attr-${a}`,className:`font-medium ${i?"text-primary":"text-gray-700"}`,children:[r.AttributeValueText,(r.PriceAdjustment||0===r.PriceAdjustment)&&(0,s.jsxs)("span",{className:"ml-2 text-sm font-normal text-green-600",children:["(",1===r.PriceAdjustmentType?"+":"","$",r.PriceAdjustment," ",2===r.PriceAdjustmentType?"%":"",")"]})]})})]},a)})})]},`attr-group-${e}`))}):(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"No additional product details available."})]}),(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"mr-4 text-sm font-medium",children:"Quantity:"}),(0,s.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,s.jsx)("button",{onClick:()=>{if(o){let e=o.OrderMinimumQuantity>0?o.OrderMinimumQuantity:1;$>e?(M(e=>e-1),en("decrement")):v.oR.info(`Minimum quantity is ${e}`)}},className:el("decrement"),disabled:$<=(o.OrderMinimumQuantity||1),"aria-label":"Decrease quantity",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z",clipRule:"evenodd"})})}),(0,s.jsx)(()=>(0,s.jsxs)("div",{className:"relative flex items-center justify-center w-16",children:[(0,s.jsx)("span",{className:`text-lg font-medium transition-all duration-200 ${B?"scale-125 text-primary":"scale-100"}`,children:$}),B&&(0,s.jsx)("span",{className:`absolute text-xs font-bold text-primary transition-all duration-200 ${"increment"===K?"-top-6":"top-6"}`,children:"increment"===K?"+1":"-1"})]}),{}),(0,s.jsx)("button",{onClick:()=>{if(o){let e=o.OrderMaximumQuantity>0?Math.min(o.OrderMaximumQuantity,o.StockQuantity):o.StockQuantity;$<e?(M(e=>e+1),en("increment")):v.oR.info(`Maximum quantity of ${e} reached`)}},className:el("increment"),disabled:o.OrderMaximumQuantity>0?$>=Math.min(o.OrderMaximumQuantity,o.StockQuantity):$>=o.StockQuantity,"aria-label":"Increase quantity",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z",clipRule:"evenodd"})})})]}),o.OrderMinimumQuantity>1&&(0,s.jsxs)("span",{className:"ml-3 text-xs text-gray-500",children:["Min: ",o.OrderMinimumQuantity]}),o.OrderMaximumQuantity>0&&(0,s.jsxs)("span",{className:"ml-3 text-xs text-gray-500",children:["Max: ",Math.min(o.OrderMaximumQuantity,o.StockQuantity)]})]})}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,s.jsxs)("button",{type:"button",className:"flex-1 flex items-center justify-center gap-2 py-3 px-4 rounded-md bg-primary text-white hover:bg-primary/90 disabled:opacity-50 disabled:pointer-events-none",disabled:o.StockQuantity<=0||V,onClick:()=>{if(o){_(!0);try{let e=o.ProductImagesJson&&o.ProductImagesJson.length>0?T(o.ProductImagesJson[0].AttachmentURL):"/placeholder.jpg",r=(o.AttributesJson||[]).filter(e=>Y[`${e.ProductAttributeID}_${e.AttributeValueID}`]);t.addToCart({id:o.ProductId,name:o.ProductName,price:o.DiscountPrice||o.Price,discountPrice:o.DiscountPrice,image:e,originalPrice:o.Price},$,r,o.PriceIQD,i),v.oR.success(`${$} \xd7 ${o.ProductName} added to your cart`)}catch(e){console.error("Error adding to cart:",e),v.oR.error("Failed to add product to cart. Please try again.")}finally{_(!1)}}},children:[V?(0,s.jsx)("div",{className:"h-5 w-5 border-2 border-current border-t-transparent rounded-full animate-spin"}):(0,s.jsx)(u.A,{className:"h-5 w-5"}),(0,s.jsx)("span",{children:V?"Adding...":"Add to Cart"})]}),(0,s.jsxs)("button",{type:"button",className:"flex items-center justify-center gap-2 py-3 px-4 rounded-md border border-input bg-transparent hover:bg-accent hover:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none",disabled:F,onClick:()=>{if(o){L(!0);try{r.isInWishlist(o.ProductId)?(r.removeFromWishlist(o.ProductId),v.oR.success(`${o.ProductName} removed from wishlist`)):(r.addToWishlist(o.ProductId),v.oR.success(`${o.ProductName} added to wishlist`))}catch(e){console.error("Error updating wishlist:",e),v.oR.error("Failed to update wishlist. Please try again.")}finally{L(!1)}}},children:[F?(0,s.jsx)("div",{className:"h-5 w-5 border-2 border-current border-t-transparent rounded-full animate-spin"}):(0,s.jsx)(m.A,{className:"h-5 w-5",fill:o&&r.isInWishlist(o.ProductId)?"currentColor":"none"}),(0,s.jsx)("span",{className:"sr-only md:not-sr-only md:inline",children:F?"Updating...":o&&r.isInWishlist(o.ProductId)?"Remove":"Wishlist"})]}),(0,s.jsxs)("button",{type:"button",className:"flex items-center justify-center gap-2 py-3 px-4 rounded-md border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",onClick:()=>{navigator.share?navigator.share({title:o?.MetaTitle||o?.ProductName,text:o?.MetaDescription||`Check out this product: ${o?.ProductName}`,url:window.location.href}).catch(e=>console.error("Error sharing:",e)):(navigator.clipboard.writeText(window.location.href),v.oR.success("Product link copied to clipboard"))},children:[(0,s.jsx)(x,{className:"h-5 w-5"}),(0,s.jsx)("span",{className:"sr-only md:not-sr-only md:inline",children:"Share"})]})]}),o.MetaKeywords&&(0,s.jsxs)("div",{className:"mt-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200",children:[(0,s.jsx)("h3",{className:"font-medium text-gray-900 mb-3",children:"Product Tags"}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:o.MetaKeywords.split(",").map((e,t)=>(0,s.jsx)(j.E,{variant:"secondary",className:"text-xs bg-white/70 hover:bg-white transition-colors",children:e.trim()},t))})]}),o.MetaDescription&&(0,s.jsxs)("div",{className:"mt-6 p-6 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border border-green-200",children:[(0,s.jsxs)("h3",{className:"font-medium text-gray-900 mb-3 flex items-center",children:[(0,s.jsx)(h.A,{className:"h-5 w-5 text-green-600 mr-2"}),"About This Product"]}),(0,s.jsx)("p",{className:"text-gray-700 leading-relaxed",children:o.MetaDescription})]})]})]}),(0,s.jsx)("div",{className:"mt-12",children:(0,s.jsxs)(b.tU,{defaultValue:"description",className:"w-full",value:H,onValueChange:W,children:[(0,s.jsxs)(b.j7,{className:"grid w-full grid-cols-3 mb-6 gap-2",children:[(0,s.jsx)(b.Xi,{value:"description",className:"shadow-sm hover:shadow transition-shadow",children:"Description"}),(0,s.jsx)(b.Xi,{value:"reviews",className:"shadow-sm hover:shadow transition-shadow",children:"Reviews"}),(0,s.jsx)(b.Xi,{value:"shipping",className:"shadow-sm hover:shadow transition-shadow",children:"Shipping & Returns"})]}),(0,s.jsx)(b.av,{value:"description",className:"mt-4 bg-white rounded-lg shadow-sm",children:(0,s.jsx)("div",{className:"p-8",children:o.FullDescription?(0,s.jsx)("div",{className:"prose max-w-none",dangerouslySetInnerHTML:{__html:o.FullDescription}}):(0,s.jsx)("p",{className:"text-gray-500 italic",children:"No description available for this product."})})}),(0,s.jsx)(b.av,{value:"reviews",className:"mt-4 bg-white rounded-lg shadow-sm",children:(0,s.jsx)("div",{className:"p-8",children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-4",children:[(0,s.jsx)("div",{className:"flex items-center",children:[1,2,3,4,5].map(e=>(0,s.jsx)(d.A,{className:`w-6 h-6 ${e<=Math.floor(o.Rating||0)?"text-yellow-400 fill-yellow-400":"text-gray-300"}`},e))}),(0,s.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,s.jsx)("span",{className:"font-medium",children:o.Rating?.toFixed(1)||"0.0"})," out of 5",o.TotalReviews?(0,s.jsxs)("span",{children:[" • ",o.TotalReviews," review",1!==o.TotalReviews?"s":""]}):null]})]}),(0,s.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,s.jsx)("h4",{className:"font-medium mb-2",children:"Customer Reviews"}),o.TotalReviews?(0,s.jsx)("div",{className:"space-y-4",children:(0,s.jsx)("div",{className:"text-center py-8 text-gray-500",children:"Reviews will be displayed here"})}):(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("p",{className:"text-gray-500 mb-4",children:"No reviews yet"}),(0,s.jsx)(g.$,{variant:"outline",size:"sm",children:"Be the first to review"})]})]})]})})}),(0,s.jsx)(b.av,{value:"shipping",className:"mt-4 bg-white rounded-lg shadow-sm",children:(0,s.jsx)("div",{className:"p-8",children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("p",{className:"text-gray-600",children:"Shipping and delivery information will be provided during checkout based on your location."}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4",children:[(0,s.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,s.jsx)(p.A,{className:"h-6 w-6 text-primary mb-2"}),(0,s.jsx)("h4",{className:"font-medium mb-1",children:"Fast Delivery"}),(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:["Estimated delivery time: ",o.EstimatedShippingDays||"3-5"," business days"]})]}),o.IsReturnAble&&(0,s.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,s.jsx)(y,{className:"h-6 w-6 text-primary mb-2"}),(0,s.jsx)("h4",{className:"font-medium mb-1",children:"Easy Returns"}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Hassle-free returns within 30 days"})]})]})]})})})]})})]}):(0,s.jsxs)("div",{className:"container mx-auto px-4 py-8 text-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"Product Not Found"}),(0,s.jsxs)("p",{className:"mb-6",children:['The product with ID "',e,'" could not be found. It may not exist in the database.']}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(n(),{href:"/products",children:(0,s.jsxs)(g.$,{children:[(0,s.jsx)(c,{className:"mr-2 h-4 w-4"}),"View All Products"]})}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Check the products list to find available product IDs"})]})]})}},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40383:(e,t,r)=>{Promise.resolve().then(r.bind(r,20730))},43464:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(41680).A)("Award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},45037:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(41680).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},52706:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(41680).A)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71918:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(41680).A)("Truck",[["path",{d:"M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2",key:"wrbu53"}],["path",{d:"M15 18H9",key:"1lyqi6"}],["path",{d:"M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14",key:"lysw3i"}],["circle",{cx:"17",cy:"18",r:"2",key:"332jqn"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}]])},74075:e=>{"use strict";e.exports=require("zlib")},76800:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,generateMetadata:()=>o});var s=r(62740),a=r(65859),i=r(78381);let n=e=>{if(!e)return"/placeholder.svg?height=400&width=400";if(e.startsWith("http"))return e;let t=e.startsWith("/")?e:`/${e}`;return`https://admin.codemedicalapps.com${t}`};async function l(e){try{let t=JSON.stringify({requestParameters:{ProductId:Number.parseInt(e,10),recordValueJson:"[]"}}),r={method:"post",maxBodyLength:Number.POSITIVE_INFINITY,url:"https://admin.codemedicalapps.com/api/v1/dynamic/dataoperation/get-product_detail",headers:{Accept:"application/json","Content-Type":"application/json"},data:t},s=await a.A.request(r);if(s.data&&s.data.data){let e=JSON.parse(s.data.data);return(Array.isArray(e)?e[0]:e)||null}return null}catch(e){return console.error("Error fetching product for metadata:",e),null}}async function o({params:e}){let{id:t}=await e,r=await l(t);if(!r)return{title:"Product Not Found - Medical Equipment",description:"The requested product could not be found."};let s=r.MetaTitle||`${r.ProductName} - Medical Equipment`,a=r.MetaDescription||r.ShortDescription||`Buy ${r.ProductName} at the best price. High-quality medical equipment with fast delivery.`,i=r.MetaKeywords||`${r.ProductName}, medical equipment, healthcare, ${r.CategoryName||"medical supplies"}`,o=r.ProductImagesJson&&r.ProductImagesJson.length>0?n(r.ProductImagesJson[0].AttachmentURL):"/placeholder.svg?height=400&width=400";return{title:s,description:a,keywords:i,openGraph:{title:s,description:a,type:"website",images:[{url:o,width:400,height:400,alt:r.ProductName}]},twitter:{card:"summary_large_image",title:s,description:a,images:[o]},other:{"product:price:amount":(r.DiscountPrice||r.Price).toString(),"product:price:currency":"USD","product:availability":r.StockQuantity>0?"in stock":"out of stock","product:condition":"new"}}}async function c({params:e}){let{id:t}=await e;return(0,s.jsx)(i.default,{productId:t})}},77252:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var s=r(45512),a=r(21643),i=r(59462);let n=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,...r}){return(0,s.jsx)("div",{className:(0,i.cn)(n({variant:t}),e),...r})}},78381:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\product\\[id]\\product-details-client.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},93878:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var s=r(70260),a=r(28203),i=r(25155),n=r.n(i),l=r(67292),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let c={children:["",{children:["product",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,76800)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\product\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,19611)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\product\\[id]\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/product/[id]/page",pathname:"/product/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[638,551,859,875,669],()=>r(93878));module.exports=s})();