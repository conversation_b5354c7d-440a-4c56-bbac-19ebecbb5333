(()=>{var e={};e.id=778,e.ids=[778],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10985:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34462:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\orders\\page.tsx","default")},38597:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>v});var r=t(45512),a=t(58009),i=t(37778),d=t(97643),l=t(87021),n=t(60248),c=t(28531),o=t.n(c),u=t(71901),x=t(46583),m=t(71918),p=t(45037),h=t(10985),f=t(16873);let j=(0,t(41680).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);var y=t(99905);function v(){let{t:e}=(0,u.t)(),[s,t]=(0,a.useState)("all"),c=[{id:"ORD-2023-1001",date:"2023-12-15",total:129.99,status:"delivered",items:[{name:"Medical Terminology Book",quantity:1,price:49.99},{name:"Anatomy Atlas",quantity:1,price:79.99}]},{id:"ORD-2023-0892",date:"2023-11-28",total:199.99,status:"processing",items:[{name:"Clinical Medicine Course",quantity:1,price:199.99}]},{id:"ORD-2023-0765",date:"2023-10-05",total:45.99,status:"cancelled",items:[{name:"Pharmacology Flashcards",quantity:1,price:45.99}]}],v="all"===s?c:c.filter(e=>e.status===s),g=e=>{switch(e){case"delivered":return(0,r.jsx)(x.A,{className:"h-5 w-5 text-green-500"});case"processing":return(0,r.jsx)(m.A,{className:"h-5 w-5 text-blue-500"});case"cancelled":return(0,r.jsx)(p.A,{className:"h-5 w-5 text-red-500"});default:return(0,r.jsx)(h.A,{className:"h-5 w-5 text-gray-500"})}},N=e=>{switch(e){case"delivered":return"Delivered";case"processing":return"Processing";case"cancelled":return"Cancelled";default:return"Unknown"}};return(0,r.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,r.jsx)(i.Qp,{className:"mb-6",children:(0,r.jsxs)(i.AB,{children:[(0,r.jsx)(i.J5,{children:(0,r.jsx)(i.w1,{asChild:!0,children:(0,r.jsx)(o(),{href:"/",children:e("home")})})}),(0,r.jsx)(i.tH,{}),(0,r.jsx)(i.J5,{children:(0,r.jsx)(i.tJ,{children:e("orders")})})]})}),(0,r.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold mb-6",children:e("orders")}),(0,r.jsx)(n.tU,{defaultValue:"all",className:"mb-6",onValueChange:t,children:(0,r.jsxs)(n.j7,{children:[(0,r.jsx)(n.Xi,{value:"all",children:"All Orders"}),(0,r.jsx)(n.Xi,{value:"processing",children:"Processing"}),(0,r.jsx)(n.Xi,{value:"delivered",children:"Delivered"}),(0,r.jsx)(n.Xi,{value:"cancelled",children:"Cancelled"})]})}),(0,r.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6",children:[(0,r.jsxs)("div",{className:"relative w-full md:w-64",children:[(0,r.jsx)(f.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,r.jsx)("input",{type:"text",placeholder:"Search orders...",className:"w-full pl-10 pr-4 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"text-sm text-muted-foreground",children:"Sort by:"}),(0,r.jsxs)("select",{className:"border border-input rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent",children:[(0,r.jsx)("option",{value:"date-desc",children:"Date (Newest)"}),(0,r.jsx)("option",{value:"date-asc",children:"Date (Oldest)"}),(0,r.jsx)("option",{value:"total-desc",children:"Amount (High to Low)"}),(0,r.jsx)("option",{value:"total-asc",children:"Amount (Low to High)"})]})]})]}),v.length>0?(0,r.jsx)("div",{className:"space-y-4",children:v.map(e=>(0,r.jsxs)(d.Zp,{className:"overflow-hidden",children:[(0,r.jsxs)("div",{className:"border-b border-border p-4 flex flex-col md:flex-row justify-between items-start md:items-center gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("h3",{className:"font-medium",children:e.id}),(0,r.jsxs)("div",{className:"flex items-center gap-1 px-2 py-1 rounded-full bg-muted text-xs",children:[g(e.status),(0,r.jsx)("span",{children:N(e.status)})]})]}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Ordered on ",e.date]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("p",{className:"font-medium",children:["$",e.total.toFixed(2)]}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:[e.items.length," item(s)"]})]}),(0,r.jsxs)(l.$,{variant:"outline",size:"sm",className:"flex items-center gap-1",children:[(0,r.jsx)(j,{className:"h-4 w-4"}),"Details",(0,r.jsx)(y.A,{className:"h-4 w-4"})]})]})]}),(0,r.jsxs)("div",{className:"p-4 bg-muted/30",children:[(0,r.jsx)("h4",{className:"text-sm font-medium mb-2",children:"Order Items"}),(0,r.jsx)("div",{className:"space-y-2",children:e.items.map((e,s)=>(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-muted rounded-md flex items-center justify-center",children:(0,r.jsx)(h.A,{className:"h-4 w-4"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm",children:e.name}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Qty: ",e.quantity]})]})]}),(0,r.jsxs)("p",{className:"text-sm font-medium",children:["$",e.price.toFixed(2)]})]},s))})]})]},e.id))}):(0,r.jsx)(d.Zp,{children:(0,r.jsxs)("div",{className:"p-8 text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(h.A,{className:"h-8 w-8 text-muted-foreground"})}),(0,r.jsx)("h3",{className:"text-lg font-medium mb-2",children:"No orders found"}),(0,r.jsxs)("p",{className:"text-muted-foreground mb-4",children:["You don't have any ","all"!==s?s:""," orders yet."]}),(0,r.jsx)(l.$,{asChild:!0,children:(0,r.jsx)(o(),{href:"/",children:"Continue Shopping"})})]})})]})]})}},45037:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},46583:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},54084:(e,s,t)=>{Promise.resolve().then(t.bind(t,38597))},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71918:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("Truck",[["path",{d:"M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2",key:"wrbu53"}],["path",{d:"M15 18H9",key:"1lyqi6"}],["path",{d:"M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14",key:"lysw3i"}],["circle",{cx:"17",cy:"18",r:"2",key:"332jqn"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}]])},72644:(e,s,t)=>{Promise.resolve().then(t.bind(t,34462))},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")},97602:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>d.a,__next_app__:()=>u,pages:()=>o,routeModule:()=>x,tree:()=>c});var r=t(70260),a=t(28203),i=t(25155),d=t.n(i),l=t(67292),n={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);t.d(s,n);let c={children:["",{children:["orders",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,34462)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\orders\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,19611)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"]}]}.children,o=["C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\orders\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/orders/page",pathname:"/orders",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},97643:(e,s,t)=>{"use strict";t.d(s,{Wu:()=>l,Zp:()=>d,wL:()=>n});var r=t(45512),a=t(58009),i=t(59462);let d=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));d.displayName="Card",a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...s})).displayName="CardHeader",a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("h3",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s})).displayName="CardTitle",a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("p",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",e),...s})).displayName="CardDescription";let l=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",e),...s}));l.displayName="CardContent";let n=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",e),...s}));n.displayName="CardFooter"}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[638,551,875,669],()=>t(97602));module.exports=r})();