(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6763],{2523:(e,t,r)=>{"use strict";r.d(t,{T:()=>a});let a={ADMIN_BASE_URL:"https://admin.codemedicalapps.com/",DYNAMIC_METHOD_SUB_URL:"api/",COMMON_CONTROLLER_SUB_URL:"api/common/",END_POINT_NAMES:{DOWNLOAD_DIGITAL_FILE:"downloadDigitalFile",GET_COUPON_CODE_DISCOUNT:"get-coupon-code-discount-value/calculate-coupon-discount"}}},2862:(e,t,r)=>{"use strict";r.d(t,{$g:()=>u,MakeApiCallAsync:()=>i,TS:()=>o,XX:()=>d,k6:()=>c});var a=r(2651),s=r(2523);a.A.defaults.timeout=3e4,"https:"===window.location.protocol&&s.T.ADMIN_BASE_URL.includes("localhost")&&(a.A.defaults.httpsAgent={rejectUnauthorized:!1});let o={ADMIN_BASE_URL:s.T.ADMIN_BASE_URL,API_VERSION:"v1",DYNAMIC_METHOD_SUB_URL:"api/v1/dynamic/dataoperation/",END_POINT_NAMES:{...s.T.END_POINT_NAMES,GET_CATEGORIES_LIST:"get-categories-list",SIGNUP_USER:"signup-user",GET_USER_LOGIN:"get-user-login",GET_USER_BY_PHONE:"get-user-by-phone",RESET_PASSWORD_BY_PHONE:"reset-password-by-phone",GET_HOME_SCREEN_BANNER:"get-home-screen-banner",GET_RECENT_PRODUCTS:"get-recents-products-list",GET_POPULAR_PRODUCTS:"get-popular-products-list",GET_HOT_DEAL_PRODUCTS:"get-hot-deal-products",GET_CAMPAIGNS_LIST:"get-web-campaign-list",GET_PRODUCTS_LIST:"get-products-list",GET_ALL_PRODUCTS:"api/v1/products/get-all-products",GET_MANUFACTURERS_LIST:"get-manufacturers-list",GET_TAGS_LIST:"get-tags-list",GET_CURRENCY_RATE:"get-currency-rate",GET_COUPON_CODE_DISCOUNT:"get-coupon-code-discount-value/calculate-coupon-discount"},COMMON_CONTROLLER_SUB_URL:"api/v1/common/"},n=async()=>localStorage.getItem("token")||null,l=async()=>localStorage.getItem("userId")||null,i=async function(e,t,r,s,i){arguments.length>5&&void 0!==arguments[5]&&arguments[5];try{let c={...s};if(!c.hasOwnProperty("Authorization")){let e=await n();e&&(c.Authorization="Bearer "+e)}if(!c.hasOwnProperty("Token")){let e=await n();c.Token=null!=e?e:""}if(!c.hasOwnProperty("UserID")){let e=await l();c.UserID=null!=e?e:""}c.hasOwnProperty("Accept")||(c.Accept="application/json"),c.hasOwnProperty("Content-Type")||(c["Content-Type"]="application/json");let d=o.ADMIN_BASE_URL+(null===t||void 0==t?o.DYNAMIC_METHOD_SUB_URL:t)+e;i=null!=i?i:"POST";let u={headers:c,responseType:"json",timeout:3e4,withCredentials:!1};if("POST"===i)return await a.A.post(d,r,u);if("GET"==i)return u.params=r,await a.A.get(d,u);return{data:{errorMessage:"Unsupported method type: ".concat(i),status:"method_not_supported"}}}catch(t){console.error("API call failed:",t);let e={data:{errorMessage:"An unexpected error occurred",status:"unknown_error"}};if(t&&"object"==typeof t&&"response"in t&&t.response){var c,d;let r=null===(c=t.response)||void 0===c?void 0:c.data;e.data={errorMessage:(null==r?void 0:r.errorMessage)||"An error occurred while processing your request.",status:null===(d=t.response)||void 0===d?void 0:d.status}}else if(t&&"object"==typeof t&&"request"in t){let r="Network error: No response received from server.";t.message&&t.message.includes("Network Error")&&(r="Network Error: This may be due to CORS policy restrictions, server unavailability, or an invalid SSL certificate. Please check that:\n1. The server is running and accessible\n2. The URL is correct: "+o.ADMIN_BASE_URL+"\n3. CORS is properly configured on the server\n4. If using HTTPS, the SSL certificate is valid"),e.data={errorMessage:r,status:"network_error"}}else{let r=t&&"object"==typeof t&&"message"in t?t.message:"An unexpected error occurred";e.data={errorMessage:r,status:"request_error"}}return e}},c=async()=>{try{let e=await i("getrate","api/v1/common/",{},{},"GET");if(e&&e.data&&!e.data.errorMessage)return parseInt(e.data)||1500;return 1500}catch(e){return console.error("Error fetching currency rate:",e),1500}},d=(e,t)=>Math.round(e*t),u=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD";return"IQD"===t?"".concat(e.toLocaleString()," IQD"):"$".concat(e.toFixed(2))}},3265:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m});var a=r(5155),s=r(2115),o=r(6046),n=r(9426),l=r(5007),i=r(8173),c=r.n(i),d=r(7110),u=r(2862);function m(){let{categoryId:e}=(0,o.useParams)(),{t,primaryColor:r}=(0,d.t)(),[i,m]=(0,s.useState)([]),[p,g]=(0,s.useState)(!0),[h,f]=(0,s.useState)("");return((0,s.useEffect)(()=>{(async()=>{try{var t;let r={"Content-Type":"application/json",Accept:"application/json",Authorization:"Bearer "+localStorage.getItem("token")},a=await (0,u.MakeApiCallAsync)("get-products-list",null,{PageNumber:1,PageSize:100,SortColumn:"Name",SortOrder:"ASC",CategoryId:e},r,"POST",!0);if(null==a?void 0:null===(t=a.data)||void 0===t?void 0:t.data){let e=JSON.parse(a.data.data),t=e.map(e=>{var t,r;return{id:e.ProductId,name:e.ProductName,price:e.Price,discountPrice:e.DiscountedPrice||void 0,image:null===(r=e.ProductImagesJson)||void 0===r?void 0:null===(t=r[0])||void 0===t?void 0:t.AttachmentURL,description:e.ShortDescription||void 0,categoryId:e.CategoryID}});m(t),e.length>0&&f(e[0].CategoryName||"")}}catch(e){console.error("Error fetching products:",e)}finally{g(!1)}})()},[e]),p)?(0,a.jsx)("div",{className:"container mx-auto py-8 px-4",children:"Loading..."}):(0,a.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,a.jsx)(n.Qp,{className:"mb-6",children:(0,a.jsxs)(n.AB,{children:[(0,a.jsx)(n.J5,{children:(0,a.jsx)(n.w1,{asChild:!0,children:(0,a.jsx)(c(),{href:"/",children:t("home")})})}),(0,a.jsx)(n.tH,{}),(0,a.jsx)(n.J5,{children:(0,a.jsx)(n.tJ,{children:h})})]})}),(0,a.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold mb-8",children:h}),0===i.length?(0,a.jsx)(l.Zp,{className:"p-6 text-center",children:(0,a.jsx)("p",{className:"text-muted-foreground",children:"No products found in this category."})}):(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:i.map(e=>(0,a.jsx)(l.Zp,{className:"overflow-hidden hover:shadow-lg transition-shadow",children:(0,a.jsxs)(c(),{href:"/product/".concat(e.id),children:[(0,a.jsx)("div",{className:"aspect-[3/4] relative bg-muted",children:(0,a.jsx)("img",{src:e.image?e.image.startsWith("http")?e.image:e.image.startsWith("/")?"".concat(u.TS.ADMIN_BASE_URL.replace(/\/$/,"")).concat(e.image):"".concat(u.TS.ADMIN_BASE_URL.replace(/\/$/,""),"/").concat(e.image):"/products/book".concat(e.id,".jpg"),alt:e.name,className:"object-cover w-full h-full",onError:t=>{let r=t.target;r.onerror=null,r.src="/products/book".concat(e.id,".jpg")}})}),(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsx)("h3",{className:"font-medium mb-2 line-clamp-2",children:e.name}),(0,a.jsx)("div",{className:"flex items-baseline gap-2",children:e.discountPrice?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("span",{className:"text-lg font-bold",style:{color:r},children:["$",e.discountPrice.toFixed(2)]}),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground line-through",children:["$",e.price.toFixed(2)]})]}):(0,a.jsxs)("span",{className:"text-lg font-bold",style:{color:r},children:["$",e.price.toFixed(2)]})})]})]})},e.id))})]})]})}},4858:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(7401).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},5007:(e,t,r)=>{"use strict";r.d(t,{Wu:()=>l,Zp:()=>n,wL:()=>i});var a=r(5155),s=r(2115),o=r(9602);let n=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,o.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...s})});n.displayName="Card",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",r),...s})}).displayName="CardHeader",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("h3",{ref:t,className:(0,o.cn)("text-2xl font-semibold leading-none tracking-tight",r),...s})}).displayName="CardTitle",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("p",{ref:t,className:(0,o.cn)("text-sm text-muted-foreground",r),...s})}).displayName="CardDescription";let l=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,o.cn)("p-6 pt-0",r),...s})});l.displayName="CardContent";let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,o.cn)("flex items-center p-6 pt-0",r),...s})});i.displayName="CardFooter"},6046:(e,t,r)=>{"use strict";var a=r(6658);r.o(a,"useParams")&&r.d(t,{useParams:function(){return a.useParams}}),r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})},6967:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(7401).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},7110:(e,t,r)=>{"use strict";r.d(t,{Z:()=>l,t:()=>i});var a=r(5155),s=r(2115);let o={en:{menu:"Menu",newsletter:"Newsletter",enterEmail:"Enter your email",newsletterDisclaimer:"Subscribe to our newsletter to receive updates and exclusive offers",popularCategories:"Popular Categories!",settings:"Settings",language:"Language",theme:"Theme",color:"Color",save:"Save",cancel:"Cancel",close:"Close",phone:"***************",phonenumber:"Phone Number",email:"<EMAIL>",liveChat:"Live Chat",welcome:"Welcome",logout:"Logout",login:"Login",signup:"Sign Up",signUp:"Sign Up",category:"Category",categories:"Categories",filters:"Filters",clearAll:"Clear All",products:"Products",loadingCategories:"Loading categories...",home:"Home",todayDeals:"Today's Deals",followUs:"Follow Us",aboutUs:"About Us",contactUs:"Contact Us",tryAgain:"Try Again",noProductsFound:"No products found",allProducts:"All Products",quickLinks:"Quick Links",about:"About Us",contact:"Contact Us",hotDeals:"Hot Deals",customerArea:"Customer Area",myAccount:"My Account",orders:"Orders",cart:"Cart",wishlist:"Wishlist",paymentMethods:"Payment Methods",location:"Location",callUs:"Call Us",emailUs:"Email Us",subscribe:"Subscribe",name:"Name",subject:"Subject",message:"Message",sendMessage:"Send Message",sending:"Sending...",messageSent:"Message sent successfully!",messageError:"Failed to send message",contactInfo:"Contact Information",address:"Address",findUs:"Find Us"},ar:{menu:"القائمة",popularCategories:"الفئات الشائعة!",settings:"الإعدادات",language:"اللغة",theme:"المظهر",color:"اللون",save:"حفظ",cancel:"إلغاء",close:"إغلاق",phone:"***************",phonenumber:"رقم الهاتف",email:"<EMAIL>",liveChat:"محادثة مباشرة",welcome:"مرحباً",logout:"تسجيل الخروج",login:"تسجيل الدخول",signup:"تسجيل جديد",signUp:"تسجيل جديد",category:"الفئة",categories:"الفئات",products:"المنتجات",loadingCategories:"جاري تحميل الفئات...",filters:"المرشحات",clearAll:"مسح الكل",home:"الرئيسية",todayDeals:"عروض اليوم",followUs:"تابعنا",aboutUs:"من نحن",contactUs:"اتصل بنا",tryAgain:"حاول مرة أخرى",noProductsFound:"لم يتم العثور على منتجات",allProducts:"جميع المنتجات",quickLinks:"روابط سريعة",about:"من نحن",contact:"اتصل بنا",hotDeals:"عروض ساخنة",customerArea:"منطقة العملاء",myAccount:"حسابي",orders:"الطلبات",cart:"السلة",wishlist:"المفضلة",paymentMethods:"طرق الدفع",location:"الموقع",callUs:"اتصل بنا",emailUs:"راسلنا",subscribe:"اشترك",name:"الاسم",subject:"الموضوع",message:"الرسالة",sendMessage:"إرسال الرسالة",sending:"جاري الإرسال...",messageSent:"تم إرسال الرسالة بنجاح!",messageError:"فشل في إرسال الرسالة",contactInfo:"معلومات الاتصال",address:"العنوان",findUs:"موقعنا"}},n=(0,s.createContext)(void 0);function l(e){let{children:t}=e,[r,l]=(0,s.useState)("light"),[i,c]=(0,s.useState)("en"),[d,u]=(0,s.useState)("#0074b2");return(0,s.useEffect)(()=>{document.documentElement.style.setProperty("--primary",d),document.documentElement.style.setProperty("--primary-foreground","#ffffff")},[]),(0,a.jsx)(n.Provider,{value:{theme:r,language:i,primaryColor:d,toggleTheme:()=>{l("light"===r?"dark":"light")},setLanguage:e=>{c(e),document.documentElement.dir="ar"===e?"rtl":"ltr"},setPrimaryColor:e=>{u(e)},t:e=>(function(e,t){let r=o[t];return e in r?r[e]:"en"!==t&&e in o.en?o.en[e]:e})(e,i)},children:t})}function i(){let e=(0,s.useContext)(n);if(void 0===e)throw Error("useSettings must be used within a SettingsProvider");return e}},7401:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(2115);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&r.indexOf(e)===t).join(" ")};var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,a.forwardRef)((e,t)=>{let{color:r="currentColor",size:s=24,strokeWidth:l=2,absoluteStrokeWidth:i,className:c="",children:d,iconNode:u,...m}=e;return(0,a.createElement)("svg",{ref:t,...n,width:s,height:s,stroke:r,strokeWidth:i?24*Number(l)/Number(s):l,className:o("lucide",c),...m},[...u.map(e=>{let[t,r]=e;return(0,a.createElement)(t,r)}),...Array.isArray(d)?d:[d]])}),i=(e,t)=>{let r=(0,a.forwardRef)((r,n)=>{let{className:i,...c}=r;return(0,a.createElement)(l,{ref:n,iconNode:t,className:o("lucide-".concat(s(e)),i),...c})});return r.displayName="".concat(e),r}},8890:(e,t,r)=>{Promise.resolve().then(r.bind(r,3265))},9426:(e,t,r)=>{"use strict";r.d(t,{AB:()=>c,J5:()=>d,Qp:()=>i,tH:()=>p,tJ:()=>m,w1:()=>u});var a=r(5155),s=r(2115),o=r(2317),n=r(6967),l=(r(4858),r(9602));let i=s.forwardRef((e,t)=>{let{...r}=e;return(0,a.jsx)("nav",{ref:t,"aria-label":"breadcrumb",...r})});i.displayName="Breadcrumb";let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("ol",{ref:t,className:(0,l.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",r),...s})});c.displayName="BreadcrumbList";let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("li",{ref:t,className:(0,l.cn)("inline-flex items-center gap-1.5",r),...s})});d.displayName="BreadcrumbItem";let u=s.forwardRef((e,t)=>{let{asChild:r,className:s,...n}=e,i=r?o.DX:"a";return(0,a.jsx)(i,{ref:t,className:(0,l.cn)("transition-colors hover:text-foreground",s),...n})});u.displayName="BreadcrumbLink";let m=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("span",{ref:t,role:"link","aria-disabled":"true","aria-current":"page",className:(0,l.cn)("font-normal text-foreground",r),...s})});m.displayName="BreadcrumbPage";let p=e=>{let{children:t,className:r,...s}=e;return(0,a.jsx)("li",{role:"presentation","aria-hidden":"true",className:(0,l.cn)("[&>svg]:size-3.5",r),...s,children:null!=t?t:(0,a.jsx)(n.A,{})})};p.displayName="BreadcrumbSeparator"},9602:(e,t,r)=>{"use strict";r.d(t,{cn:()=>o});var a=r(3463),s=r(9795);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[1345,2651,8441,6587,7358],()=>t(8890)),_N_E=e.O()}]);