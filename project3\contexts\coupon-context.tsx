'use client';

import React, { createContext, useContext, useState } from 'react';
import { MakeApiCallAsync, Config } from '@/lib/api-helper';
import { useCart } from './cart-context';

type Coupon = {
  code: string;
  discount: number;
  type: 'percentage' | 'fixed';
  minAmount?: number;
  expiryDate?: Date;
};

interface CouponContextType {
  appliedCoupon: Coupon | null;
  validateCoupon: (code: string, amount: number, cartItems?: any[]) => Promise<{ valid: boolean; message: string; discount: number }>;
  clearCoupon: () => void;
  isLoading: boolean;
}

const CouponContext = createContext<CouponContextType | undefined>(undefined);

export function CouponProvider({ children }: { children: React.ReactNode }) {
  const [appliedCoupon, setAppliedCoupon] = useState<Coupon | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const validateCoupon = async (code: string, amount: number, cartItems?: any[]): Promise<{ valid: boolean; message: string; discount: number }> => {
    if (!code.trim()) {
      return { valid: false, message: 'Please enter a coupon code', discount: 0 };
    }

    setIsLoading(true);

    try {
      // Prepare cart data from cart items
      const cartData = cartItems?.map(item => ({
        ProductId: item.id,
        ProductName: item.name,
        Price: item.adjustedPrice || item.price,
        Quantity: item.quantity,
        IsDiscountAllowed: true
      })) || [];

      const cartJsonData = JSON.stringify(cartData);

      const param = {
        requestParameters: {
          CouponCode: code.toUpperCase(),
          cartJsonData: cartJsonData
        }
      };

      const headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      };

      const response = await MakeApiCallAsync(
        Config.END_POINT_NAMES.GET_COUPON_CODE_DISCOUNT,
        Config.COMMON_CONTROLLER_SUB_URL,
        param,
        headers,
        'POST'
      );

      if (response && response.data && !response.data.errorMessage) {
        let couponData;

        // Parse the response data
        if (typeof response.data.data === 'string') {
          couponData = JSON.parse(response.data.data);
        } else {
          couponData = response.data.data;
        }

        if (couponData && couponData.DiscountValueAfterCouponAppliedWithQuantity > 0) {
          const discountAmount = couponData.DiscountValueAfterCouponAppliedWithQuantity;

          // Create coupon object for state
          const coupon: Coupon = {
            code: code.toUpperCase(),
            discount: discountAmount,
            type: 'fixed' // Assuming fixed amount from API
          };

          setAppliedCoupon(coupon);

          return {
            valid: true,
            message: 'Coupon applied successfully!',
            discount: discountAmount
          };
        } else {
          return {
            valid: false,
            message: 'Invalid coupon code or coupon not applicable to your cart',
            discount: 0
          };
        }
      } else {
        return {
          valid: false,
          message: response.data?.errorMessage || 'Failed to validate coupon',
          discount: 0
        };
      }
    } catch (error) {
      console.error('Coupon validation error:', error);
      return {
        valid: false,
        message: 'Error validating coupon. Please try again.',
        discount: 0
      };
    } finally {
      setIsLoading(false);
    }
  };

  const clearCoupon = () => {
    setAppliedCoupon(null);
  };

  return (
    <CouponContext.Provider
      value={{
        appliedCoupon,
        validateCoupon,
        clearCoupon,
        isLoading
      }}
    >
      {children}
    </CouponContext.Provider>
  );
}

export function useCoupon() {
  const context = useContext(CouponContext);
  if (context === undefined) {
    throw new Error('useCoupon must be used within a CouponProvider');
  }
  return context;
}