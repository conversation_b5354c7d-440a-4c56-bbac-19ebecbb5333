/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  // Disable static generation of API routes
  output: 'standalone',
  experimental: {
    outputFileTracingExcludes: {
      '*': ['node_modules/**/@swc/core-linux-x64-gnu', 'node_modules/**/@swc/core-linux-x64-musl'],
    },
  },
  // Exclude API routes from static generation
  exportPathMap: async function() {
    return {
      '/': { page: '/' },
      // Add other static pages here
      // Exclude API routes
    };
  },
  // Disable static optimization for all pages
  output: 'export',
  // Configure image optimization for static export
  images: {
    remotePatterns: [
      {
        protocol: 'http',
        hostname: '**',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: '**',
        pathname: '/**',
      },
    ],
    unoptimized: true
  },
  compiler: {
    styledComponents: true
  }
};

module.exports = nextConfig;
