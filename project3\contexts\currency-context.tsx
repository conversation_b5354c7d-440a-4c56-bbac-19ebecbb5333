'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { fetchCurrencyRate, convertUSDToIQD, formatPrice } from '@/lib/api-helper';

interface CurrencyContextType {
  rate: number;
  isLoading: boolean;
  convertToIQD: (usdPrice: number) => number;
  formatUSD: (price: number) => string;
  formatIQD: (price: number) => string;
  refreshRate: () => Promise<void>;
}

const CurrencyContext = createContext<CurrencyContextType | undefined>(undefined);

export function CurrencyProvider({ children }: { children: React.ReactNode }) {
  const [rate, setRate] = useState<number>(1500); // Default rate
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const loadCurrencyRate = async () => {
    setIsLoading(true);
    try {
      const fetchedRate = await fetchCurrencyRate();
      setRate(fetchedRate);
    } catch (error) {
      console.error('Failed to load currency rate:', error);
      // Keep default rate
    } finally {
      setIsLoading(false);
    }
  };

  const refreshRate = async () => {
    await loadCurrencyRate();
  };

  const convertToIQD = (usdPrice: number): number => {
    return convertUSDToIQD(usdPrice, rate);
  };

  const formatUSD = (price: number): string => {
    return formatPrice(price, 'USD');
  };

  const formatIQD = (price: number): string => {
    return formatPrice(price, 'IQD');
  };

  useEffect(() => {
    loadCurrencyRate();
  }, []);

  return (
    <CurrencyContext.Provider
      value={{
        rate,
        isLoading,
        convertToIQD,
        formatUSD,
        formatIQD,
        refreshRate
      }}
    >
      {children}
    </CurrencyContext.Provider>
  );
}

export function useCurrency() {
  const context = useContext(CurrencyContext);
  if (context === undefined) {
    throw new Error('useCurrency must be used within a CurrencyProvider');
  }
  return context;
}
