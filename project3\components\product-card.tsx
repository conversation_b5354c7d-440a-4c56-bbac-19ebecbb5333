"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { Star, ShoppingCart, Heart, Clock, Eye } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useCart } from "@/contexts/cart-context"
import { useWishlist } from "@/contexts/wishlist-context"
import { useToast } from "@/components/ui/toast"
import { useSettings } from "@/contexts/settings-context"

interface Product {
  ProductId: number
  ProductName: string
  Price: number
  OldPrice?: number
  DiscountPrice?: number
  Rating: number
  ProductImageUrl?: string
  CategoryName: string
  StockQuantity: number
  ProductTypeName?: string
  IQDPrice?: number
  IsDiscountAllowed?: boolean
  MarkAsNew?: boolean
  SellStartDatetimeUTC?: string
  SellEndDatetimeUTC?: string
}

interface ProductCardProps {
  product: Product
}

function CountdownTimer({ endDate }: { endDate: string }) {
  const [timeLeft, setTimeLeft] = useState<{
    days: number
    hours: number
    minutes: number
    seconds: number
  } | null>(null)

  useEffect(() => {
    const calculateTimeLeft = () => {
      const now = new Date().getTime()
      const end = new Date(endDate).getTime()
      const difference = end - now

      if (difference > 0) {
        return {
          days: Math.floor(difference / (1000 * 60 * 60 * 24)),
          hours: Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
          minutes: Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60)),
          seconds: Math.floor((difference % (1000 * 60)) / 1000),
        }
      }
      return null
    }

    const timer = setInterval(() => {
      setTimeLeft(calculateTimeLeft())
    }, 1000)

    // Initial calculation
    setTimeLeft(calculateTimeLeft())

    return () => clearInterval(timer)
  }, [endDate])

  const TimeBox = ({ value, label }: { value: number; label: string }) => (
    <div className="flex flex-col items-center mx-0.5">
      <div className="relative w-7 h-7 sm:w-8 sm:h-8 flex items-center justify-center bg-gradient-to-br from-pink-500 to-red-600 rounded-md shadow-md">
        <div className="absolute inset-0.5 bg-black/20 rounded-sm"></div>
        <span className="relative z-10 text-white font-bold text-xs sm:text-sm">
          {String(value).padStart(2, '0')}
        </span>
      </div>
      <span className="text-[9px] sm:text-[10px] text-white/80 mt-0.5 font-medium">
        {label}
      </span>
    </div>
  )

  if (!timeLeft) {
    return (
      <div className="px-3 py-1.5 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center">
        <Clock className="w-3.5 h-3.5 mr-1.5 text-white animate-pulse" />
        <span className="text-xs font-semibold text-white">Sale Ended</span>
      </div>
    )
  }

  return (
    <div className="bg-gradient-to-r from-pink-600/90 to-red-600/90 backdrop-blur-sm p-1.5 sm:p-2 rounded-lg shadow-lg">
      <div className="flex justify-center items-center space-x-0.5">
        {timeLeft.days > 0 && (
          <>
            <TimeBox value={timeLeft.days} label="Days" />
            <span className="text-white font-bold text-sm -mb-2">:</span>
          </>
        )}
        <TimeBox value={timeLeft.hours} label="Hrs" />
        <span className="text-white font-bold text-sm -mb-2">:</span>
        <TimeBox value={timeLeft.minutes} label="Min" />
        <span className="text-white font-bold text-sm -mb-2">:</span>
        <TimeBox value={timeLeft.seconds} label="Sec" />
      </div>
    </div>
  )
}

export default function ProductCard({ product }: ProductCardProps) {
  const cart = useCart()
  const wishlist = useWishlist()
  const { toast } = useToast()
  const { primaryColor } = useSettings()
  const [addingToCart, setAddingToCart] = useState(false)
  const [addingToWishlist, setAddingToWishlist] = useState(false)

  const handleAddToCart = () => {
    if (!cart.isHydrated) return

    setAddingToCart(true)
    try {
      const productImage = product.ProductImageUrl || "/placeholder.svg?height=300&width=300"
      cart.addToCart(
        {
          id: product.ProductId,
          name: product.ProductName,
          price: product.DiscountPrice || product.Price, // Use discount price if available
          discountPrice: product.DiscountPrice,
          image: productImage,
          originalPrice: product.Price, // Always store the original price
        },
        1,
        [], // No attributes by default
        product.IQDPrice // Pass IQD price if available
      )
      toast({ description: `${product.ProductName} added to cart`, type: "success" })
    } catch (error) {
      console.error("Error adding to cart:", error)
      toast({ description: "Failed to add product to cart", type: "error" })
    } finally {
      setTimeout(() => {
        setAddingToCart(false)
      }, 500)
    }
  }

  const handleAddToWishlist = () => {
    if (!wishlist.isHydrated) return

    setAddingToWishlist(true)
    try {
      const isInWishlist = wishlist.isInWishlist(product.ProductId)
      if (isInWishlist) {
        wishlist.removeFromWishlist(product.ProductId)
        toast({ description: `${product.ProductName} removed from wishlist`, type: "success" })
      } else {
        wishlist.addToWishlist(product.ProductId)
        toast({ description: `${product.ProductName} added to wishlist`, type: "success" })
      }
    } catch (error) {
      console.error("Error updating wishlist:", error)
      toast({ description: "Failed to update wishlist", type: "error" })
    } finally {
      setTimeout(() => {
        setAddingToWishlist(false)
      }, 500)
    }
  }

  const formatPrice = (price: number, currency: "USD" | "IQD" = "USD") => {
    if (currency === "IQD") {
      return `IQD ${price.toLocaleString()}`
    }
    return `$${price.toFixed(2)}`
  }

  const isOnSale = product.SellStartDatetimeUTC && product.SellEndDatetimeUTC
  const currentDate = new Date()
  const saleStartDate = product.SellStartDatetimeUTC ? new Date(product.SellStartDatetimeUTC) : null
  const saleEndDate = product.SellEndDatetimeUTC ? new Date(product.SellEndDatetimeUTC) : null
  const isSaleActive = saleStartDate && saleEndDate && currentDate >= saleStartDate && currentDate <= saleEndDate

  return (
    <Card className="overflow-hidden flex flex-col h-full relative">
      {/* Badges */}
      <div className="absolute top-2 left-2 z-10 flex flex-col gap-1">
        {product.MarkAsNew && (
          <Badge variant="secondary" className="bg-blue-500 text-white text-xs">
            New
          </Badge>
        )}
        {product.DiscountPrice && product.DiscountPrice > 0 && (
          <Badge variant="destructive" className="bg-red-500 text-white text-xs">
            Sale
          </Badge>
        )}
        {isSaleActive && !product.DiscountPrice && (
          <Badge variant="destructive" className="bg-red-500 text-white text-xs">
            Sale
          </Badge>
        )}
      </div>

      <Link href={`/product/${product.ProductId}`}>
        <div className="aspect-square overflow-hidden relative">
          <div className="h-full w-full relative">
            <Image
              src={product.ProductImageUrl || "/placeholder.svg?height=300&width=300"}
              alt={product.ProductName || "Product"}
              fill
              className="object-cover transition-transform hover:scale-105"
              onError={(e) => {
                const target = e.target as HTMLImageElement
                target.src = "/placeholder.svg?height=300&width=300"
              }}
              priority={false}
              loading="lazy"
            />
          </div>

          {/* Countdown Timer Overlay */}
          {isSaleActive && product.SellEndDatetimeUTC && (
            <div className="absolute bottom-0 left-0 right-0 p-2 flex justify-center">
              <CountdownTimer endDate={product.SellEndDatetimeUTC} />
            </div>
          )}
        </div>
      </Link>

      <CardContent className="pt-4 flex-grow">
        {/* Rating */}
        <div className="flex items-center mb-2">
          <div className="flex">
            {[...Array(5)].map((_, i) => (
              <Star
                key={i}
                className={`w-4 h-4 ${
                  i < Math.floor(product.Rating || 0) ? "text-yellow-400 fill-yellow-400" : "text-gray-300"
                }`}
              />
            ))}
          </div>
          <span className="text-xs text-gray-500 ml-1">({product.Rating || 0})</span>
        </div>

        {/* Product Name */}
        <Link href={`/product/${product.ProductId}`} className="hover:underline">
          <h3 className="font-semibold text-lg line-clamp-2 mb-2">{product.ProductName || "Unnamed Product"}</h3>
        </Link>

        {/* Product Type */}
        {product.ProductTypeName && <p className="text-sm text-gray-500 mb-2">Type: {product.ProductTypeName}</p>}

        {/* Pricing */}
        <div className="flex flex-col">
          <div className="flex items-center gap-2">
            {/* Current Price */}
            {product.DiscountPrice ? (
              <>
                <span className="text-lg font-bold text-red-500">{formatPrice(product.DiscountPrice)}</span>
                <span className="text-xs text-gray-500 line-through">{formatPrice(product.Price || 0)}</span>
              </>
            ) : product.OldPrice && product.OldPrice > product.Price ? (
              <>
                <span className="text-lg font-bold text-red-500">{formatPrice(product.Price || 0)}</span>
                <span className="text-xs text-gray-500 line-through">{formatPrice(product.OldPrice)}</span>
              </>
            ) : (
              <span className="text-lg font-bold text-primary">{formatPrice(product.Price || 0)}</span>
            )}
          </div>

          {/* IQD Price */}
          {product.IQDPrice && (
            <span className="text-sm font-medium text-green-600 mt-0.5">{formatPrice(product.IQDPrice, "IQD")}</span>
          )}
        </div>
      </CardContent>

      <CardFooter className="p-3 pt-1 mt-auto">
        <div className="w-full">
          <div className="flex items-center justify-between gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 px-2 text-xs font-medium flex-1 gap-1.5 text-white hover:opacity-90"
              style={{ backgroundColor: primaryColor }}
              asChild
            >
              <Link href={`/product/${product.ProductId}`}>
                <Eye className="h-3.5 w-3.5" />
                <span>View</span>
              </Link>
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 rounded-full"
              onClick={handleAddToWishlist}
              disabled={addingToWishlist}
            >
              <Heart
                className={`h-4 w-4 ${wishlist.isInWishlist(product.ProductId) ? "fill-red-500 text-red-500" : ""}`}
              />
            </Button>
          </div>
        </div>
      </CardFooter>
    </Card>
  )
}
