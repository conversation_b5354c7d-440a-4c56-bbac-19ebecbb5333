"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"48bf27e3853b\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xceWh5YXNvZnRcXERvd25sb2Fkc1xcZWNcXC5ORVQgOCBWZXJzaW9uIC0gTGF0ZXN0XFxwcm9qZWN0XFxjb2RlbWVkaWNhbFxccHJvamVjdDNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI0OGJmMjdlMzg1M2JcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/providers.tsx":
/*!**********************************!*\
  !*** ./components/providers.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _contexts_settings_context__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/settings-context */ \"(app-pages-browser)/./contexts/settings-context.tsx\");\n/* harmony import */ var _contexts_cart_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/cart-context */ \"(app-pages-browser)/./contexts/cart-context.tsx\");\n/* harmony import */ var _contexts_contact_info__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/contact-info */ \"(app-pages-browser)/./contexts/contact-info.tsx\");\n/* harmony import */ var _contexts_coupon_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/coupon-context */ \"(app-pages-browser)/./contexts/coupon-context.tsx\");\n/* harmony import */ var _contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/wishlist-context */ \"(app-pages-browser)/./contexts/wishlist-context.tsx\");\n/* harmony import */ var _contexts_currency_context__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/currency-context */ \"(app-pages-browser)/./contexts/currency-context.tsx\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\n\n\n\nfunction Providers(param) {\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_settings_context__WEBPACK_IMPORTED_MODULE_1__.SettingsProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_currency_context__WEBPACK_IMPORTED_MODULE_6__.CurrencyProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_cart_context__WEBPACK_IMPORTED_MODULE_2__.CartProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_contact_info__WEBPACK_IMPORTED_MODULE_3__.ContactProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_coupon_context__WEBPACK_IMPORTED_MODULE_4__.CouponProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_5__.WishlistProvider, {\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\providers.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\providers.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\providers.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\providers.tsx\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\providers.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\providers.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n_c = Providers;\nvar _c;\n$RefreshReg$(_c, \"Providers\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/providers.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./contexts/currency-context.tsx":
/*!***************************************!*\
  !*** ./contexts/currency-context.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CurrencyProvider: () => (/* binding */ CurrencyProvider),\n/* harmony export */   useCurrency: () => (/* binding */ useCurrency)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api_helper__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api-helper */ \"(app-pages-browser)/./lib/api-helper.ts\");\n/* __next_internal_client_entry_do_not_use__ CurrencyProvider,useCurrency auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst CurrencyContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction CurrencyProvider(param) {\n    let { children } = param;\n    _s();\n    const [rate, setRate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1500); // Default rate\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const loadCurrencyRate = async ()=>{\n        setIsLoading(true);\n        try {\n            const fetchedRate = await (0,_lib_api_helper__WEBPACK_IMPORTED_MODULE_2__.fetchCurrencyRate)();\n            setRate(fetchedRate);\n        } catch (error) {\n            console.error('Failed to load currency rate:', error);\n        // Keep default rate\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const refreshRate = async ()=>{\n        await loadCurrencyRate();\n    };\n    const convertToIQD = (usdPrice)=>{\n        return (0,_lib_api_helper__WEBPACK_IMPORTED_MODULE_2__.convertUSDToIQD)(usdPrice, rate);\n    };\n    const formatUSD = (price)=>{\n        return (0,_lib_api_helper__WEBPACK_IMPORTED_MODULE_2__.formatPrice)(price, 'USD');\n    };\n    const formatIQD = (price)=>{\n        return (0,_lib_api_helper__WEBPACK_IMPORTED_MODULE_2__.formatPrice)(price, 'IQD');\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CurrencyProvider.useEffect\": ()=>{\n            loadCurrencyRate();\n        }\n    }[\"CurrencyProvider.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CurrencyContext.Provider, {\n        value: {\n            rate,\n            isLoading,\n            convertToIQD,\n            formatUSD,\n            formatIQD,\n            refreshRate\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\contexts\\\\currency-context.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n_s(CurrencyProvider, \"07V9kDSpd6toiGdBXchpKUCZoWI=\");\n_c = CurrencyProvider;\nfunction useCurrency() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(CurrencyContext);\n    if (context === undefined) {\n        throw new Error('useCurrency must be used within a CurrencyProvider');\n    }\n    return context;\n}\n_s1(useCurrency, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"CurrencyProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./contexts/currency-context.tsx\n"));

/***/ })

});