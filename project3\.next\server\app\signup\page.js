(()=>{var e={};e.id=879,e.ids=[879],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4306:(e,r,t)=>{"use strict";t.d(r,{j:()=>o});var s=t(46722),a=t(33066);let i=(0,s.Wp)({apiKey:"AIzaSyBlr1YG3CXkyn3yUJ44xvFFCcpfSj0pwFU",authDomain:"codemedical-19ec6.firebaseapp.com",projectId:"codemedical-19ec6",storageBucket:"codemedical-19ec6.firebasestorage.app",messagingSenderId:"494556459416",appId:"1:494556459416:web:180c7e662f6e3ae4e43bed",measurementId:"G-BRC9LRF5DE"}),o=(0,a.xI)(i)},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},25409:(e,r,t)=>{"use strict";t.d(r,{p:()=>o});var s=t(45512),a=t(58009),i=t(59462);let o=a.forwardRef(({className:e,type:r,...t},a)=>(0,s.jsx)("input",{type:r,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...t}));o.displayName="Input"},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30979:(e,r,t)=>{Promise.resolve().then(t.bind(t,90087))},31659:(e,r,t)=>{Promise.resolve().then(t.bind(t,75395))},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},37067:e=>{"use strict";e.exports=require("node:http")},37540:e=>{"use strict";e.exports=require("node:console")},38522:e=>{"use strict";e.exports=require("node:zlib")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},47699:(e,r,t)=>{"use strict";t.d(r,{J:()=>d});var s=t(45512),a=t(58009),i=t(92405),o=t(21643),n=t(59462);let l=(0,o.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)(i.b,{ref:t,className:(0,n.cn)(l(),e),...r}));d.displayName=i.b.displayName},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55591:e=>{"use strict";e.exports=require("https")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},58022:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=t(70260),a=t(28203),i=t(25155),o=t.n(i),n=t(67292),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);t.d(r,l);let d={children:["",{children:["signup",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,75395)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\signup\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,19611)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\signup\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/signup/page",pathname:"/signup",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},74075:e=>{"use strict";e.exports=require("zlib")},75395:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\signup\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\signup\\page.tsx","default")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},90087:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>C});var s=t(45512),a=t(58009),i=t(4306),o=t(33066),n=t(97643),l=t(87021),d=t(25409),c=t(47699),u=t(36752),m=t.n(u);t(90895);var p=t(24540),x=t(28531),f=t.n(x),h=t(1734),g=t(92056),b=t(87798),y=t(92557),v=t(15907),j=t(8866),N=t(72734),w=t(21956),q=t(69208);function C(){let[e,r]=(0,a.useState)("phone"),[t,u]=(0,a.useState)("964"),[x,C]=(0,a.useState)("iq"),[k,P]=(0,a.useState)(""),[S,A]=(0,a.useState)(null),[E,_]=(0,a.useState)(0),[I,$]=(0,a.useState)(!1),[R,F]=(0,a.useState)(""),[L,D]=(0,a.useState)(!1),[J,V]=(0,a.useState)({firstName:"",lastName:"",email:"",password:"",mobileNo:t,cityId:"-999",stateProvinceId:"-999",countryId:"1"}),M=()=>{_(60)},T=async e=>{if(e.preventDefault(),$(!0),F(""),!t){F("Phone number is required"),$(!1);return}if(t.length<8){F("Phone number must be at least 8 digits"),$(!1);return}if(!/^\+?[1-9]\d{1,14}$/.test(t)){F("Please enter a valid phone number"),$(!1);return}try{let e=new o.kT(i.j,"recaptcha-container",{size:"invisible",callback:()=>{}}),s=`+${t}`,a=await (0,o.ik)(i.j,s,e);A(a),r("verification"),M()}catch(e){F(e.message||"Failed to send verification code"),console.error("Error:",e)}finally{$(!1)}},z=async()=>{if(!(E>0)){$(!0),F("");try{let e=Math.floor(1e5+9e5*Math.random()).toString();console.log("Mock verification code:",e),A({mockVerificationCode:e}),M()}catch(e){F("Failed to resend verification code"),console.error("Error:",e)}finally{$(!1)}}},G=async e=>{e.preventDefault(),$(!0),F("");try{(await S.confirm(k)).user&&r("details")}catch(e){F(e.message||"Invalid verification code"),console.error("Error:",e)}finally{$(!1)}},U=async e=>{if(e.preventDefault(),$(!0),F(""),!J.firstName.trim()){F("First name is required"),$(!1);return}if(!J.lastName.trim()){F("Last name is required"),$(!1);return}if(!J.email.trim()||!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(J.email)){F("Please enter a valid email address"),$(!1);return}if(!J.password||J.password.length<8){F("Password must be at least 8 characters long"),$(!1);return}try{let e={FirstName:J.firstName,LastName:J.lastName,EmailAddress:J.email,Password:J.password,MobileNo:J.mobileNo,CityId:J.cityId,StateProvinceId:J.stateProvinceId,CountryID:J.countryId};console.log("Request parameters:",e)}catch(e){F(e.message||"Failed to create account"),console.error("Error:",e)}finally{$(!1)}};return(0,s.jsx)("div",{className:"min-h-screen bg-background flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("h2",{className:"mt-6 text-3xl font-extrabold",children:["phone"===e&&"Get Started","verification"===e&&"Verify Your Phone","details"===e&&"Complete Your Profile"]}),(0,s.jsxs)("p",{className:"mt-2 text-sm text-muted-foreground",children:["phone"===e&&"Enter your phone number to create an account","verification"===e&&"Enter the code we sent to your phone","details"===e&&"Just a few more details to complete your account"]})]}),(0,s.jsxs)(n.Zp,{className:"mt-8 p-8 shadow-xl bg-card/100",children:[(0,s.jsx)("div",{className:"flex justify-center mb-8",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:`w-8 h-8 rounded-full flex items-center justify-center ${"phone"===e?"bg-primary text-primary-foreground":"bg-primary/20 text-primary"}`,children:(0,s.jsx)(h.A,{className:"w-4 h-4"})}),(0,s.jsx)("div",{className:`w-16 h-1 ${"phone"===e?"bg-primary/20":"bg-primary"}`}),(0,s.jsx)("div",{className:`w-8 h-8 rounded-full flex items-center justify-center ${"verification"===e?"bg-primary text-primary-foreground":"details"===e?"bg-primary":"bg-primary/20 text-primary"}`,children:(0,s.jsx)(g.A,{className:"w-4 h-4"})}),(0,s.jsx)("div",{className:`w-16 h-1 ${"details"===e?"bg-primary":"bg-primary/20"}`}),(0,s.jsx)("div",{className:`w-8 h-8 rounded-full flex items-center justify-center ${"details"===e?"bg-primary text-primary-foreground":"bg-primary/20 text-primary"}`,children:(0,s.jsx)(b.A,{className:"w-4 h-4"})})]})}),(0,s.jsxs)(p.P.div,{variants:{hidden:{opacity:0,x:-20},visible:{opacity:1,x:0},exit:{opacity:0,x:20}},initial:"hidden",animate:"visible",exit:"exit",transition:{duration:.3},children:["phone"===e&&(0,s.jsxs)("form",{onSubmit:T,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{className:"block text-sm font-medium mb-2 text-center",children:"Phone Number"}),(0,s.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,s.jsx)("div",{className:"w-full max-w-[300px]",children:(0,s.jsx)(m(),{country:x,value:t,onChange:e=>{u(e),F("")},enableSearch:!0,searchPlaceholder:"Search country...",containerClass:"w-full",inputClass:`w-full p-4 border rounded-lg focus:ring-2 focus:ring-primary/50 ${R?"border-destructive":""}`,buttonClass:"!border-input !bg-background hover:!bg-accent",dropdownClass:"!bg-background !border-input",disabled:I,countryCodeEditable:!1,isValid:(e,r)=>!!(e&&!(e.length<8)&&/^\+?[1-9]\d{1,14}$/.test(e))})}),R&&(0,s.jsx)("p",{className:"text-sm text-destructive",children:R})]})]}),(0,s.jsx)("div",{id:"recaptcha-container"}),(0,s.jsx)(l.$,{type:"submit",className:"w-full h-12 text-lg bg-primary text-primary-foreground hover:bg-primary/90 transition-colors flex items-center justify-center gap-2",disabled:I,children:I?(0,s.jsx)(y.A,{className:"w-4 h-4 animate-spin"}):(0,s.jsxs)(s.Fragment,{children:["Continue ",(0,s.jsx)(v.A,{className:"w-4 h-4"})]})}),(0,s.jsxs)("div",{className:"text-center text-sm mt-4",children:[(0,s.jsx)("span",{className:"text-muted-foreground",children:"Already have an account? "}),(0,s.jsx)(f(),{href:"/login",className:"text-primary hover:text-primary/80 hover:underline transition-colors",children:"Login"})]})]}),"verification"===e&&(0,s.jsxs)("form",{onSubmit:G,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{className:"block text-sm font-medium mb-4 text-center",children:"Verification Code"}),(0,s.jsx)("div",{className:"flex justify-center items-center gap-3",children:[...Array(6)].map((e,r)=>(0,s.jsx)(d.p,{type:"text",maxLength:1,className:"w-12 h-12 text-center text-2xl font-semibold rounded-lg focus:ring-2 focus:ring-primary/50 transition-all",value:k[r]||"",onChange:e=>{let t=k.split("");t[r]=e.target.value,P(t.join("")),e.target.value&&e.target.nextElementSibling&&e.target.nextElementSibling.focus()},disabled:I},r))}),(0,s.jsx)("div",{className:"mt-4 text-center",children:(0,s.jsx)("button",{type:"button",onClick:z,className:`text-sm ${E>0?"text-muted-foreground":"text-primary hover:underline"}`,disabled:E>0||I,children:E>0?`Resend code in ${(e=>{let r=Math.floor(e/60);return`${r}:${(e%60).toString().padStart(2,"0")}`})(E)}`:"Resend code"})})]}),(0,s.jsx)(l.$,{type:"submit",className:"w-full h-12 text-lg bg-primary text-primary-foreground hover:bg-primary/90 transition-colors flex items-center justify-center gap-2",disabled:I||6!==k.length,children:I?(0,s.jsx)(y.A,{className:"w-4 h-4 animate-spin"}):(0,s.jsxs)(s.Fragment,{children:["Verify ",(0,s.jsx)(g.A,{className:"w-4 h-4"})]})})]}),"details"===e&&(0,s.jsxs)("form",{onSubmit:U,className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{className:"block text-sm font-medium mb-2",children:"First Name"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(d.p,{type:"text",value:J.firstName,onChange:e=>V({...J,firstName:e.target.value}),className:"pl-10",required:!0,disabled:I}),(0,s.jsx)(b.A,{className:"w-4 h-4 absolute left-3 top-3 text-muted-foreground"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{className:"block text-sm font-medium mb-2",children:"Last Name"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(d.p,{type:"text",value:J.lastName,onChange:e=>V({...J,lastName:e.target.value}),className:"pl-10",required:!0,disabled:I}),(0,s.jsx)(b.A,{className:"w-4 h-4 absolute left-3 top-3 text-muted-foreground"})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{className:"block text-sm font-medium mb-2",children:"Email"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(d.p,{type:"email",value:J.email,onChange:e=>V({...J,email:e.target.value}),className:"pl-10",required:!0,disabled:I}),(0,s.jsx)(j.A,{className:"w-4 h-4 absolute left-3 top-3 text-muted-foreground"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{className:"block text-sm font-medium mb-2",children:"Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(d.p,{type:L?"text":"password",value:J.password,onChange:e=>V({...J,password:e.target.value}),className:"pl-10 pr-10",required:!0,minLength:8,disabled:I}),(0,s.jsx)(N.A,{className:"w-4 h-4 absolute left-3 top-3 text-muted-foreground"}),(0,s.jsxs)(l.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 hover:bg-transparent",onClick:()=>D(!L),disabled:I,children:[L?(0,s.jsx)(w.A,{className:"w-4 h-4 text-muted-foreground"}):(0,s.jsx)(q.A,{className:"w-4 h-4 text-muted-foreground"}),(0,s.jsx)("span",{className:"sr-only",children:L?"Hide password":"Show password"})]})]})]}),(0,s.jsx)(l.$,{type:"submit",className:"w-full h-12 text-lg bg-primary text-primary-foreground hover:bg-primary/90 transition-colors",disabled:I,children:I?(0,s.jsx)(y.A,{className:"w-4 h-4 animate-spin"}):"Create Account"})]})]},e)]})]})})}},94735:e=>{"use strict";e.exports=require("events")},97643:(e,r,t)=>{"use strict";t.d(r,{Wu:()=>n,Zp:()=>o,wL:()=>l});var s=t(45512),a=t(58009),i=t(59462);let o=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));o.displayName="Card",a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...r})).displayName="CardHeader",a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("h3",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r})).displayName="CardTitle",a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("p",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",e),...r})).displayName="CardDescription";let n=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",e),...r}));n.displayName="CardContent";let l=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",e),...r}));l.displayName="CardFooter"}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[638,551,400,151,875],()=>t(58022));module.exports=s})();