(()=>{var e={};e.id=977,e.ids=[977],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6531:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\contact\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\contact\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},14494:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(41680).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19523:(e,s,r)=>{Promise.resolve().then(r.bind(r,6531))},21820:e=>{"use strict";e.exports=require("os")},25409:(e,s,r)=>{"use strict";r.d(s,{p:()=>n});var t=r(45512),a=r(58009),i=r(59462);let n=a.forwardRef(({className:e,type:s,...r},a)=>(0,t.jsx)("input",{type:s,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...r}));n.displayName="Input"},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33595:(e,s,r)=>{Promise.resolve().then(r.bind(r,43198))},33873:e=>{"use strict";e.exports=require("path")},37778:(e,s,r)=>{"use strict";r.d(s,{AB:()=>d,J5:()=>c,Qp:()=>o,tH:()=>x,tJ:()=>u,w1:()=>m});var t=r(45512),a=r(58009),i=r(12705),n=r(99905),l=(r(14494),r(59462));let o=a.forwardRef(({...e},s)=>(0,t.jsx)("nav",{ref:s,"aria-label":"breadcrumb",...e}));o.displayName="Breadcrumb";let d=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("ol",{ref:r,className:(0,l.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",e),...s}));d.displayName="BreadcrumbList";let c=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("li",{ref:r,className:(0,l.cn)("inline-flex items-center gap-1.5",e),...s}));c.displayName="BreadcrumbItem";let m=a.forwardRef(({asChild:e,className:s,...r},a)=>{let n=e?i.DX:"a";return(0,t.jsx)(n,{ref:a,className:(0,l.cn)("transition-colors hover:text-foreground",s),...r})});m.displayName="BreadcrumbLink";let u=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("span",{ref:r,role:"link","aria-disabled":"true","aria-current":"page",className:(0,l.cn)("font-normal text-foreground",e),...s}));u.displayName="BreadcrumbPage";let x=({children:e,className:s,...r})=>(0,t.jsx)("li",{role:"presentation","aria-hidden":"true",className:(0,l.cn)("[&>svg]:size-3.5",s),...r,children:e??(0,t.jsx)(n.A,{})});x.displayName="BreadcrumbSeparator"},43198:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>N});var t=r(45512),a=r(58009),i=r(37778),n=r(97643),l=r(87021),o=r(25409),d=r(47699),c=r(59462);let m=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("textarea",{className:(0,c.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...s}));m.displayName="Textarea";var u=r(28531),x=r.n(u),p=r(71901),h=r(46583),f=r(15607),j=r(48857),g=r(94209),b=r(8866),y=r(88651);function N(){let{t:e,primaryColor:s}=(0,p.t)(),[r,c]=(0,a.useState)(!1),[u,N]=(0,a.useState)(!1),[v,w]=(0,a.useState)(""),{executeRecaptcha:k}=(0,y._Y)(),[C,A]=(0,a.useState)({name:"",email:"",phoneNumber:"",subject:"",message:""}),q=e=>{let{name:s,value:r}=e.target;A(e=>({...e,[s]:r}))},P=async e=>{if(e.preventDefault(),c(!0),w(""),!k){w("reCAPTCHA not ready"),c(!1);return}try{let e=await k("contact_us"),s=await fetch("https://admin.codemedicalapps.com/api/v1/dynamic/dataoperation/contact-us",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({requestParameters:{FullName:C.name,Email:C.email,PhoneNumber:C.phoneNumber,Subject:C.subject,Message:C.message,RecaptchaToken:e}})}),r=await s.json();s.ok?(N(!0),A({name:"",email:"",phoneNumber:"",subject:"",message:""}),setTimeout(()=>N(!1),5e3)):w(r.message||"Failed to send message.")}catch(e){w("An error occurred. Please try again.")}finally{c(!1)}};return(0,t.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,t.jsx)(i.Qp,{className:"mb-6",children:(0,t.jsxs)(i.AB,{children:[(0,t.jsx)(i.J5,{children:(0,t.jsx)(i.w1,{asChild:!0,children:(0,t.jsx)(x(),{href:"/",children:e("home")})})}),(0,t.jsx)(i.tH,{}),(0,t.jsx)(i.J5,{children:(0,t.jsx)(i.tJ,{children:e("contact")})})]})}),(0,t.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold mb-6",children:e("contact")}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-[1fr_400px] gap-8 mb-12",children:[(0,t.jsx)(n.Zp,{children:(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsx)("h2",{className:"text-2xl font-semibold mb-6",children:"Get in Touch"}),u?(0,t.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-6 text-center",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)(h.A,{className:"h-8 w-8 text-green-600"})}),(0,t.jsx)("h3",{className:"text-xl font-medium text-green-800 mb-2",children:"Message Sent!"}),(0,t.jsx)("p",{className:"text-green-700 mb-4",children:"Thank you for contacting us. We'll get back to you as soon as possible."}),(0,t.jsx)(l.$,{onClick:()=>N(!1),children:"Send Another Message"})]}):(0,t.jsxs)("form",{onSubmit:P,children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(d.J,{htmlFor:"name",children:"Your Name"}),(0,t.jsx)(o.p,{id:"name",name:"name",value:C.name,onChange:q,required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(d.J,{htmlFor:"email",children:"Email Address"}),(0,t.jsx)(o.p,{id:"email",name:"email",type:"email",value:C.email,onChange:q,required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(d.J,{htmlFor:"phoneNumber",children:e("phonenumber")}),(0,t.jsx)(o.p,{id:"phoneNumber",name:"phoneNumber",type:"tel",value:C.phoneNumber,onChange:q,required:!0})]})]}),(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)(d.J,{htmlFor:"subject",children:"Subject"}),(0,t.jsx)(o.p,{id:"subject",name:"subject",value:C.subject,onChange:q,required:!0})]}),(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)(d.J,{htmlFor:"message",children:"Message"}),(0,t.jsx)(m,{id:"message",name:"message",value:C.message,onChange:q,rows:6,required:!0})]}),v&&(0,t.jsx)("p",{className:"text-red-500 text-sm mb-4",children:v}),(0,t.jsx)(l.$,{type:"submit",disabled:r,className:"w-full",children:r?(0,t.jsxs)("span",{className:"flex items-center gap-2",children:[(0,t.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,t.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,t.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Sending..."]}):(0,t.jsxs)("span",{className:"flex items-center gap-2",children:[(0,t.jsx)(f.A,{className:"h-4 w-4"}),"Send Message"]})})]})]})}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(n.Zp,{children:(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Contact Information"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"w-10 h-10 rounded-full flex items-center justify-center mr-4",style:{backgroundColor:`${s}20`},children:(0,t.jsx)(j.A,{className:"h-5 w-5",style:{color:s}})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium",children:"Address"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Iraq"})]})]}),(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"w-10 h-10 rounded-full flex items-center justify-center mr-4",style:{backgroundColor:`${s}20`},children:(0,t.jsx)(g.A,{className:"h-5 w-5",style:{color:s}})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium",children:"Phone"}),(0,t.jsx)(x(),{href:`tel:${e("phone")}`,className:"text-muted-foreground hover:text-primary transition-colors",children:e("phone")})]})]}),(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"w-10 h-10 rounded-full flex items-center justify-center mr-4",style:{backgroundColor:`${s}20`},children:(0,t.jsx)(b.A,{className:"h-5 w-5",style:{color:s}})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium",children:"Email"}),(0,t.jsx)(x(),{href:`mailto:${e("email")}`,className:"text-muted-foreground hover:text-primary transition-colors",children:e("email")})]})]})]})]})}),(0,t.jsx)(n.Zp,{children:(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Business Hours"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Monday - Friday"}),(0,t.jsx)("span",{children:"9:00 AM - 11:00 PM"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Saturday"}),(0,t.jsx)("span",{children:"10:00 AM - 10:00 PM"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Sunday"}),(0,t.jsx)("span",{children:"Closed"})," "]}),(0,t.jsx)("div",{className:"flex justify-between",children:(0,t.jsx)("p",{children:"We’re available 24/7 for orders and inquiries. Our team will get back to you during our regular working hours."})})]})]})})]})]}),(0,t.jsxs)("div",{className:"mb-12",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold mb-6",children:"Frequently Asked Questions"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsx)(n.Zp,{children:(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"How can I track my order?"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"You can track your order by logging into your account and visiting the Orders section. Alternatively, you can use the tracking number provided in your order confirmation email."})]})}),(0,t.jsx)(n.Zp,{children:(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"What payment methods do you accept?"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"We accept various payment methods including credit/debit cards, PayPal, and bank transfers. For more information, please visit our Payment Methods page."})]})}),(0,t.jsx)(n.Zp,{children:(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"How long does shipping take?"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Shipping times vary depending on your location. Domestic orders typically take 3-5 business days, while international orders may take 7-14 business days."})]})}),(0,t.jsx)(n.Zp,{children:(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"What is your return policy?"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"We offer a 30-day return policy for most products. Please visit our Returns page for detailed information on our return process and eligibility criteria."})]})})]})]})]})]})}},46583:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(41680).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},47699:(e,s,r)=>{"use strict";r.d(s,{J:()=>d});var t=r(45512),a=r(58009),i=r(92405),n=r(21643),l=r(59462);let o=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)(i.b,{ref:r,className:(0,l.cn)(o(),e),...s}));d.displayName=i.b.displayName},48857:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(41680).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},92405:(e,s,r)=>{"use strict";r.d(s,{b:()=>l});var t=r(58009),a=r(30830),i=r(45512),n=t.forwardRef((e,s)=>(0,i.jsx)(a.sG.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));n.displayName="Label";var l=n},93372:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var t=r(70260),a=r(28203),i=r(25155),n=r.n(i),l=r(67292),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(s,o);let d={children:["",{children:["contact",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,6531)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\contact\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,19611)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\contact\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/contact/page",pathname:"/contact",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},94735:e=>{"use strict";e.exports=require("events")},97643:(e,s,r)=>{"use strict";r.d(s,{Wu:()=>l,Zp:()=>n,wL:()=>o});var t=r(45512),a=r(58009),i=r(59462);let n=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));n.displayName="Card",a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...s})).displayName="CardHeader",a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("h3",{ref:r,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s})).displayName="CardTitle",a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("p",{ref:r,className:(0,i.cn)("text-sm text-muted-foreground",e),...s})).displayName="CardDescription";let l=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,i.cn)("p-6 pt-0",e),...s}));l.displayName="CardContent";let o=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,i.cn)("flex items-center p-6 pt-0",e),...s}));o.displayName="CardFooter"},99905:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(41680).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[638,551,875],()=>r(93372));module.exports=t})();