(()=>{var e={};e.id=571,e.ids=[571],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3970:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(70260),a=r(28203),l=r(25155),n=r.n(l),i=r(67292),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);r.d(t,o);let d={children:["",{children:["products",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,61551)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\products\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,69e3)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\products\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,19611)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\products\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/products/page",pathname:"/products",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},4643:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(41680).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},5884:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var s=r(45512),a=r(59462);function l({className:e,...t}){return(0,s.jsx)("div",{className:(0,a.cn)("animate-pulse rounded-md bg-muted",e),...t})}},6467:(e,t,r)=>{Promise.resolve().then(r.bind(r,61551))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},14494:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(41680).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19978:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>tn});var s=r(45512),a=r(58009),l=r(79334),n=r(10985),i=r(45037),o=r(41680);let d=(0,o.A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]);var c=r(87021),u=r(5884),p=r(55740);function m(e,[t,r]){return Math.min(r,Math.max(t,e))}var h=r(31412),x=r(39217),f=r(29952),g=r(6004),v=r(59018),y=r(41675),j=r(19632),w=r(82534),N=r(30096),b=r(34666),S=r(80707),P=r(30830),C=r(12705),T=r(92828),A=r(13024),I=r(49397),D=r(66582),k=r(56441),E=r(72421),R=r(54669),M=[" ","Enter","ArrowUp","ArrowDown"],U=[" ","Enter"],_="Select",[L,O,F]=(0,x.N)(_),[z,$]=(0,g.A)(_,[F,b.Bk]),q=(0,b.Bk)(),[V,H]=z(_),[B,W]=z(_),G=e=>{let{__scopeSelect:t,children:r,open:l,defaultOpen:n,onOpenChange:i,value:o,defaultValue:d,onValueChange:c,dir:u,name:p,autoComplete:m,disabled:h,required:x,form:f}=e,g=q(t),[y,j]=a.useState(null),[w,S]=a.useState(null),[P,C]=a.useState(!1),T=(0,v.jH)(u),[I=!1,D]=(0,A.i)({prop:l,defaultProp:n,onChange:i}),[k,E]=(0,A.i)({prop:o,defaultProp:d,onChange:c}),R=a.useRef(null),M=!y||f||!!y.closest("form"),[U,_]=a.useState(new Set),O=Array.from(U).map(e=>e.props.value).join(";");return(0,s.jsx)(b.bL,{...g,children:(0,s.jsxs)(V,{required:x,scope:t,trigger:y,onTriggerChange:j,valueNode:w,onValueNodeChange:S,valueNodeHasChildren:P,onValueNodeHasChildrenChange:C,contentId:(0,N.B)(),value:k,onValueChange:E,open:I,onOpenChange:D,dir:T,triggerPointerDownPosRef:R,disabled:h,children:[(0,s.jsx)(L.Provider,{scope:t,children:(0,s.jsx)(B,{scope:e.__scopeSelect,onNativeOptionAdd:a.useCallback(e=>{_(t=>new Set(t).add(e))},[]),onNativeOptionRemove:a.useCallback(e=>{_(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),M?(0,s.jsxs)(eE,{"aria-hidden":!0,required:x,tabIndex:-1,name:p,autoComplete:m,value:k,onChange:e=>E(e.target.value),disabled:h,form:f,children:[void 0===k?(0,s.jsx)("option",{value:""}):null,Array.from(U)]},O):null]})})};G.displayName=_;var K="SelectTrigger",Q=a.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:l=!1,...n}=e,i=q(r),o=H(K,r),d=o.disabled||l,c=(0,f.s)(t,o.onTriggerChange),u=O(r),p=a.useRef("touch"),[m,x,g]=eR(e=>{let t=u().filter(e=>!e.disabled),r=t.find(e=>e.value===o.value),s=eM(t,e,r);void 0!==s&&o.onValueChange(s.value)}),v=e=>{d||(o.onOpenChange(!0),g()),e&&(o.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,s.jsx)(b.Mz,{asChild:!0,...i,children:(0,s.jsx)(P.sG.button,{type:"button",role:"combobox","aria-controls":o.contentId,"aria-expanded":o.open,"aria-required":o.required,"aria-autocomplete":"none",dir:o.dir,"data-state":o.open?"open":"closed",disabled:d,"data-disabled":d?"":void 0,"data-placeholder":ek(o.value)?"":void 0,...n,ref:c,onClick:(0,h.m)(n.onClick,e=>{e.currentTarget.focus(),"mouse"!==p.current&&v(e)}),onPointerDown:(0,h.m)(n.onPointerDown,e=>{p.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(v(e),e.preventDefault())}),onKeyDown:(0,h.m)(n.onKeyDown,e=>{let t=""!==m.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||x(e.key),(!t||" "!==e.key)&&M.includes(e.key)&&(v(),e.preventDefault())})})})});Q.displayName=K;var Z="SelectValue",J=a.forwardRef((e,t)=>{let{__scopeSelect:r,className:a,style:l,children:n,placeholder:i="",...o}=e,d=H(Z,r),{onValueNodeHasChildrenChange:c}=d,u=void 0!==n,p=(0,f.s)(t,d.onValueNodeChange);return(0,I.N)(()=>{c(u)},[c,u]),(0,s.jsx)(P.sG.span,{...o,ref:p,style:{pointerEvents:"none"},children:ek(d.value)?(0,s.jsx)(s.Fragment,{children:i}):n})});J.displayName=Z;var X=a.forwardRef((e,t)=>{let{__scopeSelect:r,children:a,...l}=e;return(0,s.jsx)(P.sG.span,{"aria-hidden":!0,...l,ref:t,children:a||"▼"})});X.displayName="SelectIcon";var Y=e=>(0,s.jsx)(S.Z,{asChild:!0,...e});Y.displayName="SelectPortal";var ee="SelectContent",et=a.forwardRef((e,t)=>{let r=H(ee,e.__scopeSelect),[l,n]=a.useState();return((0,I.N)(()=>{n(new DocumentFragment)},[]),r.open)?(0,s.jsx)(ea,{...e,ref:t}):l?p.createPortal((0,s.jsx)(er,{scope:e.__scopeSelect,children:(0,s.jsx)(L.Slot,{scope:e.__scopeSelect,children:(0,s.jsx)("div",{children:e.children})})}),l):null});et.displayName=ee;var[er,es]=z(ee),ea=a.forwardRef((e,t)=>{let{__scopeSelect:r,position:l="item-aligned",onCloseAutoFocus:n,onEscapeKeyDown:i,onPointerDownOutside:o,side:d,sideOffset:c,align:u,alignOffset:p,arrowPadding:m,collisionBoundary:x,collisionPadding:g,sticky:v,hideWhenDetached:N,avoidCollisions:b,...S}=e,P=H(ee,r),[T,A]=a.useState(null),[I,D]=a.useState(null),k=(0,f.s)(t,e=>A(e)),[M,U]=a.useState(null),[_,L]=a.useState(null),F=O(r),[z,$]=a.useState(!1),q=a.useRef(!1);a.useEffect(()=>{if(T)return(0,E.Eq)(T)},[T]),(0,j.Oh)();let V=a.useCallback(e=>{let[t,...r]=F().map(e=>e.ref.current),[s]=r.slice(-1),a=document.activeElement;for(let r of e)if(r===a||(r?.scrollIntoView({block:"nearest"}),r===t&&I&&(I.scrollTop=0),r===s&&I&&(I.scrollTop=I.scrollHeight),r?.focus(),document.activeElement!==a))return},[F,I]),B=a.useCallback(()=>V([M,T]),[V,M,T]);a.useEffect(()=>{z&&B()},[z,B]);let{onOpenChange:W,triggerPointerDownPosRef:G}=P;a.useEffect(()=>{if(T){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(G.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(G.current?.y??0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():T.contains(r.target)||W(!1),document.removeEventListener("pointermove",t),G.current=null};return null!==G.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[T,W,G]),a.useEffect(()=>{let e=()=>W(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[W]);let[K,Q]=eR(e=>{let t=F().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),s=eM(t,e,r);s&&setTimeout(()=>s.ref.current.focus())}),Z=a.useCallback((e,t,r)=>{let s=!q.current&&!r;(void 0!==P.value&&P.value===t||s)&&(U(e),s&&(q.current=!0))},[P.value]),J=a.useCallback(()=>T?.focus(),[T]),X=a.useCallback((e,t,r)=>{let s=!q.current&&!r;(void 0!==P.value&&P.value===t||s)&&L(e)},[P.value]),Y="popper"===l?en:el,et=Y===en?{side:d,sideOffset:c,align:u,alignOffset:p,arrowPadding:m,collisionBoundary:x,collisionPadding:g,sticky:v,hideWhenDetached:N,avoidCollisions:b}:{};return(0,s.jsx)(er,{scope:r,content:T,viewport:I,onViewportChange:D,itemRefCallback:Z,selectedItem:M,onItemLeave:J,itemTextRefCallback:X,focusSelectedItem:B,selectedItemText:_,position:l,isPositioned:z,searchRef:K,children:(0,s.jsx)(R.A,{as:C.DX,allowPinchZoom:!0,children:(0,s.jsx)(w.n,{asChild:!0,trapped:P.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,h.m)(n,e=>{P.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,s.jsx)(y.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:o,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>P.onOpenChange(!1),children:(0,s.jsx)(Y,{role:"listbox",id:P.contentId,"data-state":P.open?"open":"closed",dir:P.dir,onContextMenu:e=>e.preventDefault(),...S,...et,onPlaced:()=>$(!0),ref:k,style:{display:"flex",flexDirection:"column",outline:"none",...S.style},onKeyDown:(0,h.m)(S.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||Q(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=F().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,s=t.indexOf(r);t=t.slice(s+1)}setTimeout(()=>V(t)),e.preventDefault()}})})})})})})});ea.displayName="SelectContentImpl";var el=a.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:l,...n}=e,i=H(ee,r),o=es(ee,r),[d,c]=a.useState(null),[u,p]=a.useState(null),h=(0,f.s)(t,e=>p(e)),x=O(r),g=a.useRef(!1),v=a.useRef(!0),{viewport:y,selectedItem:j,selectedItemText:w,focusSelectedItem:N}=o,b=a.useCallback(()=>{if(i.trigger&&i.valueNode&&d&&u&&y&&j&&w){let e=i.trigger.getBoundingClientRect(),t=u.getBoundingClientRect(),r=i.valueNode.getBoundingClientRect(),s=w.getBoundingClientRect();if("rtl"!==i.dir){let a=s.left-t.left,l=r.left-a,n=e.left-l,i=e.width+n,o=Math.max(i,t.width),c=m(l,[10,Math.max(10,window.innerWidth-10-o)]);d.style.minWidth=i+"px",d.style.left=c+"px"}else{let a=t.right-s.right,l=window.innerWidth-r.right-a,n=window.innerWidth-e.right-l,i=e.width+n,o=Math.max(i,t.width),c=m(l,[10,Math.max(10,window.innerWidth-10-o)]);d.style.minWidth=i+"px",d.style.right=c+"px"}let a=x(),n=window.innerHeight-20,o=y.scrollHeight,c=window.getComputedStyle(u),p=parseInt(c.borderTopWidth,10),h=parseInt(c.paddingTop,10),f=parseInt(c.borderBottomWidth,10),v=p+h+o+parseInt(c.paddingBottom,10)+f,N=Math.min(5*j.offsetHeight,v),b=window.getComputedStyle(y),S=parseInt(b.paddingTop,10),P=parseInt(b.paddingBottom,10),C=e.top+e.height/2-10,T=j.offsetHeight/2,A=p+h+(j.offsetTop+T);if(A<=C){let e=a.length>0&&j===a[a.length-1].ref.current;d.style.bottom="0px";let t=Math.max(n-C,T+(e?P:0)+(u.clientHeight-y.offsetTop-y.offsetHeight)+f);d.style.height=A+t+"px"}else{let e=a.length>0&&j===a[0].ref.current;d.style.top="0px";let t=Math.max(C,p+y.offsetTop+(e?S:0)+T);d.style.height=t+(v-A)+"px",y.scrollTop=A-C+y.offsetTop}d.style.margin="10px 0",d.style.minHeight=N+"px",d.style.maxHeight=n+"px",l?.(),requestAnimationFrame(()=>g.current=!0)}},[x,i.trigger,i.valueNode,d,u,y,j,w,i.dir,l]);(0,I.N)(()=>b(),[b]);let[S,C]=a.useState();(0,I.N)(()=>{u&&C(window.getComputedStyle(u).zIndex)},[u]);let T=a.useCallback(e=>{e&&!0===v.current&&(b(),N?.(),v.current=!1)},[b,N]);return(0,s.jsx)(ei,{scope:r,contentWrapper:d,shouldExpandOnScrollRef:g,onScrollButtonChange:T,children:(0,s.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:S},children:(0,s.jsx)(P.sG.div,{...n,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...n.style}})})})});el.displayName="SelectItemAlignedPosition";var en=a.forwardRef((e,t)=>{let{__scopeSelect:r,align:a="start",collisionPadding:l=10,...n}=e,i=q(r);return(0,s.jsx)(b.UC,{...i,...n,ref:t,align:a,collisionPadding:l,style:{boxSizing:"border-box",...n.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});en.displayName="SelectPopperPosition";var[ei,eo]=z(ee,{}),ed="SelectViewport",ec=a.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:l,...n}=e,i=es(ed,r),o=eo(ed,r),d=(0,f.s)(t,i.onViewportChange),c=a.useRef(0);return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,s.jsx)(L.Slot,{scope:r,children:(0,s.jsx)(P.sG.div,{"data-radix-select-viewport":"",role:"presentation",...n,ref:d,style:{position:"relative",flex:1,overflow:"hidden auto",...n.style},onScroll:(0,h.m)(n.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:s}=o;if(s?.current&&r){let e=Math.abs(c.current-t.scrollTop);if(e>0){let s=window.innerHeight-20,a=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(a<s){let l=a+e,n=Math.min(s,l),i=l-n;r.style.height=n+"px","0px"===r.style.bottom&&(t.scrollTop=i>0?i:0,r.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});ec.displayName=ed;var eu="SelectGroup",[ep,em]=z(eu);a.forwardRef((e,t)=>{let{__scopeSelect:r,...a}=e,l=(0,N.B)();return(0,s.jsx)(ep,{scope:r,id:l,children:(0,s.jsx)(P.sG.div,{role:"group","aria-labelledby":l,...a,ref:t})})}).displayName=eu;var eh="SelectLabel",ex=a.forwardRef((e,t)=>{let{__scopeSelect:r,...a}=e,l=em(eh,r);return(0,s.jsx)(P.sG.div,{id:l.id,...a,ref:t})});ex.displayName=eh;var ef="SelectItem",[eg,ev]=z(ef),ey=a.forwardRef((e,t)=>{let{__scopeSelect:r,value:l,disabled:n=!1,textValue:i,...o}=e,d=H(ef,r),c=es(ef,r),u=d.value===l,[p,m]=a.useState(i??""),[x,g]=a.useState(!1),v=(0,f.s)(t,e=>c.itemRefCallback?.(e,l,n)),y=(0,N.B)(),j=a.useRef("touch"),w=()=>{n||(d.onValueChange(l),d.onOpenChange(!1))};if(""===l)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,s.jsx)(eg,{scope:r,value:l,disabled:n,textId:y,isSelected:u,onItemTextChange:a.useCallback(e=>{m(t=>t||(e?.textContent??"").trim())},[]),children:(0,s.jsx)(L.ItemSlot,{scope:r,value:l,disabled:n,textValue:p,children:(0,s.jsx)(P.sG.div,{role:"option","aria-labelledby":y,"data-highlighted":x?"":void 0,"aria-selected":u&&x,"data-state":u?"checked":"unchecked","aria-disabled":n||void 0,"data-disabled":n?"":void 0,tabIndex:n?void 0:-1,...o,ref:v,onFocus:(0,h.m)(o.onFocus,()=>g(!0)),onBlur:(0,h.m)(o.onBlur,()=>g(!1)),onClick:(0,h.m)(o.onClick,()=>{"mouse"!==j.current&&w()}),onPointerUp:(0,h.m)(o.onPointerUp,()=>{"mouse"===j.current&&w()}),onPointerDown:(0,h.m)(o.onPointerDown,e=>{j.current=e.pointerType}),onPointerMove:(0,h.m)(o.onPointerMove,e=>{j.current=e.pointerType,n?c.onItemLeave?.():"mouse"===j.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,h.m)(o.onPointerLeave,e=>{e.currentTarget===document.activeElement&&c.onItemLeave?.()}),onKeyDown:(0,h.m)(o.onKeyDown,e=>{(c.searchRef?.current===""||" "!==e.key)&&(U.includes(e.key)&&w()," "===e.key&&e.preventDefault())})})})})});ey.displayName=ef;var ej="SelectItemText",ew=a.forwardRef((e,t)=>{let{__scopeSelect:r,className:l,style:n,...i}=e,o=H(ej,r),d=es(ej,r),c=ev(ej,r),u=W(ej,r),[m,h]=a.useState(null),x=(0,f.s)(t,e=>h(e),c.onItemTextChange,e=>d.itemTextRefCallback?.(e,c.value,c.disabled)),g=m?.textContent,v=a.useMemo(()=>(0,s.jsx)("option",{value:c.value,disabled:c.disabled,children:g},c.value),[c.disabled,c.value,g]),{onNativeOptionAdd:y,onNativeOptionRemove:j}=u;return(0,I.N)(()=>(y(v),()=>j(v)),[y,j,v]),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(P.sG.span,{id:c.textId,...i,ref:x}),c.isSelected&&o.valueNode&&!o.valueNodeHasChildren?p.createPortal(i.children,o.valueNode):null]})});ew.displayName=ej;var eN="SelectItemIndicator",eb=a.forwardRef((e,t)=>{let{__scopeSelect:r,...a}=e;return ev(eN,r).isSelected?(0,s.jsx)(P.sG.span,{"aria-hidden":!0,...a,ref:t}):null});eb.displayName=eN;var eS="SelectScrollUpButton",eP=a.forwardRef((e,t)=>{let r=es(eS,e.__scopeSelect),l=eo(eS,e.__scopeSelect),[n,i]=a.useState(!1),o=(0,f.s)(t,l.onScrollButtonChange);return(0,I.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){i(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),n?(0,s.jsx)(eA,{...e,ref:o,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});eP.displayName=eS;var eC="SelectScrollDownButton",eT=a.forwardRef((e,t)=>{let r=es(eC,e.__scopeSelect),l=eo(eC,e.__scopeSelect),[n,i]=a.useState(!1),o=(0,f.s)(t,l.onScrollButtonChange);return(0,I.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;i(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),n?(0,s.jsx)(eA,{...e,ref:o,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eT.displayName=eC;var eA=a.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:l,...n}=e,i=es("SelectScrollButton",r),o=a.useRef(null),d=O(r),c=a.useCallback(()=>{null!==o.current&&(window.clearInterval(o.current),o.current=null)},[]);return a.useEffect(()=>()=>c(),[c]),(0,I.N)(()=>{let e=d().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[d]),(0,s.jsx)(P.sG.div,{"aria-hidden":!0,...n,ref:t,style:{flexShrink:0,...n.style},onPointerDown:(0,h.m)(n.onPointerDown,()=>{null===o.current&&(o.current=window.setInterval(l,50))}),onPointerMove:(0,h.m)(n.onPointerMove,()=>{i.onItemLeave?.(),null===o.current&&(o.current=window.setInterval(l,50))}),onPointerLeave:(0,h.m)(n.onPointerLeave,()=>{c()})})}),eI=a.forwardRef((e,t)=>{let{__scopeSelect:r,...a}=e;return(0,s.jsx)(P.sG.div,{"aria-hidden":!0,...a,ref:t})});eI.displayName="SelectSeparator";var eD="SelectArrow";function ek(e){return""===e||void 0===e}a.forwardRef((e,t)=>{let{__scopeSelect:r,...a}=e,l=q(r),n=H(eD,r),i=es(eD,r);return n.open&&"popper"===i.position?(0,s.jsx)(b.i3,{...l,...a,ref:t}):null}).displayName=eD;var eE=a.forwardRef((e,t)=>{let{value:r,...l}=e,n=a.useRef(null),i=(0,f.s)(t,n),o=(0,D.Z)(r);return a.useEffect(()=>{let e=n.current,t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(o!==r&&t){let s=new Event("change",{bubbles:!0});t.call(e,r),e.dispatchEvent(s)}},[o,r]),(0,s.jsx)(k.s,{asChild:!0,children:(0,s.jsx)("select",{...l,ref:i,defaultValue:r})})});function eR(e){let t=(0,T.c)(e),r=a.useRef(""),s=a.useRef(0),l=a.useCallback(e=>{let a=r.current+e;t(a),function e(t){r.current=t,window.clearTimeout(s.current),""!==t&&(s.current=window.setTimeout(()=>e(""),1e3))}(a)},[t]),n=a.useCallback(()=>{r.current="",window.clearTimeout(s.current)},[]);return a.useEffect(()=>()=>window.clearTimeout(s.current),[]),[r,l,n]}function eM(e,t,r){var s,a;let l=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,n=r?e.indexOf(r):-1,i=(s=e,a=Math.max(n,0),s.map((e,t)=>s[(a+t)%s.length]));1===l.length&&(i=i.filter(e=>e!==r));let o=i.find(e=>e.textValue.toLowerCase().startsWith(l.toLowerCase()));return o!==r?o:void 0}eE.displayName="BubbleSelect";var eU=r(98755);let e_=(0,o.A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]),eL=(0,o.A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);var eO=r(59462);let eF=a.forwardRef(({className:e,children:t,...r},a)=>(0,s.jsxs)(Q,{ref:a,className:(0,eO.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...r,children:[t,(0,s.jsx)(X,{asChild:!0,children:(0,s.jsx)(eU.A,{className:"h-4 w-4 opacity-50"})})]}));eF.displayName=Q.displayName;let ez=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(eP,{ref:r,className:(0,eO.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(e_,{className:"h-4 w-4"})}));ez.displayName=eP.displayName;let e$=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(eT,{ref:r,className:(0,eO.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(eU.A,{className:"h-4 w-4"})}));e$.displayName=eT.displayName;let eq=a.forwardRef(({className:e,children:t,position:r="popper",...a},l)=>(0,s.jsx)(Y,{children:(0,s.jsxs)(et,{ref:l,className:(0,eO.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-white text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...a,children:[(0,s.jsx)(ez,{}),(0,s.jsx)(ec,{className:(0,eO.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,s.jsx)(e$,{})]})}));eq.displayName=et.displayName,a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(ex,{ref:r,className:(0,eO.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=ex.displayName;let eV=a.forwardRef(({className:e,children:t,...r},a)=>(0,s.jsxs)(ey,{ref:a,className:(0,eO.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...r,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(eb,{children:(0,s.jsx)(eL,{className:"h-4 w-4"})})}),(0,s.jsx)(ew,{children:t})]}));eV.displayName=ey.displayName,a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(eI,{ref:r,className:(0,eO.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=eI.displayName;var eH=r(70801),eB=r(28531),eW=r.n(eB),eG=r(45103),eK=r(4643),eQ=r(10617),eZ=r(21956),eJ=r(10453),eX=r(97643),eY=r(77252),e0=r(84194),e1=r(31961),e2=r(71901);function e5({endDate:e}){let[t,r]=(0,a.useState)(null),l=({value:e,label:t})=>(0,s.jsxs)("div",{className:"flex flex-col items-center mx-0.5",children:[(0,s.jsxs)("div",{className:"relative w-7 h-7 sm:w-8 sm:h-8 flex items-center justify-center bg-gradient-to-br from-pink-500 to-red-600 rounded-md shadow-md",children:[(0,s.jsx)("div",{className:"absolute inset-0.5 bg-black/20 rounded-sm"}),(0,s.jsx)("span",{className:"relative z-10 text-white font-bold text-xs sm:text-sm",children:String(e).padStart(2,"0")})]}),(0,s.jsx)("span",{className:"text-[9px] sm:text-[10px] text-white/80 mt-0.5 font-medium",children:t})]});return t?(0,s.jsx)("div",{className:"bg-gradient-to-r from-pink-600/90 to-red-600/90 backdrop-blur-sm p-1.5 sm:p-2 rounded-lg shadow-lg",children:(0,s.jsxs)("div",{className:"flex justify-center items-center space-x-0.5",children:[t.days>0&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(l,{value:t.days,label:"Days"}),(0,s.jsx)("span",{className:"text-white font-bold text-sm -mb-2",children:":"})]}),(0,s.jsx)(l,{value:t.hours,label:"Hrs"}),(0,s.jsx)("span",{className:"text-white font-bold text-sm -mb-2",children:":"}),(0,s.jsx)(l,{value:t.minutes,label:"Min"}),(0,s.jsx)("span",{className:"text-white font-bold text-sm -mb-2",children:":"}),(0,s.jsx)(l,{value:t.seconds,label:"Sec"})]})}):(0,s.jsxs)("div",{className:"px-3 py-1.5 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center",children:[(0,s.jsx)(eK.A,{className:"w-3.5 h-3.5 mr-1.5 text-white animate-pulse"}),(0,s.jsx)("span",{className:"text-xs font-semibold text-white",children:"Sale Ended"})]})}function e4({product:e}){(0,e0._)();let t=(0,e1.n)(),{toast:r}=(0,eH.dj)(),{primaryColor:l}=(0,e2.t)(),[n,i]=(0,a.useState)(!1),[o,d]=(0,a.useState)(!1),u=(e,t="USD")=>"IQD"===t?`IQD ${e.toLocaleString()}`:`$${e.toFixed(2)}`;e.SellStartDatetimeUTC&&e.SellEndDatetimeUTC;let p=new Date,m=e.SellStartDatetimeUTC?new Date(e.SellStartDatetimeUTC):null,h=e.SellEndDatetimeUTC?new Date(e.SellEndDatetimeUTC):null,x=m&&h&&p>=m&&p<=h;return(0,s.jsxs)(eX.Zp,{className:"overflow-hidden flex flex-col h-full relative",children:[(0,s.jsxs)("div",{className:"absolute top-2 left-2 z-10 flex flex-col gap-1",children:[e.MarkAsNew&&(0,s.jsx)(eY.E,{variant:"secondary",className:"bg-blue-500 text-white text-xs",children:"New"}),e.DiscountPrice&&e.DiscountPrice>0&&(0,s.jsx)(eY.E,{variant:"destructive",className:"bg-red-500 text-white text-xs",children:"Sale"}),x&&!e.DiscountPrice&&(0,s.jsx)(eY.E,{variant:"destructive",className:"bg-red-500 text-white text-xs",children:"Sale"})]}),(0,s.jsx)(eW(),{href:`/product/${e.ProductId}`,children:(0,s.jsxs)("div",{className:"aspect-square overflow-hidden relative",children:[(0,s.jsx)("div",{className:"h-full w-full relative",children:(0,s.jsx)(eG.default,{src:e.ProductImageUrl||"/placeholder.svg?height=300&width=300",alt:e.ProductName||"Product",fill:!0,className:"object-cover transition-transform hover:scale-105",onError:e=>{e.target.src="/placeholder.svg?height=300&width=300"},priority:!1,loading:"lazy"})}),x&&e.SellEndDatetimeUTC&&(0,s.jsx)("div",{className:"absolute bottom-0 left-0 right-0 p-2 flex justify-center",children:(0,s.jsx)(e5,{endDate:e.SellEndDatetimeUTC})})]})}),(0,s.jsxs)(eX.Wu,{className:"pt-4 flex-grow",children:[(0,s.jsxs)("div",{className:"flex items-center mb-2",children:[(0,s.jsx)("div",{className:"flex",children:[void 0,void 0,void 0,void 0,void 0].map((t,r)=>(0,s.jsx)(eQ.A,{className:`w-4 h-4 ${r<Math.floor(e.Rating||0)?"text-yellow-400 fill-yellow-400":"text-gray-300"}`},r))}),(0,s.jsxs)("span",{className:"text-xs text-gray-500 ml-1",children:["(",e.Rating||0,")"]})]}),(0,s.jsx)(eW(),{href:`/product/${e.ProductId}`,className:"hover:underline",children:(0,s.jsx)("h3",{className:"font-semibold text-lg line-clamp-2 mb-2",children:e.ProductName||"Unnamed Product"})}),e.ProductTypeName&&(0,s.jsxs)("p",{className:"text-sm text-gray-500 mb-2",children:["Type: ",e.ProductTypeName]}),(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsx)("div",{className:"flex items-center gap-2",children:e.DiscountPrice?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("span",{className:"text-lg font-bold text-red-500",children:u(e.DiscountPrice)}),(0,s.jsx)("span",{className:"text-xs text-gray-500 line-through",children:u(e.Price||0)})]}):e.OldPrice&&e.OldPrice>e.Price?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("span",{className:"text-lg font-bold text-red-500",children:u(e.Price||0)}),(0,s.jsx)("span",{className:"text-xs text-gray-500 line-through",children:u(e.OldPrice)})]}):(0,s.jsx)("span",{className:"text-lg font-bold text-primary",children:u(e.Price||0)})}),e.IQDPrice&&(0,s.jsx)("span",{className:"text-sm font-medium text-green-600 mt-0.5",children:u(e.IQDPrice,"IQD")})]})]}),(0,s.jsx)(eX.wL,{className:"p-3 pt-1 mt-auto",children:(0,s.jsx)("div",{className:"w-full",children:(0,s.jsxs)("div",{className:"flex items-center justify-between gap-2",children:[(0,s.jsx)(c.$,{variant:"ghost",size:"sm",className:"h-8 px-2 text-xs font-medium flex-1 gap-1.5 text-white hover:opacity-90",style:{backgroundColor:l},asChild:!0,children:(0,s.jsxs)(eW(),{href:`/product/${e.ProductId}`,children:[(0,s.jsx)(eZ.A,{className:"h-3.5 w-3.5"}),(0,s.jsx)("span",{children:"View"})]})}),(0,s.jsx)(c.$,{variant:"ghost",size:"icon",className:"h-8 w-8 rounded-full",onClick:()=>{if(t.isHydrated){d(!0);try{t.isInWishlist(e.ProductId)?(t.removeFromWishlist(e.ProductId),r({description:`${e.ProductName} removed from wishlist`,type:"success"})):(t.addToWishlist(e.ProductId),r({description:`${e.ProductName} added to wishlist`,type:"success"}))}catch(e){console.error("Error updating wishlist:",e),r({description:"Failed to update wishlist",type:"error"})}finally{setTimeout(()=>{d(!1)},500)}}},disabled:o,children:(0,s.jsx)(eJ.A,{className:`h-4 w-4 ${t.isInWishlist(e.ProductId)?"fill-red-500 text-red-500":""}`})})]})})})]})}var e3=r(52706),e6=r(99905),e9=r(14494);let e8=({className:e,...t})=>(0,s.jsx)("nav",{role:"navigation","aria-label":"pagination",className:(0,eO.cn)("mx-auto flex w-full justify-center",e),...t});e8.displayName="Pagination";let e7=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("ul",{ref:r,className:(0,eO.cn)("flex flex-row items-center gap-1",e),...t}));e7.displayName="PaginationContent";let te=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("li",{ref:r,className:(0,eO.cn)("",e),...t}));te.displayName="PaginationItem";let tt=({className:e,isActive:t,size:r="icon",...a})=>(0,s.jsx)("a",{"aria-current":t?"page":void 0,className:(0,eO.cn)((0,c.r)({variant:t?"outline":"ghost",size:r}),e),...a});tt.displayName="PaginationLink";let tr=({className:e,...t})=>(0,s.jsxs)(tt,{"aria-label":"Go to previous page",size:"default",className:(0,eO.cn)("gap-1 pl-2.5",e),...t,children:[(0,s.jsx)(e3.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"Previous"})]});tr.displayName="PaginationPrevious";let ts=({className:e,...t})=>(0,s.jsxs)(tt,{"aria-label":"Go to next page",size:"default",className:(0,eO.cn)("gap-1 pr-2.5",e),...t,children:[(0,s.jsx)("span",{children:"Next"}),(0,s.jsx)(e6.A,{className:"h-4 w-4"})]});ts.displayName="PaginationNext";let ta=({className:e,...t})=>(0,s.jsxs)("span",{"aria-hidden":!0,className:(0,eO.cn)("flex h-9 w-9 items-center justify-center",e),...t,children:[(0,s.jsx)(e9.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"More pages"})]});function tl({children:e}){let t=(0,l.useSearchParams)(),r=t.get("search")||"",a=t.get("category")||"all",n=t.get("productType")||"all";return(0,s.jsx)(s.Fragment,{children:e({searchTerm:r,categoryId:a,productTypeId:n})})}function tn(){return(0,s.jsx)(a.Suspense,{fallback:(0,s.jsx)(ti,{}),children:(0,s.jsx)(tl,{children:({searchTerm:e,categoryId:t,productTypeId:r})=>(0,s.jsx)(to,{initialSearchTerm:e,initialCategoryId:t,initialProductTypeId:r})})})}function ti(){return(0,s.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,s.jsx)("div",{className:"h-8 w-64 bg-gray-200 rounded mb-4 animate-pulse"}),(0,s.jsx)("div",{className:"h-4 w-96 bg-gray-200 rounded mb-6 animate-pulse"}),(0,s.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[(0,s.jsx)("div",{className:"lg:w-1/4",children:(0,s.jsxs)("div",{className:"bg-white p-4 rounded-lg shadow",children:[(0,s.jsx)("div",{className:"h-6 w-32 bg-gray-200 rounded mb-4 animate-pulse"}),(0,s.jsx)("div",{className:"space-y-4",children:[,,,,].fill(0).map((e,t)=>(0,s.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"},t))})]})}),(0,s.jsx)("div",{className:"lg:w-3/4",children:(0,s.jsx)("div",{className:"grid grid-cols-2 gap-4 sm:gap-6 sm:grid-cols-2 lg:grid-cols-3",children:Array(12).fill(0).map((e,t)=>(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,s.jsx)("div",{className:"aspect-square bg-gray-200 animate-pulse"}),(0,s.jsxs)("div",{className:"p-4 space-y-2",children:[(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse"}),(0,s.jsx)("div",{className:"h-4 w-3/4 bg-gray-200 rounded animate-pulse"}),(0,s.jsx)("div",{className:"h-6 w-1/3 bg-gray-200 rounded animate-pulse"})]})]},t))})})]})]})}function to({initialSearchTerm:e,initialCategoryId:t,initialProductTypeId:r}){var l;let o;let{toast:p}=(0,eH.dj)(),[m,h]=(0,a.useState)([]),[x,f]=(0,a.useState)([]),[g,v]=(0,a.useState)(!0),[y,j]=(0,a.useState)(1),[w,N]=(0,a.useState)(1),[b,S]=(0,a.useState)("Price DESC"),[P,C]=(0,a.useState)("all"!==t?Number.parseInt(t):null),[T,A]=(0,a.useState)("all"!==r?Number.parseInt(r):null),[I,D]=(0,a.useState)(e),[k,E]=(0,a.useState)(e),[R,M]=(0,a.useState)([]),[U,_]=(0,a.useState)([]),[L,O]=(0,a.useState)({min:null,max:null}),[F,z]=(0,a.useState)(!1),[$,q]=(0,a.useState)(null),[V,H]=(0,a.useState)([]),[B,W]=(0,a.useState)({}),[K,Q]=(0,a.useState)({}),Z=(0,a.useCallback)((l=e=>{D(e),j(1)},(...e)=>{clearTimeout(o),o=setTimeout(()=>{l(...e)},500)}),[]),X=e=>{let t=new Map;e.forEach(e=>{e.Attributes&&e.Attributes.length>0&&e.Attributes.forEach(e=>{let r=`${e.AttributeName}|${e.DisplayName}`;t.has(r)||t.set(r,new Map);let s=t.get(r),a=s.get(e.AttributeValueID);a?a.count++:s.set(e.AttributeValueID,{text:e.AttributeValueText,count:1})})});let r=[];t.forEach((e,t)=>{let[s,a]=t.split("|"),l=Array.from(e.entries()).map(([e,t])=>({id:e,text:t.text,count:t.count}));r.push({attributeName:s,displayName:a,values:l.sort((e,t)=>e.text.localeCompare(t.text))})}),H(r.sort((e,t)=>e.displayName.localeCompare(t.displayName)))},Y=async()=>{v(1===y),z(y>1),q(null);try{let e={requestParameters:{SearchTerm:I||"",SizeID:null,ColorID:null,CategoryID:P,TagID:null,ManufacturerID:null,producttypeId:T,MinPrice:L.min,MaxPrice:L.max,Rating:null,OrderByColumnName:(e=>{switch(e){case"Price ASC":return 1;case"Price DESC":default:return 0;case"ProductName ASC":return 2;case"ProductName DESC":return 3;case"Rating DESC":return 4}})(b),PageNo:1,PageSize:1e3,recordValueJson:"[]"}};console.log("Sending request to Next.js API route:",e);let t=await fetch("/api/products/get-products",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(console.log("API route response status:",t.status),!t.ok){let e=await t.json();throw Error(e.error||`HTTP error! status: ${t.status}`)}let r=await t.json();if(console.log("API route response data:",r),r&&r.data){let e=[];try{if("string"==typeof r.data?(console.log("Parsing string data:",r.data),e=JSON.parse(r.data)):Array.isArray(r.data)?(console.log("Using array data directly"),e=r.data):(console.log("Converting object to array:",r.data),e=[r.data]),console.log("Processed products data:",e),!Array.isArray(e))throw Error("Parsed data is not an array");if(0===e.length){console.log("No products found in API response"),h([]),f([]),N(0);return}console.log("First product in response:",e[0]),console.log("Available fields:",Object.keys(e[0]));let t=e.map(e=>{let t=e.ProductImagesUrl||e.ProductImageUrl,r=null;try{if(t){let e=t;if("string"==typeof t&&(t.startsWith("[")||t.startsWith('"')))try{let r=JSON.parse(t);Array.isArray(r)&&r.length>0?e=r[0].AttachmentURL||r[0]:"string"==typeof r&&(e=r)}catch(r){e=t.replace(/^"|"/g,"")}if("string"==typeof e&&""!==e.trim()&&(e=e.replace(/^"|"$/g,"").trim())){let t=decodeURIComponent(e),s=t.startsWith("/")||t.startsWith("http")?t:`/${t}`;r=s.startsWith("http")?s:`https://admin.codemedicalapps.com${s}`}}}catch(t){console.error("Error processing URL for product",e.ProductID||e.ProductId,":",t)}let s=e.ProductID||e.ProductId||e.Id||e.ID||e.id;return{...e,ProductId:s,ProductID:s,ProductName:e.ProductName||"Unnamed Product",Price:Number.parseFloat(e.Price)||0,OldPrice:e.OldPrice?Number.parseFloat(e.OldPrice):void 0,IQDPrice:Number.parseFloat(e.IQDPrice)||0,ProductTypeID:e.ProductTypeID,ProductTypeName:e.ProductTypeName,CategoryName:e.CategoryName||"Uncategorized",Rating:Number.parseFloat(e.Rating)||0,StockQuantity:Number.parseInt(e.StockQuantity,10)||0,ProductImageUrl:r,IsDiscountAllowed:!!e.IsDiscountAllowed,MarkAsNew:!!e.MarkAsNew,SellStartDatetimeUTC:e.SellStartDatetimeUTC||void 0,SellEndDatetimeUTC:e.SellEndDatetimeUTC||void 0,Attributes:e.Attributes||[],...e.DiscountPrice&&{DiscountPrice:Number.parseFloat(e.DiscountPrice)}}});if(console.log("Processed products:",t),f(t),h(t),X(t),e.length>0&&(e[0].TotalRecords||e[0].totalRecords)){let t=e[0].TotalRecords||e[0].totalRecords;N(Math.ceil(Number.parseInt(t,10)/12))}}catch(e){throw console.error("Error parsing product data:",e),console.error("Raw response data:",r),q(`Error parsing data: ${e.message||"Unknown"}`),e}}else console.warn("No data field in API response:",r),q("API response missing data field"),h([]),f([])}catch(e){console.error("Error fetching products:",e),q(`API Error: ${e.message||"Unknown"}`),p({description:"Failed to load products. Please try again.",type:"error"}),h([]),f([])}finally{z(!1),v(!1)}},ee=()=>{let e=new URLSearchParams;I&&e.append("search",I),null!==P&&e.append("category",P.toString()),null!==T&&e.append("productType",T.toString()),window.history.pushState({},"",`${window.location.pathname}?${e.toString()}`)},et=e=>{j(e),window.scrollTo(0,0)};return(0,s.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold mb-6",children:null!==P&&R.length>0?`${R.find(e=>e.id===P)?.name||"Category"} Products`:"All Products"}),(0,s.jsx)("div",{className:"bg-white p-6 rounded-lg shadow mb-6",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-4",children:[(0,s.jsx)(n.A,{className:"h-5 w-5 text-gray-600"}),(0,s.jsx)("span",{className:"font-medium text-gray-700",children:"Filter by Product Type"})]}),(0,s.jsxs)("div",{className:"flex justify-center gap-3 flex-wrap",children:[(0,s.jsx)(c.$,{variant:null===T?"default":"outline",size:"lg",onClick:()=>{A(null),j(1),ee()},className:"min-w-[120px]",children:"All Types"}),(0,s.jsx)(c.$,{variant:1===T?"default":"outline",size:"lg",onClick:()=>{A(1),j(1),ee()},className:"min-w-[120px]",children:"Courses"}),(0,s.jsx)(c.$,{variant:2===T?"default":"outline",size:"lg",onClick:()=>{A(2),j(1),ee()},className:"min-w-[120px]",children:"Books"}),(0,s.jsx)(c.$,{variant:3===T?"default":"outline",size:"lg",onClick:()=>{A(3),j(1),ee()},className:"min-w-[120px]",children:"Journals"}),(0,s.jsx)(c.$,{variant:4===T?"default":"outline",size:"lg",onClick:()=>{A(4),j(1),ee()},className:"min-w-[120px]",children:"Medical Apps"})]})]})}),$&&(0,s.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 text-yellow-700 p-4 mb-6 rounded-md",children:[(0,s.jsxs)("div",{className:"flex items-center mb-2",children:[(0,s.jsx)(i.A,{className:"h-5 w-5 mr-2"}),(0,s.jsx)("h3",{className:"font-semibold",children:"API Notice"})]}),(0,s.jsx)("p",{className:"text-sm",children:$}),(0,s.jsx)("div",{className:"mt-3 flex gap-2",children:(0,s.jsx)(c.$,{size:"sm",onClick:()=>Y(),children:"Retry API Call"})})]}),(0,s.jsxs)("div",{className:"mb-6",children:[I&&(0,s.jsxs)("p",{className:"text-lg",children:["Search results for: ",(0,s.jsxs)("span",{className:"font-semibold",children:['"',I,'"']}),null!==P&&R.length>0&&(0,s.jsxs)("span",{children:[" in ",R.find(e=>e.id===P)?.name||"selected category"]}),null!==T&&U.length>0&&(0,s.jsxs)("span",{children:[" - ",U.find(e=>e.producttypeID===T)?.Name||"selected type"]})]}),!I&&(null!==P||null!==T)&&(0,s.jsxs)("p",{className:"text-lg",children:["Browsing:",null!==P&&R.length>0&&(0,s.jsx)("span",{className:"font-semibold ml-1",children:R.find(e=>e.id===P)?.name||"selected category"}),null!==T&&U.length>0&&(0,s.jsxs)("span",{className:"font-semibold ml-1",children:[null!==P?" - ":" ",U.find(e=>e.producttypeID===T)?.Name||"selected type"]})]})]}),(0,s.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[(0,s.jsx)("div",{className:"lg:w-1/4 space-y-6",children:(0,s.jsxs)("div",{className:"bg-white p-4 rounded-lg shadow",children:[(0,s.jsxs)("h2",{className:"text-lg font-semibold mb-4 flex items-center",children:[(0,s.jsx)(d,{className:"mr-2 h-5 w-5"}),"Filters"]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-medium mb-2",children:"Category"}),(0,s.jsxs)(G,{value:P?.toString()||"all",onValueChange:e=>{C("all"===e?null:Number(e)),j(1),ee()},children:[(0,s.jsx)(eF,{children:(0,s.jsx)(J,{placeholder:"All Categories"})}),(0,s.jsxs)(eq,{children:[(0,s.jsx)(eV,{value:"all",children:"All Categories"}),R.map(e=>(0,s.jsx)(eV,{value:e.id.toString(),children:e.name},e.id))]})]})]}),(0,s.jsxs)("div",{className:"hidden",children:[(0,s.jsx)("h3",{className:"font-medium mb-2",children:"Price Range"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"number",placeholder:"Min",className:"w-full p-2 border rounded",onChange:e=>O({...L,min:e.target.value?Number.parseFloat(e.target.value):null})}),(0,s.jsx)("span",{children:"-"}),(0,s.jsx)("input",{type:"number",placeholder:"Max",className:"w-full p-2 border rounded",onChange:e=>O({...L,max:e.target.value?Number.parseFloat(e.target.value):null})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-medium mb-2",children:"Sort By"}),(0,s.jsxs)(G,{value:b,onValueChange:S,children:[(0,s.jsx)(eF,{children:(0,s.jsx)(J,{})}),(0,s.jsxs)(eq,{children:[(0,s.jsx)(eV,{value:"ProductName ASC",children:"Name: A to Z"}),(0,s.jsx)(eV,{value:"ProductName DESC",children:"Name: Z to A"}),(0,s.jsx)(eV,{value:"Rating DESC",children:"Rating: High to Low"})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-medium mb-2",children:"Search"}),(0,s.jsx)("input",{type:"text",placeholder:"Search products...",value:k,onChange:e=>{E(e.target.value),Z(e.target.value)},className:"w-full p-2 border rounded"})]}),(0,s.jsx)(c.$,{className:"w-full",onClick:()=>{C(null),A(null),O({min:null,max:null}),S("Price DESC"),j(1),D(""),E(""),W({}),Q({}),window.history.pushState({},"",window.location.pathname)},disabled:F,children:"Reset Filters"})]})]})}),(0,s.jsxs)("div",{className:"lg:w-3/4",children:[(0,s.jsx)("div",{className:"grid grid-cols-2 gap-4 sm:gap-6 sm:grid-cols-2 lg:grid-cols-3",children:g?Array(12).fill(0).map((e,t)=>(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,s.jsx)("div",{className:"aspect-square",children:(0,s.jsx)(u.E,{className:"h-full w-full"})}),(0,s.jsxs)("div",{className:"p-4 space-y-2",children:[(0,s.jsx)(u.E,{className:"h-4 w-full"}),(0,s.jsx)(u.E,{className:"h-4 w-3/4"}),(0,s.jsx)(u.E,{className:"h-6 w-1/3"})]}),(0,s.jsx)("div",{className:"p-4 pt-0",children:(0,s.jsxs)("div",{className:"flex w-full gap-2",children:[(0,s.jsx)(u.E,{className:"h-10 flex-1"}),(0,s.jsx)(u.E,{className:"h-10 w-10"})]})})]},`skeleton-${t}`)):(()=>{let e=(y-1)*12;return m.slice(e,e+12)})().map(e=>e.ProductId?(0,s.jsx)(e4,{product:e},e.ProductId):null)}),!g&&0===m.length&&(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold mb-2",children:$?"Failed to load products":"No products found"}),(0,s.jsx)("p",{className:"text-gray-500",children:$?"Please check your connection and try again":"Try adjusting your filters or search criteria"}),$&&(0,s.jsx)(c.$,{className:"mt-4",onClick:()=>Y(),children:"Retry"})]}),!g&&m.length>0&&(()=>{let e=[],t=Math.max(1,y-Math.floor(2.5)),r=Math.min(w,t+5-1);r-t+1<5&&(t=Math.max(1,r-5+1));for(let a=t;a<=r;a++)e.push((0,s.jsx)(te,{children:(0,s.jsx)(tt,{onClick:()=>et(a),isActive:y===a,children:a})},a));return(0,s.jsx)(e8,{className:"mt-8",children:(0,s.jsxs)(e7,{children:[(0,s.jsx)(te,{children:(0,s.jsx)(tr,{onClick:()=>y>1&&et(y-1),className:1===y?"pointer-events-none opacity-50":"cursor-pointer"})}),t>1&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(te,{children:(0,s.jsx)(tt,{onClick:()=>et(1),children:"1"})}),t>2&&(0,s.jsx)(ta,{})]}),e,r<w&&(0,s.jsxs)(s.Fragment,{children:[r<w-1&&(0,s.jsx)(ta,{}),(0,s.jsx)(te,{children:(0,s.jsx)(tt,{onClick:()=>et(w),children:w})})]}),(0,s.jsx)(te,{children:(0,s.jsx)(ts,{onClick:()=>y<w&&et(y+1),className:y===w?"pointer-events-none opacity-50":"cursor-pointer"})})]})})})()]})]})]})}ta.displayName="PaginationEllipsis"},21820:e=>{"use strict";e.exports=require("os")},21956:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(41680).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},45037:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(41680).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},55591:e=>{"use strict";e.exports=require("https")},61551:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\products\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69e3:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,metadata:()=>a});var s=r(62740);let a={title:"All Products - Medical Store",description:"Browse our complete collection of medical products and equipment"};function l({children:e}){return(0,s.jsx)("section",{className:"min-h-screen bg-background",children:e})}},70801:(e,t,r)=>{"use strict";r.d(t,{dj:()=>p});var s=r(58009);let a=0,l=new Map,n=e=>{if(l.has(e))return;let t=setTimeout(()=>{l.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);l.set(e,t)},i=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?n(r):e.toasts.forEach(e=>{n(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},o=[],d={toasts:[]};function c(e){d=i(d,e),o.forEach(e=>{e(d)})}function u({...e}){let t=(a=(a+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>c({type:"DISMISS_TOAST",toastId:t});return c({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||r()}}}),{id:t,dismiss:r,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function p(){let[e,t]=s.useState(d);return s.useEffect(()=>(o.push(t),()=>{let e=o.indexOf(t);e>-1&&o.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},72123:(e,t,r)=>{Promise.resolve().then(r.bind(r,19978))},74075:e=>{"use strict";e.exports=require("zlib")},77252:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var s=r(45512),a=r(21643),l=r(59462);let n=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i({className:e,variant:t,...r}){return(0,s.jsx)("div",{className:(0,l.cn)(n({variant:t}),e),...r})}},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{},97643:(e,t,r)=>{"use strict";r.d(t,{Wu:()=>i,Zp:()=>n,wL:()=>o});var s=r(45512),a=r(58009),l=r(59462);let n=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));n.displayName="Card",a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",e),...t})).displayName="CardHeader",a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("h3",{ref:r,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t})).displayName="CardTitle",a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("p",{ref:r,className:(0,l.cn)("text-sm text-muted-foreground",e),...t})).displayName="CardDescription";let i=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,l.cn)("p-6 pt-0",e),...t}));i.displayName="CardContent";let o=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,l.cn)("flex items-center p-6 pt-0",e),...t}));o.displayName="CardFooter"}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[638,551,609,875],()=>r(3970));module.exports=s})();