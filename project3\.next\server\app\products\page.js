(()=>{var e={};e.id=571,e.ids=[571],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3970:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>d,pages:()=>u,routeModule:()=>f,tree:()=>c});var n=r(70260),o=r(28203),a=r(25155),i=r.n(a),l=r(67292),s={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(s[e]=()=>l[e]);r.d(t,s);let c={children:["",{children:["products",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,61551)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\products\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,69e3)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\products\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,19611)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=["C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\products\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},f=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/products/page",pathname:"/products",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},4643:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(41680).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},5884:(e,t,r)=>{"use strict";r.d(t,{E:()=>a});var n=r(45512),o=r(59462);function a({className:e,...t}){return(0,n.jsx)("div",{className:(0,o.cn)("animate-pulse rounded-md bg-muted",e),...t})}},6004:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(58009),o=r(45512);function a(e,t=[]){let r=[],i=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return i.scopeName=e,[function(t,a){let i=n.createContext(a),l=r.length;r=[...r,a];let s=t=>{let{scope:r,children:a,...s}=t,c=r?.[e]?.[l]||i,u=n.useMemo(()=>s,Object.values(s));return(0,o.jsx)(c.Provider,{value:u,children:a})};return s.displayName=t+"Provider",[s,function(r,o){let s=o?.[e]?.[l]||i,c=n.useContext(s);if(c)return c;if(void 0!==a)return a;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(i,...t)]}},6467:(e,t,r)=>{Promise.resolve().then(r.bind(r,61551))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13024:(e,t,r)=>{"use strict";r.d(t,{i:()=>a});var n=r(58009),o=r(92828);function a({prop:e,defaultProp:t,onChange:r=()=>{}}){let[a,i]=function({defaultProp:e,onChange:t}){let r=n.useState(e),[a]=r,i=n.useRef(a),l=(0,o.c)(t);return n.useEffect(()=>{i.current!==a&&(l(a),i.current=a)},[a,i,l]),r}({defaultProp:t,onChange:r}),l=void 0!==e,s=l?e:a,c=(0,o.c)(r);return[s,n.useCallback(t=>{if(l){let r="function"==typeof t?t(e):t;r!==e&&c(r)}else i(t)},[l,e,i,c])]}},14494:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(41680).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},21956:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(41680).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30096:(e,t,r)=>{"use strict";r.d(t,{B:()=>s});var n,o=r(58009),a=r(49397),i=(n||(n=r.t(o,2)))["useId".toString()]||(()=>void 0),l=0;function s(e){let[t,r]=o.useState(i());return(0,a.N)(()=>{e||r(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}},30830:(e,t,r)=>{"use strict";r.d(t,{hO:()=>s,sG:()=>l});var n=r(58009),o=r(55740),a=r(12705),i=r(45512),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=n.forwardRef((e,r)=>{let{asChild:n,...o}=e,l=n?a.DX:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(l,{...o,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function s(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},31412:(e,t,r)=>{"use strict";function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}r.d(t,{m:()=>n})},33873:e=>{"use strict";e.exports=require("path")},39217:(e,t,r)=>{"use strict";r.d(t,{N:()=>s});var n=r(58009),o=r(6004),a=r(29952),i=r(12705),l=r(45512);function s(e){let t=e+"CollectionProvider",[r,s]=(0,o.A)(t),[c,u]=r(t,{collectionRef:{current:null},itemMap:new Map}),d=e=>{let{scope:t,children:r}=e,o=n.useRef(null),a=n.useRef(new Map).current;return(0,l.jsx)(c,{scope:t,itemMap:a,collectionRef:o,children:r})};d.displayName=t;let f=e+"CollectionSlot",p=n.forwardRef((e,t)=>{let{scope:r,children:n}=e,o=u(f,r),s=(0,a.s)(t,o.collectionRef);return(0,l.jsx)(i.DX,{ref:s,children:n})});p.displayName=f;let m=e+"CollectionItemSlot",h="data-radix-collection-item",g=n.forwardRef((e,t)=>{let{scope:r,children:o,...s}=e,c=n.useRef(null),d=(0,a.s)(t,c),f=u(m,r);return n.useEffect(()=>(f.itemMap.set(c,{ref:c,...s}),()=>void f.itemMap.delete(c))),(0,l.jsx)(i.DX,{[h]:"",ref:d,children:o})});return g.displayName=m,[{Provider:d,Slot:p,ItemSlot:g},function(t){let r=u(e+"CollectionConsumer",t);return n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${h}]`));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},s]}},45037:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(41680).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},49397:(e,t,r)=>{"use strict";r.d(t,{N:()=>o});var n=r(58009),o=globalThis?.document?n.useLayoutEffect:()=>{}},55591:e=>{"use strict";e.exports=require("https")},59018:(e,t,r)=>{"use strict";r.d(t,{jH:()=>a});var n=r(58009);r(45512);var o=n.createContext(void 0);function a(e){let t=n.useContext(o);return e||t||"ltr"}},61551:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\products\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69e3:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a,metadata:()=>o});var n=r(62740);let o={title:"All Products - Medical Store",description:"Browse our complete collection of medical products and equipment"};function a({children:e}){return(0,n.jsx)("section",{className:"min-h-screen bg-background",children:e})}},70801:(e,t,r)=>{"use strict";r.d(t,{dj:()=>f});var n=r(58009);let o=0,a=new Map,i=e=>{if(a.has(e))return;let t=setTimeout(()=>{a.delete(e),u({type:"REMOVE_TOAST",toastId:e})},1e6);a.set(e,t)},l=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?i(r):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},s=[],c={toasts:[]};function u(e){c=l(c,e),s.forEach(e=>{e(c)})}function d({...e}){let t=(o=(o+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>u({type:"DISMISS_TOAST",toastId:t});return u({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||r()}}}),{id:t,dismiss:r,update:e=>u({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function f(){let[e,t]=n.useState(c);return n.useEffect(()=>(s.push(t),()=>{let e=s.indexOf(t);e>-1&&s.splice(e,1)}),[e]),{...e,toast:d,dismiss:e=>u({type:"DISMISS_TOAST",toastId:e})}}},72123:(e,t,r)=>{Promise.resolve().then(r.bind(r,87412))},74075:e=>{"use strict";e.exports=require("zlib")},77252:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var n=r(45512),o=r(21643),a=r(59462);let i=(0,o.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,...r}){return(0,n.jsx)("div",{className:(0,a.cn)(i({variant:t}),e),...r})}},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},87412:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>nA});var n,o,a,i=r(45512),l=r(58009),s=r(79334),c=r(10985),u=r(45037),d=r(41680);let f=(0,d.A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]);var p=r(87021),m=r(5884),h=r(55740);function g(e,[t,r]){return Math.min(r,Math.max(t,e))}var x=r(31412),v=r(39217),y=r(29952),w=r(6004),b=r(59018),j=r(30830),N=r(92828),S="dismissableLayer.update",E=l.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),C=l.forwardRef((e,t)=>{let{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:n,onPointerDownOutside:a,onFocusOutside:s,onInteractOutside:c,onDismiss:u,...d}=e,f=l.useContext(E),[p,m]=l.useState(null),h=p?.ownerDocument??globalThis?.document,[,g]=l.useState({}),v=(0,y.s)(t,e=>m(e)),w=Array.from(f.layers),[b]=[...f.layersWithOutsidePointerEventsDisabled].slice(-1),C=w.indexOf(b),T=p?w.indexOf(p):-1,R=f.layersWithOutsidePointerEventsDisabled.size>0,k=T>=C,D=function(e,t=globalThis?.document){let r=(0,N.c)(e),n=l.useRef(!1),o=l.useRef(()=>{});return l.useEffect(()=>{let e=e=>{if(e.target&&!n.current){let n=function(){A("dismissableLayer.pointerDownOutside",r,a,{discrete:!0})},a={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",o.current),o.current=n,t.addEventListener("click",o.current,{once:!0})):n()}else t.removeEventListener("click",o.current);n.current=!1},a=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(a),t.removeEventListener("pointerdown",e),t.removeEventListener("click",o.current)}},[t,r]),{onPointerDownCapture:()=>n.current=!0}}(e=>{let t=e.target,r=[...f.branches].some(e=>e.contains(t));!k||r||(a?.(e),c?.(e),e.defaultPrevented||u?.())},h),I=function(e,t=globalThis?.document){let r=(0,N.c)(e),n=l.useRef(!1);return l.useEffect(()=>{let e=e=>{e.target&&!n.current&&A("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,r]),{onFocusCapture:()=>n.current=!0,onBlurCapture:()=>n.current=!1}}(e=>{let t=e.target;[...f.branches].some(e=>e.contains(t))||(s?.(e),c?.(e),e.defaultPrevented||u?.())},h);return function(e,t=globalThis?.document){let r=(0,N.c)(e);l.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{T===f.layers.size-1&&(n?.(e),!e.defaultPrevented&&u&&(e.preventDefault(),u()))},h),l.useEffect(()=>{if(p)return r&&(0===f.layersWithOutsidePointerEventsDisabled.size&&(o=h.body.style.pointerEvents,h.body.style.pointerEvents="none"),f.layersWithOutsidePointerEventsDisabled.add(p)),f.layers.add(p),P(),()=>{r&&1===f.layersWithOutsidePointerEventsDisabled.size&&(h.body.style.pointerEvents=o)}},[p,h,r,f]),l.useEffect(()=>()=>{p&&(f.layers.delete(p),f.layersWithOutsidePointerEventsDisabled.delete(p),P())},[p,f]),l.useEffect(()=>{let e=()=>g({});return document.addEventListener(S,e),()=>document.removeEventListener(S,e)},[]),(0,i.jsx)(j.sG.div,{...d,ref:v,style:{pointerEvents:R?k?"auto":"none":void 0,...e.style},onFocusCapture:(0,x.m)(e.onFocusCapture,I.onFocusCapture),onBlurCapture:(0,x.m)(e.onBlurCapture,I.onBlurCapture),onPointerDownCapture:(0,x.m)(e.onPointerDownCapture,D.onPointerDownCapture)})});function P(){let e=new CustomEvent(S);document.dispatchEvent(e)}function A(e,t,r,{discrete:n}){let o=r.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&o.addEventListener(e,t,{once:!0}),n?(0,j.hO)(o,a):o.dispatchEvent(a)}C.displayName="DismissableLayer",l.forwardRef((e,t)=>{let r=l.useContext(E),n=l.useRef(null),o=(0,y.s)(t,n);return l.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,i.jsx)(j.sG.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var T=0;function R(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var k="focusScope.autoFocusOnMount",D="focusScope.autoFocusOnUnmount",I={bubbles:!1,cancelable:!0},L=l.forwardRef((e,t)=>{let{loop:r=!1,trapped:n=!1,onMountAutoFocus:o,onUnmountAutoFocus:a,...s}=e,[c,u]=l.useState(null),d=(0,N.c)(o),f=(0,N.c)(a),p=l.useRef(null),m=(0,y.s)(t,e=>u(e)),h=l.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;l.useEffect(()=>{if(n){let e=function(e){if(h.paused||!c)return;let t=e.target;c.contains(t)?p.current=t:F(p.current,{select:!0})},t=function(e){if(h.paused||!c)return;let t=e.relatedTarget;null===t||c.contains(t)||F(p.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&F(c)});return c&&r.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[n,c,h.paused]),l.useEffect(()=>{if(c){W.add(h);let e=document.activeElement;if(!c.contains(e)){let t=new CustomEvent(k,I);c.addEventListener(k,d),c.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let r=document.activeElement;for(let n of e)if(F(n,{select:t}),document.activeElement!==r)return}(M(c).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&F(c))}return()=>{c.removeEventListener(k,d),setTimeout(()=>{let t=new CustomEvent(D,I);c.addEventListener(D,f),c.dispatchEvent(t),t.defaultPrevented||F(e??document.body,{select:!0}),c.removeEventListener(D,f),W.remove(h)},0)}}},[c,d,f,h]);let g=l.useCallback(e=>{if(!r&&!n||h.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[n,a]=function(e){let t=M(e);return[O(t,e),O(t.reverse(),e)]}(t);n&&a?e.shiftKey||o!==a?e.shiftKey&&o===n&&(e.preventDefault(),r&&F(a,{select:!0})):(e.preventDefault(),r&&F(n,{select:!0})):o===t&&e.preventDefault()}},[r,n,h.paused]);return(0,i.jsx)(j.sG.div,{tabIndex:-1,...s,ref:m,onKeyDown:g})});function M(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function O(e,t){for(let r of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function F(e,{select:t=!1}={}){if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}L.displayName="FocusScope";var W=function(){let e=[];return{add(t){let r=e[0];t!==r&&r?.pause(),(e=_(e,t)).unshift(t)},remove(t){e=_(e,t),e[0]?.resume()}}}();function _(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}var H=r(30096);let $=["top","right","bottom","left"],B=Math.min,z=Math.max,U=Math.round,V=Math.floor,q=e=>({x:e,y:e}),G={left:"right",right:"left",bottom:"top",top:"bottom"},K={start:"end",end:"start"};function X(e,t){return"function"==typeof e?e(t):e}function Y(e){return e.split("-")[0]}function Q(e){return e.split("-")[1]}function Z(e){return"x"===e?"y":"x"}function J(e){return"y"===e?"height":"width"}function ee(e){return["top","bottom"].includes(Y(e))?"y":"x"}function et(e){return e.replace(/start|end/g,e=>K[e])}function er(e){return e.replace(/left|right|bottom|top/g,e=>G[e])}function en(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function eo(e){let{x:t,y:r,width:n,height:o}=e;return{width:n,height:o,top:r,left:t,right:t+n,bottom:r+o,x:t,y:r}}function ea(e,t,r){let n,{reference:o,floating:a}=e,i=ee(t),l=Z(ee(t)),s=J(l),c=Y(t),u="y"===i,d=o.x+o.width/2-a.width/2,f=o.y+o.height/2-a.height/2,p=o[s]/2-a[s]/2;switch(c){case"top":n={x:d,y:o.y-a.height};break;case"bottom":n={x:d,y:o.y+o.height};break;case"right":n={x:o.x+o.width,y:f};break;case"left":n={x:o.x-a.width,y:f};break;default:n={x:o.x,y:o.y}}switch(Q(t)){case"start":n[l]-=p*(r&&u?-1:1);break;case"end":n[l]+=p*(r&&u?-1:1)}return n}let ei=async(e,t,r)=>{let{placement:n="bottom",strategy:o="absolute",middleware:a=[],platform:i}=r,l=a.filter(Boolean),s=await (null==i.isRTL?void 0:i.isRTL(t)),c=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:u,y:d}=ea(c,n,s),f=n,p={},m=0;for(let r=0;r<l.length;r++){let{name:a,fn:h}=l[r],{x:g,y:x,data:v,reset:y}=await h({x:u,y:d,initialPlacement:n,placement:f,strategy:o,middlewareData:p,rects:c,platform:i,elements:{reference:e,floating:t}});u=null!=g?g:u,d=null!=x?x:d,p={...p,[a]:{...p[a],...v}},y&&m<=50&&(m++,"object"==typeof y&&(y.placement&&(f=y.placement),y.rects&&(c=!0===y.rects?await i.getElementRects({reference:e,floating:t,strategy:o}):y.rects),{x:u,y:d}=ea(c,f,s)),r=-1)}return{x:u,y:d,placement:f,strategy:o,middlewareData:p}};async function el(e,t){var r;void 0===t&&(t={});let{x:n,y:o,platform:a,rects:i,elements:l,strategy:s}=e,{boundary:c="clippingAncestors",rootBoundary:u="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=X(t,e),m=en(p),h=l[f?"floating"===d?"reference":"floating":d],g=eo(await a.getClippingRect({element:null==(r=await (null==a.isElement?void 0:a.isElement(h)))||r?h:h.contextElement||await (null==a.getDocumentElement?void 0:a.getDocumentElement(l.floating)),boundary:c,rootBoundary:u,strategy:s})),x="floating"===d?{x:n,y:o,width:i.floating.width,height:i.floating.height}:i.reference,v=await (null==a.getOffsetParent?void 0:a.getOffsetParent(l.floating)),y=await (null==a.isElement?void 0:a.isElement(v))&&await (null==a.getScale?void 0:a.getScale(v))||{x:1,y:1},w=eo(a.convertOffsetParentRelativeRectToViewportRelativeRect?await a.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:x,offsetParent:v,strategy:s}):x);return{top:(g.top-w.top+m.top)/y.y,bottom:(w.bottom-g.bottom+m.bottom)/y.y,left:(g.left-w.left+m.left)/y.x,right:(w.right-g.right+m.right)/y.x}}function es(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function ec(e){return $.some(t=>e[t]>=0)}async function eu(e,t){let{placement:r,platform:n,elements:o}=e,a=await (null==n.isRTL?void 0:n.isRTL(o.floating)),i=Y(r),l=Q(r),s="y"===ee(r),c=["left","top"].includes(i)?-1:1,u=a&&s?-1:1,d=X(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:m}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return l&&"number"==typeof m&&(p="end"===l?-1*m:m),s?{x:p*u,y:f*c}:{x:f*c,y:p*u}}function ed(){return"undefined"!=typeof window}function ef(e){return eh(e)?(e.nodeName||"").toLowerCase():"#document"}function ep(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function em(e){var t;return null==(t=(eh(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function eh(e){return!!ed()&&(e instanceof Node||e instanceof ep(e).Node)}function eg(e){return!!ed()&&(e instanceof Element||e instanceof ep(e).Element)}function ex(e){return!!ed()&&(e instanceof HTMLElement||e instanceof ep(e).HTMLElement)}function ev(e){return!!ed()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof ep(e).ShadowRoot)}function ey(e){let{overflow:t,overflowX:r,overflowY:n,display:o}=eS(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+r)&&!["inline","contents"].includes(o)}function ew(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function eb(e){let t=ej(),r=eg(e)?eS(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!r[e]&&"none"!==r[e])||!!r.containerType&&"normal"!==r.containerType||!t&&!!r.backdropFilter&&"none"!==r.backdropFilter||!t&&!!r.filter&&"none"!==r.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(r.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(r.contain||"").includes(e))}function ej(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function eN(e){return["html","body","#document"].includes(ef(e))}function eS(e){return ep(e).getComputedStyle(e)}function eE(e){return eg(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function eC(e){if("html"===ef(e))return e;let t=e.assignedSlot||e.parentNode||ev(e)&&e.host||em(e);return ev(t)?t.host:t}function eP(e,t,r){var n;void 0===t&&(t=[]),void 0===r&&(r=!0);let o=function e(t){let r=eC(t);return eN(r)?t.ownerDocument?t.ownerDocument.body:t.body:ex(r)&&ey(r)?r:e(r)}(e),a=o===(null==(n=e.ownerDocument)?void 0:n.body),i=ep(o);if(a){let e=eA(i);return t.concat(i,i.visualViewport||[],ey(o)?o:[],e&&r?eP(e):[])}return t.concat(o,eP(o,[],r))}function eA(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function eT(e){let t=eS(e),r=parseFloat(t.width)||0,n=parseFloat(t.height)||0,o=ex(e),a=o?e.offsetWidth:r,i=o?e.offsetHeight:n,l=U(r)!==a||U(n)!==i;return l&&(r=a,n=i),{width:r,height:n,$:l}}function eR(e){return eg(e)?e:e.contextElement}function ek(e){let t=eR(e);if(!ex(t))return q(1);let r=t.getBoundingClientRect(),{width:n,height:o,$:a}=eT(t),i=(a?U(r.width):r.width)/n,l=(a?U(r.height):r.height)/o;return i&&Number.isFinite(i)||(i=1),l&&Number.isFinite(l)||(l=1),{x:i,y:l}}let eD=q(0);function eI(e){let t=ep(e);return ej()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eD}function eL(e,t,r,n){var o;void 0===t&&(t=!1),void 0===r&&(r=!1);let a=e.getBoundingClientRect(),i=eR(e),l=q(1);t&&(n?eg(n)&&(l=ek(n)):l=ek(e));let s=(void 0===(o=r)&&(o=!1),n&&(!o||n===ep(i))&&o)?eI(i):q(0),c=(a.left+s.x)/l.x,u=(a.top+s.y)/l.y,d=a.width/l.x,f=a.height/l.y;if(i){let e=ep(i),t=n&&eg(n)?ep(n):n,r=e,o=eA(r);for(;o&&n&&t!==r;){let e=ek(o),t=o.getBoundingClientRect(),n=eS(o),a=t.left+(o.clientLeft+parseFloat(n.paddingLeft))*e.x,i=t.top+(o.clientTop+parseFloat(n.paddingTop))*e.y;c*=e.x,u*=e.y,d*=e.x,f*=e.y,c+=a,u+=i,o=eA(r=ep(o))}}return eo({width:d,height:f,x:c,y:u})}function eM(e,t){let r=eE(e).scrollLeft;return t?t.left+r:eL(em(e)).left+r}function eO(e,t,r){void 0===r&&(r=!1);let n=e.getBoundingClientRect();return{x:n.left+t.scrollLeft-(r?0:eM(e,n)),y:n.top+t.scrollTop}}function eF(e,t,r){let n;if("viewport"===t)n=function(e,t){let r=ep(e),n=em(e),o=r.visualViewport,a=n.clientWidth,i=n.clientHeight,l=0,s=0;if(o){a=o.width,i=o.height;let e=ej();(!e||e&&"fixed"===t)&&(l=o.offsetLeft,s=o.offsetTop)}return{width:a,height:i,x:l,y:s}}(e,r);else if("document"===t)n=function(e){let t=em(e),r=eE(e),n=e.ownerDocument.body,o=z(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),a=z(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight),i=-r.scrollLeft+eM(e),l=-r.scrollTop;return"rtl"===eS(n).direction&&(i+=z(t.clientWidth,n.clientWidth)-o),{width:o,height:a,x:i,y:l}}(em(e));else if(eg(t))n=function(e,t){let r=eL(e,!0,"fixed"===t),n=r.top+e.clientTop,o=r.left+e.clientLeft,a=ex(e)?ek(e):q(1),i=e.clientWidth*a.x,l=e.clientHeight*a.y;return{width:i,height:l,x:o*a.x,y:n*a.y}}(t,r);else{let r=eI(e);n={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return eo(n)}function eW(e){return"static"===eS(e).position}function e_(e,t){if(!ex(e)||"fixed"===eS(e).position)return null;if(t)return t(e);let r=e.offsetParent;return em(e)===r&&(r=r.ownerDocument.body),r}function eH(e,t){let r=ep(e);if(ew(e))return r;if(!ex(e)){let t=eC(e);for(;t&&!eN(t);){if(eg(t)&&!eW(t))return t;t=eC(t)}return r}let n=e_(e,t);for(;n&&["table","td","th"].includes(ef(n))&&eW(n);)n=e_(n,t);return n&&eN(n)&&eW(n)&&!eb(n)?r:n||function(e){let t=eC(e);for(;ex(t)&&!eN(t);){if(eb(t))return t;if(ew(t))break;t=eC(t)}return null}(e)||r}let e$=async function(e){let t=this.getOffsetParent||eH,r=this.getDimensions,n=await r(e.floating);return{reference:function(e,t,r){let n=ex(t),o=em(t),a="fixed"===r,i=eL(e,!0,a,t),l={scrollLeft:0,scrollTop:0},s=q(0);if(n||!n&&!a){if(("body"!==ef(t)||ey(o))&&(l=eE(t)),n){let e=eL(t,!0,a,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else o&&(s.x=eM(o))}let c=!o||n||a?q(0):eO(o,l);return{x:i.left+l.scrollLeft-s.x-c.x,y:i.top+l.scrollTop-s.y-c.y,width:i.width,height:i.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}},eB={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:r,offsetParent:n,strategy:o}=e,a="fixed"===o,i=em(n),l=!!t&&ew(t.floating);if(n===i||l&&a)return r;let s={scrollLeft:0,scrollTop:0},c=q(1),u=q(0),d=ex(n);if((d||!d&&!a)&&(("body"!==ef(n)||ey(i))&&(s=eE(n)),ex(n))){let e=eL(n);c=ek(n),u.x=e.x+n.clientLeft,u.y=e.y+n.clientTop}let f=!i||d||a?q(0):eO(i,s,!0);return{width:r.width*c.x,height:r.height*c.y,x:r.x*c.x-s.scrollLeft*c.x+u.x+f.x,y:r.y*c.y-s.scrollTop*c.y+u.y+f.y}},getDocumentElement:em,getClippingRect:function(e){let{element:t,boundary:r,rootBoundary:n,strategy:o}=e,a=[..."clippingAncestors"===r?ew(t)?[]:function(e,t){let r=t.get(e);if(r)return r;let n=eP(e,[],!1).filter(e=>eg(e)&&"body"!==ef(e)),o=null,a="fixed"===eS(e).position,i=a?eC(e):e;for(;eg(i)&&!eN(i);){let t=eS(i),r=eb(i);r||"fixed"!==t.position||(o=null),(a?!r&&!o:!r&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||ey(i)&&!r&&function e(t,r){let n=eC(t);return!(n===r||!eg(n)||eN(n))&&("fixed"===eS(n).position||e(n,r))}(e,i))?n=n.filter(e=>e!==i):o=t,i=eC(i)}return t.set(e,n),n}(t,this._c):[].concat(r),n],i=a[0],l=a.reduce((e,r)=>{let n=eF(t,r,o);return e.top=z(n.top,e.top),e.right=B(n.right,e.right),e.bottom=B(n.bottom,e.bottom),e.left=z(n.left,e.left),e},eF(t,i,o));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}},getOffsetParent:eH,getElementRects:e$,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:r}=eT(e);return{width:t,height:r}},getScale:ek,isElement:eg,isRTL:function(e){return"rtl"===eS(e).direction}};function ez(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eU=e=>({name:"arrow",options:e,async fn(t){let{x:r,y:n,placement:o,rects:a,platform:i,elements:l,middlewareData:s}=t,{element:c,padding:u=0}=X(e,t)||{};if(null==c)return{};let d=en(u),f={x:r,y:n},p=Z(ee(o)),m=J(p),h=await i.getDimensions(c),g="y"===p,x=g?"clientHeight":"clientWidth",v=a.reference[m]+a.reference[p]-f[p]-a.floating[m],y=f[p]-a.reference[p],w=await (null==i.getOffsetParent?void 0:i.getOffsetParent(c)),b=w?w[x]:0;b&&await (null==i.isElement?void 0:i.isElement(w))||(b=l.floating[x]||a.floating[m]);let j=b/2-h[m]/2-1,N=B(d[g?"top":"left"],j),S=B(d[g?"bottom":"right"],j),E=b-h[m]-S,C=b/2-h[m]/2+(v/2-y/2),P=z(N,B(C,E)),A=!s.arrow&&null!=Q(o)&&C!==P&&a.reference[m]/2-(C<N?N:S)-h[m]/2<0,T=A?C<N?C-N:C-E:0;return{[p]:f[p]+T,data:{[p]:P,centerOffset:C-P-T,...A&&{alignmentOffset:T}},reset:A}}}),eV=(e,t,r)=>{let n=new Map,o={platform:eB,...r},a={...o.platform,_c:n};return ei(e,t,{...o,platform:a})};var eq="undefined"!=typeof document?l.useLayoutEffect:l.useEffect;function eG(e,t){let r,n,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((r=e.length)!==t.length)return!1;for(n=r;0!=n--;)if(!eG(e[n],t[n]))return!1;return!0}if((r=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(n=r;0!=n--;)if(!({}).hasOwnProperty.call(t,o[n]))return!1;for(n=r;0!=n--;){let r=o[n];if(("_owner"!==r||!e.$$typeof)&&!eG(e[r],t[r]))return!1}return!0}return e!=e&&t!=t}function eK(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eX(e,t){let r=eK(e);return Math.round(t*r)/r}function eY(e){let t=l.useRef(e);return eq(()=>{t.current=e}),t}let eQ=e=>({name:"arrow",options:e,fn(t){let{element:r,padding:n}="function"==typeof e?e(t):e;return r&&({}).hasOwnProperty.call(r,"current")?null!=r.current?eU({element:r.current,padding:n}).fn(t):{}:r?eU({element:r,padding:n}).fn(t):{}}}),eZ=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var r,n;let{x:o,y:a,placement:i,middlewareData:l}=t,s=await eu(t,e);return i===(null==(r=l.offset)?void 0:r.placement)&&null!=(n=l.arrow)&&n.alignmentOffset?{}:{x:o+s.x,y:a+s.y,data:{...s,placement:i}}}}}(e),options:[e,t]}),eJ=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:r,y:n,placement:o}=t,{mainAxis:a=!0,crossAxis:i=!1,limiter:l={fn:e=>{let{x:t,y:r}=e;return{x:t,y:r}}},...s}=X(e,t),c={x:r,y:n},u=await el(t,s),d=ee(Y(o)),f=Z(d),p=c[f],m=c[d];if(a){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",r=p+u[e],n=p-u[t];p=z(r,B(p,n))}if(i){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",r=m+u[e],n=m-u[t];m=z(r,B(m,n))}let h=l.fn({...t,[f]:p,[d]:m});return{...h,data:{x:h.x-r,y:h.y-n,enabled:{[f]:a,[d]:i}}}}}}(e),options:[e,t]}),e0=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:r,y:n,placement:o,rects:a,middlewareData:i}=t,{offset:l=0,mainAxis:s=!0,crossAxis:c=!0}=X(e,t),u={x:r,y:n},d=ee(o),f=Z(d),p=u[f],m=u[d],h=X(l,t),g="number"==typeof h?{mainAxis:h,crossAxis:0}:{mainAxis:0,crossAxis:0,...h};if(s){let e="y"===f?"height":"width",t=a.reference[f]-a.floating[e]+g.mainAxis,r=a.reference[f]+a.reference[e]-g.mainAxis;p<t?p=t:p>r&&(p=r)}if(c){var x,v;let e="y"===f?"width":"height",t=["top","left"].includes(Y(o)),r=a.reference[d]-a.floating[e]+(t&&(null==(x=i.offset)?void 0:x[d])||0)+(t?0:g.crossAxis),n=a.reference[d]+a.reference[e]+(t?0:(null==(v=i.offset)?void 0:v[d])||0)-(t?g.crossAxis:0);m<r?m=r:m>n&&(m=n)}return{[f]:p,[d]:m}}}}(e),options:[e,t]}),e1=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var r,n,o,a,i;let{placement:l,middlewareData:s,rects:c,initialPlacement:u,platform:d,elements:f}=t,{mainAxis:p=!0,crossAxis:m=!0,fallbackPlacements:h,fallbackStrategy:g="bestFit",fallbackAxisSideDirection:x="none",flipAlignment:v=!0,...y}=X(e,t);if(null!=(r=s.arrow)&&r.alignmentOffset)return{};let w=Y(l),b=ee(u),j=Y(u)===u,N=await (null==d.isRTL?void 0:d.isRTL(f.floating)),S=h||(j||!v?[er(u)]:function(e){let t=er(e);return[et(e),t,et(t)]}(u)),E="none"!==x;!h&&E&&S.push(...function(e,t,r,n){let o=Q(e),a=function(e,t,r){let n=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(r)return t?o:n;return t?n:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(Y(e),"start"===r,n);return o&&(a=a.map(e=>e+"-"+o),t&&(a=a.concat(a.map(et)))),a}(u,v,x,N));let C=[u,...S],P=await el(t,y),A=[],T=(null==(n=s.flip)?void 0:n.overflows)||[];if(p&&A.push(P[w]),m){let e=function(e,t,r){void 0===r&&(r=!1);let n=Q(e),o=Z(ee(e)),a=J(o),i="x"===o?n===(r?"end":"start")?"right":"left":"start"===n?"bottom":"top";return t.reference[a]>t.floating[a]&&(i=er(i)),[i,er(i)]}(l,c,N);A.push(P[e[0]],P[e[1]])}if(T=[...T,{placement:l,overflows:A}],!A.every(e=>e<=0)){let e=((null==(o=s.flip)?void 0:o.index)||0)+1,t=C[e];if(t)return{data:{index:e,overflows:T},reset:{placement:t}};let r=null==(a=T.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:a.placement;if(!r)switch(g){case"bestFit":{let e=null==(i=T.filter(e=>{if(E){let t=ee(e.placement);return t===b||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:i[0];e&&(r=e);break}case"initialPlacement":r=u}if(l!==r)return{reset:{placement:r}}}return{}}}}(e),options:[e,t]}),e2=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var r,n;let o,a;let{placement:i,rects:l,platform:s,elements:c}=t,{apply:u=()=>{},...d}=X(e,t),f=await el(t,d),p=Y(i),m=Q(i),h="y"===ee(i),{width:g,height:x}=l.floating;"top"===p||"bottom"===p?(o=p,a=m===(await (null==s.isRTL?void 0:s.isRTL(c.floating))?"start":"end")?"left":"right"):(a=p,o="end"===m?"top":"bottom");let v=x-f.top-f.bottom,y=g-f.left-f.right,w=B(x-f[o],v),b=B(g-f[a],y),j=!t.middlewareData.shift,N=w,S=b;if(null!=(r=t.middlewareData.shift)&&r.enabled.x&&(S=y),null!=(n=t.middlewareData.shift)&&n.enabled.y&&(N=v),j&&!m){let e=z(f.left,0),t=z(f.right,0),r=z(f.top,0),n=z(f.bottom,0);h?S=g-2*(0!==e||0!==t?e+t:z(f.left,f.right)):N=x-2*(0!==r||0!==n?r+n:z(f.top,f.bottom))}await u({...t,availableWidth:S,availableHeight:N});let E=await s.getDimensions(c.floating);return g!==E.width||x!==E.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),e5=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:r}=t,{strategy:n="referenceHidden",...o}=X(e,t);switch(n){case"referenceHidden":{let e=es(await el(t,{...o,elementContext:"reference"}),r.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:ec(e)}}}case"escaped":{let e=es(await el(t,{...o,altBoundary:!0}),r.floating);return{data:{escapedOffsets:e,escaped:ec(e)}}}default:return{}}}}}(e),options:[e,t]}),e4=(e,t)=>({...eQ(e),options:[e,t]});var e3=l.forwardRef((e,t)=>{let{children:r,width:n=10,height:o=5,...a}=e;return(0,i.jsx)(j.sG.svg,{...a,ref:t,width:n,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,i.jsx)("polygon",{points:"0,0 30,0 15,10"})})});e3.displayName="Arrow";var e6=r(49397),e9="Popper",[e8,e7]=(0,w.A)(e9),[te,tt]=e8(e9),tr=e=>{let{__scopePopper:t,children:r}=e,[n,o]=l.useState(null);return(0,i.jsx)(te,{scope:t,anchor:n,onAnchorChange:o,children:r})};tr.displayName=e9;var tn="PopperAnchor",to=l.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:n,...o}=e,a=tt(tn,r),s=l.useRef(null),c=(0,y.s)(t,s);return l.useEffect(()=>{a.onAnchorChange(n?.current||s.current)}),n?null:(0,i.jsx)(j.sG.div,{...o,ref:c})});to.displayName=tn;var ta="PopperContent",[ti,tl]=e8(ta),ts=l.forwardRef((e,t)=>{let{__scopePopper:r,side:n="bottom",sideOffset:o=0,align:a="center",alignOffset:s=0,arrowPadding:c=0,avoidCollisions:u=!0,collisionBoundary:d=[],collisionPadding:f=0,sticky:p="partial",hideWhenDetached:m=!1,updatePositionStrategy:g="optimized",onPlaced:x,...v}=e,w=tt(ta,r),[b,S]=l.useState(null),E=(0,y.s)(t,e=>S(e)),[C,P]=l.useState(null),A=function(e){let[t,r]=l.useState(void 0);return(0,e6.N)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let a=t[0];if("borderBoxSize"in a){let e=a.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}(C),T=A?.width??0,R=A?.height??0,k="number"==typeof f?f:{top:0,right:0,bottom:0,left:0,...f},D=Array.isArray(d)?d:[d],I=D.length>0,L={padding:k,boundary:D.filter(tf),altBoundary:I},{refs:M,floatingStyles:O,placement:F,isPositioned:W,middlewareData:_}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:r="absolute",middleware:n=[],platform:o,elements:{reference:a,floating:i}={},transform:s=!0,whileElementsMounted:c,open:u}=e,[d,f]=l.useState({x:0,y:0,strategy:r,placement:t,middlewareData:{},isPositioned:!1}),[p,m]=l.useState(n);eG(p,n)||m(n);let[g,x]=l.useState(null),[v,y]=l.useState(null),w=l.useCallback(e=>{e!==S.current&&(S.current=e,x(e))},[]),b=l.useCallback(e=>{e!==E.current&&(E.current=e,y(e))},[]),j=a||g,N=i||v,S=l.useRef(null),E=l.useRef(null),C=l.useRef(d),P=null!=c,A=eY(c),T=eY(o),R=eY(u),k=l.useCallback(()=>{if(!S.current||!E.current)return;let e={placement:t,strategy:r,middleware:p};T.current&&(e.platform=T.current),eV(S.current,E.current,e).then(e=>{let t={...e,isPositioned:!1!==R.current};D.current&&!eG(C.current,t)&&(C.current=t,h.flushSync(()=>{f(t)}))})},[p,t,r,T,R]);eq(()=>{!1===u&&C.current.isPositioned&&(C.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[u]);let D=l.useRef(!1);eq(()=>(D.current=!0,()=>{D.current=!1}),[]),eq(()=>{if(j&&(S.current=j),N&&(E.current=N),j&&N){if(A.current)return A.current(j,N,k);k()}},[j,N,k,A,P]);let I=l.useMemo(()=>({reference:S,floating:E,setReference:w,setFloating:b}),[w,b]),L=l.useMemo(()=>({reference:j,floating:N}),[j,N]),M=l.useMemo(()=>{let e={position:r,left:0,top:0};if(!L.floating)return e;let t=eX(L.floating,d.x),n=eX(L.floating,d.y);return s?{...e,transform:"translate("+t+"px, "+n+"px)",...eK(L.floating)>=1.5&&{willChange:"transform"}}:{position:r,left:t,top:n}},[r,s,L.floating,d.x,d.y]);return l.useMemo(()=>({...d,update:k,refs:I,elements:L,floatingStyles:M}),[d,k,I,L,M])}({strategy:"fixed",placement:n+("center"!==a?"-"+a:""),whileElementsMounted:(...e)=>(function(e,t,r,n){let o;void 0===n&&(n={});let{ancestorScroll:a=!0,ancestorResize:i=!0,elementResize:l="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:c=!1}=n,u=eR(e),d=a||i?[...u?eP(u):[],...eP(t)]:[];d.forEach(e=>{a&&e.addEventListener("scroll",r,{passive:!0}),i&&e.addEventListener("resize",r)});let f=u&&s?function(e,t){let r,n=null,o=em(e);function a(){var e;clearTimeout(r),null==(e=n)||e.disconnect(),n=null}return function i(l,s){void 0===l&&(l=!1),void 0===s&&(s=1),a();let c=e.getBoundingClientRect(),{left:u,top:d,width:f,height:p}=c;if(l||t(),!f||!p)return;let m=V(d),h=V(o.clientWidth-(u+f)),g={rootMargin:-m+"px "+-h+"px "+-V(o.clientHeight-(d+p))+"px "+-V(u)+"px",threshold:z(0,B(1,s))||1},x=!0;function v(t){let n=t[0].intersectionRatio;if(n!==s){if(!x)return i();n?i(!1,n):r=setTimeout(()=>{i(!1,1e-7)},1e3)}1!==n||ez(c,e.getBoundingClientRect())||i(),x=!1}try{n=new IntersectionObserver(v,{...g,root:o.ownerDocument})}catch(e){n=new IntersectionObserver(v,g)}n.observe(e)}(!0),a}(u,r):null,p=-1,m=null;l&&(m=new ResizeObserver(e=>{let[n]=e;n&&n.target===u&&m&&(m.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=m)||e.observe(t)})),r()}),u&&!c&&m.observe(u),m.observe(t));let h=c?eL(e):null;return c&&function t(){let n=eL(e);h&&!ez(h,n)&&r(),h=n,o=requestAnimationFrame(t)}(),r(),()=>{var e;d.forEach(e=>{a&&e.removeEventListener("scroll",r),i&&e.removeEventListener("resize",r)}),null==f||f(),null==(e=m)||e.disconnect(),m=null,c&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===g}),elements:{reference:w.anchor},middleware:[eZ({mainAxis:o+R,alignmentAxis:s}),u&&eJ({mainAxis:!0,crossAxis:!1,limiter:"partial"===p?e0():void 0,...L}),u&&e1({...L}),e2({...L,apply:({elements:e,rects:t,availableWidth:r,availableHeight:n})=>{let{width:o,height:a}=t.reference,i=e.floating.style;i.setProperty("--radix-popper-available-width",`${r}px`),i.setProperty("--radix-popper-available-height",`${n}px`),i.setProperty("--radix-popper-anchor-width",`${o}px`),i.setProperty("--radix-popper-anchor-height",`${a}px`)}}),C&&e4({element:C,padding:c}),tp({arrowWidth:T,arrowHeight:R}),m&&e5({strategy:"referenceHidden",...L})]}),[H,$]=tm(F),U=(0,N.c)(x);(0,e6.N)(()=>{W&&U?.()},[W,U]);let q=_.arrow?.x,G=_.arrow?.y,K=_.arrow?.centerOffset!==0,[X,Y]=l.useState();return(0,e6.N)(()=>{b&&Y(window.getComputedStyle(b).zIndex)},[b]),(0,i.jsx)("div",{ref:M.setFloating,"data-radix-popper-content-wrapper":"",style:{...O,transform:W?O.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:X,"--radix-popper-transform-origin":[_.transformOrigin?.x,_.transformOrigin?.y].join(" "),..._.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,i.jsx)(ti,{scope:r,placedSide:H,onArrowChange:P,arrowX:q,arrowY:G,shouldHideArrow:K,children:(0,i.jsx)(j.sG.div,{"data-side":H,"data-align":$,...v,ref:E,style:{...v.style,animation:W?void 0:"none"}})})})});ts.displayName=ta;var tc="PopperArrow",tu={top:"bottom",right:"left",bottom:"top",left:"right"},td=l.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,o=tl(tc,r),a=tu[o.placedSide];return(0,i.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[a]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,i.jsx)(e3,{...n,ref:t,style:{...n.style,display:"block"}})})});function tf(e){return null!==e}td.displayName=tc;var tp=e=>({name:"transformOrigin",options:e,fn(t){let{placement:r,rects:n,middlewareData:o}=t,a=o.arrow?.centerOffset!==0,i=a?0:e.arrowWidth,l=a?0:e.arrowHeight,[s,c]=tm(r),u={start:"0%",center:"50%",end:"100%"}[c],d=(o.arrow?.x??0)+i/2,f=(o.arrow?.y??0)+l/2,p="",m="";return"bottom"===s?(p=a?u:`${d}px`,m=`${-l}px`):"top"===s?(p=a?u:`${d}px`,m=`${n.floating.height+l}px`):"right"===s?(p=`${-l}px`,m=a?u:`${f}px`):"left"===s&&(p=`${n.floating.width+l}px`,m=a?u:`${f}px`),{data:{x:p,y:m}}}});function tm(e){let[t,r="center"]=e.split("-");return[t,r]}var th=l.forwardRef((e,t)=>{let{container:r,...n}=e,[o,a]=l.useState(!1);(0,e6.N)(()=>a(!0),[]);let s=r||o&&globalThis?.document?.body;return s?h.createPortal((0,i.jsx)(j.sG.div,{...n,ref:t}),s):null});th.displayName="Portal";var tg=r(12705),tx=r(13024),tv=l.forwardRef((e,t)=>(0,i.jsx)(j.sG.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));tv.displayName="VisuallyHidden";var ty=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},tw=new WeakMap,tb=new WeakMap,tj={},tN=0,tS=function(e){return e&&(e.host||tS(e.parentNode))},tE=function(e,t,r,n){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var r=tS(e);return r&&t.contains(r)?r:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});tj[r]||(tj[r]=new WeakMap);var a=tj[r],i=[],l=new Set,s=new Set(o),c=function(e){!(!e||l.has(e))&&(l.add(e),c(e.parentNode))};o.forEach(c);var u=function(e){!(!e||s.has(e))&&Array.prototype.forEach.call(e.children,function(e){if(l.has(e))u(e);else try{var t=e.getAttribute(n),o=null!==t&&"false"!==t,s=(tw.get(e)||0)+1,c=(a.get(e)||0)+1;tw.set(e,s),a.set(e,c),i.push(e),1===s&&o&&tb.set(e,!0),1===c&&e.setAttribute(r,"true"),o||e.setAttribute(n,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return u(t),l.clear(),tN++,function(){i.forEach(function(e){var t=tw.get(e)-1,o=a.get(e)-1;tw.set(e,t),a.set(e,o),t||(tb.has(e)||e.removeAttribute(n),tb.delete(e)),o||e.removeAttribute(r)}),--tN||(tw=new WeakMap,tw=new WeakMap,tb=new WeakMap,tj={})}},tC=function(e,t,r){void 0===r&&(r="data-aria-hidden");var n=Array.from(Array.isArray(e)?e:[e]),o=t||ty(e);return o?(n.push.apply(n,Array.from(o.querySelectorAll("[aria-live]"))),tE(n,o,r,"aria-hidden")):function(){return null}},tP=r(97412),tA="right-scroll-bar-position",tT="width-before-scroll-bar";function tR(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var tk="undefined"!=typeof window?l.useLayoutEffect:l.useEffect,tD=new WeakMap;function tI(e){return e}var tL=function(e){void 0===e&&(e={});var t,r,n,o,a=(t=null,void 0===r&&(r=tI),n=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var t=r(e,o);return n.push(t),function(){n=n.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){o=!0;var t=[];if(n.length){var r=n;n=[],r.forEach(e),t=n}var a=function(){var r=t;t=[],r.forEach(e)},i=function(){return Promise.resolve().then(a)};i(),n={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),n}}}});return a.options=(0,tP.Cl)({async:!0,ssr:!1},e),a}(),tM=function(){},tO=l.forwardRef(function(e,t){var r,n,o,a,i=l.useRef(null),s=l.useState({onScrollCapture:tM,onWheelCapture:tM,onTouchMoveCapture:tM}),c=s[0],u=s[1],d=e.forwardProps,f=e.children,p=e.className,m=e.removeScrollBar,h=e.enabled,g=e.shards,x=e.sideCar,v=e.noIsolation,y=e.inert,w=e.allowPinchZoom,b=e.as,j=e.gapMode,N=(0,tP.Tt)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),S=(r=[i,t],n=function(e){return r.forEach(function(t){return tR(t,e)})},(o=(0,l.useState)(function(){return{value:null,callback:n,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=n,a=o.facade,tk(function(){var e=tD.get(a);if(e){var t=new Set(e),n=new Set(r),o=a.current;t.forEach(function(e){n.has(e)||tR(e,null)}),n.forEach(function(e){t.has(e)||tR(e,o)})}tD.set(a,r)},[r]),a),E=(0,tP.Cl)((0,tP.Cl)({},N),c);return l.createElement(l.Fragment,null,h&&l.createElement(x,{sideCar:tL,removeScrollBar:m,shards:g,noIsolation:v,inert:y,setCallbacks:u,allowPinchZoom:!!w,lockRef:i,gapMode:j}),d?l.cloneElement(l.Children.only(f),(0,tP.Cl)((0,tP.Cl)({},E),{ref:S})):l.createElement(void 0===b?"div":b,(0,tP.Cl)({},E,{className:p,ref:S}),f))});tO.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},tO.classNames={fullWidth:tT,zeroRight:tA};var tF=function(e){var t=e.sideCar,r=(0,tP.Tt)(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var n=t.read();if(!n)throw Error("Sidecar medium not found");return l.createElement(n,(0,tP.Cl)({},r))};tF.isSideCarExport=!0;var tW=function(){var e=0,t=null;return{add:function(n){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=a||r.nc;return t&&e.setAttribute("nonce",t),e}())){var o,i;(o=t).styleSheet?o.styleSheet.cssText=n:o.appendChild(document.createTextNode(n)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},t_=function(){var e=tW();return function(t,r){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&r])}},tH=function(){var e=t_();return function(t){return e(t.styles,t.dynamic),null}},t$={left:0,top:0,right:0,gap:0},tB=function(e){return parseInt(e||"",10)||0},tz=function(e){var t=window.getComputedStyle(document.body),r=t["padding"===e?"paddingLeft":"marginLeft"],n=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[tB(r),tB(n),tB(o)]},tU=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return t$;var t=tz(e),r=document.documentElement.clientWidth,n=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,n-r+t[2]-t[0])}},tV=tH(),tq="data-scroll-locked",tG=function(e,t,r,n){var o=e.left,a=e.top,i=e.right,l=e.gap;return void 0===r&&(r="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(n,";\n   padding-right: ").concat(l,"px ").concat(n,";\n  }\n  body[").concat(tq,"] {\n    overflow: hidden ").concat(n,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(n,";"),"margin"===r&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(l,"px ").concat(n,";\n    "),"padding"===r&&"padding-right: ".concat(l,"px ").concat(n,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(tA," {\n    right: ").concat(l,"px ").concat(n,";\n  }\n  \n  .").concat(tT," {\n    margin-right: ").concat(l,"px ").concat(n,";\n  }\n  \n  .").concat(tA," .").concat(tA," {\n    right: 0 ").concat(n,";\n  }\n  \n  .").concat(tT," .").concat(tT," {\n    margin-right: 0 ").concat(n,";\n  }\n  \n  body[").concat(tq,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(l,"px;\n  }\n")},tK=function(){var e=parseInt(document.body.getAttribute(tq)||"0",10);return isFinite(e)?e:0},tX=function(){l.useEffect(function(){return document.body.setAttribute(tq,(tK()+1).toString()),function(){var e=tK()-1;e<=0?document.body.removeAttribute(tq):document.body.setAttribute(tq,e.toString())}},[])},tY=function(e){var t=e.noRelative,r=e.noImportant,n=e.gapMode,o=void 0===n?"margin":n;tX();var a=l.useMemo(function(){return tU(o)},[o]);return l.createElement(tV,{styles:tG(a,!t,o,r?"":"!important")})},tQ=!1;if("undefined"!=typeof window)try{var tZ=Object.defineProperty({},"passive",{get:function(){return tQ=!0,!0}});window.addEventListener("test",tZ,tZ),window.removeEventListener("test",tZ,tZ)}catch(e){tQ=!1}var tJ=!!tQ&&{passive:!1},t0=function(e,t){if(!(e instanceof Element))return!1;var r=window.getComputedStyle(e);return"hidden"!==r[t]&&(r.overflowY!==r.overflowX||"TEXTAREA"===e.tagName||"visible"!==r[t])},t1=function(e,t){var r=t.ownerDocument,n=t;do{if("undefined"!=typeof ShadowRoot&&n instanceof ShadowRoot&&(n=n.host),t2(e,n)){var o=t5(e,n);if(o[1]>o[2])return!0}n=n.parentNode}while(n&&n!==r.body);return!1},t2=function(e,t){return"v"===e?t0(t,"overflowY"):t0(t,"overflowX")},t5=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},t4=function(e,t,r,n,o){var a,i=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),l=i*n,s=r.target,c=t.contains(s),u=!1,d=l>0,f=0,p=0;do{var m=t5(e,s),h=m[0],g=m[1]-m[2]-i*h;(h||g)&&t2(e,s)&&(f+=g,p+=h),s=s instanceof ShadowRoot?s.host:s.parentNode}while(!c&&s!==document.body||c&&(t.contains(s)||t===s));return d&&(o&&1>Math.abs(f)||!o&&l>f)?u=!0:!d&&(o&&1>Math.abs(p)||!o&&-l>p)&&(u=!0),u},t3=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},t6=function(e){return[e.deltaX,e.deltaY]},t9=function(e){return e&&"current"in e?e.current:e},t8=0,t7=[];let re=(n=function(e){var t=l.useRef([]),r=l.useRef([0,0]),n=l.useRef(),o=l.useState(t8++)[0],a=l.useState(tH)[0],i=l.useRef(e);l.useEffect(function(){i.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(0,tP.fX)([e.lockRef.current],(e.shards||[]).map(t9),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var s=l.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!i.current.allowPinchZoom;var o,a=t3(e),l=r.current,s="deltaX"in e?e.deltaX:l[0]-a[0],c="deltaY"in e?e.deltaY:l[1]-a[1],u=e.target,d=Math.abs(s)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===u.type)return!1;var f=t1(d,u);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=t1(d,u)),!f)return!1;if(!n.current&&"changedTouches"in e&&(s||c)&&(n.current=o),!o)return!0;var p=n.current||o;return t4(p,t,e,"h"===p?s:c,!0)},[]),c=l.useCallback(function(e){if(t7.length&&t7[t7.length-1]===a){var r="deltaY"in e?t6(e):t3(e),n=t.current.filter(function(t){var n;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(n=t.delta)[0]===r[0]&&n[1]===r[1]})[0];if(n&&n.should){e.cancelable&&e.preventDefault();return}if(!n){var o=(i.current.shards||[]).map(t9).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?s(e,o[0]):!i.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),u=l.useCallback(function(e,r,n,o){var a={name:e,delta:r,target:n,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(n)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),d=l.useCallback(function(e){r.current=t3(e),n.current=void 0},[]),f=l.useCallback(function(t){u(t.type,t6(t),t.target,s(t,e.lockRef.current))},[]),p=l.useCallback(function(t){u(t.type,t3(t),t.target,s(t,e.lockRef.current))},[]);l.useEffect(function(){return t7.push(a),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,tJ),document.addEventListener("touchmove",c,tJ),document.addEventListener("touchstart",d,tJ),function(){t7=t7.filter(function(e){return e!==a}),document.removeEventListener("wheel",c,tJ),document.removeEventListener("touchmove",c,tJ),document.removeEventListener("touchstart",d,tJ)}},[]);var m=e.removeScrollBar,h=e.inert;return l.createElement(l.Fragment,null,h?l.createElement(a,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,m?l.createElement(tY,{gapMode:e.gapMode}):null)},tL.useMedium(n),tF);var rt=l.forwardRef(function(e,t){return l.createElement(tO,(0,tP.Cl)({},e,{ref:t,sideCar:re}))});rt.classNames=tO.classNames;var rr=[" ","Enter","ArrowUp","ArrowDown"],rn=[" ","Enter"],ro="Select",[ra,ri,rl]=(0,v.N)(ro),[rs,rc]=(0,w.A)(ro,[rl,e7]),ru=e7(),[rd,rf]=rs(ro),[rp,rm]=rs(ro),rh=e=>{let{__scopeSelect:t,children:r,open:n,defaultOpen:o,onOpenChange:a,value:s,defaultValue:c,onValueChange:u,dir:d,name:f,autoComplete:p,disabled:m,required:h,form:g}=e,x=ru(t),[v,y]=l.useState(null),[w,j]=l.useState(null),[N,S]=l.useState(!1),E=(0,b.jH)(d),[C=!1,P]=(0,tx.i)({prop:n,defaultProp:o,onChange:a}),[A,T]=(0,tx.i)({prop:s,defaultProp:c,onChange:u}),R=l.useRef(null),k=!v||g||!!v.closest("form"),[D,I]=l.useState(new Set),L=Array.from(D).map(e=>e.props.value).join(";");return(0,i.jsx)(tr,{...x,children:(0,i.jsxs)(rd,{required:h,scope:t,trigger:v,onTriggerChange:y,valueNode:w,onValueNodeChange:j,valueNodeHasChildren:N,onValueNodeHasChildrenChange:S,contentId:(0,H.B)(),value:A,onValueChange:T,open:C,onOpenChange:P,dir:E,triggerPointerDownPosRef:R,disabled:m,children:[(0,i.jsx)(ra.Provider,{scope:t,children:(0,i.jsx)(rp,{scope:e.__scopeSelect,onNativeOptionAdd:l.useCallback(e=>{I(t=>new Set(t).add(e))},[]),onNativeOptionRemove:l.useCallback(e=>{I(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),k?(0,i.jsxs)(r0,{"aria-hidden":!0,required:h,tabIndex:-1,name:f,autoComplete:p,value:A,onChange:e=>T(e.target.value),disabled:m,form:g,children:[void 0===A?(0,i.jsx)("option",{value:""}):null,Array.from(D)]},L):null]})})};rh.displayName=ro;var rg="SelectTrigger",rx=l.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:n=!1,...o}=e,a=ru(r),s=rf(rg,r),c=s.disabled||n,u=(0,y.s)(t,s.onTriggerChange),d=ri(r),f=l.useRef("touch"),[p,m,h]=r1(e=>{let t=d().filter(e=>!e.disabled),r=t.find(e=>e.value===s.value),n=r2(t,e,r);void 0!==n&&s.onValueChange(n.value)}),g=e=>{c||(s.onOpenChange(!0),h()),e&&(s.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,i.jsx)(to,{asChild:!0,...a,children:(0,i.jsx)(j.sG.button,{type:"button",role:"combobox","aria-controls":s.contentId,"aria-expanded":s.open,"aria-required":s.required,"aria-autocomplete":"none",dir:s.dir,"data-state":s.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":rJ(s.value)?"":void 0,...o,ref:u,onClick:(0,x.m)(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==f.current&&g(e)}),onPointerDown:(0,x.m)(o.onPointerDown,e=>{f.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(g(e),e.preventDefault())}),onKeyDown:(0,x.m)(o.onKeyDown,e=>{let t=""!==p.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&rr.includes(e.key)&&(g(),e.preventDefault())})})})});rx.displayName=rg;var rv="SelectValue",ry=l.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:o,children:a,placeholder:l="",...s}=e,c=rf(rv,r),{onValueNodeHasChildrenChange:u}=c,d=void 0!==a,f=(0,y.s)(t,c.onValueNodeChange);return(0,e6.N)(()=>{u(d)},[u,d]),(0,i.jsx)(j.sG.span,{...s,ref:f,style:{pointerEvents:"none"},children:rJ(c.value)?(0,i.jsx)(i.Fragment,{children:l}):a})});ry.displayName=rv;var rw=l.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...o}=e;return(0,i.jsx)(j.sG.span,{"aria-hidden":!0,...o,ref:t,children:n||"▼"})});rw.displayName="SelectIcon";var rb=e=>(0,i.jsx)(th,{asChild:!0,...e});rb.displayName="SelectPortal";var rj="SelectContent",rN=l.forwardRef((e,t)=>{let r=rf(rj,e.__scopeSelect),[n,o]=l.useState();return((0,e6.N)(()=>{o(new DocumentFragment)},[]),r.open)?(0,i.jsx)(rC,{...e,ref:t}):n?h.createPortal((0,i.jsx)(rS,{scope:e.__scopeSelect,children:(0,i.jsx)(ra.Slot,{scope:e.__scopeSelect,children:(0,i.jsx)("div",{children:e.children})})}),n):null});rN.displayName=rj;var[rS,rE]=rs(rj),rC=l.forwardRef((e,t)=>{let{__scopeSelect:r,position:n="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:a,onPointerDownOutside:s,side:c,sideOffset:u,align:d,alignOffset:f,arrowPadding:p,collisionBoundary:m,collisionPadding:h,sticky:g,hideWhenDetached:v,avoidCollisions:w,...b}=e,j=rf(rj,r),[N,S]=l.useState(null),[E,P]=l.useState(null),A=(0,y.s)(t,e=>S(e)),[k,D]=l.useState(null),[I,M]=l.useState(null),O=ri(r),[F,W]=l.useState(!1),_=l.useRef(!1);l.useEffect(()=>{if(N)return tC(N)},[N]),l.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??R()),document.body.insertAdjacentElement("beforeend",e[1]??R()),T++,()=>{1===T&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),T--}},[]);let H=l.useCallback(e=>{let[t,...r]=O().map(e=>e.ref.current),[n]=r.slice(-1),o=document.activeElement;for(let r of e)if(r===o||(r?.scrollIntoView({block:"nearest"}),r===t&&E&&(E.scrollTop=0),r===n&&E&&(E.scrollTop=E.scrollHeight),r?.focus(),document.activeElement!==o))return},[O,E]),$=l.useCallback(()=>H([k,N]),[H,k,N]);l.useEffect(()=>{F&&$()},[F,$]);let{onOpenChange:B,triggerPointerDownPosRef:z}=j;l.useEffect(()=>{if(N){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(z.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(z.current?.y??0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():N.contains(r.target)||B(!1),document.removeEventListener("pointermove",t),z.current=null};return null!==z.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[N,B,z]),l.useEffect(()=>{let e=()=>B(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[B]);let[U,V]=r1(e=>{let t=O().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=r2(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),q=l.useCallback((e,t,r)=>{let n=!_.current&&!r;(void 0!==j.value&&j.value===t||n)&&(D(e),n&&(_.current=!0))},[j.value]),G=l.useCallback(()=>N?.focus(),[N]),K=l.useCallback((e,t,r)=>{let n=!_.current&&!r;(void 0!==j.value&&j.value===t||n)&&M(e)},[j.value]),X="popper"===n?rA:rP,Y=X===rA?{side:c,sideOffset:u,align:d,alignOffset:f,arrowPadding:p,collisionBoundary:m,collisionPadding:h,sticky:g,hideWhenDetached:v,avoidCollisions:w}:{};return(0,i.jsx)(rS,{scope:r,content:N,viewport:E,onViewportChange:P,itemRefCallback:q,selectedItem:k,onItemLeave:G,itemTextRefCallback:K,focusSelectedItem:$,selectedItemText:I,position:n,isPositioned:F,searchRef:U,children:(0,i.jsx)(rt,{as:tg.DX,allowPinchZoom:!0,children:(0,i.jsx)(L,{asChild:!0,trapped:j.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,x.m)(o,e=>{j.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,i.jsx)(C,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>j.onOpenChange(!1),children:(0,i.jsx)(X,{role:"listbox",id:j.contentId,"data-state":j.open?"open":"closed",dir:j.dir,onContextMenu:e=>e.preventDefault(),...b,...Y,onPlaced:()=>W(!0),ref:A,style:{display:"flex",flexDirection:"column",outline:"none",...b.style},onKeyDown:(0,x.m)(b.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||V(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=O().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>H(t)),e.preventDefault()}})})})})})})});rC.displayName="SelectContentImpl";var rP=l.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:n,...o}=e,a=rf(rj,r),s=rE(rj,r),[c,u]=l.useState(null),[d,f]=l.useState(null),p=(0,y.s)(t,e=>f(e)),m=ri(r),h=l.useRef(!1),x=l.useRef(!0),{viewport:v,selectedItem:w,selectedItemText:b,focusSelectedItem:N}=s,S=l.useCallback(()=>{if(a.trigger&&a.valueNode&&c&&d&&v&&w&&b){let e=a.trigger.getBoundingClientRect(),t=d.getBoundingClientRect(),r=a.valueNode.getBoundingClientRect(),o=b.getBoundingClientRect();if("rtl"!==a.dir){let n=o.left-t.left,a=r.left-n,i=e.left-a,l=e.width+i,s=Math.max(l,t.width),u=g(a,[10,Math.max(10,window.innerWidth-10-s)]);c.style.minWidth=l+"px",c.style.left=u+"px"}else{let n=t.right-o.right,a=window.innerWidth-r.right-n,i=window.innerWidth-e.right-a,l=e.width+i,s=Math.max(l,t.width),u=g(a,[10,Math.max(10,window.innerWidth-10-s)]);c.style.minWidth=l+"px",c.style.right=u+"px"}let i=m(),l=window.innerHeight-20,s=v.scrollHeight,u=window.getComputedStyle(d),f=parseInt(u.borderTopWidth,10),p=parseInt(u.paddingTop,10),x=parseInt(u.borderBottomWidth,10),y=f+p+s+parseInt(u.paddingBottom,10)+x,j=Math.min(5*w.offsetHeight,y),N=window.getComputedStyle(v),S=parseInt(N.paddingTop,10),E=parseInt(N.paddingBottom,10),C=e.top+e.height/2-10,P=w.offsetHeight/2,A=f+p+(w.offsetTop+P);if(A<=C){let e=i.length>0&&w===i[i.length-1].ref.current;c.style.bottom="0px";let t=Math.max(l-C,P+(e?E:0)+(d.clientHeight-v.offsetTop-v.offsetHeight)+x);c.style.height=A+t+"px"}else{let e=i.length>0&&w===i[0].ref.current;c.style.top="0px";let t=Math.max(C,f+v.offsetTop+(e?S:0)+P);c.style.height=t+(y-A)+"px",v.scrollTop=A-C+v.offsetTop}c.style.margin="10px 0",c.style.minHeight=j+"px",c.style.maxHeight=l+"px",n?.(),requestAnimationFrame(()=>h.current=!0)}},[m,a.trigger,a.valueNode,c,d,v,w,b,a.dir,n]);(0,e6.N)(()=>S(),[S]);let[E,C]=l.useState();(0,e6.N)(()=>{d&&C(window.getComputedStyle(d).zIndex)},[d]);let P=l.useCallback(e=>{e&&!0===x.current&&(S(),N?.(),x.current=!1)},[S,N]);return(0,i.jsx)(rT,{scope:r,contentWrapper:c,shouldExpandOnScrollRef:h,onScrollButtonChange:P,children:(0,i.jsx)("div",{ref:u,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:E},children:(0,i.jsx)(j.sG.div,{...o,ref:p,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}})})})});rP.displayName="SelectItemAlignedPosition";var rA=l.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:o=10,...a}=e,l=ru(r);return(0,i.jsx)(ts,{...l,...a,ref:t,align:n,collisionPadding:o,style:{boxSizing:"border-box",...a.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});rA.displayName="SelectPopperPosition";var[rT,rR]=rs(rj,{}),rk="SelectViewport",rD=l.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:n,...o}=e,a=rE(rk,r),s=rR(rk,r),c=(0,y.s)(t,a.onViewportChange),u=l.useRef(0);return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:n}),(0,i.jsx)(ra.Slot,{scope:r,children:(0,i.jsx)(j.sG.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:(0,x.m)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=s;if(n?.current&&r){let e=Math.abs(u.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,o=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(o<n){let a=o+e,i=Math.min(n,a),l=a-i;r.style.height=i+"px","0px"===r.style.bottom&&(t.scrollTop=l>0?l:0,r.style.justifyContent="flex-end")}}}u.current=t.scrollTop})})})]})});rD.displayName=rk;var rI="SelectGroup",[rL,rM]=rs(rI);l.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=(0,H.B)();return(0,i.jsx)(rL,{scope:r,id:o,children:(0,i.jsx)(j.sG.div,{role:"group","aria-labelledby":o,...n,ref:t})})}).displayName=rI;var rO="SelectLabel",rF=l.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=rM(rO,r);return(0,i.jsx)(j.sG.div,{id:o.id,...n,ref:t})});rF.displayName=rO;var rW="SelectItem",[r_,rH]=rs(rW),r$=l.forwardRef((e,t)=>{let{__scopeSelect:r,value:n,disabled:o=!1,textValue:a,...s}=e,c=rf(rW,r),u=rE(rW,r),d=c.value===n,[f,p]=l.useState(a??""),[m,h]=l.useState(!1),g=(0,y.s)(t,e=>u.itemRefCallback?.(e,n,o)),v=(0,H.B)(),w=l.useRef("touch"),b=()=>{o||(c.onValueChange(n),c.onOpenChange(!1))};if(""===n)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,i.jsx)(r_,{scope:r,value:n,disabled:o,textId:v,isSelected:d,onItemTextChange:l.useCallback(e=>{p(t=>t||(e?.textContent??"").trim())},[]),children:(0,i.jsx)(ra.ItemSlot,{scope:r,value:n,disabled:o,textValue:f,children:(0,i.jsx)(j.sG.div,{role:"option","aria-labelledby":v,"data-highlighted":m?"":void 0,"aria-selected":d&&m,"data-state":d?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...s,ref:g,onFocus:(0,x.m)(s.onFocus,()=>h(!0)),onBlur:(0,x.m)(s.onBlur,()=>h(!1)),onClick:(0,x.m)(s.onClick,()=>{"mouse"!==w.current&&b()}),onPointerUp:(0,x.m)(s.onPointerUp,()=>{"mouse"===w.current&&b()}),onPointerDown:(0,x.m)(s.onPointerDown,e=>{w.current=e.pointerType}),onPointerMove:(0,x.m)(s.onPointerMove,e=>{w.current=e.pointerType,o?u.onItemLeave?.():"mouse"===w.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,x.m)(s.onPointerLeave,e=>{e.currentTarget===document.activeElement&&u.onItemLeave?.()}),onKeyDown:(0,x.m)(s.onKeyDown,e=>{(u.searchRef?.current===""||" "!==e.key)&&(rn.includes(e.key)&&b()," "===e.key&&e.preventDefault())})})})})});r$.displayName=rW;var rB="SelectItemText",rz=l.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:o,...a}=e,s=rf(rB,r),c=rE(rB,r),u=rH(rB,r),d=rm(rB,r),[f,p]=l.useState(null),m=(0,y.s)(t,e=>p(e),u.onItemTextChange,e=>c.itemTextRefCallback?.(e,u.value,u.disabled)),g=f?.textContent,x=l.useMemo(()=>(0,i.jsx)("option",{value:u.value,disabled:u.disabled,children:g},u.value),[u.disabled,u.value,g]),{onNativeOptionAdd:v,onNativeOptionRemove:w}=d;return(0,e6.N)(()=>(v(x),()=>w(x)),[v,w,x]),(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(j.sG.span,{id:u.textId,...a,ref:m}),u.isSelected&&s.valueNode&&!s.valueNodeHasChildren?h.createPortal(a.children,s.valueNode):null]})});rz.displayName=rB;var rU="SelectItemIndicator",rV=l.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return rH(rU,r).isSelected?(0,i.jsx)(j.sG.span,{"aria-hidden":!0,...n,ref:t}):null});rV.displayName=rU;var rq="SelectScrollUpButton",rG=l.forwardRef((e,t)=>{let r=rE(rq,e.__scopeSelect),n=rR(rq,e.__scopeSelect),[o,a]=l.useState(!1),s=(0,y.s)(t,n.onScrollButtonChange);return(0,e6.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){a(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,i.jsx)(rY,{...e,ref:s,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});rG.displayName=rq;var rK="SelectScrollDownButton",rX=l.forwardRef((e,t)=>{let r=rE(rK,e.__scopeSelect),n=rR(rK,e.__scopeSelect),[o,a]=l.useState(!1),s=(0,y.s)(t,n.onScrollButtonChange);return(0,e6.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;a(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,i.jsx)(rY,{...e,ref:s,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});rX.displayName=rK;var rY=l.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:n,...o}=e,a=rE("SelectScrollButton",r),s=l.useRef(null),c=ri(r),u=l.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return l.useEffect(()=>()=>u(),[u]),(0,e6.N)(()=>{let e=c().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[c]),(0,i.jsx)(j.sG.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,x.m)(o.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(n,50))}),onPointerMove:(0,x.m)(o.onPointerMove,()=>{a.onItemLeave?.(),null===s.current&&(s.current=window.setInterval(n,50))}),onPointerLeave:(0,x.m)(o.onPointerLeave,()=>{u()})})}),rQ=l.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,i.jsx)(j.sG.div,{"aria-hidden":!0,...n,ref:t})});rQ.displayName="SelectSeparator";var rZ="SelectArrow";function rJ(e){return""===e||void 0===e}l.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=ru(r),a=rf(rZ,r),l=rE(rZ,r);return a.open&&"popper"===l.position?(0,i.jsx)(td,{...o,...n,ref:t}):null}).displayName=rZ;var r0=l.forwardRef((e,t)=>{let{value:r,...n}=e,o=l.useRef(null),a=(0,y.s)(t,o),s=function(e){let t=l.useRef({value:e,previous:e});return l.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(r);return l.useEffect(()=>{let e=o.current,t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(s!==r&&t){let n=new Event("change",{bubbles:!0});t.call(e,r),e.dispatchEvent(n)}},[s,r]),(0,i.jsx)(tv,{asChild:!0,children:(0,i.jsx)("select",{...n,ref:a,defaultValue:r})})});function r1(e){let t=(0,N.c)(e),r=l.useRef(""),n=l.useRef(0),o=l.useCallback(e=>{let o=r.current+e;t(o),function e(t){r.current=t,window.clearTimeout(n.current),""!==t&&(n.current=window.setTimeout(()=>e(""),1e3))}(o)},[t]),a=l.useCallback(()=>{r.current="",window.clearTimeout(n.current)},[]);return l.useEffect(()=>()=>window.clearTimeout(n.current),[]),[r,o,a]}function r2(e,t,r){var n,o;let a=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=r?e.indexOf(r):-1,l=(n=e,o=Math.max(i,0),n.map((e,t)=>n[(o+t)%n.length]));1===a.length&&(l=l.filter(e=>e!==r));let s=l.find(e=>e.textValue.toLowerCase().startsWith(a.toLowerCase()));return s!==r?s:void 0}r0.displayName="BubbleSelect";let r5=(0,d.A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),r4=(0,d.A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]),r3=(0,d.A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);var r6=r(59462);let r9=l.forwardRef(({className:e,children:t,...r},n)=>(0,i.jsxs)(rx,{ref:n,className:(0,r6.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...r,children:[t,(0,i.jsx)(rw,{asChild:!0,children:(0,i.jsx)(r5,{className:"h-4 w-4 opacity-50"})})]}));r9.displayName=rx.displayName;let r8=l.forwardRef(({className:e,...t},r)=>(0,i.jsx)(rG,{ref:r,className:(0,r6.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,i.jsx)(r4,{className:"h-4 w-4"})}));r8.displayName=rG.displayName;let r7=l.forwardRef(({className:e,...t},r)=>(0,i.jsx)(rX,{ref:r,className:(0,r6.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,i.jsx)(r5,{className:"h-4 w-4"})}));r7.displayName=rX.displayName;let ne=l.forwardRef(({className:e,children:t,position:r="popper",...n},o)=>(0,i.jsx)(rb,{children:(0,i.jsxs)(rN,{ref:o,className:(0,r6.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-white text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...n,children:[(0,i.jsx)(r8,{}),(0,i.jsx)(rD,{className:(0,r6.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,i.jsx)(r7,{})]})}));ne.displayName=rN.displayName,l.forwardRef(({className:e,...t},r)=>(0,i.jsx)(rF,{ref:r,className:(0,r6.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=rF.displayName;let nt=l.forwardRef(({className:e,children:t,...r},n)=>(0,i.jsxs)(r$,{ref:n,className:(0,r6.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...r,children:[(0,i.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,i.jsx)(rV,{children:(0,i.jsx)(r3,{className:"h-4 w-4"})})}),(0,i.jsx)(rz,{children:t})]}));nt.displayName=r$.displayName,l.forwardRef(({className:e,...t},r)=>(0,i.jsx)(rQ,{ref:r,className:(0,r6.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=rQ.displayName;var nr=r(70801),nn=r(28531),no=r.n(nn),na=r(45103),ni=r(4643),nl=r(10617),ns=r(21956),nc=r(10453),nu=r(97643),nd=r(77252),nf=r(84194),np=r(31961),nm=r(71901);function nh({endDate:e}){let[t,r]=(0,l.useState)(null),n=({value:e,label:t})=>(0,i.jsxs)("div",{className:"flex flex-col items-center mx-0.5",children:[(0,i.jsxs)("div",{className:"relative w-7 h-7 sm:w-8 sm:h-8 flex items-center justify-center bg-gradient-to-br from-pink-500 to-red-600 rounded-md shadow-md",children:[(0,i.jsx)("div",{className:"absolute inset-0.5 bg-black/20 rounded-sm"}),(0,i.jsx)("span",{className:"relative z-10 text-white font-bold text-xs sm:text-sm",children:String(e).padStart(2,"0")})]}),(0,i.jsx)("span",{className:"text-[9px] sm:text-[10px] text-white/80 mt-0.5 font-medium",children:t})]});return t?(0,i.jsx)("div",{className:"bg-gradient-to-r from-pink-600/90 to-red-600/90 backdrop-blur-sm p-1.5 sm:p-2 rounded-lg shadow-lg",children:(0,i.jsxs)("div",{className:"flex justify-center items-center space-x-0.5",children:[t.days>0&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(n,{value:t.days,label:"Days"}),(0,i.jsx)("span",{className:"text-white font-bold text-sm -mb-2",children:":"})]}),(0,i.jsx)(n,{value:t.hours,label:"Hrs"}),(0,i.jsx)("span",{className:"text-white font-bold text-sm -mb-2",children:":"}),(0,i.jsx)(n,{value:t.minutes,label:"Min"}),(0,i.jsx)("span",{className:"text-white font-bold text-sm -mb-2",children:":"}),(0,i.jsx)(n,{value:t.seconds,label:"Sec"})]})}):(0,i.jsxs)("div",{className:"px-3 py-1.5 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center",children:[(0,i.jsx)(ni.A,{className:"w-3.5 h-3.5 mr-1.5 text-white animate-pulse"}),(0,i.jsx)("span",{className:"text-xs font-semibold text-white",children:"Sale Ended"})]})}function ng({product:e}){(0,nf._)();let t=(0,np.n)(),{toast:r}=(0,nr.dj)(),{primaryColor:n}=(0,nm.t)(),[o,a]=(0,l.useState)(!1),[s,c]=(0,l.useState)(!1),u=(e,t="USD")=>"IQD"===t?`IQD ${e.toLocaleString()}`:`$${e.toFixed(2)}`;e.SellStartDatetimeUTC&&e.SellEndDatetimeUTC;let d=new Date,f=e.SellStartDatetimeUTC?new Date(e.SellStartDatetimeUTC):null,m=e.SellEndDatetimeUTC?new Date(e.SellEndDatetimeUTC):null,h=f&&m&&d>=f&&d<=m;return(0,i.jsxs)(nu.Zp,{className:"overflow-hidden flex flex-col h-full relative",children:[(0,i.jsxs)("div",{className:"absolute top-2 left-2 z-10 flex flex-col gap-1",children:[e.MarkAsNew&&(0,i.jsx)(nd.E,{variant:"secondary",className:"bg-blue-500 text-white text-xs",children:"New"}),e.DiscountPrice&&e.DiscountPrice>0&&(0,i.jsx)(nd.E,{variant:"destructive",className:"bg-red-500 text-white text-xs",children:"Sale"}),h&&!e.DiscountPrice&&(0,i.jsx)(nd.E,{variant:"destructive",className:"bg-red-500 text-white text-xs",children:"Sale"})]}),(0,i.jsx)(no(),{href:`/product/${e.ProductId}`,children:(0,i.jsxs)("div",{className:"aspect-square overflow-hidden relative",children:[(0,i.jsx)("div",{className:"h-full w-full relative",children:(0,i.jsx)(na.default,{src:e.ProductImageUrl||"/placeholder.svg?height=300&width=300",alt:e.ProductName||"Product",fill:!0,className:"object-cover transition-transform hover:scale-105",onError:e=>{e.target.src="/placeholder.svg?height=300&width=300"},priority:!1,loading:"lazy"})}),h&&e.SellEndDatetimeUTC&&(0,i.jsx)("div",{className:"absolute bottom-0 left-0 right-0 p-2 flex justify-center",children:(0,i.jsx)(nh,{endDate:e.SellEndDatetimeUTC})})]})}),(0,i.jsxs)(nu.Wu,{className:"pt-4 flex-grow",children:[(0,i.jsxs)("div",{className:"flex items-center mb-2",children:[(0,i.jsx)("div",{className:"flex",children:[void 0,void 0,void 0,void 0,void 0].map((t,r)=>(0,i.jsx)(nl.A,{className:`w-4 h-4 ${r<Math.floor(e.Rating||0)?"text-yellow-400 fill-yellow-400":"text-gray-300"}`},r))}),(0,i.jsxs)("span",{className:"text-xs text-gray-500 ml-1",children:["(",e.Rating||0,")"]})]}),(0,i.jsx)(no(),{href:`/product/${e.ProductId}`,className:"hover:underline",children:(0,i.jsx)("h3",{className:"font-semibold text-lg line-clamp-2 mb-2",children:e.ProductName||"Unnamed Product"})}),e.ProductTypeName&&(0,i.jsxs)("p",{className:"text-sm text-gray-500 mb-2",children:["Type: ",e.ProductTypeName]}),(0,i.jsxs)("div",{className:"flex flex-col",children:[(0,i.jsx)("div",{className:"flex items-center gap-2",children:e.DiscountPrice?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("span",{className:"text-lg font-bold text-red-500",children:u(e.DiscountPrice)}),(0,i.jsx)("span",{className:"text-xs text-gray-500 line-through",children:u(e.Price||0)})]}):e.OldPrice&&e.OldPrice>e.Price?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("span",{className:"text-lg font-bold text-red-500",children:u(e.Price||0)}),(0,i.jsx)("span",{className:"text-xs text-gray-500 line-through",children:u(e.OldPrice)})]}):(0,i.jsx)("span",{className:"text-lg font-bold text-primary",children:u(e.Price||0)})}),e.IQDPrice&&(0,i.jsx)("span",{className:"text-sm font-medium text-green-600 mt-0.5",children:u(e.IQDPrice,"IQD")})]})]}),(0,i.jsx)(nu.wL,{className:"p-3 pt-1 mt-auto",children:(0,i.jsx)("div",{className:"w-full",children:(0,i.jsxs)("div",{className:"flex items-center justify-between gap-2",children:[(0,i.jsx)(p.$,{variant:"ghost",size:"sm",className:"h-8 px-2 text-xs font-medium flex-1 gap-1.5 text-white hover:opacity-90",style:{backgroundColor:n},asChild:!0,children:(0,i.jsxs)(no(),{href:`/product/${e.ProductId}`,children:[(0,i.jsx)(ns.A,{className:"h-3.5 w-3.5"}),(0,i.jsx)("span",{children:"View"})]})}),(0,i.jsx)(p.$,{variant:"ghost",size:"icon",className:"h-8 w-8 rounded-full",onClick:()=>{if(t.isHydrated){c(!0);try{t.isInWishlist(e.ProductId)?(t.removeFromWishlist(e.ProductId),r({description:`${e.ProductName} removed from wishlist`,type:"success"})):(t.addToWishlist(e.ProductId),r({description:`${e.ProductName} added to wishlist`,type:"success"}))}catch(e){console.error("Error updating wishlist:",e),r({description:"Failed to update wishlist",type:"error"})}finally{setTimeout(()=>{c(!1)},500)}}},disabled:s,children:(0,i.jsx)(nc.A,{className:`h-4 w-4 ${t.isInWishlist(e.ProductId)?"fill-red-500 text-red-500":""}`})})]})})})]})}var nx=r(52706),nv=r(99905),ny=r(14494);let nw=({className:e,...t})=>(0,i.jsx)("nav",{role:"navigation","aria-label":"pagination",className:(0,r6.cn)("mx-auto flex w-full justify-center",e),...t});nw.displayName="Pagination";let nb=l.forwardRef(({className:e,...t},r)=>(0,i.jsx)("ul",{ref:r,className:(0,r6.cn)("flex flex-row items-center gap-1",e),...t}));nb.displayName="PaginationContent";let nj=l.forwardRef(({className:e,...t},r)=>(0,i.jsx)("li",{ref:r,className:(0,r6.cn)("",e),...t}));nj.displayName="PaginationItem";let nN=({className:e,isActive:t,size:r="icon",...n})=>(0,i.jsx)("a",{"aria-current":t?"page":void 0,className:(0,r6.cn)((0,p.r)({variant:t?"outline":"ghost",size:r}),e),...n});nN.displayName="PaginationLink";let nS=({className:e,...t})=>(0,i.jsxs)(nN,{"aria-label":"Go to previous page",size:"default",className:(0,r6.cn)("gap-1 pl-2.5",e),...t,children:[(0,i.jsx)(nx.A,{className:"h-4 w-4"}),(0,i.jsx)("span",{children:"Previous"})]});nS.displayName="PaginationPrevious";let nE=({className:e,...t})=>(0,i.jsxs)(nN,{"aria-label":"Go to next page",size:"default",className:(0,r6.cn)("gap-1 pr-2.5",e),...t,children:[(0,i.jsx)("span",{children:"Next"}),(0,i.jsx)(nv.A,{className:"h-4 w-4"})]});nE.displayName="PaginationNext";let nC=({className:e,...t})=>(0,i.jsxs)("span",{"aria-hidden":!0,className:(0,r6.cn)("flex h-9 w-9 items-center justify-center",e),...t,children:[(0,i.jsx)(ny.A,{className:"h-4 w-4"}),(0,i.jsx)("span",{className:"sr-only",children:"More pages"})]});function nP({children:e}){let t=(0,s.useSearchParams)(),r=t.get("search")||"",n=t.get("category")||"all",o=t.get("productType")||"all";return(0,i.jsx)(i.Fragment,{children:e({searchTerm:r,categoryId:n,productTypeId:o})})}function nA(){return(0,i.jsx)(l.Suspense,{fallback:(0,i.jsx)(nT,{}),children:(0,i.jsx)(nP,{children:({searchTerm:e,categoryId:t,productTypeId:r})=>(0,i.jsx)(nR,{initialSearchTerm:e,initialCategoryId:t,initialProductTypeId:r})})})}function nT(){return(0,i.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,i.jsx)("div",{className:"h-8 w-64 bg-gray-200 rounded mb-4 animate-pulse"}),(0,i.jsx)("div",{className:"h-4 w-96 bg-gray-200 rounded mb-6 animate-pulse"}),(0,i.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[(0,i.jsx)("div",{className:"lg:w-1/4",children:(0,i.jsxs)("div",{className:"bg-white p-4 rounded-lg shadow",children:[(0,i.jsx)("div",{className:"h-6 w-32 bg-gray-200 rounded mb-4 animate-pulse"}),(0,i.jsx)("div",{className:"space-y-4",children:[,,,,].fill(0).map((e,t)=>(0,i.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"},t))})]})}),(0,i.jsx)("div",{className:"lg:w-3/4",children:(0,i.jsx)("div",{className:"grid grid-cols-2 gap-4 sm:gap-6 sm:grid-cols-2 lg:grid-cols-3",children:Array(12).fill(0).map((e,t)=>(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,i.jsx)("div",{className:"aspect-square bg-gray-200 animate-pulse"}),(0,i.jsxs)("div",{className:"p-4 space-y-2",children:[(0,i.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse"}),(0,i.jsx)("div",{className:"h-4 w-3/4 bg-gray-200 rounded animate-pulse"}),(0,i.jsx)("div",{className:"h-6 w-1/3 bg-gray-200 rounded animate-pulse"})]})]},t))})})]})]})}function nR({initialSearchTerm:e,initialCategoryId:t,initialProductTypeId:r}){var n;let o;let{toast:a}=(0,nr.dj)(),[s,d]=(0,l.useState)([]),[h,g]=(0,l.useState)([]),[x,v]=(0,l.useState)(!0),[y,w]=(0,l.useState)(1),[b,j]=(0,l.useState)(1),[N,S]=(0,l.useState)("Price DESC"),[E,C]=(0,l.useState)("all"!==t?Number.parseInt(t):null),[P,A]=(0,l.useState)("all"!==r?Number.parseInt(r):null),[T,R]=(0,l.useState)(e),[k,D]=(0,l.useState)(e),[I,L]=(0,l.useState)([]),[M,O]=(0,l.useState)([]),[F,W]=(0,l.useState)({min:null,max:null}),[_,H]=(0,l.useState)(!1),[$,B]=(0,l.useState)(null),[z,U]=(0,l.useState)([]),[V,q]=(0,l.useState)({}),[G,K]=(0,l.useState)({});(0,l.useCallback)((n=e=>{R(e),w(1)},(...e)=>{clearTimeout(o),o=setTimeout(()=>{n(...e)},500)}),[]);let X=e=>{let t=new Map;e.forEach(e=>{e.Attributes&&e.Attributes.length>0&&e.Attributes.forEach(e=>{let r=`${e.AttributeName}|${e.DisplayName}`;t.has(r)||t.set(r,new Map);let n=t.get(r),o=n.get(e.AttributeValueID);o?o.count++:n.set(e.AttributeValueID,{text:e.AttributeValueText,count:1})})});let r=[];t.forEach((e,t)=>{let[n,o]=t.split("|"),a=Array.from(e.entries()).map(([e,t])=>({id:e,text:t.text,count:t.count}));r.push({attributeName:n,displayName:o,values:a.sort((e,t)=>e.text.localeCompare(t.text))})}),U(r.sort((e,t)=>e.displayName.localeCompare(t.displayName)))},Y=async()=>{v(1===y),H(y>1),B(null);try{let e={requestParameters:{SearchTerm:T||"",SizeID:null,ColorID:null,CategoryID:E,TagID:null,ManufacturerID:null,producttypeId:P,MinPrice:F.min,MaxPrice:F.max,Rating:null,OrderByColumnName:(e=>{switch(e){case"Price ASC":return 1;case"Price DESC":default:return 0;case"ProductName ASC":return 2;case"ProductName DESC":return 3;case"Rating DESC":return 4}})(N),PageNo:1,PageSize:1e3,recordValueJson:"[]"}};console.log("Sending request to Next.js API route:",e);let t=await fetch("/api/products/get-products",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(console.log("API route response status:",t.status),!t.ok){let e=await t.json();throw Error(e.error||`HTTP error! status: ${t.status}`)}let r=await t.json();if(console.log("API route response data:",r),r&&r.data){let e=[];try{if("string"==typeof r.data?(console.log("Parsing string data:",r.data),e=JSON.parse(r.data)):Array.isArray(r.data)?(console.log("Using array data directly"),e=r.data):(console.log("Converting object to array:",r.data),e=[r.data]),console.log("Processed products data:",e),!Array.isArray(e))throw Error("Parsed data is not an array");if(0===e.length){console.log("No products found in API response"),d([]),g([]),j(0);return}console.log("First product in response:",e[0]),console.log("Available fields:",Object.keys(e[0]));let t=e.map(e=>{let t=e.ProductImagesUrl||e.ProductImageUrl,r=null;try{if(t){let e=t;if("string"==typeof t&&(t.startsWith("[")||t.startsWith('"')))try{let r=JSON.parse(t);Array.isArray(r)&&r.length>0?e=r[0].AttachmentURL||r[0]:"string"==typeof r&&(e=r)}catch(r){e=t.replace(/^"|"/g,"")}if("string"==typeof e&&""!==e.trim()&&(e=e.replace(/^"|"$/g,"").trim())){let t=decodeURIComponent(e),n=t.startsWith("/")||t.startsWith("http")?t:`/${t}`;r=n.startsWith("http")?n:`https://admin.codemedicalapps.com${n}`}}}catch(t){console.error("Error processing URL for product",e.ProductID||e.ProductId,":",t)}let n=e.ProductID||e.ProductId||e.Id||e.ID||e.id;return{...e,ProductId:n,ProductID:n,ProductName:e.ProductName||"Unnamed Product",Price:Number.parseFloat(e.Price)||0,OldPrice:e.OldPrice?Number.parseFloat(e.OldPrice):void 0,IQDPrice:Number.parseFloat(e.IQDPrice)||0,ProductTypeID:e.ProductTypeID,ProductTypeName:e.ProductTypeName,CategoryName:e.CategoryName||"Uncategorized",Rating:Number.parseFloat(e.Rating)||0,StockQuantity:Number.parseInt(e.StockQuantity,10)||0,ProductImageUrl:r,IsDiscountAllowed:!!e.IsDiscountAllowed,MarkAsNew:!!e.MarkAsNew,SellStartDatetimeUTC:e.SellStartDatetimeUTC||void 0,SellEndDatetimeUTC:e.SellEndDatetimeUTC||void 0,Attributes:e.Attributes||[],...e.DiscountPrice&&{DiscountPrice:Number.parseFloat(e.DiscountPrice)}}});if(console.log("Processed products:",t),g(t),d(t),X(t),e.length>0&&(e[0].TotalRecords||e[0].totalRecords)){let t=e[0].TotalRecords||e[0].totalRecords;j(Math.ceil(Number.parseInt(t,10)/12))}}catch(e){throw console.error("Error parsing product data:",e),console.error("Raw response data:",r),B(`Error parsing data: ${e.message||"Unknown"}`),e}}else console.warn("No data field in API response:",r),B("API response missing data field"),d([]),g([])}catch(e){console.error("Error fetching products:",e),B(`API Error: ${e.message||"Unknown"}`),a({description:"Failed to load products. Please try again.",type:"error"}),d([]),g([])}finally{H(!1),v(!1)}},Q=()=>{let e=new URLSearchParams;T&&e.append("search",T),null!==E&&e.append("category",E.toString()),null!==P&&e.append("productType",P.toString()),window.history.pushState({},"",`${window.location.pathname}?${e.toString()}`)},Z=e=>{w(e),window.scrollTo(0,0)};return(0,i.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,i.jsx)("h1",{className:"text-3xl font-bold mb-6",children:null!==E&&I.length>0?`${I.find(e=>e.id===E)?.name||"Category"} Products`:"All Products"}),(0,i.jsx)("div",{className:"bg-white p-6 rounded-lg shadow mb-6",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-4",children:[(0,i.jsx)(c.A,{className:"h-5 w-5 text-gray-600"}),(0,i.jsx)("span",{className:"font-medium text-gray-700",children:"Filter by Product Type"})]}),(0,i.jsxs)("div",{className:"flex justify-center gap-3 flex-wrap",children:[(0,i.jsx)(p.$,{variant:null===P?"default":"outline",size:"lg",onClick:()=>{A(null),w(1),Q()},className:"min-w-[120px]",children:"All Types"}),(0,i.jsx)(p.$,{variant:1===P?"default":"outline",size:"lg",onClick:()=>{A(1),w(1),Q()},className:"min-w-[120px]",children:"Courses"}),(0,i.jsx)(p.$,{variant:2===P?"default":"outline",size:"lg",onClick:()=>{A(2),w(1),Q()},className:"min-w-[120px]",children:"Books"}),(0,i.jsx)(p.$,{variant:3===P?"default":"outline",size:"lg",onClick:()=>{A(3),w(1),Q()},className:"min-w-[120px]",children:"Journals"}),(0,i.jsx)(p.$,{variant:4===P?"default":"outline",size:"lg",onClick:()=>{A(4),w(1),Q()},className:"min-w-[120px]",children:"Medical Apps"})]})]})}),$&&(0,i.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 text-yellow-700 p-4 mb-6 rounded-md",children:[(0,i.jsxs)("div",{className:"flex items-center mb-2",children:[(0,i.jsx)(u.A,{className:"h-5 w-5 mr-2"}),(0,i.jsx)("h3",{className:"font-semibold",children:"API Notice"})]}),(0,i.jsx)("p",{className:"text-sm",children:$}),(0,i.jsx)("div",{className:"mt-3 flex gap-2",children:(0,i.jsx)(p.$,{size:"sm",onClick:()=>Y(),children:"Retry API Call"})})]}),(0,i.jsxs)("div",{className:"mb-6",children:[T&&(0,i.jsxs)("p",{className:"text-lg",children:["Search results for: ",(0,i.jsxs)("span",{className:"font-semibold",children:['"',T,'"']}),null!==E&&I.length>0&&(0,i.jsxs)("span",{children:[" in ",I.find(e=>e.id===E)?.name||"selected category"]}),null!==P&&M.length>0&&(0,i.jsxs)("span",{children:[" - ",M.find(e=>e.producttypeID===P)?.Name||"selected type"]})]}),!T&&null!==E&&I.length>0&&(0,i.jsxs)("p",{className:"text-lg",children:["Browsing:"," ",(0,i.jsx)("span",{className:"font-semibold",children:I.find(e=>e.id===E)?.name}),null!==P&&M.length>0&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("span",{children:" - "}),(0,i.jsx)("span",{className:"font-semibold",children:M.find(e=>e.producttypeID===P)?.Name})]})]}),!T&&null===E&&null!==P&&M.length>0&&(0,i.jsxs)("p",{className:"text-lg",children:["Browsing:"," ",(0,i.jsx)("span",{className:"font-semibold",children:M.find(e=>e.producttypeID===P)?.Name})]})]}),(0,i.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[(0,i.jsx)("div",{className:"lg:w-1/4 space-y-6",children:(0,i.jsxs)("div",{className:"bg-white p-4 rounded-lg shadow",children:[(0,i.jsxs)("h2",{className:"text-lg font-semibold mb-4 flex items-center",children:[(0,i.jsx)(f,{className:"mr-2 h-5 w-5"}),"Filters"]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"font-medium mb-2",children:"Category"}),(0,i.jsxs)(rh,{value:E?.toString()||"all",onValueChange:e=>{C("all"===e?null:Number(e)),w(1),Q()},children:[(0,i.jsx)(r9,{children:(0,i.jsx)(ry,{placeholder:"All Categories"})}),(0,i.jsxs)(ne,{children:[(0,i.jsx)(nt,{value:"all",children:"All Categories"}),I.map(e=>(0,i.jsx)(nt,{value:e.id.toString(),children:e.name},e.id))]})]})]}),(0,i.jsxs)("div",{className:"hidden",children:[(0,i.jsx)("h3",{className:"font-medium mb-2",children:"Price Range"}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("input",{type:"number",placeholder:"Min",className:"w-full p-2 border rounded",onChange:e=>W({...F,min:e.target.value?Number.parseFloat(e.target.value):null})}),(0,i.jsx)("span",{children:"-"}),(0,i.jsx)("input",{type:"number",placeholder:"Max",className:"w-full p-2 border rounded",onChange:e=>W({...F,max:e.target.value?Number.parseFloat(e.target.value):null})})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"font-medium mb-2",children:"Sort By"}),(0,i.jsxs)(rh,{value:N,onValueChange:S,children:[(0,i.jsx)(r9,{children:(0,i.jsx)(ry,{})}),(0,i.jsxs)(ne,{children:[(0,i.jsx)(nt,{value:"ProductName ASC",children:"Name: A to Z"}),(0,i.jsx)(nt,{value:"ProductName DESC",children:"Name: Z to A"}),(0,i.jsx)(nt,{value:"Rating DESC",children:"Rating: High to Low"})]})]})]}),(0,i.jsx)(p.$,{className:"w-full",onClick:()=>{C(null),A(null),W({min:null,max:null}),S("Price DESC"),w(1),R(""),D(""),q({}),K({}),window.history.pushState({},"",window.location.pathname)},disabled:_,children:"Reset Filters"})]})]})}),(0,i.jsxs)("div",{className:"lg:w-3/4",children:[(0,i.jsx)("div",{className:"grid grid-cols-2 gap-4 sm:gap-6 sm:grid-cols-2 lg:grid-cols-3",children:x?Array(12).fill(0).map((e,t)=>(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,i.jsx)("div",{className:"aspect-square",children:(0,i.jsx)(m.E,{className:"h-full w-full"})}),(0,i.jsxs)("div",{className:"p-4 space-y-2",children:[(0,i.jsx)(m.E,{className:"h-4 w-full"}),(0,i.jsx)(m.E,{className:"h-4 w-3/4"}),(0,i.jsx)(m.E,{className:"h-6 w-1/3"})]}),(0,i.jsx)("div",{className:"p-4 pt-0",children:(0,i.jsxs)("div",{className:"flex w-full gap-2",children:[(0,i.jsx)(m.E,{className:"h-10 flex-1"}),(0,i.jsx)(m.E,{className:"h-10 w-10"})]})})]},`skeleton-${t}`)):(()=>{let e=(y-1)*12;return s.slice(e,e+12)})().map(e=>e.ProductId?(0,i.jsx)(ng,{product:e},e.ProductId):null)}),!x&&0===s.length&&(0,i.jsxs)("div",{className:"text-center py-12",children:[(0,i.jsx)("h3",{className:"text-xl font-semibold mb-2",children:$?"Failed to load products":"No products found"}),(0,i.jsx)("p",{className:"text-gray-500",children:$?"Please check your connection and try again":"Try adjusting your filters or search criteria"}),$&&(0,i.jsx)(p.$,{className:"mt-4",onClick:()=>Y(),children:"Retry"})]}),!x&&s.length>0&&(()=>{let e=[],t=Math.max(1,y-Math.floor(2.5)),r=Math.min(b,t+5-1);r-t+1<5&&(t=Math.max(1,r-5+1));for(let n=t;n<=r;n++)e.push((0,i.jsx)(nj,{children:(0,i.jsx)(nN,{onClick:()=>Z(n),isActive:y===n,children:n})},n));return(0,i.jsx)(nw,{className:"mt-8",children:(0,i.jsxs)(nb,{children:[(0,i.jsx)(nj,{children:(0,i.jsx)(nS,{onClick:()=>y>1&&Z(y-1),className:1===y?"pointer-events-none opacity-50":"cursor-pointer"})}),t>1&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(nj,{children:(0,i.jsx)(nN,{onClick:()=>Z(1),children:"1"})}),t>2&&(0,i.jsx)(nC,{})]}),e,r<b&&(0,i.jsxs)(i.Fragment,{children:[r<b-1&&(0,i.jsx)(nC,{}),(0,i.jsx)(nj,{children:(0,i.jsx)(nN,{onClick:()=>Z(b),children:b})})]}),(0,i.jsx)(nj,{children:(0,i.jsx)(nE,{onClick:()=>y<b&&Z(y+1),className:y===b?"pointer-events-none opacity-50":"cursor-pointer"})})]})})})()]})]})]})}nC.displayName="PaginationEllipsis"},92828:(e,t,r)=>{"use strict";r.d(t,{c:()=>o});var n=r(58009);function o(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{},97412:(e,t,r)=>{"use strict";r.d(t,{Cl:()=>n,Tt:()=>o,fX:()=>a});var n=function(){return(n=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function o(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r}Object.create;function a(e,t,r){if(r||2==arguments.length)for(var n,o=0,a=t.length;o<a;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))}Object.create,"function"==typeof SuppressedError&&SuppressedError},97643:(e,t,r)=>{"use strict";r.d(t,{Wu:()=>l,Zp:()=>i,wL:()=>s});var n=r(45512),o=r(58009),a=r(59462);let i=o.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));i.displayName="Card",o.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",e),...t})).displayName="CardHeader",o.forwardRef(({className:e,...t},r)=>(0,n.jsx)("h3",{ref:r,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t})).displayName="CardTitle",o.forwardRef(({className:e,...t},r)=>(0,n.jsx)("p",{ref:r,className:(0,a.cn)("text-sm text-muted-foreground",e),...t})).displayName="CardDescription";let l=o.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,a.cn)("p-6 pt-0",e),...t}));l.displayName="CardContent";let s=o.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,a.cn)("flex items-center p-6 pt-0",e),...t}));s.displayName="CardFooter"}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[638,320,609,875],()=>r(3970));module.exports=n})();